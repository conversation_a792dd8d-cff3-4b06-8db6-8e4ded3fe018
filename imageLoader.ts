import { ImageLoaderProps } from "next/dist/shared/lib/image-config";

const sanitize = (src: string) => {
  return src.endsWith("/") ? src.slice(0, -1) : src;
};

export default function cloudflareLoader({
  src,
  width,
  quality,
}: ImageLoaderProps): string {
  if (!src.includes("cdn-cgi")) {
    return src;
  }
  // This is a hack we do in fullImageURL so that if an image is rendered by a non-next/image component, it will still work
  // We remove the format=auto param because the loader below will add it back in
  if (src.includes("/format=auto")) {
    src = src.replace("/format=auto", "");
  }
  const params = [`width=${width}`];
  if (quality) {
    params.push(`quality=${quality}`);
  }
  return `${sanitize(src)}/${params.join(",")}`;
}
