import "@testing-library/jest-dom";

import { afterEach, beforeEach, jest } from "@jest/globals";
import {
  DiscountType,
  EmailAction,
  OrderFeeType,
  PrismaClient,
} from "@prisma/client";
import { mockDeep } from "jest-mock-extended";
import { loadEnvConfig } from "@next/env";

jest.mock("@prisma/client", () => {
  return {
    __esModule: true,

    EmailAction: EmailAction,
    OrderFeeType: OrderFeeType,
    DiscountType: DiscountType,
    PrismaClient: jest.fn(() => mockDeep<PrismaClient>()),
  };
});

export default async () => {
  const projectDir = process.cwd();
  loadEnvConfig(projectDir);
};

jest.mock("next/cache", () => ({
  __esModule: true,
  unstable_cache: (fn: () => {}) => {
    return async () => {
      return fn();
    };
  },
  revalidateTag: jest.fn(),
}));

jest.mock("~/server/lib/website", () => {
  return {
    updateWebsitePage: jest.fn(),
    updateWebsiteCollection: jest.fn(),
    updateWebsite: jest.fn(),
  };
});
jest.mock("~/pages/api/permissions");
jest.mock("~/server/lib/stripe");

jest.mock("~/pages/api/staff/[id]/resetPassword", () => ({
  sendResetPasswordEmail: jest.fn(),
}));

jest.mock("~/server/email/sendMail", () => ({
  sendReactEmail: jest.fn(),
}));

jest.mock("~/server/services/orderEvents", () => {
  return {
    handleOrderPayment: jest.fn(),
    handleNewOrder: jest.fn(),
    handleQuoteCreation: jest.fn(),
  };
});

jest.mock("@sentry/nextjs", () => ({
  captureMessage: jest.fn(),
}));
jest.mock("~/server/lib/password", () => ({
  comparePlainTextPassword: jest.fn(),
  handlePlainTextPassword: jest.fn(),
}));

beforeEach(() => {
  jest.clearAllMocks();
});
afterEach(() => {
  jest.clearAllMocks();
});
