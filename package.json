{"name": "platform", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev", "email": "email dev", "lint": "next lint", "lint:error": "next lint --quiet", "start": "next start", "start:prod": "prisma migrate deploy && node server.js", "test": "jest", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate-fee": "node ./prisma/migrations/20241004010834_order_fee/migrate_order_fees.js", "db:migrate-users-org": "node ./prisma/migrations/20250217010934_organization_creation/migrate_organizations.js && node ./prisma/migrations/20250217010934_organization_creation/migrate_users.js", "db:migrate-org-relationships": "node ./prisma/migrations/20250217190048_org_relationships/migrate_relationships.js", "honeycomb": "next build && node -r ./tracing.js .next"}, "precommit": ["lint:error", "test"], "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@aws-sdk/client-s3": "^3.472.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@fontsource-variable/inter": "^5.0.16", "@fontsource/inter": "^5.0.16", "@fontsource/open-sans": "^5.0.20", "@fontsource/raleway": "^5.0.16", "@googlemaps/google-maps-services-js": "^3.3.42", "@hookform/resolvers": "^3.3.4", "@hubspot/api-client": "^12.0.1", "@measured/puck": "^0.18.2", "@next-auth/prisma-adapter": "^1.0.7", "@opentelemetry/auto-instrumentations-node": "^0.56.0", "@opentelemetry/exporter-jaeger": "^1.30.1", "@opentelemetry/exporter-trace-otlp-http": "^0.57.1", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-node": "^0.57.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.28.0", "@opentelemetry/winston-transport": "^0.10.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^5.14.0", "@prisma/instrumentation": "^6.2.1", "@prp/blocks": "file:deps/prp-blocks-2.9.0.tgz", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.1.4", "@radix-ui/react-scroll-area": "^1.1.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.4", "@radix-ui/react-toggle-group": "^1.1.4", "@radix-ui/react-toolbar": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.4", "@react-email/components": "0.0.14", "@react-google-maps/api": "^2.19.2", "@sentry/nextjs": "^8.53.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.3.0", "@t3-oss/env-nextjs": "^0.7.1", "@tanstack/react-query": "^5.51.15", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-color": "^2.6.6", "@tiptap/extension-image": "^2.6.2", "@tiptap/extension-link": "^2.6.2", "@tiptap/extension-placeholder": "^2.6.2", "@tiptap/extension-text-align": "^2.6.2", "@tiptap/extension-text-style": "^2.6.6", "@tiptap/extension-underline": "^2.6.6", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@tiptap/suggestion": "^2.6.2", "@twilio/voice-sdk": "^2.10.2", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^0.2.1", "crisp-sdk-web": "^1.0.25", "date-fns": "^3.3.1", "embla-carousel-react": "^8.0.0-rc19", "html-to-image": "^1.11.11", "lodash.debounce": "^4.0.8", "lucide-react": "^0.407.0", "motion": "^12.7.4", "next": "^15.1.6", "next-auth": "^4.24.11", "posthog-js": "^1.147.0", "prom-client": "^15.1.3", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.0", "react-dnd": "^16.0.1", "react-dom": "18.2.0", "react-email": "3.0.6", "react-hook-form": "^7.49.3", "react-loading-skeleton": "^3.4.0", "react-resizable-panels": "^1.0.9", "react-signature-canvas": "^1.0.6", "recharts": "^2.15.1", "resend": "^3.5.0", "sharp": "^0.33.4", "slugify": "^1.6.6", "sonner": "^1.3.1", "stripe": "^14.11.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "twilio": "^5.0.3", "vaul": "^0.8.8", "yup": "^1.3.2", "zod": "^3.22.4"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/bcrypt": "^5.0.2", "@types/eslint": "^8.44.7", "@types/jest": "^29.5.12", "@types/lodash.debounce": "^4.0.9", "@types/node": "^18.17.0", "@types/react": "^18.2.37", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.15", "@types/react-signature-canvas": "^1.0.5", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.20", "eslint": "^8.54.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-mock-extended": "^3.0.7", "node-mocks-http": "^1.15.0", "postcss": "^8.4.33", "pre-commit": "^1.2.2", "prettier": "^3.1.1", "prisma": "^5.22.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "typescript": "^5.1.6"}, "ct3aMetadata": {"initVersion": "7.24.1"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}, "packageManager": "npm@9.8.1"}