import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import DisplayTimeRange from "~/components/DatePicker/display";
import React from "react";
import { EmailOverview } from "~/query/email";
import { EmailActionsColumn } from "~/table/EmailTable/actions";

export const EmailColumns = (): ColumnDef<EmailOverview>[] => {
  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Id" />
      ),
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
    },
    {
      accessorKey: "subject",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Subject" />
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        return (
          <DisplayTimeRange
            startTime={row.getValue("createdAt")}
            endTime={null}
          />
        );
      },
    },
    EmailActionsColumn(),
  ];
};
