import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import NextLink from "next/link";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React from "react";
import { Row } from "@tanstack/react-table";
import { useDeleteEmail } from "~/query/email";

export const EmailActionsColumn = () => {
  return {
    accessorKey: "actions",
    header: () => <div className={"w-0"}></div>,
    cell: ({ row }: { row: Row<any> }) => {
      const deleteMutation = useDeleteEmail();
      return (
        <DataTableRowActions>
          <NextLink href={`/email/${row.original.id}/edit`}>
            <DropdownMenuItem>Edit</DropdownMenuItem>
          </NextLink>
        </DataTableRowActions>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
