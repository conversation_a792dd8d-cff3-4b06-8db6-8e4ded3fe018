import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import React from "react";
import { CategoryWithThumbnailAndProductCount } from "~/pages/api/categories";
import { Badge } from "~/components/ui/badge";
import { CategoryActionsColumn } from "~/table/CategoryTable/actions";
import ImageAvatarPlaceholder from "~/components/image/ImageAvatarPlaceholder";
import Link from "next/link";

export const CategoryTable =
  (): ColumnDef<CategoryWithThumbnailAndProductCount>[] => {
    return [
      {
        accessorKey: "name",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Name" />
        ),
        cell: ({ row }) => {
          const imageUpload = row.original.CategoryImageUpload[0]?.imageUpload;
          return (
            <Link
              href={`/categories/${row.original.id}/edit`}
              className="max-w-[200px] flex flex-row items-center hover:underline"
            >
              <ImageAvatarPlaceholder imageUrl={imageUpload} alt={"Category"} />
              <span className={"ml-2"}>{row.getValue("name")}</span>
            </Link>
          );
        },
      },
      {
        accessorKey: "products",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Products" />
        ),
        cell: ({ row }) => {
          return <div className="w-[30px]">{row.original.Product.length}</div>;
        },
      },
      {
        accessorKey: "display",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
          return (
            <div className={"max-w-fit"}>
              <Badge
                className={`${
                  row.getValue("display")
                    ? "bg-green-200 text-green-800 hover:bg-green-300"
                    : "bg-blue-200 text-blue-800 hover:bg-blue-300"
                }`}
              >
                {row.getValue("display") ? "Active" : "Hidden"}
              </Badge>
            </div>
          );
        },
      },
      CategoryActionsColumn(),
    ];
  };
