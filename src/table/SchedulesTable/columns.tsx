import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { Badge } from "~/components/ui/badge";
import React from "react";
import { Schedule } from ".prisma/client";
import { ScheduleActionsColumn } from "~/table/SchedulesTable/actions";
import { formatHourRange, formatTimeRange } from "~/server/lib/time";
import { ScheduleDay } from "@prisma/client";

export const ScheduleColumns = (): ColumnDef<Schedule>[] => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[200px] flex flex-row items-center">
            <span className={"ml-2"}>{row.getValue("name")}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "hours",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Hours" />
      ),
      cell: ({ row }) => {
        const original = row.original;
        if (original.closed) {
          return <p>Closed</p>;
        }
        return <p>{formatHourRange(original.openTime, original.closeTime)}</p>;
      },
    },
    {
      accessorKey: "days",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Days" />
      ),
      cell: ({ row }) => {
        const schedule = row.original;
        if (schedule.days.length === 0) {
          return <Badge variant={"warning"}>No Days Selected</Badge>;
        }
        const time = `${formatHourRange(
          schedule.openTime,
          schedule.closeTime,
        )}`;
        let status = "Open";
        if (schedule.closed) {
          status = "Closed";
        }
        let text = "";
        if (schedule.days.length === 7) {
          text = `${status} every day from ${time}`;
        }
        const onWeekendsAtAll =
          schedule.days.find(
            (day) => day === ScheduleDay.SATURDAY || day === ScheduleDay.SUNDAY,
          ) !== undefined;
        if (text === "") {
          if (!onWeekendsAtAll && schedule.days.length === 5) {
            text = `${status} every weekday from ${time}`;
          } else if (
            onWeekendsAtAll &&
            schedule.days.every(
              (day) =>
                day === ScheduleDay.SATURDAY || day === ScheduleDay.SUNDAY,
            ) &&
            schedule.days.length === 2
          ) {
            text = `${status} every weekend Day from ${time}`;
          } else if (schedule.days.length <= 3) {
            const printedDays = schedule.days
              .map(
                (day) =>
                  `${day.toString()[0]}${day
                    .toString()
                    .toLowerCase()
                    .substring(1)}`,
              )
              .join(", ");
            text = `${status} every ${printedDays} from ${time}`;
          } else {
            const printedDays = Object.entries(ScheduleDay)
              .filter((sch) => !schedule.days.includes(sch[1]))
              .map(
                (day) =>
                  `${day.toString()[0]}${day
                    .toString()
                    .toLowerCase()
                    .substring(1, 3)}`,
              )
              .join(", ");
            text = `${status} every day except ${printedDays} from ${time}`;
          }
        }
        return (
          <div className={"truncate"}>
            <p>{text}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        return (
          <div>
            <Badge
              className={`${
                row.original.active
                  ? "bg-green-200 text-green-800"
                  : "bg-blue-200 text-blue-800"
              }`}
            >
              {row.original.active ? "Active" : "Inactive"}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: "Duration",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Duration" />
      ),
      cell: ({ row }) => {
        const isForever =
          row.original.endTime.getTime() >
          new Date().getTime() + 1000 * 60 * 60 * 24 * 365;
        if (isForever) {
          return <p>Forever</p>;
        }
        return (
          <p>{formatTimeRange(row.original.startTime, row.original.endTime)}</p>
        );
      },
    },
    ScheduleActionsColumn(),
  ];
};
