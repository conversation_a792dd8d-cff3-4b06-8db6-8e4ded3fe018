import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import NextLink from "next/link";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React from "react";
import { Row, Table } from "@tanstack/react-table";
import { captureException } from "@sentry/core";

export const ScheduleActionsColumn = () => {
  return {
    accessorKey: "actions",
    header: ({ table }: { table: Table<any> }) => <div className={"w-0"}></div>,

    cell: ({ row }: { row: Row<any> }) => {
      const schedule = row.original;
      return (
        <DataTableRowActions
          remove={{
            id: row.original.id.toString(),
            name: `the schedule ${schedule.name}`,
            onDelete: (id: string) => {
              fetch(`/api/schedules/${id}/delete`, {
                method: "DELETE",
              }).catch((err) => {
                captureException(err);
              });
            },
          }}
        >
          <NextLink href={`/schedule/${row.original.id}/edit`}>
            <DropdownMenuItem>Edit</DropdownMenuItem>
          </NextLink>
        </DataTableRowActions>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
