"use client";

import { OrderState } from "@prisma/client";
import { SelectTrigger } from "@radix-ui/react-select";
import type { Table } from "@tanstack/react-table";
import { CheckCircle2 } from "lucide-react";
import * as React from "react";

import {
  DataTableActionBar,
  DataTableActionBarAction,
  DataTableActionBarSelection,
} from "~/components/data-table/action-bar";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
} from "~/components/ui/select";
import { Separator } from "~/components/ui/separator";
import { OrderWithCustomerAndPayments } from "~/pages/api/orders";
import { useUpdateOrderState } from "~/query/order";

interface OrdersTableActionBarProps {
  table: Table<OrderWithCustomerAndPayments>;
}

export function OrderTableActionBar({ table }: OrdersTableActionBarProps) {
  const rows = table.getFilteredSelectedRowModel().rows;
  const markAs = useUpdateOrderState();

  const onOrderUpdate = React.useCallback(
    ({
      field,
      value,
    }: {
      field: "state";
      value: "quote" | "active" | "confirmed" | "cancelled" | "completed";
    }) => {
      rows
        .map((row) => row.original.id)
        .map((item) => {
          markAs.mutate({
            orderId: item,
            orderState: value as OrderState,
          });
        });
    },
    [rows],
  );

  return (
    <DataTableActionBar table={table} visible={rows.length > 0}>
      <DataTableActionBarSelection table={table} />
      <Separator
        orientation="vertical"
        className="hidden data-[orientation=vertical]:h-5 sm:block"
      />
      <div className="flex items-center gap-1.5">
        <Select
          onValueChange={(
            value: "quote" | "active" | "confirmed" | "cancelled" | "completed",
          ) => onOrderUpdate({ field: "state", value })}
        >
          <SelectTrigger asChild>
            <DataTableActionBarAction
              size="icon"
              tooltip="Update status"
              isPending={false}
            >
              <CheckCircle2 />
            </DataTableActionBarAction>
          </SelectTrigger>
          <SelectContent align="center">
            <SelectGroup>
              {[
                { label: "Quote", value: "quote" },
                { label: "Active", value: "active" },
                { label: "Confirmed", value: "confirmed" },
                { label: "Cancelled", value: "cancelled" },
                { label: "Completed", value: "completed" },
              ].map((item) => (
                <SelectItem key={item.value} value={item.value}>
                  {item.label}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </DataTableActionBar>
  );
}
