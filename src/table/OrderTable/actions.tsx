import React from "react";
import { Column, Row } from "@tanstack/react-table";
import { OrderWithCustomerAndPayments } from "~/pages/api/orders";
import OrderActionsDropdown from "~/table/OrderTable/OrderActionsDropdown";

export const OrderActions = () => {
  return {
    accessorKey: "actions",
    header: ({ column }: { column: Column<any, any> }) => (
      <div className={"max-w-[10px]"}></div>
    ),
    cell: ({ row }: { row: Row<OrderWithCustomerAndPayments> }) => {
      const order = row.original;
      return <OrderActionsDropdown order={order} />;
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
