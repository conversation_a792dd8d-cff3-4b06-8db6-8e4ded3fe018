import { OrderWithCustomerAndPayments } from "~/pages/api/orders";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { Badge } from "~/components/ui/badge";
import CustomerColumnCell from "~/components/Customer/CustomerColumnCell";
import DisplayTimeRange from "~/components/DatePicker/display";
import { getCurrencyString } from "~/server/lib/currency";
import React from "react";
import { OrderActions } from "~/table/OrderTable/actions";
import NextLink from "next/link";
import SelectColumn from "~/components/ui/datatable/select-column";

export const OrderColumns = (): ColumnDef<OrderWithCustomerAndPayments>[] => {
  return [
    SelectColumn(),
    {
      meta: {
        label: "Order Id",
        placeholder: "Search by id",
        variant: "text",
      },
      accessorKey: "id",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Order" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[50px] flex-nowrap">
          <NextLink
            href={`/orders/${row.original.id}`}
            className={"hover:underline"}
          >{`#${row.original.id.toString()}`}</NextLink>
        </div>
      ),
      filterFn: "includesString",
      enableHiding: false,
      enableColumnFilter: true,
      size: 32,
    },
    {
      id: "state",
      accessorKey: "state",
      meta: {
        label: "Order State",
        variant: "select",
        options: [
          { label: "Quote", value: "quote" },
          { label: "Active", value: "active" },
          { label: "Confirmed", value: "confirmed" },
          { label: "Cancelled", value: "cancelled" },
          { label: "Completed", value: "completed" },
        ],
      },
      enableColumnFilter: true,

      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="State" />
      ),
      cell: ({ row }) => {
        return (
          <div className={"md:text-left text-right"}>
            <Badge variant="outline">{row.original.state}</Badge>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "Customer",
      enableColumnFilter: true,
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Customer" />
      ),
      cell: ({ row }) => {
        const order = row.original;
        return <CustomerColumnCell {...order.Customer} />;
      },
    },
    {
      accessorKey: "startTime",
      id: "starttime",
      meta: {
        label: "Time",
        placeholder: "Search by start time",
        variant: "date",
      },
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Time" />
      ),
      cell: ({ row }) => {
        const order = row.original;
        return <DisplayTimeRange {...order} />;
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "finalTotal",
      enableColumnFilter: true,
      meta: {
        label: "Total",
        variant: "number",
      },
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total" />
      ),
      cell: ({ row }) => {
        return <div>{getCurrencyString(row.getValue("finalTotal"))}</div>;
      },
    },
    {
      accessorKey: "paymentStatus",
      meta: {
        label: "Payment Status",
        variant: "boolean",
        options: [
          { label: "Paid", value: "paid" },
          { label: "Payment pending", value: "pending" },
        ],
      },
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Status" />
      ),
      cell: ({ row }) => {
        const order = row.original;
        const paidOff = order.totalPaid >= order.finalTotal;
        return (
          <Badge
            variant={`${paidOff ? "secondary" : "warning"}`}
            className={"gap-1"}
          >
            <span className={"truncate"}>
              {paidOff ? "Paid" : "Payment pending"}
            </span>
          </Badge>
        );
      },
    },
    {
      accessorKey: "contractStatus",
      meta: {
        label: "Contract Status",
        variant: "boolean",
        options: [
          { label: "Signed", value: "signed" },
          { label: "Pending", value: "pending" },
        ],
      },
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Contract Status" />
      ),
      cell: ({ row }) => {
        const order = row.original;
        const signed = order.Contract.some((contract) => contract.signed);
        return (
          <Badge
            variant={`${signed ? "secondary" : "warning"}`}
            className={"gap-1"}
          >
            <span className={"truncate"}>
              {signed ? "Signed" : "Contract pending"}
            </span>
          </Badge>
        );
      },
    },
    OrderActions(),
  ];
};
