import PayActionModal from "~/components/Order/PayActionModal";
import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import NextLink from "next/link";
import {
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "~/components/ui/dropdown-menu";
import { ExternalLink } from "lucide-react";
import React, { useState } from "react";
import { useUpdateOrderState } from "~/query/order";
import { usePaymentCollection } from "~/query/payments";
import { OrderState } from ".prisma/client";

type OrderActionsDropdownProps = {
  order: {
    accountId: number;
    id: number;
    state: OrderState;
    finalTotal: number;
    totalPaid: number;
    Contract?: {
      id: string;
    }[];
  };
};

const OrderActionsDropdown = ({ order }: OrderActionsDropdownProps) => {
  const paymentMutation = usePaymentCollection();
  const markAs = useUpdateOrderState();
  const [paymentOpen, setPaymentOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  return (
    <>
      <PayActionModal
        open={paymentOpen && !dropdownOpen}
        onOpenChange={(open) => {
          setPaymentOpen(open);
        }}
        amountDue={order.finalTotal - order.totalPaid}
        onSubmit={(values) =>
          paymentMutation.mutate({
            orderId: order.id,
            values,
          })
        }
        totalPrice={order.finalTotal}
      />
      <DataTableRowActions
        onOpenChange={(open) => {
          setDropdownOpen(open);
        }}
      >
        <NextLink href={"/orders/[id]"} as={`/orders/${order.id}`}>
          <DropdownMenuItem>View</DropdownMenuItem>
        </NextLink>
        <NextLink href={"/orders/[id]/edit"} as={`/orders/${order.id}/edit`}>
          <DropdownMenuItem>Edit</DropdownMenuItem>
        </NextLink>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>Change State</DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuRadioGroup
              value={order.state}
              onValueChange={(value) => {
                markAs.mutate({
                  orderId: order.id,
                  orderState: value as OrderState,
                });
              }}
            >
              {[
                OrderState.QUOTE,
                OrderState.ACTIVE,
                OrderState.COMPLETED,
                OrderState.CANCELLED,
              ].map((label) => (
                <DropdownMenuRadioItem key={label} value={label}>
                  {label}
                </DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            setTimeout(() => {
              setPaymentOpen(true);
            }, 50);
          }}
        >
          Start Payment
        </DropdownMenuItem>
        <NextLink
          href={`/public/contract/${order.accountId}/${order.id}/${
            order.Contract?.at(0)?.id || "0"
          }`}
          target={"_blank"}
        >
          <DropdownMenuItem>
            Contract
            <DropdownMenuShortcut>
              <ExternalLink className={"h-3 w-3"} />
            </DropdownMenuShortcut>
          </DropdownMenuItem>
        </NextLink>
      </DataTableRowActions>
    </>
  );
};

export default OrderActionsDropdown;
