import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import NextLink from "next/link";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React from "react";
import { Row } from "@tanstack/react-table";
import { useDeleteSurface } from "~/query/surface/mutation";

export const SetupSurfaceActionsColumn = () => {
  return {
    accessorKey: "actions",
    header: () => <div className={"w-0"}></div>,
    cell: ({ row }: { row: Row<any> }) => {
      const deleteMutation = useDeleteSurface();
      return (
        <DataTableRowActions
          remove={{
            id: row.original.id.toString(),
            name: `the setup surface ${row.original.name}`,
            onDelete: () => {
              deleteMutation.mutate(row.original.id);
            },
          }}
        >
          <NextLink href={`/surface/${row.original.id}/edit`}>
            <DropdownMenuItem>Edit</DropdownMenuItem>
          </NextLink>
        </DataTableRowActions>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
