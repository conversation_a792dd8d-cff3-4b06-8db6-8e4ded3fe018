import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import React from "react";
import { SetupSurfaceActionsColumn } from "~/table/SetupSurfaceTable/actions";
import Link from "next/link";
import { SurfaceList } from "~/query/surface/types";

export const SetupSurfaceColumns = (): ColumnDef<SurfaceList>[] => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        return (
          <Link
            href={`/surface/${row.original.id}/edit`}
            className="max-w-[200px] flex flex-row items-center hover:underline"
          >
            <span className={"ml-2"}>{row.getValue("name")}</span>
          </Link>
        );
      },
    },
    SetupSurfaceActionsColumn(),
  ];
};
