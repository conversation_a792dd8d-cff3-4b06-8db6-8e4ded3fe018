import { ColumnDef } from "@tanstack/react-table";
import { PaymentsResponse } from "~/pages/api/orders/[id]/payment";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { Badge } from "~/components/ui/badge";
import { getCurrencyString } from "~/server/lib/currency";
import DisplayTimeRange from "~/components/DatePicker/display";
import React from "react";
import { PaymentActionsColumn } from "~/table/PaymentTable/actions";

export const PaymentColumns = (): ColumnDef<PaymentsResponse>[] => {
  return [
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),

      cell: ({ row }) => {
        let variant: any = "default";
        let status = "Success";
        if (row.original.PaymentRefund?.length) {
          variant = "secondary";
          status = "Partially Refunded";
          const refundedAmount = row.original.PaymentRefund.reduce(
            (acc, refund) => acc + refund.amount,
            0,
          );
          if (refundedAmount >= row.original.amount + (row.original.tip ?? 0)) {
            variant = "destructive";
            status = "Refunded";
          }
        }
        return (
          <Badge variant={variant}>
            <p className={"text-xs"}>{status}</p>
          </Badge>
        );
      },
    },
    {
      accessorKey: "method",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Method" />
      ),
      cell: ({ row }) => {
        return (
          <div className={"flex flex-row gap-1"}>
            <p>{`${row.original.methodId
              ?.charAt(0)
              ?.toUpperCase()}${row.original.methodId?.slice(1)}`}</p>
          </div>
        );
      },
    },
    {
      accessorKey: "processor",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Id" />
      ),
      cell: ({ row }) => {
        const processorValue = row.getValue("processor") ?? "Manual";
        return <>{processorValue}</>;
      },
    },
    {
      accessorKey: "Total Paid",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total Paid" />
      ),
      cell: ({ row }) => {
        const total = row.original.amount + (row.original.tip ?? 0);
        return <>{getCurrencyString(total)}</>;
      },
    },
    {
      accessorKey: "tip",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tip" />
      ),
      cell: ({ row }) => {
        return <>{getCurrencyString(row.getValue("tip") ?? 0)}</>;
      },
      enableHiding: true,
    },
    {
      accessorKey: "Total Refunded",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Total Refunded" />
      ),
      cell: ({ row }) => {
        return (
          <>
            {getCurrencyString(
              row.original.PaymentRefund.reduce(
                (acc, refund) => acc + refund.amount,
                0,
              ),
            )}
          </>
        );
      },
      enableHiding: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Payment Date" />
      ),
      cell: ({ row }) => {
        return (
          <DisplayTimeRange
            startTime={row.getValue("createdAt")}
            endTime={null}
          />
        );
      },
    },
    PaymentActionsColumn(),
  ];
};
