import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React, { useState } from "react";
import RefundActionModal from "~/components/Order/RefundActionModal";
import {
  useCardOnFile,
  useCardsOnFile,
  useOrderRefund,
} from "~/query/payments";
import { PaymentsResponse } from "~/pages/api/orders/[id]/payment";
import { Row } from "@tanstack/react-table";
import FormDialog from "~/components/Actions/FormDialog";
import SimpleNumberForm from "~/components/FormLayout/SimpleNumberForm";
import { toast } from "sonner";

export const PaymentActionsColumn = () => {
  const refundMutation = useOrderRefund();
  const chargeMutation = useCardOnFile();

  return {
    accessorKey: "actions",
    header: () => <div className={"w-0"}></div>,
    cell: ({ row }: { row: Row<PaymentsResponse> }) => {
      const paymentsResponse = row.original;
      const cardOnFile = useCardsOnFile(Number(row.original.orderId));
      const [refundPaymentPopup, setRefundPaymentPopup] = useState(false);
      const [chargePaymentPopup, setChargePaymentPopup] = useState(false);

      return (
        <>
          <FormDialog
            open={chargePaymentPopup}
            setOpen={setChargePaymentPopup}
            title={`Add Charge To ${paymentsResponse.methodId}`}
            form={
              <SimpleNumberForm
                title={"Charge Amount"}
                onChange={(value) => {
                  const paymentMethodId = cardOnFile.data?.cards?.find(
                    (item) => {
                      return item.intentId === paymentsResponse.processorId;
                    },
                  );
                  if (!paymentMethodId) {
                    toast.error("No payment method found for this payment");
                    setChargePaymentPopup(false);
                    return;
                  }
                  chargeMutation.mutate({
                    orderId: Number(paymentsResponse.orderId),
                    values: {
                      paymentMethodId: paymentMethodId.methodId,
                      amount: value,
                      tipAmount: 0,
                    },
                  });
                  setChargePaymentPopup(false);
                }}
                action={"Charge"}
                defaultValue={undefined}
                min={1}
              />
            }
          />
          <RefundActionModal
            onSubmit={(values) => {
              refundMutation.mutate({
                orderId: Number(paymentsResponse.orderId),
                refundedPaymentId: paymentsResponse.id,
                refund: values,
              });
            }}
            payment={paymentsResponse}
            open={refundPaymentPopup}
            onOpenChange={(value) => {
              setRefundPaymentPopup(value);
            }}
          />
          <DataTableRowActions>
            <DropdownMenuItem
              onClick={() => {
                setTimeout(() => {
                  setChargePaymentPopup(true);
                }, 50);
              }}
              disabled={row.original.processorId === null}
            >
              Add Charge
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setTimeout(() => {
                  setRefundPaymentPopup(true);
                }, 50);
              }}
            >
              Start Refund
            </DropdownMenuItem>
          </DataTableRowActions>
        </>
      );
    },
  };
};
