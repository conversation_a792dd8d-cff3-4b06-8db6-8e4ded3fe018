import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { Badge } from "~/components/ui/badge";
import React from "react";
import { ProductOrderItem } from "~/server/product/types";
import { ProductActionsColumn } from "~/table/ProductTable/actions";
import ImageAvatarPlaceholder from "~/components/image/ImageAvatarPlaceholder";
import Link from "next/link";

export const ProductColumns = (): ColumnDef<ProductOrderItem>[] => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        const imageUpload = row.original.productThumbnail;
        return (
          <Link
            href={`/products/${row.original.id}/edit`}
            className="max-w-[200px] flex flex-row items-center hover:underline"
          >
            <ImageAvatarPlaceholder imageUrl={imageUpload} alt={"Product"} />
            <span className={"ml-2"}>{row.getValue("name")}</span>
          </Link>
        );
      },
    },
    {
      accessorKey: "price",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => {
        return <div className="w-[30px]">{row.getValue("price")}</div>;
      },
    },
    {
      accessorKey: "display",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        return (
          <div>
            <Badge
              className={`${
                row.getValue("display")
                  ? "bg-green-200 text-green-800"
                  : "bg-blue-200 text-blue-800"
              }`}
            >
              {row.getValue("display") ? "Active" : "Hidden"}
            </Badge>
          </div>
        );
      },
    },
    // {
    //   accessorKey: "category",
    //   header: ({ column }) => (
    //     <DataTableColumnHeader column={column} title="Category" />
    //   ),
    //   cell: ({ row }) => {
    //     return (
    //       <NextLink
    //         className="max-w-[40px]"
    //         href={`/categories/${row.original.category.id}/edit`}
    //       >
    //         <Button variant={"link"}>{row.original.category.name}</Button>
    //       </NextLink>
    //     );
    //   },
    // },
    {
      accessorKey: "quantity",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Quantity" />
      ),
      cell: ({ row }) => {
        return <div className="max-w-[20px]">{row.getValue("quantity")}</div>;
      },
    },
    ProductActionsColumn(),
  ];
};
