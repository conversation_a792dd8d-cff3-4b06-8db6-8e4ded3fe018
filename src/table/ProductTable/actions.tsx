import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import NextLink from "next/link";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React from "react";
import { Row } from "@tanstack/react-table";
import { useDeleteProduct } from "~/query/product";

export const ProductActionsColumn = () => {
  return {
    accessorKey: "actions",
    header: () => <div className={"w-0"}></div>,
    cell: ({ row }: { row: Row<any> }) => {
      const deleteMutation = useDeleteProduct();
      return (
        <DataTableRowActions
          remove={{
            id: row.original.id.toString(),
            name: `the product ${row.original.name}`,
            onDelete: (id: string) => {
              deleteMutation.mutate(id);
            },
          }}
        >
          <NextLink href={`/products/${row.original.id}/edit`}>
            <DropdownMenuItem>Edit</DropdownMenuItem>
          </NextLink>
        </DataTableRowActions>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
