import { ColumnDef } from "@tanstack/react-table";
import SelectColumn from "~/components/ui/datatable/select-column";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import React from "react";
import { CustomerActionsColumn } from "~/table/CustomerTable/actions";
import CustomerColumnCell from "~/components/Customer/CustomerColumnCell";
import { CustomerResultProps } from "~/query/customer";

const CustomerColumns = (): ColumnDef<CustomerResultProps>[] => {
  return [
    SelectColumn(),
    {
      accessorKey: "firstName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        return <CustomerColumnCell {...row.original} />;
      },
      enableColumnFilter: true,
      enableGlobalFilter: true,
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => {
        return <span>{row.getValue("email")}</span>;
      },
      enableColumnFilter: true,
      enableGlobalFilter: true,
    },
    {
      accessorKey: "phoneNumber",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Phone" />
      ),
      cell: ({ row }) => {
        return <span>{row.getValue("phoneNumber")}</span>;
      },
    },
    CustomerActionsColumn(),
  ];
};

export default CustomerColumns;
