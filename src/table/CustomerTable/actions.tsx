import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import * as Sentry from "@sentry/react";
import NextLink from "next/link";
import { DropdownMenuItem } from "~/components/ui/dropdown-menu";
import React from "react";
import { Column, Row } from "@tanstack/react-table";

export const CustomerActionsColumn = () => {
  return {
    accessorKey: "actions",
    header: ({ column }: { column: Column<any, any> }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }: { row: Row<any> }) => {
      let customerName = row.original.email;
      if (row.original.firstName || row.original.lastName) {
        customerName = `${row.original.firstName} ${row.original.lastName}`;
      }
      return (
        <DataTableRowActions
          remove={{
            id: row.original.id.toString(),
            name: customerName,
            onDelete: (id: string) => {
              fetch(`/api/customers/${id}/delete`, {
                method: "DELETE",
              }).catch((err) => {
                Sentry.captureException(err);
              });
            },
          }}
        >
          <NextLink href={`/customers/${row.original.id}/`}>
            <DropdownMenuItem>View</DropdownMenuItem>
          </NextLink>
          <NextLink href={`/customers/${row.original.id}/edit`}>
            <DropdownMenuItem>Edit</DropdownMenuItem>
          </NextLink>
        </DataTableRowActions>
      );
    },
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  };
};
