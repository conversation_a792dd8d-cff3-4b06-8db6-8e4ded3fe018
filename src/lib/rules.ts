import { Engine, RuleProperties } from "json-rules-engine";

const PriceRule = [
  "PRICE_INCREASE",
  "PRICE_DECREASE",
  "PRICE_MULTIPLY",
  "PRICE_PER_DAY",
];

export type RuleEvent = {
  type: string;
  amount: number;
  [key: string]: any;
};

export type RuleWithJson = {
  id: number;
  name: string;
  ruleJson: RuleProperties;
  priority: number;
};

export type SimpleProductWithRules = {
  id: number;
  basePrice: number;
  price: number;
  rules: number[];
};

export type PublicRule = {
  id: number;
  name: string;
  ruleJson: string;
  priority: number;
};

export const handleRules = async (
  rules: RuleWithJson[],
  products: Omit<SimpleProductWithRules, "price">[],
  cartInfo: {
    startDate: Date | string;
    endDate: Date | string;
  },
): Promise<SimpleProductWithRules[]> => {
  const engine = new Engine(
    rules.map((item) => ({
      ...item.ruleJson,
      name: item.name,
      priority: item.priority,
      conditions: {
        all: [
          {
            fact: "productId",
            operator: "in",
            value: products
              .filter((product) => product.rules.includes(item.id))
              .map((item) => item.id),
          },
          item.ruleJson.conditions,
        ],
        priority: item.priority,
      },
    })),
    {
      replaceFactsInEventParams: true,
    },
  );

  const startDate = new Date(cartInfo.startDate);
  const endDate = new Date(cartInfo.endDate);

  const durationHours =
    Math.abs(endDate.getTime() - startDate.getTime()) / 3600000;

  engine.addFact("cart", {
    startTime: {
      hour: startDate.getHours(),
      dayOfWeek: startDate.getDay(),
      day: startDate.getDate(),
      month: startDate.getMonth(),
      year: startDate.getFullYear(),
    },
    endTime: {
      hour: endDate.getHours(),
      dayOfWeek: endDate.getDay(),
      day: endDate.getDate(),
      month: endDate.getMonth(),
      year: endDate.getFullYear(),
    },
    rentalDurationHours: durationHours,
  });

  return await Promise.all(
    products.map(async (item) => {
      const event = await getRuleResults(engine, item.id);

      if (!event) {
        return { ...item, price: item.basePrice };
      }

      const newPrice = applyPriceChange(item.basePrice, event);
      return {
        ...item,
        basePrice: item.basePrice,
        price: newPrice,
      };
    }),
  );
};

export const getRuleResults = async (
  engine: Engine,
  productId: number,
): Promise<RuleEvent | null> => {
  const results = await engine.run({
    productId: productId,
  });

  const events = results.results.sort(
    (a, b) => (b.priority || 0) - (a.priority || 0),
  );

  const event = events.filter((item) => {
    if (!item.event?.type) {
      return false;
    }
    if (item.event?.params?.price === undefined) {
      return false;
    }
    return PriceRule.includes(item.event.type);
  })[0];

  if (!event?.event?.type || !event?.event?.params?.price) {
    return null;
  }

  switch (event?.event?.type) {
    case "PRICE_INCREASE":
    case "PRICE_DECREASE":
    case "PRICE_MULTIPLY":
      return {
        type: event.event.type,
        amount: event.event.params.price,
      };
    case "PRICE_PER_DAY":
      return {
        type: event.event.type,
        ignoredDays: event.event.params.ignoredDays,
        amount: event.event.params.price,
        rentalDurationHours: event.event.params.rentalDurationHours,
      };
    default:
      return null;
  }
};

export const applyPriceChange = (
  productPrice: number,
  event: RuleEvent | null,
): number => {
  if (!event) {
    return productPrice;
  }

  switch (event.type) {
    case "PRICE_INCREASE":
      return productPrice + event.amount;
    case "PRICE_DECREASE":
      return productPrice - event.amount;
    case "PRICE_MULTIPLY":
      return productPrice * event.amount;
    case "PRICE_PER_DAY":
      const rentalDurationHours = event.rentalDurationHours || 0;
      const daysRented = Math.ceil(rentalDurationHours / 24);
      const ignoreDays = event.ignoredDays || 0;
      const afterDays = daysRented - ignoreDays;
      return productPrice + productPrice * (afterDays * event.amount);
    default:
      return productPrice;
  }
};
