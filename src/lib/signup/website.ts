import { db } from "~/server/db";
import { SystemPages } from "~/pages/website";

import home from "./home.json";
import cancellation from "./cancellation.json";
import category from "./category.json";
import category_slug from "./category_slug.json";
import faq from "./faq.json";
import terms from "./terms.json";
import store from "./store.json";
import rentals from "./rentals.json";

const PAGE_MAPPING = {
  "/": home,
  cancellation: cancellation,
  category: category,
  "category/[slug]": category_slug,
  faq: faq,
  terms: terms,
  store: store,
  rentals: rentals,
};

export const createDefaultWebsite = async (
  accountId: number,
  websiteName: string,
) => {
  const pageContent = Object.entries(PAGE_MAPPING).map(
    async ([name, fileName]) => {
      return {
        accountId,
        name: SystemPages.find((page) => page.slug === name)?.name || name,
        slug: name,
        content: JSON.parse(
          JSON.stringify(fileName).replaceAll("{{ACCOUNT_NAME}}", websiteName),
        ),
        systemPage: SystemPages.some((page) => page.slug === name),
        published: true,
      };
    },
  );
  const pages = await Promise.all(pageContent);

  await db.websitePage.createMany({
    data: pages,
  });
};
