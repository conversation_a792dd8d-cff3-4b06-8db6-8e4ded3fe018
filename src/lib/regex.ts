import { string } from "yup";

// A simple and permissive email regex:
// ^[^@\s]+@[^@\s]+\.[^@\s]+$
//
// Explanation:
// [^@\s]+      : Matches one or more characters that are not '@' or whitespace (for the local part).
// @            : Matches the '@' symbol (required in all valid email addresses).
// [^@\s]+      : Matches one or more characters that are not '@' or whitespace (for the domain part).
// \.           : Matches a literal dot (needed to separate domain and top-level domain).
// [^@\s]+      : Matches one or more characters that are not '@' or whitespace (for the top-level domain).
//
// This regex errs on the side of permissiveness, allowing valid emails while preventing
// obvious mistakes like missing '@' or including spaces, but without overly restricting
// the range of valid email formats.
export const EMAIL_REGEX = /[^@\s]+@[^@\s]+\.[^@\s]+/;
export const EMAIL_TYPE = string().matches(EMAIL_REGEX, "Invalid email format");
