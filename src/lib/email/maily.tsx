import React, { type CSSProperties, Fragment } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>umn,
  Con<PERSON>er,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Tailwind,
  Text,
} from "@react-email/components";
import {
  render as reactEmailRender,
  renderAsync as reactEmailRenderAsync,
} from "@react-email/render";
import type { JSONContent } from "@tiptap/core";
import merge from "lodash/merge";
import { createId } from "@paralleldrive/cuid2";
import ContractItemList, {
  ContractItemListProps,
} from "~/components/Contract/ItemList";
import CSATFaceComponent from "~/components/CSATFaceComponent";

interface NodeOptions {
  parent?: JSONContent;
  prev?: JSONContent;
  next?: JSONContent;
}

export interface MarkType {
  [key: string]: any;
  type: string;
  attrs?: Record<string, any> | undefined;
}

const customerVariables = [
  "FIRST_NAME",
  "LAST_NAME",
  "EMAIL",
  "PHONE_NUMBER",
  "LOYALTY_POINTS",
] as const;
export type CustomerVariables = (typeof customerVariables)[number];

const customerVariableNameMap = (
  objectName: string,
): CustomerVariables | undefined => {
  const mapping: Record<string, CustomerVariables> = {
    firstName: "FIRST_NAME",
    lastName: "LAST_NAME",
    email: "EMAIL",
    phoneNumber: "PHONE_NUMBER",
    points: "LOYALTY_POINTS",
  };

  return mapping[objectName] ?? undefined;
};

const orderVariables = [
  "ORDER_NUMBER",
  "ORDER_START_DATE",
  "ORDER_END_DATE",
  "ORDER_TOTAL",
  "ORDER_STATUS",
  "ORDER_PAYMENT_LINK",
  "ORDER_CONTRACT_LINK",
  "ORDER_SURVEY_LINK",
] as const;
export type OrderVariables = (typeof orderVariables)[number];

const orderVariableNameMap = (
  objectName: string,
): OrderVariables | undefined => {
  const mapping: Record<string, OrderVariables> = {
    id: "ORDER_NUMBER",
    startTime: "ORDER_START_DATE",
    endTime: "ORDER_END_DATE",
    finalTotal: "ORDER_TOTAL",
    state: "ORDER_STATUS",
  };

  return mapping[objectName] ?? undefined;
};

const productVariables = [
  "PRODUCT_NAME",
  "PRODUCT_PRICE",
  "PRODUCT_QUANTITY",
  "PRODUCT_IMAGE",
  "PRODUCT_DESCRIPTION",
] as const;
export type ProductVariables = (typeof productVariables)[number];

const productVariableNameMap = (
  objectName: string,
): ProductVariables | undefined => {
  const mapping: Record<string, ProductVariables> = {
    name: "PRODUCT_NAME",
    price: "PRODUCT_PRICE",
    quantity: "PRODUCT_QUANTITY",
    heroImage: "PRODUCT_IMAGE",
    description: "PRODUCT_DESCRIPTION",
  };

  return mapping[objectName] ?? undefined;
};

export const allowedVariables = [
  ...customerVariables,
  ...orderVariables,
  ...productVariables,
  "QUOTE_LINK",
] as const;
export type AllowedVariables = (typeof allowedVariables)[number];

const allowedSpacers = ["sm", "md", "lg", "xl"] as const;
export type AllowedSpacers = (typeof allowedSpacers)[number];

const spacers: Record<AllowedSpacers, string> = {
  sm: "8px",
  md: "16px",
  lg: "32px",
  xl: "64px",
};

const antialiased: CSSProperties = {
  WebkitFontSmoothing: "antialiased",
  MozOsxFontSmoothing: "grayscale",
};

export interface ThemeOptions {
  colors?: {
    heading?: string;
    paragraph?: string;
    horizontal?: string;
    footer?: string;
    blockquoteBorder?: string;
    codeBackground?: string;
    codeText?: string;
    linkCardTitle?: string;
    linkCardDescription?: string;
    linkCardBadgeText?: string;
    linkCardBadgeBackground?: string;
    linkCardSubTitle?: string;
  };
  fontSize?: {
    paragraph?: string;
    footer?: {
      size?: string;
      lineHeight?: string;
    };
  };
}

export interface MailyConfig {
  /**
   * The preview text is the snippet of text that is pulled into the inbox
   * preview of an email client, usually right after the subject line.
   *
   * Default: `undefined`
   */
  preview?: string;
  /**
   * The theme object allows you to customize the colors and font sizes of the
   * rendered email.
   *
   * Default:
   * ```js
   * {
   *   colors: {
   *     heading: 'rgb(17, 24, 39)',
   *     paragraph: 'rgb(55, 65, 81)',
   *     horizontal: 'rgb(234, 234, 234)',
   *     footer: 'rgb(100, 116, 139)',
   *   },
   *   fontSize: {
   *     paragraph: '15px',
   *     footer: {
   *       size: '14px',
   *       lineHeight: '24px',
   *     },
   *   },
   * }
   * ```
   *
   * @example
   * ```js
   * const maily = new Maily(content, {
   *   theme: {
   *     colors: {
   *       heading: 'rgb(17, 24, 39)',
   *     },
   *     fontSize: {
   *       footer: {
   *         size: '14px',
   *         lineHeight: '24px',
   *       },
   *     },
   *   },
   * });
   * ```
   */
  theme?: ThemeOptions;
}

export const DEFAULT_RENDER_OPTIONS: RenderOptions = {
  pretty: false,
  plainText: false,
};

const allowedHeadings = ["h1", "h2", "h3", "h4", "h5", "h6"] as const;
type AllowedHeadings = (typeof allowedHeadings)[number];

const headings: Record<AllowedHeadings, CSSProperties> = {
  h1: {
    fontSize: "36px",
    lineHeight: "40px",
    fontWeight: 800,
  },
  h2: {
    fontSize: "30px",
    lineHeight: "36px",
    fontWeight: 700,
  },
  h3: {
    fontSize: "24px",
    lineHeight: "38px",
    fontWeight: 600,
  },
  h4: {
    fontSize: "20px",
    lineHeight: "32px",
    fontWeight: 600,
  },
  h5: {
    fontSize: "18px",
    lineHeight: "28px",
    fontWeight: 600,
  },
  h6: {
    fontSize: "16px",
    lineHeight: "24px",
    fontWeight: 600,
  },
};

export const DEFAULT_THEME: ThemeOptions = {
  colors: {
    heading: "rgb(17, 24, 39)",
    paragraph: "rgb(55, 65, 81)",
    horizontal: "rgb(234, 234, 234)",
    footer: "rgb(100, 116, 139)",
    blockquoteBorder: "rgb(209, 213, 219)",
    codeBackground: "rgb(239, 239, 239)",
    codeText: "rgb(17, 24, 39)",
    linkCardTitle: "rgb(17, 24, 39)",
    linkCardDescription: "rgb(107, 114, 128)",
    linkCardBadgeText: "rgb(17, 24, 39)",
    linkCardBadgeBackground: "rgb(254, 240, 138)",
    linkCardSubTitle: "rgb(107, 114, 128)",
  },
  fontSize: {
    paragraph: "15px",
    footer: {
      size: "14px",
      lineHeight: "24px",
    },
  },
};

const CODE_FONT_FAMILY =
  'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace';

export interface RenderOptions {
  /**
   * The options object allows you to customize the output of the rendered
   * email.
   * - `pretty` - If `true`, the output will be formatted with indentation and
   *  line breaks.
   * - `plainText` - If `true`, the output will be plain text instead of HTML.
   * This is useful for testing purposes.
   *
   * Default: `pretty` - `false`, `plainText` - `false`
   */
  pretty?: boolean;
  plainText?: boolean;
}

export type VariableFormatter = (options: {
  variable: string;
  fallback?: string;
}) => string;
export type VariableValues = Map<string, string>;
export type LinkValues = Map<string, string>;

export class Maily {
  private readonly content: JSONContent;
  private config: MailyConfig = {
    theme: DEFAULT_THEME,
  };

  private variableFormatter: VariableFormatter = ({ variable, fallback }) => {
    return fallback
      ? `{{${variable},fallback=${fallback}}}`
      : `{{${variable}}}`;
  };

  private shouldReplaceVariableValues = false;
  private contractItems: ContractItemListProps | undefined;
  private variableValues: VariableValues = new Map<string, string>();
  private linkValues: LinkValues = new Map<string, string>();
  private openTrackingPixel: string | undefined;

  constructor(content: JSONContent = { type: "doc", content: [] }) {
    this.content = content;
  }

  setContractItems(contractItems: ContractItemListProps) {
    this.contractItems = contractItems;
  }

  setPreviewText(preview?: string) {
    this.config.preview = preview;
  }

  setTheme(theme?: ThemeOptions) {
    this.config.theme = merge(this.config.theme, theme);
  }

  setVariableFormatter(formatter: VariableFormatter) {
    this.variableFormatter = formatter;
  }

  /**
   * `setVariableValue` will set the variable value.
   * It will also set `shouldReplaceVariableValues` to `true`.
   *
   * @param variable - The variable name
   * @param value - The variable value
   */
  setVariableValue(variable: string, value: string) {
    if (!this.shouldReplaceVariableValues) {
      this.shouldReplaceVariableValues = true;
    }

    this.variableValues.set(variable, value);
  }

  /**
   * `setVariableValues` will set the variable values.
   * It will also set `shouldReplaceVariableValues` to `true`.
   *
   * @param values - The variable values
   *
   * @example
   * ```js
   * const maily = new Maily(content);
   * maily.setVariableValues({
   *  name: 'John Doe',
   *  email: '<EMAIL>',
   * });
   * ```
   */
  setVariableValues(values: Record<string, string>) {
    if (!this.shouldReplaceVariableValues) {
      this.shouldReplaceVariableValues = true;
    }

    Object.entries(values).forEach(([variable, value]) => {
      this.setVariableValue(variable, value);
    });
  }

  setVariableValueFromObject(
    item: Record<string, any>,
    mapper: (key: string) => string | undefined,
  ) {
    Object.entries(item)
      .map(([variable, value]) => {
        const mappedVariable = mapper(variable);
        if (mappedVariable) {
          return [mappedVariable, value];
        }
        return [null, null];
      })
      .filter(([variable, value]) => variable && value)
      .forEach(([variable, value]) => {
        // map the variable to the correct variable name
        // and set the value
        this.setVariableValue(variable, value.toString());
      });
  }

  setVariableValuesFromCustomer(customer: Record<string, any>) {
    if (!this.shouldReplaceVariableValues) {
      this.shouldReplaceVariableValues = true;
    }

    this.setVariableValueFromObject(customer, customerVariableNameMap);
  }

  setVariableValuesFromOrder(order: Record<string, any>) {
    if (!this.shouldReplaceVariableValues) {
      this.shouldReplaceVariableValues = true;
    }

    this.setVariableValueFromObject(order, orderVariableNameMap);
  }

  setVariableValuesFromProduct(product: Record<string, any>) {
    if (!this.shouldReplaceVariableValues) {
      this.shouldReplaceVariableValues = true;
    }

    this.setVariableValueFromObject(product, productVariableNameMap);
  }

  setLinkValue(link: string, value: string) {
    this.linkValues.set(link, value);
  }

  setLinkValues(values: Record<string, string>) {
    Object.entries(values).forEach(([link, value]) => {
      this.setLinkValue(link, value);
    });
  }

  /**
   * `setOpenTrackingPixel` will set the open tracking pixel.
   *
   * @param pixel - The open tracking pixel
   */
  setOpenTrackingPixel(pixel?: string) {
    this.openTrackingPixel = pixel;
  }

  /**
   * `setShouldReplaceVariableValues` will determine whether to replace the
   * variable values or not. Otherwise, it will just return the formatted variable.
   *
   * Default: `false`
   */
  setShouldReplaceVariableValues(shouldReplace: boolean) {
    this.shouldReplaceVariableValues = shouldReplace;
  }

  getAllLinks() {
    const nodes = this.content.content || [];
    const links = new Set<string>();

    const isValidLink = (href: string) => {
      return (
        href &&
        this.isValidUrl(href) &&
        !href.startsWith("#") &&
        !href.startsWith("mailto:") &&
        !href.startsWith("tel:") &&
        typeof href === "string"
      );
    };

    const extractLinksFromNode = (node: JSONContent) => {
      if (node.type === "button") {
        const originalLink = node.attrs?.url;
        if (isValidLink(originalLink) && originalLink) {
          links.add(originalLink);
        }
      } else if (node.content) {
        node.content.forEach((childNode) => {
          if (childNode.marks) {
            childNode.marks.forEach((mark) => {
              const originalLink = mark.attrs?.href;
              if (mark.type === "link" && isValidLink(originalLink)) {
                links.add(originalLink);
              }
            });
          }
          if (childNode.content) {
            extractLinksFromNode(childNode);
          }
        });
      }
    };

    nodes.forEach((childNode) => {
      extractLinksFromNode(childNode);
    });

    return links;
  }

  private isValidUrl(href: string) {
    try {
      const _ = new URL(href);
      return true;
    } catch (err) {
      return false;
    }
  }

  renderSync(options: RenderOptions = DEFAULT_RENDER_OPTIONS): string {
    const markup = this.markup();
    return reactEmailRender(markup, options);
  }

  async renderAsync(
    options: RenderOptions = DEFAULT_RENDER_OPTIONS,
  ): Promise<string> {
    const markup = this.markup();
    return reactEmailRenderAsync(markup, options);
  }

  getNodes() {
    const nodes = this.content.content || [];
    const jsxNodes = nodes.map((node, index) => {
      const nodeOptions: NodeOptions = {
        prev: nodes[index - 1],
        next: nodes[index + 1],
        parent: node,
      };

      const component = this.renderNode(node, nodeOptions);
      if (!component) {
        return null;
      }

      return <Fragment key={createId()}>{component}</Fragment>;
    });
    return jsxNodes;
  }

  /**
   * `markup` will render the JSON content into React Email markup.
   * and return the raw React Tree.
   */
  markup() {
    const jsxNodes = this.getNodes();
    const { preview } = this.config;

    return (
      <Html>
        <Head>
          <Font
            fallbackFontFamily="sans-serif"
            fontFamily="Inter"
            fontStyle="normal"
            fontWeight={400}
            webFont={{
              url: "https://rsms.me/inter/font-files/Inter-Regular.woff2?v=3.19",
              format: "woff2",
            }}
          />
          <style
            dangerouslySetInnerHTML={{
              __html: `blockquote,h1,h2,h3,img,li,ol,p,ul{margin-top:0;margin-bottom:0}`,
            }}
          />

          <meta content="width=device-width" name="viewport" />
          <meta content="IE=edge" httpEquiv="X-UA-Compatible" />
          <meta name="x-apple-disable-message-reformatting" />
          <meta
            // http://www.html-5.com/metatags/format-detection-meta-tag.html
            // It will prevent iOS from automatically detecting possible phone numbers in a block of text
            content="telephone=no,address=no,email=no,date=no,url=no"
            name="format-detection"
          />
          <meta content="light" name="color-scheme" />
          <meta content="light" name="supported-color-schemes" />
        </Head>
        <Body>
          {preview ? (
            <Preview id="__react-email-preview">{preview}</Preview>
          ) : null}
          <Container
            style={{
              maxWidth: "465px",
              minWidth: "300px",
              width: "100%",
              marginLeft: "auto",
              marginRight: "auto",
              marginTop: "40px",
              marginBottom: "40px",
              padding: "20px",
            }}
          >
            {jsxNodes}
          </Container>
          {this.openTrackingPixel ? (
            <Img
              alt=""
              src={this.openTrackingPixel}
              style={{
                display: "none",
                width: "1px",
                height: "1px",
              }}
            />
          ) : null}
        </Body>
      </Html>
    );
  }

  // `getMappedContent` will call corresponding node type
  // and return text content
  private getMappedContent(
    node: JSONContent,
    options?: NodeOptions,
  ): JSX.Element[] {
    return node.content
      ?.map((childNode) => {
        const component = this.renderNode(childNode, options);
        if (!component) {
          return null;
        }

        return <Fragment key={createId()}>{component}</Fragment>;
      })
      .filter((n) => n !== null) as JSX.Element[];
  }

  // `renderNode` will call the method of the corresponding node type
  private renderNode(
    node: JSONContent,
    options: NodeOptions = {},
  ): JSX.Element | null {
    const type = node.type || "";

    if (type in this) {
      // @ts-expect-error - `this` is not assignable to type 'never'
      return this[type]?.(node, options) as JSX.Element;
    }

    return null;
  }

  // `renderMark` will call the method of the corresponding mark type
  private renderMark(node: JSONContent): JSX.Element {
    // It will wrap the text with the corresponding mark type
    const text = node.text || <>&nbsp;</>;
    const marks = node.marks || [];

    return marks.reduce(
      (acc, mark) => {
        const type = mark.type;
        if (type in this) {
          // @ts-expect-error - `this` is not assignable to type 'never'
          return this[type]?.(mark, acc) as JSX.Element;
        }

        return <>{text}</>;
      },
      <>{text}</>,
    );
  }

  private paragraph(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const alignment = attrs?.textAlign || "left";

    const { parent, next } = options || {};
    const isParentListItem = parent?.type === "listItem";
    const isNextSpacer = next?.type === "spacer";

    return (
      <Text
        style={{
          textAlign: alignment,
          marginBottom: isParentListItem || isNextSpacer ? "0px" : "10px",
          marginTop: "0px",
          fontSize: this.config.theme?.fontSize?.paragraph,
          color: this.config.theme?.colors?.paragraph,
          ...antialiased,
        }}
      >
        {node.content ? this.getMappedContent(node) : <>&nbsp;</>}
      </Text>
    );
  }

  private text(node: JSONContent, _?: NodeOptions): JSX.Element {
    const text = node.text || "&nbsp";
    if (node.marks) {
      return this.renderMark(node);
    }

    return <>{text}</>;
  }

  private bold(_: MarkType, text: JSX.Element): JSX.Element {
    return <strong>{text}</strong>;
  }

  private italic(_: MarkType, text: JSX.Element): JSX.Element {
    return <em>{text}</em>;
  }

  private underline(_: MarkType, text: JSX.Element): JSX.Element {
    return <u>{text}</u>;
  }

  private strike(_: MarkType, text: JSX.Element): JSX.Element {
    return <s style={{ textDecoration: "line-through" }}>{text}</s>;
  }

  private link(mark: MarkType, text: JSX.Element): JSX.Element {
    const { attrs } = mark;
    let href = attrs?.href || "#";
    const target = attrs?.target || "_blank";
    const rel = attrs?.rel || "noopener noreferrer nofollow";

    // If the href value is provided, use it to replace the link
    // Otherwise, use the original link
    if (
      typeof this.linkValues === "object" ||
      typeof this.variableValues === "object"
    ) {
      href = this.linkValues.get(href) || this.variableValues.get(href) || href;
    }

    return (
      <Link
        href={href}
        rel={rel}
        style={{
          fontWeight: 500,
          textDecoration: "underline",
          color: this.config.theme?.colors?.heading,
        }}
        target={target}
      >
        {text}
      </Link>
    );
  }

  private orderProducts(mark: MarkType, text: JSX.Element): JSX.Element {
    if (!this.contractItems) {
      return <></>;
    }

    return (
      <Tailwind>
        <ContractItemList {...this.contractItems} email={true} />
      </Tailwind>
    );
  }

  private csatSurvey(mark: MarkType, text: JSX.Element): JSX.Element {
    const href =
      this.linkValues.get("ORDER_SURVEY_LINK") ||
      this.variableValues.get("ORDER_SURVEY_LINK");

    if (!href) {
      return <></>;
    }

    return (
      <Tailwind>
        <CSATFaceComponent link={href} />
      </Tailwind>
    );
  }

  private heading(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const { next, prev } = options || {};

    const level = `h${Number(attrs?.level) || 1}`;
    const alignment = attrs?.textAlign || "left";
    const isNextSpacer = next?.type === "spacer";
    const isPrevSpacer = prev?.type === "spacer";

    const { fontSize, lineHeight, fontWeight } =
      headings[level as AllowedHeadings];

    return (
      <Heading
        // @ts-expect-error - `this` is not assignable to type 'never'
        as={level}
        style={{
          textAlign: alignment,
          color: this.config.theme?.colors?.heading,
          marginBottom: isNextSpacer ? "0px" : "12px",
          marginTop: isPrevSpacer ? "0px" : "0px",
          fontSize,
          lineHeight,
          fontWeight,
        }}
      >
        {this.getMappedContent(node)}
      </Heading>
    );
  }

  private variable(node: JSONContent, _?: NodeOptions): JSX.Element {
    const { id: variable, fallback } = node.attrs || {};

    let formattedVariable = this.variableFormatter({
      variable,
      fallback,
    });

    // If `shouldReplaceVariableValues` is true, replace the variable values
    // Otherwise, just return the formatted variable
    if (this.shouldReplaceVariableValues) {
      formattedVariable =
        this.variableValues.get(variable) || fallback || formattedVariable;
    }

    return <>{formattedVariable}</>;
  }

  private horizontalRule(_: JSONContent, __?: NodeOptions): JSX.Element {
    return (
      <Hr
        style={{
          marginTop: "32px",
          marginBottom: "32px",
        }}
      />
    );
  }

  private orderedList(node: JSONContent, _?: NodeOptions): JSX.Element {
    return (
      <Container>
        <ol
          style={{
            marginTop: "0px",
            marginBottom: "20px",
            paddingLeft: "26px",
            listStyleType: "decimal",
          }}
        >
          {this.getMappedContent(node)}
        </ol>
      </Container>
    );
  }

  private bulletList(node: JSONContent, _?: NodeOptions): JSX.Element {
    return (
      <Container
        style={{
          maxWidth: "100%",
        }}
      >
        <ul
          style={{
            marginTop: "0px",
            marginBottom: "20px",
            paddingLeft: "26px",
            listStyleType: "disc",
          }}
        >
          {this.getMappedContent(node)}
        </ul>
      </Container>
    );
  }

  private listItem(node: JSONContent, options?: NodeOptions): JSX.Element {
    return (
      <Container
        style={{
          maxWidth: "100%",
        }}
      >
        <li
          style={{
            marginBottom: "8px",
            paddingLeft: "6px",
            ...antialiased,
          }}
        >
          {this.getMappedContent(node, { ...options, parent: node })}
        </li>
      </Container>
    );
  }

  private button(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const {
      text,
      url,
      variant,
      buttonColor,
      textColor,
      borderRadius,
      // @TODO: Update the attribute to `textAlign`
      alignment = "left",
    } = attrs || {};

    let radius: string | undefined = "0px";
    if (borderRadius === "round") {
      radius = "9999px";
    } else if (borderRadius === "smooth") {
      radius = "6px";
    }

    const { next } = options || {};
    const isNextSpacer = next?.type === "spacer";

    const href =
      this.linkValues.get(url) || this.variableValues.get(url) || url;

    return (
      <Container
        style={{
          textAlign: alignment,
          maxWidth: "100%",
          marginBottom: isNextSpacer ? "0px" : "20px",
        }}
      >
        <Button
          href={href}
          style={{
            color: String(textColor),
            backgroundColor:
              variant === "filled" ? String(buttonColor) : "transparent",
            borderColor: String(buttonColor),
            padding: variant === "filled" ? "12px 34px" : "10px 34px",
            borderWidth: "2px",
            borderStyle: "solid",
            textDecoration: "none",
            fontSize: "14px",
            fontWeight: 500,
            borderRadius: radius,
          }}
        >
          {text}
        </Button>
      </Container>
    );
  }

  private spacer(node: JSONContent, _?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const { height = "auto" } = attrs || {};

    return (
      <Container
        style={{
          height: spacers[height as AllowedSpacers] || height,
        }}
      />
    );
  }

  private hardBreak(_: JSONContent, __?: NodeOptions): JSX.Element {
    return <br />;
  }

  private image(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const {
      src,
      alt,
      title,
      width = "auto",
      height = "auto",
      alignment = "center",
      externalLink = "",
    } = attrs || {};

    const { next } = options || {};
    const isNextSpacer = next?.type === "spacer";

    const mainImage = (
      <Img
        alt={alt || title || "Image"}
        src={src}
        style={{
          height,
          width,
          maxWidth: "100%",
          outline: "none",
          border: "none",
          textDecoration: "none",
        }}
        title={title || alt || "Image"}
      />
    );

    return (
      <Row
        style={{
          marginTop: "0px",
          marginBottom: isNextSpacer ? "0px" : "32px",
        }}
      >
        <Column align={alignment}>
          {externalLink ? (
            <a
              href={externalLink}
              rel="noopener noreferrer"
              style={{
                display: "block",
                maxWidth: "100%",
                textDecoration: "none",
              }}
              target="_blank"
            >
              {mainImage}
            </a>
          ) : (
            mainImage
          )}
        </Column>
      </Row>
    );
  }

  private footer(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const { textAlign = "left" } = attrs || {};

    const { next } = options || {};
    const isNextSpacer = next?.type === "spacer";

    return (
      <Text
        style={{
          fontSize: this.config.theme?.fontSize?.footer?.size,
          lineHeight: this.config.theme?.fontSize?.footer?.lineHeight,
          color: this.config.theme?.colors?.footer,
          marginTop: "0px",
          marginBottom: isNextSpacer ? "0px" : "20px",
          textAlign,
          ...antialiased,
        }}
      >
        {this.getMappedContent(node)}
      </Text>
    );
  }

  private blockquote(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { next, prev } = options || {};
    const isNextSpacer = next?.type === "spacer";
    const isPrevSpacer = prev?.type === "spacer";

    return (
      <blockquote
        style={{
          borderLeftWidth: "4px",
          borderLeftStyle: "solid",
          borderLeftColor: this.config.theme?.colors?.blockquoteBorder,
          paddingLeft: "16px",
          marginLeft: "0px",
          marginRight: "0px",
          marginTop: isPrevSpacer ? "0px" : "20px",
          marginBottom: isNextSpacer ? "0px" : "20px",
        }}
      >
        {this.getMappedContent(node)}
      </blockquote>
    );
  }
  private code(_: MarkType, text: JSX.Element): JSX.Element {
    return (
      <code
        style={{
          backgroundColor: this.config.theme?.colors?.codeBackground,
          color: this.config.theme?.colors?.codeText,
          padding: "2px 4px",
          borderRadius: "6px",
          fontFamily: CODE_FONT_FAMILY,
          fontWeight: 400,
          letterSpacing: 0,
        }}
      >
        {text}
      </code>
    );
  }
  private linkCard(node: JSONContent, options?: NodeOptions): JSX.Element {
    const { attrs } = node;
    const { next } = options || {};
    const isNextSpacer = next?.type === "spacer";

    const width = 600;
    const aspectRatio = 16 / 9;
    const height = width / aspectRatio;

    const { title, description, link, linkTitle, image, badgeText, subTitle } =
      attrs || {};
    const href =
      this.linkValues.get(link) || this.variableValues.get(link) || link || "#";

    return (
      <a
        href={href}
        rel="noopener noreferrer"
        style={{
          border: "1px solid #eaeaea",
          borderRadius: "10px",
          textDecoration: "none",
          color: "inherit",
          display: "block",
          marginBottom: isNextSpacer ? "0px" : "20px",
        }}
        target="_blank"
      >
        {image ? (
          <Row
            style={{
              marginBottom: "6px",
            }}
          >
            <Column
              style={{
                width: `${width}px`,
                height: `${height}px`,
              }}
            >
              <Img
                alt={title || "Link Card"}
                src={image}
                style={{
                  borderRadius: "10px 10px 0 0",
                  width: "100%",
                  height: "100%",
                  objectFit: "cover",
                }}
                title={title || "Link Card"}
              />
            </Column>
          </Row>
        ) : null}

        <Row
          style={{
            padding: "15px",
            marginTop: 0,
            marginBottom: 0,
          }}
        >
          <Column
            style={{
              verticalAlign: "top",
            }}
          >
            <Row
              align={undefined}
              style={{
                marginBottom: "8px",
                marginTop: "0px",
              }}
              width="auto"
            >
              <Column>
                <Text
                  style={{
                    fontSize: "18px",
                    fontWeight: 600,
                    color: this.config.theme?.colors?.linkCardTitle,
                    margin: "0px",
                    ...antialiased,
                  }}
                >
                  {title}
                </Text>
              </Column>
              {badgeText || subTitle ? (
                <Column
                  style={{
                    paddingLeft: "6px",
                    verticalAlign: "middle",
                  }}
                >
                  {badgeText ? (
                    <span
                      style={{
                        fontWeight: 600,
                        color: this.config.theme?.colors?.linkCardBadgeText,
                        padding: "4px 8px",
                        borderRadius: "8px",
                        backgroundColor:
                          this.config.theme?.colors?.linkCardBadgeBackground,
                        fontSize: "12px",
                        lineHeight: "12px",
                      }}
                    >
                      {badgeText}
                    </span>
                  ) : null}{" "}
                  {subTitle && !badgeText ? (
                    <span
                      style={{
                        fontWeight: "normal",
                        color: this.config.theme?.colors?.linkCardSubTitle,
                        fontSize: "12px",
                        lineHeight: "12px",
                      }}
                    >
                      {subTitle}
                    </span>
                  ) : null}
                </Column>
              ) : null}
            </Row>
            <Text
              style={{
                fontSize: "16px",
                color: this.config.theme?.colors?.linkCardDescription,
                marginTop: "0px",
                marginBottom: "0px",
                ...antialiased,
              }}
            >
              {description}{" "}
              {linkTitle ? (
                <a
                  href={href}
                  rel="noopener noreferrer"
                  style={{
                    color: this.config.theme?.colors?.linkCardTitle,
                    fontSize: "14px",
                    fontWeight: 600,
                    textDecoration: "underline",
                  }}
                >
                  {linkTitle}
                </a>
              ) : null}
            </Text>
          </Column>
        </Row>
      </a>
    );
  }
}
