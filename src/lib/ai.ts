import Anthropic from "@anthropic-ai/sdk";
import { AIRouteTask } from "~/components/driver/types";

const client = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

export const handleRouting = async (tasks: AIRouteTask[], date: Date) => {
  console.log("Input", JSON.stringify(tasks));
  const msg = await client.messages.create({
    model: "claude-sonnet-4-********",
    max_tokens: 10_000,
    temperature: 1,
    system: `You are an advanced route optimization system for a delivery company. Your task is to analyze delivery location data and determine the most efficient routes while adhering to various constraints.

Routing Date: ${date.toISOString()}`,
    stream: false,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: `Here is the delivery location data you need to analyze:

<delivery_location_data>
${JSON.stringify(tasks)}
</delivery_location_data>

Your goal is to create optimized routes that ensure all deliveries are completed on time. Follow these steps to complete the task:

1. Analyze the provided delivery location data, which includes the following information for each location:
   - Location ID
   - Setup Minutes: The time (in minutes) that must be allocated for setup at the delivery location
   - Distance from Depot: The number of minutes between the delivery location and the warehouse
   - End Time Window: The last possible time (in minutes) the delivery can be started to still be on time
   - Start Time Window: The first possible time (in minutes) the delivery can start
   - Distances to Other Locations: Distance (in minutes traveled) between delivery locations, which can impact total route time

2. Calculate the minimum number of routes needed to complete all deliveries.

3. Determine the optimal start time for each route.

4. Assign delivery locations to routes, ensuring that:
   - No delivery is late
   - All time windows are respected
   - Setup times are accounted for
   - Total route time is minimized

5. Optimize for minimal travel time and the fewest number of routes. Only use concurrent routes if absolutely necessary, otherwise combine the two routes into a single route.

6. Prefer routes that starts after 05:00 and end before 23:00. Morning routes that start later than 07:00 are preferable to early morning routes but not required.

7. The time the route takes directly correlates to how much labor is being paid, avoid long breaks or downtime if possible.

Before providing the final output, conduct your analysis in a thinking block wrapped in <route_optimization_analysis> tags. In this analysis:
- List all locations with their time windows and setup times.
- Calculate total travel time between locations.
- Group locations by time windows and proximity.
- Consider different route combinations and their efficiency.
- Show your reasoning process for determining the optimal routes.
- List out all constraints and consider their impact on routing.
- Evaluate different routing scenarios.
- Provide a step-by-step breakdown of your route optimization process.

It's acceptable for this analysis section to be quite long. Ensure that ALL thinking and analysis is done within these tags.

After your analysis, if a solution is possible, format the output as a JSON object containing a list of routes, with each route having a list of locationIds. Use this structure:

\`\`\`json
{
  "routes": [
    {
      "routeId": 1,
      "locations": ["loc1", "loc2", "loc3"],
      "startTime": "06:00"
    },
    {
      "routeId": 2,
      "locations": ["loc4", "loc5", "loc6"],
      "startTime": "14:00"
    }
  ]
}
\`\`\`

Ensure that your final output adheres to this JSON structure, with actual locationIds from the provided data.

If no solution is possible, simply output:
\`\`\`json
{"error": "No possible solution"}
\`\`\`

Remember, all analysis and thinking should be done within the <route_optimization_analysis> tags. The only output after these tags should be the final JSON response. Your final output should consist only of the JSON structure and should not duplicate or rehash any of the work you did in the analysis section.`,
          },
        ],
      },
    ],
    thinking: {
      type: "enabled",
      budget_tokens: 6000,
    },
  });

  const response = msg.content.find((item) => item.type === "text");

  if (!response) {
    throw new Error("No response from AI");
  }

  const responseText = (
    response as {
      type: "text";
      text: string;
    }
  )?.text?.trim();

  if (!responseText) {
    throw new Error("No text response from AI");
  }

  const [thinking, jsonBlock] = responseText
    .split("</route_optimization_analysis>")
    .map((part) => part.trim())
    .filter(Boolean);

  if (!thinking || !jsonBlock) {
    throw new Error("Invalid response format from AI");
  }

  console.log("thought", thinking);

  const jsonMatch = jsonBlock.match(/```json\s*([\s\S]*?)\s*```/);
  if (!jsonMatch || jsonMatch.length < 2) {
    throw new Error("No valid JSON block found in AI response");
  }

  const jsonString = jsonMatch[1]?.trim();

  if (!jsonString) {
    throw new Error("No JSON string found in AI response");
  }

  return JSON.parse(jsonString);
};
