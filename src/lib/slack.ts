import { z } from "zod";
import { formatDuration } from "date-fns";

const SlackMessageSchema = z.object({
  text: z.string().optional(),
  channel: z.string().optional(),
  username: z.string().optional(),
  icon_emoji: z.string().optional(),
  blocks: z.array(z.any()).optional(), // Block Kit elements
  attachments: z
    .array(
      z.object({
        color: z.string().optional(),
        title: z.string().optional(),
        text: z.string().optional(),
        url: z.string().url().optional(),
        fields: z
          .array(
            z.object({
              title: z.string(),
              value: z.string(),
              short: z.boolean().optional(),
            }),
          )
          .optional(),
        footer: z.string().optional(),
        ts: z.number().optional(),
      }),
    )
    .optional(),
});

type SlackMessage = z.infer<typeof SlackMessageSchema>;

export class SlackWebhook {
  constructor(private webhookUrl: string) {}

  async send(message: SlackMessage): Promise<boolean> {
    try {
      const validatedMessage = SlackMessageSchema.parse(message);

      const response = await fetch(this.webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(validatedMessage),
      });

      return response.ok;
    } catch (error) {
      console.error("Slack webhook failed:", error);
      return false;
    }
  }

  // Helper for SMS notifications
  async sendSMSNotification(sms: {
    from: string;
    message: string;
    timestamp: Date;
    customerName?: string;
    quickReplyButtonUrl?: string;
  }): Promise<boolean> {
    const accessoryButton = sms.quickReplyButtonUrl
      ? {
          type: "button",
          text: {
            type: "plain_text",
            text: "Reply",
          },
          url: sms.quickReplyButtonUrl,
        }
      : undefined;
    const message: SlackMessage = {
      blocks: [
        {
          type: "section",
          text: {
            type: "plain_text",
            text: sms.message,
          },
          accessory: accessoryButton,
        },
        {
          type: "context",
          elements: [
            {
              type: "plain_text",
              text: `${
                sms.customerName || sms.from
              } • ${sms.timestamp.toLocaleTimeString()}`,
            },
          ],
        },
      ],
    };
    return this.send(message);
  }

  async sendMissedCallNotification(call: {
    from: string;
    timestamp: Date;
    customerName?: string;
    duration?: number; // How long they waited before hanging up
    callbackUrl?: string;
  }): Promise<boolean> {
    const durationText = call.duration
      ? ` (${formatDuration({
          seconds: call.duration,
        })})`
      : "";

    const message: SlackMessage = {
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*📞 Missed call from ${
              call.customerName || call.from
            }*${durationText}`,
          },
          accessory: call.callbackUrl
            ? {
                type: "button",
                text: {
                  type: "plain_text",
                  text: "📞 Call Back",
                },
                url: call.callbackUrl,
              }
            : undefined,
        },
        {
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `${call.from} • ${call.timestamp.toLocaleTimeString()}`,
            },
          ],
        },
      ],
    };

    return this.send(message);
  }

  // Test the webhook
  async test(): Promise<boolean> {
    return this.send({
      text: "✅ Slack integration test successful!",
      attachments: [
        {
          color: "good",
          text: "Your notifications will appear here.",
        },
      ],
    });
  }
}

export const createSlackWebhook = (webhookUrl: string) => {
  return new SlackWebhook(webhookUrl);
};

export const SlackConfigSchema = z.object({
  webhookUrl: z.string().url("Invalid webhook URL"),
  enabled: z.boolean().default(true),
  channelName: z.string().min(1, "Channel name required").optional(),
});

export type SlackConfig = z.infer<typeof SlackConfigSchema>;
