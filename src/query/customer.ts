import { useQuery } from "@tanstack/react-query";
import { OrdersByCustomer } from "~/pages/api/customers/[id]/orders";
import { boolean, number, object, string } from "yup";
import { DocumentsByCustomer } from "~/pages/api/customers/[id]/documents";
import { MessageHistory } from "~/pages/phone";

export const CUSTOMER_CONVERSATION_QUERY = "customers";

export const CustomerSchema = object().shape({
  firstName: string().label("First Name").required(),
  lastName: string().label("Last Name").required(),
  company: string().nullable(),
  email: string().label("Email").required(),
  phoneNumber: string().nullable(),
  internalNotes: string().nullable(),
  points: number().default(0),
  reference: string().nullable(),
  banned: boolean(),
});

export type CustomerValues = typeof CustomerSchema.__outputType;

export type CustomerResultProps = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  company: string | null;
  email: string;
  phoneNumber: string | null;
};

const loadCustomer = async (
  customerId: string,
): Promise<{ customer: CustomerValues; resultProp: CustomerResultProps }> => {
  const response = await fetch(`/api/customers/${customerId}`, {
    method: "GET",
  });
  return response.json();
};

const fetchCustomers = async (
  search?: string,
): Promise<CustomerResultProps[]> => {
  const searchQuery = search ? `?search=${search}` : "";
  const response = await fetch(`/api/customers${searchQuery}`);
  const data: { customers: CustomerResultProps[] } = await response.json();
  return data.customers;
};

export const useCustomerSearch = (search: string) => {
  return useQuery({
    queryKey: [CUSTOMER_CONVERSATION_QUERY, search],
    queryFn: () => fetchCustomers(search),
  });
};

export const useCustomers = () => {
  return useQuery({
    queryKey: [CUSTOMER_CONVERSATION_QUERY],
    queryFn: () => fetchCustomers(),
  });
};

export const useCustomerResultPop = (customerId: string | undefined) => {
  return useQuery({
    queryKey: [CUSTOMER_CONVERSATION_QUERY, customerId],
    queryFn: async () => {
      if (!customerId) {
        return null;
      }
      const customer = await loadCustomer(customerId);
      return customer.resultProp;
    },
    enabled: !!customerId,
  });
};

export const useCustomer = (customerId: string | undefined) => {
  return useQuery({
    queryKey: [CUSTOMER_CONVERSATION_QUERY, customerId],
    queryFn: async () => {
      if (!customerId) {
        return null;
      }
      const customer = await loadCustomer(customerId);
      return customer.customer;
    },
    enabled: !!customerId,
  });
};

export const useCustomerOrders = (customerId: string) => {
  return useQuery({
    queryKey: [`${CUSTOMER_CONVERSATION_QUERY}_orders`, customerId],
    queryFn: async () => {
      const response = await fetch(`/api/customers/${customerId}/orders`);
      const data: { orders: OrdersByCustomer[] } = await response.json();
      return data.orders;
    },
  });
};

export const useCustomerDocuments = (customerId: string) => {
  return useQuery({
    queryKey: [`${CUSTOMER_CONVERSATION_QUERY}_documents`, customerId],
    queryFn: async () => {
      const response = await fetch(`/api/customers/${customerId}/documents`);
      const data: {
        documents: DocumentsByCustomer[];
      } = await response.json();
      return data.documents;
    },
  });
};

export const useCustomerReachData = (customerId: string) => {
  return useQuery({
    queryKey: [`${CUSTOMER_CONVERSATION_QUERY}_phone`, customerId],
    queryFn: async () => {
      const response = await fetch(`/api/customers/${customerId}/phone`);
      const data: {
        conversationId: string;
        recentInteractions: MessageHistory[];
      } = await response.json();
      return data;
    },
  });
};
