import { Data } from "@measured/puck";
import { captureMessage } from "@sentry/nextjs";
import { toast } from "sonner";
import { useMutation, useQuery } from "@tanstack/react-query";
import { WebsiteCustomCodeValues } from "~/form/WebsiteCustomCodeForm";

export const WEBSITE_QUERY = "website_saving";

const loadWebsiteSettings =
  async (): Promise<WebsiteCustomCodeValues | null> => {
    const request = await fetch("/api/websites/settings/custom-code");
    if (!request.ok) {
      return null;
    }
    const data = (await request.json()) as WebsiteCustomCodeValues;
    if (!data || Object.values(data).filter(Boolean).length === 0) {
      return null;
    }
    return data;
  };

const save = (data: Data, page: string) => {
  const loading = toast.loading("Publishing...");
  fetch(`/api/websites/pages/${encodeURIComponent(page)}/save`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      data: { ...data },
      published: true,
    }),
  }).then((response) => {
    if (!response.ok) {
      toast.dismiss(loading);
      toast.error("Failed to publish");
      captureMessage("Failed to save website data", {
        extra: {
          response,
        },
      });
    } else {
      toast.dismiss(loading);
      toast.success("Published successfully!");
      console.log("saved");
    }
  });
};

export const useSaveWebsite = () => {
  return useMutation({
    mutationFn: async ({ data, page }: { data: Data; page: string }) => {
      const currentSessionData = localStorage.getItem("websiteDataTemp");
      let sessionData: Record<string, any> = {};
      if (currentSessionData) {
        sessionData = JSON.parse(currentSessionData);
        Object.entries(sessionData).forEach(([key, value]) => {
          console.log(key, value);
        });
      }

      localStorage.removeItem("websiteDataTemp");
      return save(data, page);
    },
  });
};

export const useWebsiteSettings = () => {
  return useQuery({
    queryKey: [WEBSITE_QUERY],
    queryFn: async () => {
      return loadWebsiteSettings();
    },
  });
};
