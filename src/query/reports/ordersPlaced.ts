import { useQuery } from "@tanstack/react-query";

export type OrdersPlacedResponse = {
  sales: {
    date: string;
    total: number;
  }[];
};

export const useOrdersPlaced = (data?: { startDate: Date; endDate: Date }) => {
  return useQuery({
    enabled: !!data,
    queryKey: [
      "ORDERS_PLACED",
      data?.startDate?.toString(),
      data?.endDate?.toString(),
    ],
    queryFn: async (): Promise<OrdersPlacedResponse> => {
      if (!data) {
        return { sales: [] };
      }
      const request = await fetch(
        `/api/reports/ordersPlaced?startDate=${encodeURIComponent(
          data.startDate.toISOString(),
        )}&endDate=${encodeURIComponent(data.endDate.toISOString())}`,
        {
          method: "GET",
        },
      );
      return request.json();
    },
  });
};
