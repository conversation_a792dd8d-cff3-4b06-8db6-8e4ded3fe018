import { useQuery } from "@tanstack/react-query";

export type SalesReportByProduct = {
  sales: {
    date: string;
    // Using an index signature to allow additional properties of type number.
    [productName: string]: number | string;
  }[];
  totalByProduct: [string, number][];
  bookings: {
    label: string;
    total: number;
  }[];
};

export const useSalesByProductReport = (data?: {
  startDate: Date;
  endDate: Date;
}) => {
  return useQuery({
    enabled: !!data,
    queryKey: [
      "SALES_BY_PRODUCT_REPORT",
      data?.startDate?.toString(),
      data?.endDate?.toString(),
    ],
    queryFn: async (): Promise<SalesReportByProduct> => {
      if (!data) {
        return { sales: [], bookings: [], totalByProduct: [] };
      }
      const request = await fetch(
        `/api/reports/salesByProduct?startDate=${encodeURIComponent(
          data.startDate.toISOString(),
        )}&endDate=${encodeURIComponent(data.endDate.toISOString())}`,
        {
          method: "GET",
        },
      );
      return request.json();
    },
  });
};
