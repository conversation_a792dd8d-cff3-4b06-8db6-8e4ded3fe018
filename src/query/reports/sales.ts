import { useQuery } from "@tanstack/react-query";

export type SalesReportResponse = {
  sales: {
    date: string;
    total: number;
  }[];
  collectionReason: {
    label: string;
    total: number;
  }[];
  paymentType: {
    label: string;
    total: number;
  }[];
};

export const useSalesReport = (data?: { startDate: Date; endDate: Date }) => {
  return useQuery({
    enabled: !!data,
    queryKey: [
      "SALES_REPORT",
      data?.startDate?.toString(),
      data?.endDate?.toString(),
    ],
    queryFn: async (): Promise<SalesReportResponse> => {
      if (!data) {
        return { sales: [], collectionReason: [], paymentType: [] };
      }
      const request = await fetch(
        `/api/reports/sales?startDate=${encodeURIComponent(
          data.startDate.toISOString(),
        )}&endDate=${encodeURIComponent(data.endDate.toISOString())}`,
        {
          method: "GET",
        },
      );
      return request.json();
    },
  });
};
