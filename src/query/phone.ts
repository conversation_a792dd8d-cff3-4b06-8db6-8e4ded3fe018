import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ConversationDetails } from "~/pages/api/phone/text/[conversationId]";
import { toast } from "sonner";
import { array, boolean, object, string } from "yup";
import { PhoneResponse } from "~/pages/api/phone";
import { RecentCall } from "~/pages/api/phone/calls";

export const PHONE_CONVERSATION_QUERY = "phone_conversations";
export const PHONE_SETTINGS = "phone_settings";
export const PHONE_PROVIDER = "phone_provider";

export type RecentConversation = {
  id: number;
  name: string;
  phoneNumber: string;
  lastMessage: string;
  lastMessageTime: string;
  read: boolean;
};

export const PhoneSettingsSchema = object().shape({
  voicemailText: string().required("Voice mail text is required"),
  showCallerId: boolean().required(),
  forwardIncomingCallsToMobile: boolean().required(),
  forwardIncomingCallsTo: array(string()).of(
    string()
      .matches(
        /^[\d+\-() ]+$/,
        "Forwarding number must be a valid phone number",
      )
      .required("You must enter a phone number"),
  ),
  forwardIncomingMessagesToMobile: boolean().required(),
  mobileClickToCall: boolean().required(),
  forwardIncomingMessagesTo: array().of(
    string()
      .matches(
        /^[\d+\-() ]+$/,
        "Forwarding number must be a valid phone number",
      )
      .required("You must enter a phone number"),
  ),
  forwardIncomingMessagesToSlack: boolean().default(false),
  slackWebhookUrl: string().url("Invalid webhook URL").optional(),
  slackChannelName: string().optional(),
});

export type PhoneSettings = typeof PhoneSettingsSchema.__outputType;

const createNewMessage = async ({
  phoneNumber,
  body,
}: {
  phoneNumber: string;
  body: string;
}): Promise<{ error: string | undefined; id: number | undefined }> => {
  const request = await fetch("/api/phone/text/create", {
    method: "POST",
    body: JSON.stringify({
      phoneNumber,
      body,
    }),
    headers: {
      "Content-Type": "application/json",
    },
  });

  const response: { error: string | undefined; id: number | undefined } = {
    error: undefined,
    id: undefined,
  };

  if (!request.ok) {
    if (request.status === 403) {
      response.error = "You do not have permission to send messages";
      return response;
    }
    if (request.status >= 400 && request.status < 500) {
      response.error = "Invalid phone number";
      return response;
    }
    response.error = "Failed to send message, please try again.";
    return response;
  }
  const data: { id: number } = await request.json();
  response.id = data.id;
  return response;
};

const requestRecentCalls = async (): Promise<RecentCall[]> => {
  const request = await fetch("/api/phone/calls");

  const data: { calls: RecentCall[] } = await request.json();
  return data.calls;
};

const requestRecentConversations = async (): Promise<RecentConversation[]> => {
  const request = await fetch("/api/phone/text");

  const data: { conversations: RecentConversation[] } = await request.json();
  return data.conversations;
};

const getFullConversation = async (
  conversationId: number | undefined,
): Promise<ConversationDetails> => {
  if (!conversationId) {
    return {
      hasMore: false,
      messages: [],
      customerDetails: {
        displayName: "",
        phoneNumber: "",
      },
    };
  }

  const request = await fetch(`/api/phone/text/${conversationId}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  });

  return await request.json();
};

const requestPhoneSettings = async (): Promise<{
  settings: PhoneSettings | null;
}> => {
  const request = await fetch("/api/phone/settings");
  if (request.status === 404) {
    return { settings: null };
  }
  return await request.json();
};

const requestPhoneProviderInfo = async (): Promise<PhoneResponse | null> => {
  const request = await fetch("/api/phone/");
  if (!request.ok) {
    return null;
  }
  return await request.json();
};

export const usePhoneProviderInfo = () => {
  return useQuery({
    queryKey: [PHONE_PROVIDER],
    queryFn: requestPhoneProviderInfo,
    retry: false,
    retryOnMount: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false, // be explicit
    refetchOnReconnect: false, // probably this one
  });
};

export const usePhoneSettings = () => {
  return useQuery({
    queryKey: [PHONE_SETTINGS],
    queryFn: requestPhoneSettings,
  });
};

export const useRecentCalls = () => {
  return useQuery({
    queryKey: [`${PHONE_CONVERSATION_QUERY}_calls`],
    queryFn: requestRecentCalls,
  });
};

export const useRecentPhoneConversations = () => {
  return useQuery({
    queryKey: [PHONE_CONVERSATION_QUERY],
    queryFn: requestRecentConversations,
  });
};

export const usePhoneConversation = (conversationId: number | undefined) => {
  return useQuery({
    queryKey: [PHONE_CONVERSATION_QUERY, conversationId],
    queryFn: () => getFullConversation(conversationId),
  });
};

export const useSavePhoneSettings = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (settings: PhoneSettings) => {
      const request = await fetch("/api/phone/settings/save", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (!request.ok) {
        toast.error("Failed to save settings");
        return;
      }

      await queryClient.invalidateQueries({
        queryKey: [PHONE_SETTINGS],
      });
      await queryClient.invalidateQueries({
        queryKey: [PHONE_PROVIDER],
      });
      toast.success("Settings saved");
    },
  });
};

export const useCreateNewMessage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      phoneNumber,
      message,
    }: {
      phoneNumber: string;
      message: string;
    }) => {
      const request = await createNewMessage({
        phoneNumber: phoneNumber,
        body: message,
      });

      await queryClient.invalidateQueries({
        queryKey: [PHONE_CONVERSATION_QUERY],
      });
      return request;
    },
  });
};

export const useSendTextMessage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      conversationId,
      message,
    }: {
      conversationId: string;
      message: string;
    }) => {
      const request = await fetch(
        `/api/phone/text/${conversationId}/sendMessage`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            body: message,
          }),
        },
      );
      if (!request.ok) {
        toast.error("Failed to send message");
        return { error: "Failed to send message" };
      }
      const response = await request.json();
      await queryClient.invalidateQueries({
        queryKey: [PHONE_CONVERSATION_QUERY, Number(conversationId)],
      });
      await queryClient.invalidateQueries({
        queryKey: [PHONE_CONVERSATION_QUERY],
      });
      return response;
    },
  });
};
