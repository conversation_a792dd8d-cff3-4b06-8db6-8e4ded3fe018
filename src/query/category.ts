import { CategoryWithThumbnailAndProductCount } from "~/pages/api/categories";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { captureException } from "@sentry/core";
import { boolean, object, string } from "yup";

export const CATEGORY_QUERY_KEY = "categories";

export const CategorySchema = object().shape({
  name: string().required("Category name is required"),
  description: string().required("Category description is required"),
  display: boolean().required("You must define if the category is displayed"),
  slug: string().nullable().optional(),
  metaTitle: string().nullable().optional(),
  metaDescription: string().nullable().optional(),
  productSortStrategy: string().optional(),
});

export type CategorySchemaType = typeof CategorySchema.__outputType;

const fetchCategories = async (): Promise<
  Array<CategoryWithThumbnailAndProductCount>
> => {
  const response = await fetch(`/api/categories?include=images`);
  const results = (await response.json()) as {
    categories: CategoryWithThumbnailAndProductCount[];
  };
  return results.categories;
};

export const useCategories = () => {
  return useQuery({
    queryKey: [CATEGORY_QUERY_KEY],
    queryFn: fetchCategories,
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (categoryId: number) => {
      const request = fetch(`/api/categories/${categoryId}/delete`, {
        method: "GET",
      });
      return toast.promise(request, {
        loading: "Deleting Category...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [CATEGORY_QUERY_KEY],
          });
          return "Successfully deleted category!";
        },
        error: (error) => {
          if (error.includes("409")) {
            return "Cannot delete category with products";
          }
          captureException(new Error("Failed to delete category"), {
            extra: {
              error,
            },
          });

          return "Error deleting category";
        },
      });
    },
  });
};
