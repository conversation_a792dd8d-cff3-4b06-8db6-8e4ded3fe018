import { useQuery } from "@tanstack/react-query";
import { ChargeType } from "@prisma/client";

export const COUPON_LIST_QUERY = "coupons";

export type CouponListValues = {
  id: number;
  name: string;
  displayName: string;
  formattedDisplayName: string;
  discount: number;
  discountType: ChargeType;
  usageCount: number;
  status: string;
};

export type CouponListResponse = {
  coupons: CouponListValues[];
};

const fetchCoupons = async () => {
  const response = await fetch("/api/coupons");

  if (!response.ok) {
    throw new Error("Failed to fetch coupons");
  }

  const data: CouponListResponse = await response.json();
  return data.coupons;
};

export const useCoupons = () => {
  return useQuery({
    queryKey: [COUPON_LIST_QUERY],
    queryFn: () => fetchCoupons(),
  });
};
