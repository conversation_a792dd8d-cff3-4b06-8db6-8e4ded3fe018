import { CouponValues } from "~/form/CouponForm";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { captureException } from "@sentry/core";
import { COUPON_LIST_QUERY } from "~/query/coupon/query";

const createCoupon = async (values: CouponValues) => {
  const request = await fetch("/api/coupons/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });

  if (!request.ok) {
    toast.error(`Failed to create coupon: ${await request.text()}`);
    return;
  }

  return request.json();
};

const updateCoupon = async (id: number, values: CouponValues) => {
  const request = await fetch(`/api/coupons/${id}/edit`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });

  if (!request.ok) {
    toast.error(`Failed to update coupon: ${await request.text()}`);
    return;
  }

  return request.json();
};

const deleteCoupon = async (id: number) => {
  const request = await fetch(`/api/coupons/${id}/delete`, {
    method: "DELETE",
  });

  if (!request.ok) {
    toast.error(`Failed to delete coupon: ${await request.text()}`);
    return;
  }

  return request.json();
};

export const useCreateCouponMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (values: CouponValues) => {
      toast.promise(createCoupon(values), {
        loading: "Creating Coupon...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [COUPON_LIST_QUERY],
          });
          return "Coupon created successfully!";
        },
        error: (exc) => {
          captureException(new Error("Failed to create coupon"), {
            extra: {
              error: exc?.message || exc,
            },
          });

          return "Failed to create coupon!";
        },
      });
    },
  });
};

export const useUpdateCouponMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      values,
    }: {
      id: number;
      values: CouponValues;
    }) => {
      return toast.promise(updateCoupon(id, values), {
        loading: "Updating Coupon...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [COUPON_LIST_QUERY],
          });
          return "Coupon updated successfully!";
        },
        error: (exc) => {
          captureException(new Error("Failed to update coupon"), {
            extra: {
              error: exc?.message || exc,
            },
          });

          return "Failed to update coupon!";
        },
      });
    },
  });
};

export const useDeleteCouponMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      return toast.promise(deleteCoupon(id), {
        loading: "Deleting Coupon...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [COUPON_LIST_QUERY],
          });
          return "Coupon delete successfully!";
        },
        error: (exc) => {
          captureException(new Error("Failed to delete category"), {
            extra: {
              error: exc?.message || exc,
            },
          });

          return "Failed to delete coupon!";
        },
      });
    },
  });
};
