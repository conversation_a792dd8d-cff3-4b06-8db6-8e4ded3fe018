import { ImageUploadSimpleType } from "~/components/image/image";
import { useInfiniteQuery } from "@tanstack/react-query";

export const IMAGE_KEY = "images";

export type ImageLookupResult = {
  images: ImageUploadSimpleType[];
  cursor: string | undefined;
};

const refreshImageUploads = async (
  cursor: string,
): Promise<ImageLookupResult> => {
  const request = await fetch(
    `/api/images${cursor !== "0" ? `?cursor=${cursor}` : ""}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    },
  );
  if (!request.ok) {
    return {
      images: [],
      cursor: undefined,
    };
  }

  return await request.json();
};

export const useImages = (shouldQuery: boolean) => {
  return useInfiniteQuery({
    queryKey: [IMAGE_KEY],
    queryFn: async ({ pageParam }) => {
      return await refreshImageUploads(pageParam);
    },
    initialPageParam: "0",
    getNextPageParam: (lastPage) => lastPage?.cursor ?? undefined,
    enabled: shouldQuery,
  });
};
