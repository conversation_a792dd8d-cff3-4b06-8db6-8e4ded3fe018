import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { captureException } from "@sentry/core";
import { OrderWithCustomerAndPayments } from "~/pages/api/orders";
import { Order, OrderState } from ".prisma/client";
import { OrderWithCustomerAndPaymentsAndAddress } from "~/pages/api/orders/day";
import { MonthCountResponse } from "~/pages/api/orders/monthCount";
import { isDateBetweenOrDuring } from "~/server/lib/time";
import { isSameDay } from "date-fns";
import { OrderDate } from "~/components/Calendar/OrderCalendar";
import { OrderFormSchema } from "~/form/OrderForm";
import { OrderExtraInfo } from "~/pages/orders/[id]";

export const ORDER_QUERY_KEY = "orders";

const fetchOrders = async (): Promise<Array<OrderWithCustomerAndPayments>> => {
  const response = await fetch(`/api/orders`);
  const results = (await response.json()) as {
    orders: OrderWithCustomerAndPayments[];
  };
  return results.orders;
};

const handleOrderDate = (
  orders: Order[],
  startOfMonth: Date,
  endOfMonth: Date,
): OrderDate[] => {
  const orderDate: OrderDate[] = [];

  const startDatePointer = new Date(startOfMonth);
  while (startDatePointer <= endOfMonth) {
    try {
      const cursorDate = new Date(startDatePointer);
      const currentInfo: OrderDate = {
        date: cursorDate,
        ordersStarting: 0,
        ordersEnding: 0,
        ordersOut: 0,
      };
      orders
        .filter((order) => {
          order.startTime = new Date(order.startTime);
          order.endTime = new Date(order.endTime);

          return isDateBetweenOrDuring(
            cursorDate,
            new Date(order.startTime),
            new Date(order.endTime),
          );
        })
        .forEach((order) => {
          const sameStartEndDay = isSameDay(order.startTime, order.endTime);
          const sameStartCursorDay = isSameDay(order.startTime, cursorDate);
          const sameEndCursorDay = isSameDay(order.endTime, cursorDate);

          if (sameStartEndDay || sameStartCursorDay) {
            currentInfo.ordersStarting++;
            return;
          }
          if (!sameStartEndDay && sameEndCursorDay) {
            currentInfo.ordersEnding++;
            return;
          }
          currentInfo.ordersOut++;
        });
      orderDate.push(currentInfo);
    } catch (e) {
      captureException(e);
      console.error(e);
    }
    startDatePointer.setDate(startDatePointer.getDate() + 1);
  }
  return orderDate;
};

const sendReceiptEmail = async (
  orderId: number,
  silent: boolean,
  newOrder: boolean,
) => {
  const request = await fetch(
    `/api/orders/${orderId}/sendReceipt?silent=${silent}&newOrder=${newOrder}`,
  );
  return await request.json();
};

const fetchOrdersForMonth = async (date: Date): Promise<MonthCountResponse> => {
  const response = await fetch(
    `/api/orders/monthCount?specificDate=${date.toISOString()}`,
  );
  return await response.json();
};

const fetchOrdersByDay = async (
  date: Date,
): Promise<Array<OrderWithCustomerAndPaymentsAndAddress>> => {
  const response = await fetch(
    `/api/orders/day?startTime=${date.toISOString()}`,
  );
  const results = (await response.json()) as {
    orders: OrderWithCustomerAndPaymentsAndAddress[];
  };
  return results.orders;
};

const fetchOrder = async (
  orderId: number | undefined,
): Promise<
  | {
      order: OrderFormSchema;
      extraInfo: OrderExtraInfo;
      raw: Order;
    }
  | undefined
> => {
  if (!orderId) {
    return undefined;
  }
  const response = await fetch(`/api/orders/${orderId}`);
  return await response.json();
};

export const useOrder = (orderId: number | undefined) => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY, orderId],
    enabled: !!orderId,
    queryFn: async () =>
      fetchOrder(orderId).then((data) => {
        if (!data) {
          return undefined;
        }
        return { raw: data.raw, order: data.order, extraInfo: data.extraInfo };
      }),
  });
};

export const useOrderCalendar = (date: Date) => {
  return useQuery({
    queryKey: [`${ORDER_QUERY_KEY}_calendar`, date],
    queryFn: async () => {
      const monthOrders = await fetchOrdersForMonth(date);
      return handleOrderDate(
        monthOrders.activeOrders,
        new Date(monthOrders.startOfMonth),
        new Date(monthOrders.endOfMonth),
      );
    },
  });
};

export const useOrders = () => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY],
    queryFn: fetchOrders,
  });
};

export const useAbandonedOrders = (enabled: boolean) => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY, "abandoned"],
    queryFn: async () => {
      const response = await fetch(`/api/orders/abandoned`);
      return (await response.json())?.orders;
    },
    enabled,
  });
};

export const useOrdersByDay = (date: Date) => {
  return useQuery({
    queryKey: [ORDER_QUERY_KEY, date],
    queryFn: (): Promise<OrderWithCustomerAndPaymentsAndAddress[]> =>
      fetchOrdersByDay(date),
  });
};

export const useOrderSendReceipt = () => {
  return useMutation({
    mutationFn: async ({
      orderId,
      silent,
      newOrder,
    }: {
      orderId: number;
      silent: boolean;
      newOrder: boolean;
    }) => {
      return toast.promise(sendReceiptEmail(orderId, silent, newOrder), {
        loading: `${
          silent ? "Silently " : ""
        }Sending Order #${orderId} Receipt Email...`,
        success: () => {
          return `Receipt Email Sent!`;
        },
        error: (err) => {
          captureException(err);
          return "Order Receipt Failed!";
        },
      });
    },
  });
};

export const useOrderSendEmail = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      templateId,
    }: {
      orderId: number;
      templateId: number;
    }) => {
      const request = fetch(`/api/orders/${orderId}/emails/send`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId,
        }),
      });
      return toast.promise(request, {
        loading: `Sending Order #${orderId} Email...`,
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [ORDER_QUERY_KEY],
          });
          return `Order #${orderId} Email Sent!`;
        },
        error: (err) => {
          captureException(err);
          return "Order Email Failed!";
        },
      });
    },
  });
};

export const useUpdateOrderState = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      orderId,
      orderState,
    }: {
      orderId: number;
      orderState: OrderState;
    }) => {
      const request = fetch(`/api/orders/${orderId}/updateState`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          state: orderState,
        }),
      });
      return toast.promise(request, {
        loading: `Updating Order #${orderId} State...`,
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [ORDER_QUERY_KEY],
          });
          return `Order #${orderId} State Updated!`;
        },
        error: (err) => {
          captureException(err);
          return "Order State Update Failed!";
        },
      });
    },
  });
};
