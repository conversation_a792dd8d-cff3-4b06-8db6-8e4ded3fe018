import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { captureException } from "@sentry/core";
import { ProductOrderItem } from "~/server/product/types";

export const PRODUCT_QUERY_KEY = "products";

export type ProductFilters = {
  excludedIds?: number[];
  search?: string;
  limit?: number;
  startTime?: Date;
  endTime?: Date;
};

const fetchProducts = async ({
  excludedIds,
  search,
  limit,
  startTime,
  endTime,
}: ProductFilters = {}): Promise<
  Array<ProductOrderItem & { accountId: number }>
> => {
  const searchQuery = search ?? "";
  const excludeQ = excludedIds?.length
    ? `&exclude=${excludedIds.join(",")}`
    : "";
  const endDateQ = endTime ? `&endTime=${endTime.toISOString()}` : "";
  const startDateQ = startTime ? `&startTime=${startTime.toISOString()}` : "";
  const limitQ = limit ? `&limit=${limit}` : "";
  const request = await fetch(
    `/api/products?search=${searchQuery}${excludeQ}${endDateQ}${startDateQ}${limitQ}`,
  );
  if (!request.ok) {
    return [];
  }
  const response = await request.json();

  return response?.products ?? [];
};

export const useAllProducts = () => {
  return useQuery({
    queryKey: [PRODUCT_QUERY_KEY],
    queryFn: () => fetchProducts(),
  });
};

export const useFilteredProducts = (filter: ProductFilters) => {
  return useQuery({
    queryKey: [PRODUCT_QUERY_KEY, filter],
    queryFn: () => fetchProducts(filter),
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (productId: string) => {
      const request = fetch(`/api/products/${productId}/delete`, {
        method: "DELETE",
      });
      return toast.promise(request, {
        loading: "Deleting Product...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [PRODUCT_QUERY_KEY],
          });
          return "Successfully deleted product!";
        },
        error: (error) => {
          captureException(new Error("Failed to delete product"), {
            extra: {
              error,
            },
          });

          return "Error deleting product!";
        },
      });
    },
  });
};
