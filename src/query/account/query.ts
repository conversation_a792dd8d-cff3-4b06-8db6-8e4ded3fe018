import { requestCurrentAccountData } from "~/query/account/request";
import { useQuery } from "@tanstack/react-query";
import { BarChartItem } from "~/components/Graph/BarChart";
import { RecentSaleObject } from "~/pages/api/account/stats/recentSales";
import { getFormattedRateOfChange } from "~/server/lib/stats";
import { StatCardProps } from "~/pages";

export const ACCOUNT_QUERY = "account_query";

export const useAccountData = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY],
    queryFn: async () => requestCurrentAccountData(),
  });
};

export const useCartSessions = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY + "cart_sessions"],
    queryFn: async (): Promise<StatCardProps> => {
      const sessions = await fetch("/api/account/stats/cartSessions", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!sessions.ok) {
        return {
          title: "Cart Sessions",
          value: 0,
          percentage: 0,
        };
      }

      const data = await sessions.json();
      return {
        title: "Cart Sessions",
        value: data?.current,
        percentage: getFormattedRateOfChange(data.current, data.previous),
      };
    },
  });
};

export const useOrderStats = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY + "order_stats"],
    queryFn: async (): Promise<StatCardProps> => {
      const orders = await fetch("/api/account/stats/orders", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!orders.ok) {
        return {
          title: "Orders This Week",
          value: 0,
          percentage: 0,
        };
      }

      const data = await orders.json();

      return {
        title: "Orders This Week",
        value: data?.current,
        percentage: getFormattedRateOfChange(data.current, data.previous),
      };
    },
  });
};

export const useRecentSales = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY + "recent_sales"],
    queryFn: async (): Promise<{
      orders: RecentSaleObject[];
      orderCount: number;
    }> => {
      return await fetch("/api/account/stats/recentSales", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }).then((res) => res.json());
    },
  });
};

export const useSalesOverview = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY + "sales_overview"],
    queryFn: async (): Promise<{
      payments: BarChartItem[];
      totalAmount: number;
    }> => {
      const response = await fetch("/api/account/stats/sales", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        return {
          payments: [],
          totalAmount: 0,
        };
      }

      return await response.json();
    },
  });
};

export type TeamResponse = {
  [orgId: string]: { id: number; name: string; logo: string }[];
};

export const useTeams = () => {
  return useQuery({
    queryKey: [ACCOUNT_QUERY, "teams"],
    queryFn: async (): Promise<TeamResponse> => {
      const response = await fetch(`/api/account/teams`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        return {};
      }
      return await response.json().then((res) => {
        return res.organizations;
      });
    },
  });
};
