import { SetupSurfaceValues, SurfaceList } from "~/query/surface/types";

export const fetchSurface = async (id: string) => {
  const request = await fetch(`/api/surface/${id}`);
  if (!request.ok) {
    return null;
  }
  const response = await request.json();

  return response?.setupSurface;
};

export const fetchSurfaces = async (
  includeArchived: boolean,
): Promise<SurfaceList[]> => {
  const request = await fetch(
    `/api/surface?includeArchived=${includeArchived}`,
  );
  if (!request.ok) {
    return [];
  }
  const response = await request.json();

  return response?.setupSurface ?? [];
};

export const deleteSurface = async (id: number) => {
  const request = await fetch(`/api/surface/${id}/delete`, {
    method: "DELETE",
  });

  return request.ok;
};

export const editSurface = async (id: number, data: SetupSurfaceValues) => {
  const request = await fetch(`/api/surface/${id}/edit`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const response = await request.json();

  return response.success ?? false;
};

export const createSurface = async (data: SetupSurfaceValues) => {
  const request = await fetch(`/api/surface/create`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const response = await request.json();

  return response.success ?? false;
};
