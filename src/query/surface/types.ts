import { boolean, number, object, string } from "yup";

export type SurfaceList = {
  id: number;
  name: string;
  archived: boolean;
  feeAmount?: number | null;
  scaleFee?: boolean;
};

export const SetupSurfaceSchema = object().shape({
  name: string().required("Name is required"),
  description: string().required("Description is required"),
  feeAmount: number().nullable().optional(),
  scaleFee: boolean().nullable().optional(),
});

export type SetupSurfaceValues = typeof SetupSurfaceSchema.__outputType;
