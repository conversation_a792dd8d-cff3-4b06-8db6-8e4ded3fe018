import { useQuery } from "@tanstack/react-query";
import { fetchSurface, fetchSurfaces } from "~/query/surface/request";

export const SURFACE_QUERY_KEY = "surfaces";

export const useAllSurfaces = (includeArchived: boolean) => {
  return useQuery({
    queryKey: [SURFACE_QUERY_KEY, includeArchived],
    queryFn: () => fetchSurfaces(includeArchived),
  });
};

export const useSurface = (surfaceId: string) => {
  return useQuery({
    queryKey: [SURFACE_QUERY_KEY, surfaceId],
    queryFn: () => fetchSurface(surfaceId),
  });
};
