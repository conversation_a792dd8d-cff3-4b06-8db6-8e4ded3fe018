import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createSurface,
  deleteSurface,
  editSurface,
} from "~/query/surface/request";
import { SURFACE_QUERY_KEY } from "~/query/surface/query";
import { toast } from "sonner";
import { SetupSurfaceValues } from "~/query/surface/types";

export const useDeleteSurface = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: number) => {
      const request = deleteSurface(id);

      return toast.promise(request, {
        loading: "Deleting surface...",
        success: () => {
          void queryClient.invalidateQueries({
            queryKey: [SURFACE_QUERY_KEY, true],
          });
          void queryClient.invalidateQueries({
            queryKey: [SURFACE_QUERY_KEY, false],
          });
          return "Surface deleted";
        },
        error: "Failed to delete surface",
      });
    },
  });
};

export const useCreateSurface = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: SetupSurfaceValues) => {
      const request = createSurface(data);

      return toast.promise(request, {
        loading: "Creating surface...",
        success: () => {
          void queryClient.invalidateQueries({
            queryKey: [SURFACE_QUERY_KEY, false],
          });
          void queryClient.invalidateQueries({
            queryKey: [SURFACE_QUERY_KEY, true],
          });
          return "Surface created";
        },
        error: "Failed to create surface",
      });
    },
  });
};

export const useEditSurface = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: number;
      data: SetupSurfaceValues;
    }) => {
      const request = editSurface(id, data);

      return toast.promise(request, {
        loading: "Editing surface...",
        success: () => {
          void queryClient.invalidateQueries({
            queryKey: [SURFACE_QUERY_KEY, id],
          });
          return "Surface edited";
        },
        error: "Failed to edit surface",
      });
    },
  });
};
