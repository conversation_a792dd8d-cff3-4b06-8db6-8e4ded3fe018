import { Schedule } from "@prisma/client";
import { useQuery } from "@tanstack/react-query";

export const useSchedules = () => {
  return useQuery({
    queryKey: ["schedules"],
    queryFn: async () => {
      const response = await fetch("/api/schedules");
      const results = (await response.json()) as {
        schedules: Schedule[];
      };
      return (
        results.schedules?.map((schedule) => ({
          ...schedule,
          createdAt: new Date(schedule.createdAt),
          updatedAt: new Date(schedule.updatedAt),
          startTime: new Date(schedule.startTime),
          endTime: new Date(schedule.endTime),
          openTime: new Date(schedule.openTime),
          closeTime: new Date(schedule.closeTime),
        })) || []
      );
    },
  });
};
