import { OrderPaymentValues } from "~/components/Order/PayAction";
import { toast } from "sonner";
import { captureException } from "@sentry/core";
import { CollectCardOnFileValues } from "~/pages/api/orders/[id]/payment/collectCardOnFile";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { OrderPaymentRefund } from "~/components/Order/RefundAction";
import { ORDER_QUERY_KEY } from "~/query/order";
import { PaymentsResponse } from "~/pages/api/orders/[id]/payment";
import { OrderPaymentInfoResponse } from "~/pages/api/orders/[id]/payment/info";

export const ORDER_PAYMENTS_QUERY_KEY = "order_payments";
export const ORDER_PAYMENT_INFO_QUERY_KEY = "order_payment_info";
export const ORDER_CARD_ON_FILE_QUERY_KEY = "order_card_on_file";

const handlePayment = async (orderId: number, values: OrderPaymentValues) => {
  const request = fetch(`/api/orders/${orderId}/payment/collect`, {
    headers: {
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify(values),
  });
  return toast.promise(request, {
    loading: "Adding New Payment...",
    success: `Successfully added ${values.paymentMethod} payment!`,
    error: (error) => {
      captureException(error);
      return "Error adding payment!";
    },
  });
};

const chargePaymentMethod = async (
  orderId: number,
  values: CollectCardOnFileValues,
) => {
  const loadingToast = toast.loading("Charging Payment on file...", {
    duration: 25_000,
  });
  const request = await fetch(
    `/api/orders/${orderId}/payment/collectCardOnFile`,
    {
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify(values),
    },
  );
  const response = await request.json();
  toast.dismiss(loadingToast);
  if (response?.success === true) {
    toast.success("Successfully charged payment on file!");
    return true;
  }
  toast.error("Failed to charge payment on file!", {
    description: response?.error || "PRP Error Occurred!",
  });
  return false;
};

const loadCardsOnFile = async (
  orderId: number,
): Promise<{
  success: boolean;
  payment?: string;
  paymentFound?: boolean;
  cards?: { methodId: string; intentId: string }[];
}> => {
  const response = await fetch(`/api/orders/${orderId}/payment/cardOnFile`);
  const data = await response.json();
  return data;
};

const loadPaymentsMade = async (
  orderId: number,
): Promise<Array<PaymentsResponse>> => {
  const response = await fetch(`/api/orders/${orderId}/payment`);
  const data: { payments: PaymentsResponse[] } = await response.json();
  return data.payments;
};

const loadPaymentInfo = async (orderId: number, includePaidItems: boolean) => {
  const response = await fetch(
    `/api/orders/${orderId}/payment/info?includePaidItems=${includePaidItems}`,
  );
  const data: OrderPaymentInfoResponse = await response.json();
  return data;
};

// Queries

export const useCardsOnFile = (orderId: number) => {
  return useQuery({
    queryKey: [ORDER_CARD_ON_FILE_QUERY_KEY, orderId],
    queryFn: () => loadCardsOnFile(orderId),
  });
};

export const useOrderPaymentInfo = (
  orderId: number,
  includePaidItems: boolean,
) => {
  return useQuery({
    queryKey: [ORDER_PAYMENT_INFO_QUERY_KEY, orderId, includePaidItems],
    queryFn: () => loadPaymentInfo(orderId, includePaidItems),
  });
};

export const useOrderPayments = (orderId: number) => {
  return useQuery({
    queryKey: [ORDER_PAYMENTS_QUERY_KEY, orderId],
    queryFn: () => loadPaymentsMade(orderId),
  });
};

// Mutations

export const useOrderRefund = () => {
  const queryClient = useQueryClient();
  return useMutation({
    onSettled: (_, _1, variables) => {
      void queryClient.invalidateQueries({
        queryKey: [ORDER_PAYMENTS_QUERY_KEY, variables.orderId],
      });
      void queryClient.invalidateQueries({
        queryKey: [ORDER_QUERY_KEY],
      });
    },
    mutationFn: async (data: {
      orderId: number;
      refundedPaymentId: number;
      refund: OrderPaymentRefund;
    }) => {
      const loadingToastId = toast.loading("Refunding Payment...", {
        duration: 25_000,
      });
      const request = await fetch(
        `/api/orders/${data.orderId}/payment/${data.refundedPaymentId}/refund`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data.refund),
        },
      );
      const response = await request.json();
      toast.dismiss(loadingToastId);
      if (request.ok) {
        if (response?.success === true) {
          toast.success("Refund Successful");
        } else {
          toast.info(
            `Refund Submitted: ${response?.message || "Refund Successful"}`,
          );
        }
      } else {
        toast.error(
          `Refund Failed: ${
            response.error ||
            response.message ||
            `Unknown Error (${response.status})!`
          }`,
        );
      }
    },
  });
};

export const useCardOnFile = () => {
  const queryClient = useQueryClient();
  return useMutation({
    onSuccess: async (_, variables) => {
      await queryClient.invalidateQueries({
        queryKey: [ORDER_PAYMENTS_QUERY_KEY, variables.orderId],
      });
      await queryClient.invalidateQueries({
        queryKey: [ORDER_QUERY_KEY],
      });
    },
    mutationFn: async (data: {
      orderId: number;
      values: CollectCardOnFileValues;
    }) => {
      return chargePaymentMethod(data.orderId, data.values);
    },
  });
};

export const usePaymentCollection = () => {
  const queryClient = useQueryClient();
  return useMutation({
    onSuccess: async (_, variables) => {
      await queryClient.invalidateQueries({
        queryKey: [ORDER_PAYMENTS_QUERY_KEY, variables.orderId],
      });
      await queryClient.invalidateQueries({
        queryKey: [ORDER_QUERY_KEY],
      });
    },
    mutationFn: async (data: {
      orderId: number;
      values: OrderPaymentValues;
    }) => {
      if (data.values.paymentMethod === "cardOnFile") {
        const loadingToastId = toast.loading(
          "Checking for payment on file...",
          {
            duration: 15_000,
          },
        );
        const response = await fetch(
          `/api/orders/${data.orderId}/payment/cardOnFile`,
        );
        const cardOnFileResponse = await response.json();
        toast.dismiss(loadingToastId);

        if (cardOnFileResponse?.paymentFound === true) {
          return chargePaymentMethod(data.orderId, {
            paymentMethodId: cardOnFileResponse?.payment,
            amount: data.values.paymentAmount,
            tipAmount: data.values?.tipAmount || 0,
          });
        } else {
          toast.error("Failed to find payment on file!");
          captureException(new Error("Failed to find payment on file!"), {
            extra: cardOnFileResponse,
          });
          return false;
        }
      } else {
        return handlePayment(data.orderId, data.values);
      }
    },
  });
};
