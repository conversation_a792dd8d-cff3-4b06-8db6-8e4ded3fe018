import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { captureException } from "@sentry/core";
import { object, string } from "yup";

export const EMAIL_QUERY_KEY = "emails";

export const EMAIL_SCHEMA = object().shape({
  name: string().required(),
  subject: string().required(),
  previewText: string(),
  text: string().required(),
});

export type EmailOverview = {
  createdAt: Date;
  id: number;
  name: string;
  subject: string;
};

export type EmailDetail = typeof EMAIL_SCHEMA.__outputType;

const fetchEmails = async (): Promise<Array<EmailOverview>> => {
  const response = await fetch(`/api/emails`);
  const results = (await response.json()) as {
    emails: EmailOverview[];
  };
  return results.emails.map((email) => ({
    ...email,
    createdAt: new Date(email.createdAt),
  }));
};

export const useEmails = () => {
  return useQuery({
    queryKey: [EMAIL_QUERY_KEY],
    queryFn: fetchEmails,
  });
};

export const useEmail = (emailId: number | undefined) => {
  return useQuery({
    queryKey: [EMAIL_QUERY_KEY, emailId],
    queryFn: async (): Promise<EmailDetail> => {
      if (!emailId) {
        return {
          name: "",
          subject: "",
          previewText: "",
          text: "",
        };
      }
      const response = await fetch(`/api/emails/${emailId}`);
      const results: { email: EmailDetail } = await response.json();
      return results.email;
    },
    enabled: !!emailId,
  });
};

export const useUpdateEmail = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      email,
      emailId,
    }: {
      email: EmailDetail;
      emailId: number;
    }) => {
      const request = fetch(`/api/emails/${emailId}/edit`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(email),
      });
      return toast.promise(request, {
        loading: "Updating Email...",
        success: () => {
          void Promise.all([
            queryClient.invalidateQueries({
              queryKey: [EMAIL_QUERY_KEY],
            }),
            queryClient.invalidateQueries({
              queryKey: [EMAIL_QUERY_KEY, emailId],
            }),
          ]);
          return "Successfully updated email!";
        },
        error: (error) => {
          captureException(new Error("Failed to update email"), {
            extra: {
              error,
            },
          });

          return "Error updating email";
        },
      });
    },
  });
};

export const useCreateEmail = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (email: EmailDetail) => {
      const request = fetch(`/api/emails/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(email),
      });
      return toast.promise(request, {
        loading: "Creating Email...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [EMAIL_QUERY_KEY],
          });
          return "Successfully created email!";
        },
        error: (error) => {
          captureException(new Error("Failed to create email"), {
            extra: {
              error,
            },
          });

          return "Error creating email";
        },
      });
    },
  });
};

export const useDeleteEmail = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (emailId: number) => {
      const request = fetch(`/api/categories/${emailId}/delete`, {
        method: "GET",
      });
      return toast.promise(request, {
        loading: "Deleting Email...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [EMAIL_QUERY_KEY],
          });
          queryClient.invalidateQueries({
            queryKey: [EMAIL_QUERY_KEY, emailId],
          });
          return "Successfully deleted email!";
        },
        error: (error) => {
          captureException(new Error("Failed to delete email"), {
            extra: {
              error,
            },
          });

          return "Error deleting email";
        },
      });
    },
  });
};
