import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { array, boolean, mixed, number, object } from "yup";
import { EmailAction } from "@prisma/client";
import { toast } from "sonner";
import { captureException } from "@sentry/core";

export const EMAIL_AUTOMATION_KEY = "emailAutomations";

const AUTOMATION_SCHEMA = object().shape({
  action: mixed<EmailAction>().oneOf(Object.values(EmailAction)).required(),
  enabled: boolean().required(),
  minutesAfter: number().required(),
  emailTemplateId: number()
    .when("enabled", (enabled, schema) => {
      if (enabled[0] !== true) {
        return schema;
      }
      return schema.min(
        0,
        "You must select an email template for an active automation",
      );
    })
    .required(),
});

export const AUTOMATIONS_SCHEMA = object().shape({
  automations: array().of(AUTOMATION_SCHEMA).required(),
});

export type AutomationBody = typeof AUTOMATION_SCHEMA.__outputType;

export type AutomationsBody = typeof AUTOMATIONS_SCHEMA.__outputType;

const fetchEmailAutomations = async (): Promise<Array<AutomationBody>> => {
  const response = await fetch(`/api/emails/automations`);
  const results: AutomationsBody = await response.json();
  return results.automations;
};

export const useEmailAutomations = () => {
  return useQuery({
    queryKey: [EMAIL_AUTOMATION_KEY],
    queryFn: fetchEmailAutomations,
  });
};

export const useUpdateEmailAutomations = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: AutomationsBody) => {
      const request = fetch(`/api/emails/automations/update`, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
          "Content-Type": "application/json",
        },
      });
      return toast.promise(request, {
        loading: "Saving email automations...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [EMAIL_AUTOMATION_KEY],
          });
          return "Email automations saved!";
        },
        error: (error) => {
          captureException(new Error("Failed to delete category"), {
            extra: {
              error,
            },
          });

          return "Failed to save email automations";
        },
      });
    },
  });
};
