import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { LogisticTimeline } from "~/pages/api/driver/timelines";

export const useCreateTimelineMutation = () => {
  return useMutation({
    mutationFn: async ({
      startTime,
    }: {
      startTime: Date;
    }): Promise<LogisticTimeline[] | null> => {
      const request = await fetch("/api/driver/timelines/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          startTime: startTime.toISOString(),
        }),
      });
      if (!request.ok) {
        toast.error(`Failed to create timeline ${await request.text()}`);
        return null;
      }
      return (await request.json()).timelines;
    },
  });
};
