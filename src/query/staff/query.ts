import { useQuery } from "@tanstack/react-query";
import { StaffValues } from "~/form/form";

export const STAFF_QUERY = `staff`;

export type StaffResponse = {
  staff: (StaffValues & {
    id: string;
  })[];
  roles: string[];
};

export const useStaff = () => {
  return useQuery({
    queryKey: [STAFF_QUERY],
    queryFn: async (): Promise<StaffResponse> => {
      const response = await fetch(`/api/staff`, {
        method: "GET",
      });
      if (!response.ok) {
        throw new Error(`Failed to fetch staff: ${response.statusText}`);
      }

      return response.json();
    },
  });
};
