import { useForm } from "react-hook-form";
import { array, boolean, date, mixed, number, object, string } from "yup";
import { AddressValidation } from "~/server/lib/location/types";
import { Form, FormControl } from "~/components/ui/form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  ProductOrderItem,
  ProductOrderItemSchema,
} from "~/server/product/types";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import FormGroup from "~/components/FormLayout/FormGroup";
import DateTimePickerField from "~/components/DatePicker/DateTimePickerField";
import ProductBrowser from "~/components/Product/ProductBrowser";
import AutoCompleteAddress from "~/components/AutoCompleteAddress";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { getCurrencyString, getPaymentInfo } from "~/server/lib/currency";
import { AddressPaymentInfoResponse } from "~/pages/api/backend/addressPaymentInfo";
import { OrderPaidItems } from "~/components/Contract/Top/types";
import PaymentDetailsCard from "~/components/Order/PaymentDetailsCard";
import { useRouter } from "next/router";
import { Label } from "~/components/ui/label";
import { useAllSurfaces } from "~/query/surface/query";
import { ChargeType, DiscountType, OrderFeeType } from "@prisma/client";
import { OrderDiscountInput, OrderFeeInput } from "~/server/lib/orderUtil";
import { useCoupons } from "~/query/coupon/query";
import { Button } from "~/components/ui/button";
import CustomerSearchFormItem from "~/components/Customer/CustomerSearchFormItem";

export const OrderFormSchema = object().shape({
  products: array().of(ProductOrderItemSchema).required(),
  startDate: date().required(),
  endDate: date()
    .when(
      "startDate",
      (startDate, schema) =>
        startDate &&
        schema.min(startDate, "End date must be after start date."),
    )
    .required(),
  eventLocation: AddressValidation.required(),
  customerId: string().required("You must select a customer."),
  setupSurface: number().min(0, "You must select a setup surface.").required(),
  fees: array(
    object().shape({
      type: mixed<OrderFeeType>().oneOf(Object.values(OrderFeeType)).required(),
      amount: number().required(),
      name: string().required().nullable(),
      taxable: boolean().default(true),
    }),
  ).required(),
  discount: array(
    object().shape({
      type: mixed<DiscountType>().oneOf(Object.values(DiscountType)).required(),
      chargeType: mixed<ChargeType>()
        .oneOf(Object.values(ChargeType))
        .required(),
      amount: number().required(),
      entityId: number().nullable(),
    }),
  ).required(),
  taxRate: number().required(),
  damageWaiver: boolean().required(),
  taxExempt: boolean().required(),
});

export type OrderFormSchema = typeof OrderFormSchema.__outputType;

type OrderFormProps = {
  initialValues?: OrderFormSchema;
  damageWaiverPercentage: number;
  onSubmit: (data: OrderFormSchema, showNotification: boolean) => void;
  paidItems?: OrderPaidItems[];
};

const updateOrAddDiscount = (
  currentDiscounts: OrderDiscountInput[],
  newDiscount: OrderDiscountInput,
) => {
  const discounts = [...currentDiscounts];
  const existingDiscount = discounts.find(
    (discount) => discount.type === newDiscount.type,
  );

  if (newDiscount.amount !== 0) {
    if (existingDiscount) {
      existingDiscount.amount = newDiscount.amount;
    } else {
      discounts.push(newDiscount);
    }
  } else {
    if (existingDiscount) {
      discounts.splice(discounts.indexOf(existingDiscount), 1);
    }
  }
  return discounts;
};

const updateOrAddFee = (
  currentFees: OrderFeeInput[],
  newFee: OrderFeeInput,
) => {
  const fees = [...currentFees];
  const existingFee = fees.find((fee) => {
    if (fee.type !== "CUSTOM_FEE" && newFee.type !== "CUSTOM_FEE") {
      return fee.type === newFee.type;
    }
    return fee.name === newFee.name;
  });

  if (newFee.amount !== 0) {
    if (existingFee) {
      existingFee.amount = newFee.amount;
      existingFee.taxable = newFee.taxable;
    } else {
      fees.push(newFee);
    }
  } else {
    if (existingFee) {
      fees.splice(fees.indexOf(existingFee), 1);
    }
  }
  return fees;
};

const OrderForm = (props: OrderFormProps) => {
  const setupSurfaceOptions = useAllSurfaces(true);
  const coupons = useCoupons();
  const [value, setValue] = React.useState<string>("");
  const [recalculate, setRecalculate] = useState<number>(0);
  const [products, setProducts] = useState<ProductOrderItem[]>([]);
  const { specificDate } = useRouter().query;

  const form = useForm<OrderFormSchema>({
    defaultValues: {
      products: [],
      startDate: new Date(),
      endDate: new Date(),
      eventLocation: {
        line1: "",
        line2: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
      },
      fees: [],
      discount: [],
      taxRate: 0,
      damageWaiver: true,
      setupSurface: -1,
      customerId: undefined,
      taxExempt: false,
      ...props.initialValues,
    },
    resolver: yupResolver(OrderFormSchema),
  });

  const getCouponFromId = useCallback(
    (id: number) => {
      return coupons.data?.find((coupon) => coupon.id === id);
    },
    [coupons.data],
  );

  const calculateTotals = useCallback(() => {
    const subTotal = form.watch("products").reduce((acc, product) => {
      return acc + product.quantity * product.price;
    }, 0);
    const taxExempt = form.watch("taxExempt") === true;
    const fees = form.watch("fees").map((fee) => {
      return {
        type: fee.type,
        amount: fee.amount,
        name: fee.name,
        taxable: fee.taxable,
      };
    });
    const discounts: OrderDiscountInput[] =
      form.watch("discount")?.map((discount) => {
        const item: OrderDiscountInput = {
          amount: discount.amount,
          chargeType: discount.chargeType,
          entityId: discount.entityId ?? null,
          type: discount.type,
        };
        return item;
      }) || [];
    return getPaymentInfo({
      baseTotal: subTotal,
      discounts: discounts,
      damageWaiver: form.watch("damageWaiver")
        ? {
            percentage: props.damageWaiverPercentage || 0,
          }
        : null,
      fees: fees,
      taxRate: taxExempt
        ? null
        : {
            percentage: form.watch("taxRate"),
          },
      totalPaid: 0,
    });
  }, [
    form.watch("products"),
    form.watch("taxExempt"),
    form.watch("fees"),
    form.watch("taxRate"),
    form.watch("damageWaiver"),
    form.watch("discount"),
    recalculate,
    products,
  ]);

  useEffect(() => {
    const isFullAddress =
      form.watch("eventLocation.line1") !== "" &&
      form.watch("eventLocation.city") !== "" &&
      form.watch("eventLocation.state") !== "" &&
      form.watch("eventLocation.postalCode")?.toString().length === 5;

    const isAddressDifferent =
      form.watch("eventLocation.line1") !==
      props.initialValues?.eventLocation.line1;

    if (isFullAddress && isAddressDifferent) {
      fetch("/api/backend/addressPaymentInfo", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          location: form.watch("eventLocation"),
        }),
      })
        .then((response) => response.json())
        .then((data: AddressPaymentInfoResponse) => {
          const fees = form.watch("fees");

          form.setValue(
            "fees",
            updateOrAddFee(fees, {
              name: null,
              type: "TRAVEL_FEE",
              amount: data.travelFee,
              taxable: true,
            }),
          );
          form.setValue("taxRate", data.taxRate);
        });
    }
  }, [form.watch("eventLocation")]);

  useEffect(() => {
    if (props.initialValues) {
      form.reset(props.initialValues);
      setProducts(form.getValues("products"));
      setValue(props.initialValues?.eventLocation.line1 ?? "");
      form.setValue("startDate", new Date(props.initialValues.startDate));
      form.setValue("endDate", new Date(props.initialValues.endDate));
    } else {
      if (specificDate) {
        form.setValue("startDate", new Date(specificDate as string));
        form.setValue("endDate", new Date(specificDate as string));
      }
    }
  }, [props.initialValues]);

  const originalPaymentInfo = useMemo(() => {
    if (props.initialValues === undefined) {
      return undefined;
    }
    const originalSubTotal =
      props.initialValues?.products.reduce((acc, product) => {
        return acc + product.quantity * product.price;
      }, 0) ?? 0;

    return getPaymentInfo({
      baseTotal: originalSubTotal,
      discounts:
        props?.initialValues?.discount?.map((item) => {
          return {
            amount: item.amount,
            chargeType:
              item.chargeType === "PERCENTAGE" ? "PERCENTAGE" : "DOLLAR",
            entityId: item.entityId ?? null,
            type: item.type,
          };
        }) || [],
      damageWaiver: props.initialValues?.damageWaiver
        ? {
            percentage: props.damageWaiverPercentage || 0,
          }
        : null,
      fees: props.initialValues.fees,

      taxRate: props.initialValues?.taxExempt
        ? null
        : {
            percentage: props.initialValues?.taxRate ?? 0,
          },
      totalPaid: 0,
    });
  }, [props.initialValues]);

  return (
    <Form
      {...form}
      onSubmit={() => {
        console.log("submit");
      }}
    >
      <Card className={"mb-3"}>
        <CardHeader>
          <CardTitle>Event Information</CardTitle>
        </CardHeader>
        <CardContent>
          <CustomerSearchFormItem
            customerId={props?.initialValues?.customerId ?? undefined}
            onCustomerChange={(id: string) => {
              form.setValue("customerId", id);
            }}
          />
          <div className={"pt-3"}>
            <Label>Address</Label>
            <AutoCompleteAddress
              defaultValue={form.watch("eventLocation")}
              onValueChange={(search) => {
                setValue(search);
                form.setValue("eventLocation.line1", search);
              }}
              value={value}
              onSelectAddress={(address) => {
                const formatted = {
                  line1: address.line1,
                  line2: address.line2,
                  city: address.city,
                  state: address.state,
                  postalCode: address.postalCode,
                  country: address.country || "US",
                };
                form.setValue("eventLocation", formatted);
                setValue(formatted.line1 ?? "");
              }}
            />
            {form.watch("eventLocation.line1") !== "" && (
              <div>
                <FormItem
                  label={"Line 2"}
                  name={"eventLocation.line2"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormGroup>
                  <FormItem
                    label={"City"}
                    name={"eventLocation.city"}
                    render={({ field }) => {
                      return <Input {...field} />;
                    }}
                  />
                  <FormItem
                    label={"State"}
                    name={"eventLocation.state"}
                    render={({ field }) => {
                      return <Input {...field} />;
                    }}
                  />
                  <FormItem
                    label={"Zip Code"}
                    name={"eventLocation.postalCode"}
                    render={({ field }) => {
                      return <Input type={"number"} {...field} />;
                    }}
                  />
                </FormGroup>
              </div>
            )}
          </div>
          <FormGroup>
            <DateTimePickerField
              key={"start"}
              label={"Event Start Time"}
              name={"startDate"}
              presets={[
                {
                  label: "Today",
                  value: new Date(),
                },
                {
                  label: "This Saturday",
                  value: new Date(
                    new Date().setDate(
                      new Date().getDate() +
                        ((6 - new Date().getDay() + 7) % 7),
                    ),
                  ),
                },
                {
                  label: "This Sunday",
                  value: new Date(
                    new Date().setDate(
                      new Date().getDate() +
                        ((7 - new Date().getDay() + 7) % 7),
                    ),
                  ),
                },
              ]}
            />
            <DateTimePickerField
              key={"end"}
              label={"Event End Time"}
              name={"endDate"}
              presets={[
                {
                  label: "Today",
                  value: new Date(),
                },
                {
                  label: "Start + 4 Hour",
                  value: new Date(
                    new Date(form.watch("startDate")).setHours(
                      new Date(form.watch("startDate")).getHours() + 4,
                    ),
                  ),
                },
                {
                  label: "Start + 6 Hour",
                  value: new Date(
                    new Date(form.watch("startDate")).setHours(
                      new Date(form.watch("startDate")).getHours() + 6,
                    ),
                  ),
                },
                {
                  label: "Start @ 6:00 PM",
                  value: new Date(
                    new Date(form.watch("startDate")).setHours(18, 0, 0, 0),
                  ),
                },
              ]}
            />
          </FormGroup>
          <FormGroup>
            <FormItem
              label={"Setup Surface"}
              name={"setupSurface"}
              removeFormControl={true}
              render={({ field }) => {
                return (
                  <Select
                    value={field.value.toString()}
                    onValueChange={(value) => {
                      const chosenSurface = setupSurfaceOptions?.data?.find(
                        (surface) => surface.id === parseInt(value, 10),
                      );
                      const fees = form.watch("fees");
                      form.setValue(
                        "fees",
                        updateOrAddFee(fees, {
                          name: chosenSurface?.name ?? null,
                          type: "SURFACE_FEE",
                          amount: chosenSurface?.feeAmount ?? 0,
                          taxable: true,
                        }),
                      );
                      field.onChange(parseInt(value, 10));
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={"Select a setup surface"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {setupSurfaceOptions?.data?.map((option) => {
                        if (option.archived && option.id !== field.value) {
                          return null;
                        }

                        const feeName =
                          option.name +
                          (option.feeAmount !== null
                            ? ` - ${getCurrencyString(option.feeAmount ?? 0)}`
                            : "");

                        return (
                          <SelectItem
                            key={option.id}
                            value={option.id.toString()}
                            disabled={option.archived}
                          >
                            {feeName}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                );
              }}
            />
            <FormItem
              label={"Rental Protection"}
              name={"damageWaiver"}
              removeFormControl={true}
              render={({ field }) => {
                return (
                  <Select
                    value={field.value.toString()}
                    onValueChange={(value) => {
                      field.onChange(value === "true");
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={"Include Rental Protection"}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={"false"}>Not Included</SelectItem>
                      <SelectItem value={"true"}>Included</SelectItem>
                    </SelectContent>
                  </Select>
                );
              }}
            />
            <FormItem
              label={"Tax Status"}
              name={"taxExempt"}
              removeFormControl={true}
              render={({ field }) => {
                return (
                  <Select
                    value={field.value.toString()}
                    disabled={
                      props.paidItems !== undefined &&
                      props.paidItems.length > 0
                    }
                    onValueChange={(value) => {
                      field.onChange(value === "true");
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={"Tax Exemption Status"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={"false"}>Included</SelectItem>
                      <SelectItem value={"true"}>Exempt</SelectItem>
                    </SelectContent>
                  </Select>
                );
              }}
            />
          </FormGroup>
        </CardContent>
      </Card>
      <div className={"pb-3"}>
        <ProductBrowser
          selectedProducts={products}
          onSelectionUpdate={(values) => {
            setProducts(values);
            form.setValue("products", values);
          }}
          startDate={form.watch("startDate")}
          endDate={form.watch("endDate")}
          onProductUpdate={(product, index) => {
            form.setValue(`products.${index}`, product);
            setProducts(form.getValues("products"));
            setRecalculate(Math.random());
          }}
        />
      </div>

      <PaymentDetailsCard
        edit={true}
        onChange={({ type, name, taxable }, amount) => {
          const currentFees = form.watch("fees");

          form.setValue(
            "fees",
            updateOrAddFee(currentFees, {
              name: name,
              type: type,
              amount: amount,
              taxable: taxable,
            }),
          );

          setRecalculate(Math.random());
        }}
        originalPaymentInfo={originalPaymentInfo}
        paymentInfo={calculateTotals()}
        paidItems={props.paidItems}
        removeCoupon={() => {
          const discounts = form.watch("discount");
          form.setValue(
            "discount",
            updateOrAddDiscount(
              discounts?.map((item) => {
                return {
                  amount: item.amount,
                  chargeType:
                    item.chargeType === "PERCENTAGE" ? "PERCENTAGE" : "DOLLAR",
                  entityId: item.entityId ?? null,
                  type: item.type,
                };
              }) || [],
              {
                type: "COUPON",
                chargeType: "DOLLAR",
                amount: 0,
                entityId: null,
              },
            ),
          );
          setRecalculate(Math.random());
        }}
        onCouponChange={(coupon) => {
          const discounts = form.watch("discount");
          form.setValue(
            "discount",
            updateOrAddDiscount(
              discounts?.map((item) => {
                return {
                  amount: item.amount,
                  chargeType:
                    item.chargeType === "PERCENTAGE" ? "PERCENTAGE" : "DOLLAR",
                  entityId: item.entityId ?? null,
                  type: item.type,
                };
              }) || [],
              {
                type: "COUPON",
                chargeType:
                  coupon.discountType === "PERCENTAGE"
                    ? "PERCENTAGE"
                    : "DOLLAR",
                amount: coupon.discount,
                entityId: coupon.id,
              },
            ),
          );
          setRecalculate(Math.random());
        }}
        coupon={
          form.watch("discount")?.find((item) => item.type === "COUPON")
            ?.entityId
            ? getCouponFromId(
                form.watch("discount")!.find((item) => item.type === "COUPON")!
                  .entityId ?? -1,
              )
            : null
        }
      />
      <div className="flex flex-row justify-start gap-2">
        <Button
          variant={"primary"}
          className={"justify-end mt-2"}
          onClick={() => {
            form.handleSubmit((values) => {
              props.onSubmit(values, true);
            })();
          }}
        >
          Save
        </Button>
        <Button
          variant={"primary"}
          className={"justify-end mt-2"}
          onClick={() => {
            form.handleSubmit((values) => {
              props.onSubmit(values, false);
            })();
          }}
        >
          Save Silently
        </Button>
      </div>
    </Form>
  );
};

export default OrderForm;
