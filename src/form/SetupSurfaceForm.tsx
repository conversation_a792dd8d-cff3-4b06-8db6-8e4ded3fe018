import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Form,
  FormControl,
  FormDescription,
  FormItem as FormValueItem,
  FormLabel,
} from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormCard from "~/components/FormLayout/FormCard";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import React, { useEffect } from "react";
import { SetupSurfaceSchema, SetupSurfaceValues } from "~/query/surface/types";
import { Textarea } from "~/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Switch } from "~/components/ui/switch";
import { Button } from "~/components/ui/button";

export type SetupSurfaceFormProps = {
  initialValues?: SetupSurfaceValues;
  loading?: boolean;
  onSubmit: (values: SetupSurfaceValues) => void;
};

const SetupSurfaceForm = (props: SetupSurfaceFormProps) => {
  const form = useForm<SetupSurfaceValues>({
    defaultValues: {
      ...props.initialValues,
    },
    resolver: yupResolver(SetupSurfaceSchema),
  });

  useEffect(() => {
    form.reset(props.initialValues);
  }, [props.initialValues]);

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormLayout>
        <FormLayoutSection>
          <FormCard>
            <FormItem
              label={"Surface Name"}
              name={"name"}
              render={({ field }) => {
                return <Input {...field} disabled={props.loading} />;
              }}
            />
            <FormItem
              label={"Description"}
              name={"description"}
              description={{
                position: "bottom",
                label:
                  "A brief description of the surface that can be displayed to your customers.",
              }}
              render={({ field }) => {
                return <Textarea {...field} disabled={props.loading} />;
              }}
            />
          </FormCard>
          <Card>
            <CardHeader>
              <CardTitle>Fees & Pricing</CardTitle>
              <CardDescription>
                Add a fee when an order is placed with this setup surface
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={"px-8 border-l-2 border-gray-200 space-y-5 mb-2"}>
                <FormItem
                  label={""}
                  name={"feeAmount"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <div className={"rounded-lg border p-4 pb-0"}>
                        <FormValueItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Charge a fee
                            </FormLabel>
                            <FormDescription>
                              Should we charge a fee for the customer when they
                              place an order with this setup surface?
                            </FormDescription>
                          </div>
                          <Switch
                            checked={field.value !== null}
                            onCheckedChange={(v) => {
                              if (v) {
                                field.onChange(0);
                              } else {
                                field.onChange(null);
                              }
                            }}
                          />
                        </FormValueItem>
                        {field.value !== null && (
                          <div className={"space-y-2"}>
                            <FormLabel>Fee Amount</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                disabled={props.loading}
                                inputMode={"numeric"}
                              />
                            </FormControl>

                            {/*<FormItem*/}
                            {/*  label={"Scale Fee With Quantity"}*/}
                            {/*  description={{*/}
                            {/*    position: "bottom",*/}
                            {/*    label:*/}
                            {/*      "Should the fee amount scale with the amount of products ordered?",*/}
                            {/*  }}*/}
                            {/*  name={"scaleFee"}*/}
                            {/*  render={({ field }) => {*/}
                            {/*    return (*/}
                            {/*      <Switch*/}
                            {/*        checked={field.value}*/}
                            {/*        onCheckedChange={(v) => {*/}
                            {/*          field.onChange(v);*/}
                            {/*        }}*/}
                            {/*      />*/}
                            {/*    );*/}
                            {/*  }}*/}
                            {/*/>*/}
                          </div>
                        )}
                      </div>
                    );
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
      <Button variant={"primary"} type={"submit"} className={"mt-2"}>
        Save
      </Button>
    </Form>
  );
};

export default SetupSurfaceForm;
