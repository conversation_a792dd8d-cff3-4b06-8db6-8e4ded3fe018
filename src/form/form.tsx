import { object, string } from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form, FormControl } from "~/components/ui/form";
import React from "react";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import FormItem from "~/components/FormLayout/FormItem";
import { EMAIL_TYPE } from "~/lib/regex";

export const STAFF_FORM_SCHEMA = object().shape({
  email: EMAIL_TYPE.required("Email is required"),
  phoneNumber: string(),
  name: string().required("Name is required"),
  role: string().required("Role is required"),
});

export type StaffValues = typeof STAFF_FORM_SCHEMA.__outputType;

type StaffFormProps = {
  initialValues?: StaffValues;
  onSubmit: (values: StaffValues) => void;
  onResetPassword?: (values: StaffValues) => void;
  roles: string[];
  self: boolean;
};

const StaffForm = (props: StaffFormProps) => {
  const form = useForm<StaffValues>({
    defaultValues: {
      email: "",
      name: "",
      role: "",
      ...props.initialValues,
    },

    resolver: yupResolver(STAFF_FORM_SCHEMA),
  });

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormItem
        name={"email"}
        label={"Email"}
        render={({ field }) => (
          <Input
            readOnly={props.initialValues !== undefined && !props.self}
            placeholder="email"
            {...field}
          />
        )}
      />
      <FormItem
        name={"name"}
        label={"Name"}
        render={({ field }) => <Input placeholder="Joe Smith" {...field} />}
      />
      <FormItem
        label={"Phone Number"}
        name={"phoneNumber"}
        render={({ field }) => <Input placeholder="Phone Number" {...field} />}
      />
      <FormItem
        removeFormControl={true}
        name={"role"}
        label={"Role"}
        render={({ field }) => (
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Edit Role" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {props.roles.map((role) => (
                <SelectItem key={role} value={role}>
                  {role}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />
      {props.initialValues && (
        <Button
          className={"mt-2"}
          variant="secondary"
          type={"button"}
          onClick={() => {
            if (props.onResetPassword !== undefined && props.initialValues) {
              props.onResetPassword(props.initialValues);
            }
          }}
        >
          Reset Password...
        </Button>
      )}
      <div className={"flex flex-row w-full justify-end"}>
        <Button variant={"primary"} className={"mt-2 "} type="submit">
          {props.initialValues ? "Update Staff" : "Add Staff"}
        </Button>
      </div>
    </Form>
  );
};

export default StaffForm;
