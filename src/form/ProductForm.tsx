import { ProductSchema, ProductSchemaType } from "~/server/product/types";
import { useFieldArray, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form, FormControl } from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import FormGroup from "~/components/FormLayout/FormGroup";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import React, { useEffect, useState } from "react";
import ImageIntake from "~/components/image/ImageIntake";
import { ImageUploadSimpleType } from "~/components/image/image";
import { Button } from "~/components/ui/button";
import Editor from "~/components/RichText/editor";
import { Label } from "~/components/ui/label";
import { Description } from "~/components/ui/description";
import { Plus } from "lucide-react";
import { Switch } from "~/components/ui/switch";

type ProductFormProps = {
  initialValues?: ProductSchemaType;
  productId: number | null;
  categoryOptions: { name: string; id: number }[];
  onSubmit: (values: ProductSchemaType, imagesToAttach: string[]) => void;
};

const ProductForm = (props: ProductFormProps) => {
  const [productImages, setProductImages] = useState<ImageUploadSimpleType[]>(
    [],
  );
  const [itemsToAttach, setItemsToAttach] = useState<string[]>([]);
  const form = useForm<ProductSchemaType>({
    defaultValues: {
      display: true,
      ...props.initialValues,
      categories: Array.from(new Set(props.initialValues?.categories ?? [])),
    },
    resolver: yupResolver(ProductSchema),
  });

  const {
    fields: fieldArray,
    append,
    remove,
  } = useFieldArray({
    control: form.control,
    // @ts-expect-error idk this is fine
    name: "categories",
  });

  const fetchProductImages = async () => {
    const response = await fetch(`/api/products/${props.productId}/images`);
    const data = await response.json();
    setProductImages(data.images);
  };

  const detachImage = async (id: string) => {
    if (!props.productId) {
      setProductImages(productImages.filter((image) => image.id !== id));
      setItemsToAttach((items) => items.filter((item) => item !== id));
      return;
    }
    const response = await fetch(
      `/api/products/${props.productId}/images/${id}/delete`,
    );
    const data = await response.json();
    if (data.success) {
      setProductImages(productImages.filter((image) => image.id !== id));
    }
  };

  const attachImage = async (image: ImageUploadSimpleType) => {
    if (!props.productId) {
      setItemsToAttach((items) => [...items, image.id]);
      setProductImages((productImage) => [...productImage, image]);

      return;
    }
    const response = await fetch(
      `/api/products/${props.productId}/images/attach`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imageId: image.id, priority: 1 }),
      },
    );
    if (response.ok) {
      setProductImages((productImage) => [...productImage, image]);
    }
  };

  useEffect(() => {
    if (props.productId) {
      void fetchProductImages();
    }
  }, [props.productId]);

  return (
    <Form
      {...form}
      onSubmit={(values) => props.onSubmit(values, itemsToAttach)}
    >
      <FormLayout>
        <FormLayoutSection style={"twoThirds"}>
          <Card>
            <CardHeader>
              <CardTitle>Product Details</CardTitle>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Name"}
                name={"name"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Description"}
                name={"description"}
                render={({ field }) => {
                  return (
                    <Editor
                      content={field.value}
                      onChange={field.onChange}
                      placeholder="Category Description..."
                    />
                  );
                }}
              />
              <FormGroup>
                {fieldArray.map((field, index) => (
                  <CategoryFormSelector
                    categoryOptions={props.categoryOptions}
                    label={"Category"}
                    name={`categories.${index}`}
                    placeholder={"Select a category"}
                    key={field.id}
                    canDelete={fieldArray.length > 1}
                    onRemove={() => {
                      if (fieldArray.length === 1) {
                        form.setError("categories", {
                          type: "manual",
                          message: "At least one category is required",
                        });
                      }

                      remove(index);
                      form.setValue(
                        "categories",
                        form
                          .getValues("categories")
                          .filter((_, i) => i !== index),
                      );
                    }}
                  />
                ))}
                <Button
                  onClick={(e) => {
                    e.preventDefault();
                    append(-1);
                    form.clearErrors("categories");
                  }}
                  variant={"secondary"}
                >
                  <Plus className={"h-4 w-4"} />
                  Add Category
                </Button>
              </FormGroup>
              {form.formState.errors?.categories?.root?.message && (
                <p className={"text-destructive"}>
                  {form.formState.errors?.categories?.root?.message}
                </p>
              )}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
              <CardDescription>
                Price per unit and the stock available for the product.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormGroup>
                <FormItem
                  label={"Price"}
                  name={"price"}
                  render={({ field }) => {
                    return <Input type={"number"} {...field} />;
                  }}
                />
                <FormItem
                  label={"Quantity"}
                  name={"quantity"}
                  render={({ field }) => {
                    return <Input type={"number"} {...field} />;
                  }}
                />
              </FormGroup>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Delivery</CardTitle>
              <CardDescription>
                The times it takes to set up and take down the product, used
                primarily in the delivery management system.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Eligible for Delivery"}
                name={"deliverable"}
                render={({ field }) => {
                  return <Switch checked={field.value} {...field} />;
                }}
              />
              <FormGroup>
                <FormItem
                  label={"Setup Time"}
                  name={"setupTimeMinutes"}
                  render={({ field }) => {
                    return (
                      <Input
                        type={"number"}
                        extraEnd={"minute(s)"}
                        {...field}
                      />
                    );
                  }}
                />
                <FormItem
                  label={"Take Down Time"}
                  name={"takeDownTimeMinutes"}
                  render={({ field }) => {
                    return (
                      <Input
                        type={"number"}
                        extraEnd={"minute(s)"}
                        {...field}
                      />
                    );
                  }}
                />
              </FormGroup>
              <div>
                <Label
                  className={
                    "text-xl font-semibold leading-none tracking-tight"
                  }
                >
                  Rental Padding
                </Label>
                <Description>
                  Extra time before or after a rental before the product is
                  available for rent again
                </Description>
                <FormGroup className={"pt-5"}>
                  <FormItem
                    label={"Before"}
                    name={"beforeRentalBufferMinutes"}
                    render={({ field }) => {
                      return (
                        <Input
                          type={"number"}
                          {...field}
                          extraEnd={"minute(s)"}
                        />
                      );
                    }}
                  />
                  <FormItem
                    label={"After"}
                    name={"afterRentalBufferMinutes"}
                    render={({ field }) => {
                      return (
                        <Input
                          type={"number"}
                          {...field}
                          extraEnd={"minute(s)"}
                        />
                      );
                    }}
                  />
                </FormGroup>
              </div>
            </CardContent>
          </Card>
        </FormLayoutSection>
        <FormLayoutSection style={"oneThird"}>
          <Card>
            <CardHeader>
              <CardTitle>Display Status</CardTitle>
              <CardDescription>
                Determines whether the product is displayed on the website.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormItem
                label={""}
                name={"display"}
                removeFormControl={true}
                render={({ field }) => {
                  return (
                    <Select
                      value={field.value?.toString() ?? "false"}
                      onValueChange={(value) => {
                        field.onChange(value === "true");
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={"Select Status"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={"true"}>Website</SelectItem>
                        <SelectItem value={"false"}>Admin Only</SelectItem>
                      </SelectContent>
                    </Select>
                  );
                }}
              />
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardHeader>
              <CardTitle>Images</CardTitle>
              <CardDescription>
                Images that will be displayed on the website.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageIntake
                images={productImages}
                onUploaded={(image) => attachImage(image)}
                onDeleted={(image) => detachImage(image.id)}
                remove={true}
              />
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
      <Button type={"submit"} variant={"primary"} className={"mt-2"}>
        Save
      </Button>
    </Form>
  );
};

type CategoryFormSelectorProps = {
  categoryOptions: { name: string; id: number }[];
  label: string;
  name: string;
  placeholder: string;
  onRemove: () => void;
  canDelete?: boolean;
};

const CategoryFormSelector = (props: CategoryFormSelectorProps) => {
  return (
    <FormItem
      label={props.label}
      name={props.name}
      removeFormControl={true}
      render={({ field }) => {
        return (
          <Select
            value={field.value?.toString() ?? ""}
            onValueChange={(value) => {
              if (value === "unset") {
                field.onChange(undefined);
                props.onRemove?.();
                return;
              }
              field.onChange(value);
            }}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={props.placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {props.categoryOptions.map((category) => {
                return (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                );
              })}
              {props.canDelete && (
                <SelectItem value={"unset"}>{"Unset Category"}</SelectItem>
              )}
            </SelectContent>
          </Select>
        );
      }}
    />
  );
};

export default ProductForm;
