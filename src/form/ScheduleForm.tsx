import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem as ShadFormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormGroup from "~/components/FormLayout/FormGroup";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { ScheduleSchema, ScheduleValues } from "~/server/schedule/types";
import TimePickerField from "~/components/DatePicker/TimePickerField";
import { Checkbox } from "~/components/ui/checkbox";

export type ScheduleFormProps = {
  initialValues?: ScheduleValues;
  onSubmit: (values: ScheduleValues) => void;
};

const ScheduleForm = (props: ScheduleFormProps) => {
  const form = useForm<ScheduleValues>({
    defaultValues: {
      closed: false,
      openTime: new Date(),
      closeTime: new Date(),
      ...props.initialValues,
      SATURDAY: props.initialValues?.days?.includes("SATURDAY") ?? false,
      SUNDAY: props.initialValues?.days?.includes("SUNDAY") ?? false,
      MONDAY: props.initialValues?.days?.includes("MONDAY") ?? false,
      TUESDAY: props.initialValues?.days?.includes("TUESDAY") ?? false,
      WEDNESDAY: props.initialValues?.days?.includes("WEDNESDAY") ?? false,
      THURSDAY: props.initialValues?.days?.includes("THURSDAY") ?? false,
      FRIDAY: props.initialValues?.days?.includes("FRIDAY") ?? false,
    },
    resolver: yupResolver(ScheduleSchema),
  });

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormLayout>
        <FormLayoutSection>
          <Card>
            <CardHeader>
              <CardTitle>Schedule Information</CardTitle>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Name"}
                name={"name"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Description"}
                name={"description"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormGroup>
                <FormItem
                  label={"Status"}
                  description={{
                    label:
                      "Should this schedule affect when customers can book right now?",
                    position: "bottom",
                  }}
                  name={"active"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <Select
                        value={field.value?.toString() ?? "true"}
                        onValueChange={(value) => {
                          field.onChange(value === "true");
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={"Select Status"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={"true"}>Active</SelectItem>
                          <SelectItem value={"false"}>Draft</SelectItem>
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </FormGroup>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Schedule Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="days"
                render={() => (
                  <ShadFormItem>
                    <div className="mb-4">
                      <FormLabel className="text-base">Days</FormLabel>
                    </div>
                    {[
                      "SUNDAY",
                      "MONDAY",
                      "TUESDAY",
                      "WEDNESDAY",
                      "THURSDAY",
                      "FRIDAY",
                      "SATURDAY",
                    ].map((item) => (
                      <FormField
                        key={item}
                        control={form.control}
                        name="days"
                        render={({ field }) => {
                          return (
                            <ShadFormItem
                              key={item}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(item)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([
                                          ...(field?.value || []),
                                          item,
                                        ])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== item,
                                          ),
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {item}
                              </FormLabel>
                            </ShadFormItem>
                          );
                        }}
                      />
                    ))}
                    <FormMessage />
                  </ShadFormItem>
                )}
              />
              <FormGroup>
                <FormItem
                  label={"Open Time"}
                  name={"openTime"}
                  render={({ field }) => (
                    <TimePickerField
                      date={new Date(field.value)}
                      setDate={field.onChange}
                      showAmPm={true}
                      showSeconds={false}
                    />
                  )}
                />
                <FormItem
                  label={"Close Time"}
                  name={"closeTime"}
                  render={({ field }) => (
                    <TimePickerField
                      date={new Date(field.value)}
                      setDate={field.onChange}
                      showAmPm={true}
                      showSeconds={false}
                    />
                  )}
                />
              </FormGroup>
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
      <Button type="submit" variant={"primary"}>
        Save
      </Button>
    </Form>
  );
};

export default ScheduleForm;
