import { useForm } from "react-hook-form";
import React, { useEffect } from "react";
import { Form } from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import { createId } from "@paralleldrive/cuid2";
import { ITEM_LIST_TYPE } from "~/pages/orders/driver/calendar";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import TimePickerField from "~/components/DatePicker/TimePickerField";
import { Checkbox } from "~/components/ui/checkbox";
import { endOfDay, startOfDay } from "date-fns";
import { Label } from "~/components/ui/label";
import { getTimeWindowForTask } from "~/components/driver/Timeline";
import { Textarea } from "~/components/ui/textarea";
import { yupResolver } from "@hookform/resolvers/yup";
import { LogisticTaskItem } from "~/components/driver/types";
import { boolean, date, number, object, string } from "yup";
import { undefined } from "zod";

type LogisticTaskFormProps = {
  date: Date;
  initialValue?: LogisticTaskItem;
  onSubmit: (value: LogisticTaskItem) => void;
};

export const LogisticTaskSchema = object().shape({
  index: number().required(),
  id: string().required(),
  timelineId: string().required(),
  orderId: number().nullable(),
  displayAction: string().required(),
  durationMinutes: number().required(),
  durationAffectsTime: boolean().required(),
  actionTimeFlexibilityMinutes: number().required(),
  actionTime: date().required(),
  notes: string().nullable(),
  address: object().shape({
    line1: string().nullable(),
    line2: string().nullable(),
    city: string().nullable(),
    state: string().nullable(),
    postalCode: string().nullable(),
    country: string().nullable(),
  }),
});

const LogisticTaskForm = (props: LogisticTaskFormProps) => {
  // const [address, setAddress] = useState<string | undefined>(undefined);
  const [startDate, setStartDate] = React.useState<Date | undefined>(
    startOfDay(props.date),
  );
  const [endDate, setEndDate] = React.useState<Date | undefined>(
    endOfDay(props.date),
  );
  const form = useForm<typeof LogisticTaskSchema.__outputType>({
    defaultValues: {
      index: 0,
      id: createId(),
      timelineId: ITEM_LIST_TYPE,
      actionTime: props.date,
      orderId: null,
      ...props.initialValue,
    },
    resolver: yupResolver(LogisticTaskSchema),
  });
  useEffect(() => {
    if (props.initialValue) {
      const times = getTimeWindowForTask(props.initialValue);
      setStartDate(new Date(times.firstValidArrivalTime));
      setEndDate(new Date(times.lastValidArrivalTime));

      form.reset(props.initialValue);
      // setAddress(props.initialValue.address?.line1 ?? "");
    }
  }, [props.initialValue]);

  // create fields for: Display Action (Input), Notes (text area), Duration Minutes (Input), Duration Affects Time (Slider), earliest arrival (Date), latest arrival (Date), Address (Address Field)

  const baselineDate = new Date();

  return (
    <Form
      {...form}
      formItemProps={{
        id: "logistic-form",
      }}
      onSubmit={(values) => {
        console.log("values", values);
        const earliestArrival = startDate || props.date;
        const flexibilityMinutes =
          earliestArrival.getTime() - (endDate?.getTime() || 0);
        props.onSubmit({
          ...values,
          address: {
            line1: values.address.line1 ?? null,
            line2: values.address.line2 ?? null,
            city: values.address.city ?? null,
            state: values.address.state ?? null,
            postalCode: values.address.postalCode ?? null,
            country: values.address.country ?? null,
            id: -1,
            createdAt: new Date(),
            updatedAt: new Date(),
            latitude: -1,
            longitude: -1,
          },
          notes: values.notes ? values.notes : null,
          orderId: null,
          timelineId: ITEM_LIST_TYPE,
          actionTime: earliestArrival,
          actionTimeFlexibilityMinutes: (flexibilityMinutes / 60) * 1000,
        });
      }}
    >
      <FormLayout>
        <FormLayoutSection style={"full"} className={"gap-0 lg:gap-0"}>
          <FormItem
            name={"displayAction"}
            label={"Display Action (Name)"}
            render={({ field }) => {
              return <Input {...field} />;
            }}
          />
          <FormItem
            name={"durationMinutes"}
            label={"Duration (Minutes)"}
            render={({ field }) => {
              return (
                <TimePickerField
                  date={
                    field.value
                      ? new Date(baselineDate.getTime() + field.value)
                      : baselineDate
                  }
                  setDate={(date) => {
                    if (!date) {
                      field.onChange(0);
                    } else {
                      field.onChange(date.getTime() - baselineDate.getTime());
                    }
                  }}
                  {...field}
                />
              );
            }}
          />
          <FormItem
            name={"durationAffectsTime"}
            label={"Duration Affects Start Time"}
            render={({ field }) => {
              return (
                <Checkbox
                  checked={field.value === true}
                  onCheckedChange={(value) => field.onChange(value)}
                  aria-label="Duration Affects Start Time"
                />
              );
            }}
          />
          <div className={"gap-2 flex flex-row items-center"}>
            <Label>Earliest Arrival Time</Label>
            <TimePickerField
              date={startDate}
              setDate={setStartDate}
              showAmPm={true}
            />
          </div>
          <div className={"gap-2 flex flex-row items-center"}>
            <Label>Latest Arrival Time</Label>
            <TimePickerField
              date={endDate}
              setDate={setEndDate}
              showAmPm={true}
            />
          </div>
          <div className={"pt-3"}>
            {/*<div className="flex flex-col justify-center pb-3 gap-1">*/}
            {/*<p className="text-sm font-medium">Line 1</p>*/}
            {/*  <AutoCompleteAddress*/}
            {/*    defaultValue={form.watch("address") as AddressType}*/}
            {/*    onValueChange={(search) => {*/}
            {/*      form.setValue("address.line1", search);*/}
            {/*    }}*/}
            {/*    value={address}*/}
            {/*    onSelectAddress={(address) => {*/}
            {/*      const formatted = {*/}
            {/*        line1: address.line1 ?? null,*/}
            {/*        line2: address.line2 ?? null,*/}
            {/*        city: address.city ?? null,*/}
            {/*        state: address.state ?? null,*/}
            {/*        postalCode: address.postalCode ?? null,*/}
            {/*        country: address.country || "US",*/}
            {/*      };*/}
            {/*      form.setValue("address", formatted);*/}
            {/*      setAddress(address?.line1);*/}
            {/*    }}*/}
            {/*  />*/}
            {/*</div>*/}
            {/*{form.watch("address.line1") !== "" && (*/}
            {/*  <div>*/}
            {/*    <FormItem*/}
            {/*      label={"Line 2"}*/}
            {/*      name={"address.line2"}*/}
            {/*      render={({ field }) => {*/}
            {/*        return <Input {...field} />;*/}
            {/*      }}*/}
            {/*    />*/}
            {/*    <FormGroup>*/}
            {/*      <FormItem*/}
            {/*        label={"City"}*/}
            {/*        name={"address.city"}*/}
            {/*        render={({ field }) => {*/}
            {/*          return <Input {...field} />;*/}
            {/*        }}*/}
            {/*      />*/}
            {/*      <FormItem*/}
            {/*        label={"State"}*/}
            {/*        name={"address.state"}*/}
            {/*        render={({ field }) => {*/}
            {/*          return <Input {...field} />;*/}
            {/*        }}*/}
            {/*      />*/}
            {/*      <FormItem*/}
            {/*        label={"Zip Code"}*/}
            {/*        name={"address.postalCode"}*/}
            {/*        render={({ field }) => {*/}
            {/*          return <Input type={"number"} {...field} />;*/}
            {/*        }}*/}
            {/*      />*/}
            {/*    </FormGroup>*/}
            {/*  </div>*/}
            {/*)}*/}
          </div>

          <FormItem
            name={"notes"}
            label={"Notes (Optional)"}
            render={({ field }) => {
              return <Textarea {...field} />;
            }}
          />
        </FormLayoutSection>
      </FormLayout>
    </Form>
  );
};

export default LogisticTaskForm;
