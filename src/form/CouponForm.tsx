import { number, object, string } from "yup";
import { ChargeType } from ".prisma/client";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { Form, FormControl } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import React from "react";
import { Button } from "~/components/ui/button";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormGroup from "~/components/FormLayout/FormGroup";

export const COUPON_SCHEMA = object().shape({
  startDate: string(),
  endDate: string(),
  name: string().required("Code is required"),
  description: string(),
  displayName: string().required("Display name is required"),
  discountType: string<ChargeType>().required("Discount type is required"),
  discount: number().required("Amount is required"),
});

export type CouponValues = typeof COUPON_SCHEMA.__outputType;

export type CouponFormProps = {
  initialValues?: CouponValues;
  onSubmit: (values: CouponValues) => void;
};

const CouponForm = (props: CouponFormProps) => {
  const form = useForm<CouponValues>({
    defaultValues: {
      ...props.initialValues,
    },
    resolver: yupResolver(COUPON_SCHEMA),
  });

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormLayout>
        <FormLayoutSection style={"full"}>
          <FormGroup>
            <FormItem
              label={"Coupon Code"}
              name={"name"}
              description={{
                label: "The code your customers will use",
                position: "top",
              }}
              render={({ field }) => {
                return <Input {...field} />;
              }}
            />
            <FormItem
              label={"Name"}
              name={"displayName"}
              description={{
                label: "Internal name for the coupon",
                position: "top",
              }}
              render={({ field }) => {
                return <Input {...field} />;
              }}
            />
          </FormGroup>
          <FormGroup>
            <FormItem
              label={"Discount"}
              name={"discount"}
              render={({ field }) => {
                return (
                  <Input
                    {...field}
                    placeholder={"10.00"}
                    extraEnd={
                      <>
                        {form.watch("discountType") === ChargeType.PERCENTAGE
                          ? "%"
                          : "$"}
                      </>
                    }
                  />
                );
              }}
            />
            <FormItem
              label={"Type"}
              name={"discountType"}
              removeFormControl={true}
              render={({ field }) => {
                return (
                  <Select
                    value={field?.value?.toString() || ""}
                    onValueChange={(value) => {
                      field.onChange(value);
                    }}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={"Choose a Discount Type"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={ChargeType.DOLLAR.toString()}>
                        Amount Off
                      </SelectItem>
                      <SelectItem value={ChargeType.PERCENTAGE.toString()}>
                        Percentage Off
                      </SelectItem>
                    </SelectContent>
                  </Select>
                );
              }}
            />
          </FormGroup>
        </FormLayoutSection>
      </FormLayout>
      <Button variant={"primary"} type={"submit"}>
        Save
      </Button>
    </Form>
  );
};

export default CouponForm;
