import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import React from "react";
import { Form, FormControl } from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormGroup from "~/components/FormLayout/FormGroup";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Button } from "~/components/ui/button";
import { CustomerSchema, CustomerValues } from "~/query/customer";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import FormCard from "~/components/FormLayout/FormCard";

export type CustomerFormProps = {
  initialValues?: CustomerValues;
  onSubmit: (values: CustomerValues) => void;
  dialog?: boolean;
  showBottomForm?: boolean;
};

const CustomerForm = (props: CustomerFormProps) => {
  const form = useForm<CustomerValues>({
    defaultValues: {
      banned: false,
      points: 0,
      ...props.initialValues,
    },
    resolver: yupResolver(CustomerSchema),
  });

  return (
    <Form
      {...form}
      onSubmit={props.onSubmit}
      formItemProps={{
        id: "custom-form",
      }}
    >
      <FormLayout>
        <FormLayoutSection style={props.dialog ? "full" : "twoThirds"}>
          <Card>
            {props.showBottomForm !== false && (
              <CardHeader>
                <CardTitle>Customer Details</CardTitle>
              </CardHeader>
            )}
            <CardContent>
              <FormItem
                label={"First Name"}
                name={"firstName"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Last Name"}
                name={"lastName"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Email"}
                name={"email"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormGroup>
                <FormItem
                  label={"Phone Number"}
                  name={"phoneNumber"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Company"}
                  name={"company"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
              </FormGroup>
            </CardContent>
          </Card>
          {props.showBottomForm !== false && (
            <FormCard>
              <FormGroup>
                <FormItem
                  label={"Loyalty Points"}
                  name={"points"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Reference"}
                  name={"reference"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Status"}
                  name={"banned"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <Select
                        value={field.value?.toString() ?? "false"}
                        onValueChange={(value) => {
                          field.onChange(value === "true");
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={"Customer Status"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={"true"}>Banned</SelectItem>
                          <SelectItem value={"false"}>Active</SelectItem>
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </FormGroup>
              <FormItem
                label={"Internal Notes"}
                name={"internalNotes"}
                render={({ field }) => {
                  return <Textarea {...field} />;
                }}
              />
            </FormCard>
          )}
        </FormLayoutSection>
      </FormLayout>
      {!props.dialog && (
        <Button type="submit" variant={"primary"} className={"mt-2"}>
          Save
        </Button>
      )}
    </Form>
  );
};

export default CustomerForm;
