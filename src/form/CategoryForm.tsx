import { CategorySchema, CategorySchemaType } from "~/query/category";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { ImageUploadSimpleType } from "~/components/image/image";
import React, { useEffect, useState } from "react";
import { Form, FormControl } from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import ImageIntake from "~/components/image/ImageIntake";
import { Button } from "~/components/ui/button";
import slugify from "slugify";
import Editor from "~/components/RichText/editor";

type CategoryFormProps = {
  onSubmit: (values: CategorySchemaType, imageToAttach: string | null) => void;
  initialValues?: CategorySchemaType;
  categoryId: number | null;
};

const CategoryForm = (props: CategoryFormProps) => {
  const form = useForm<CategorySchemaType>({
    defaultValues: {
      display: true,
      ...props.initialValues,
    },
    resolver: yupResolver(CategorySchema),
  });
  const [image, setImage] = useState<ImageUploadSimpleType | null>(null);
  const [imageToAttach, setImageToAttach] = useState<string | null>(null);

  const fetchCategoryImage = async () => {
    const response = await fetch(`/api/categories/${props.categoryId}/images`);
    const data = await response.json();
    setImage(data.image);
  };

  useEffect(() => {
    if (props.categoryId) {
      void fetchCategoryImage();
    }
  }, [props.categoryId]);

  const detachImage = async (id: string) => {
    if (!props.categoryId) {
      setImage(null);
      setImageToAttach(null);
      return;
    }
    const response = await fetch(
      `/api/categories/${props.categoryId}/images/${id}/delete`,
    );
    const data = await response.json();
    if (data.success) {
      setImage(null);
    }
  };

  const attachImage = async (image: ImageUploadSimpleType) => {
    if (!props.categoryId) {
      setImage(image);
      setImageToAttach(image.id);
      return;
    }
    const response = await fetch(
      `/api/categories/${props.categoryId}/images/attach`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imageId: image.id }),
      },
    );
    if (response.ok) {
      setImage(image);
    }
  };

  // todo add product sorting & Display products

  return (
    <Form
      {...form}
      onSubmit={(values) => props.onSubmit(values, imageToAttach)}
    >
      <FormLayout>
        <FormLayoutSection>
          <Card>
            <CardHeader>
              <CardTitle>Category Details</CardTitle>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Name"}
                name={"name"}
                render={({ field }) => {
                  return (
                    <Input
                      {...field}
                      onChange={(event) => {
                        field.onChange(event);
                        if (!form.getFieldState("metaTitle").isTouched) {
                          form.setValue("metaTitle", event.target.value);
                        }
                        if (!form.getFieldState("slug").isTouched) {
                          form.setValue(
                            "slug",
                            slugify(event.target.value, { lower: true }),
                          );
                        }
                      }}
                    />
                  );
                }}
              />
              <FormItem
                label={"Description"}
                name={"description"}
                render={({ field }) => {
                  return (
                    <Editor
                      content={field.value}
                      onChange={field.onChange}
                      placeholder="Category Description..."
                    />
                  );
                }}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>SEO</CardTitle>
              <CardDescription>
                Override PRP automatic SEO settings for this category.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Meta Title"}
                name={"metaTitle"}
                render={({ field }) => {
                  return <Input {...field} placeholder={form.watch("name")} />;
                }}
              />
              <FormItem
                label={"Meta Description"}
                name={"metaDescription"}
                render={({ field }) => {
                  return <Textarea className={"resize-none"} {...field} />;
                }}
              />
              <FormItem
                label={"Handle"}
                name={"slug"}
                description={{
                  label:
                    "A handle is how the customer will access this category on the website. For example, if the handle is 'example', the category will be accessible at /categories/example.",
                  position: "bottom",
                }}
                render={({ field }) => {
                  return (
                    <Input disabled={props.categoryId !== null} {...field} />
                  );
                }}
              />
            </CardContent>
          </Card>
        </FormLayoutSection>
        <FormLayoutSection style={"oneThird"}>
          <Card>
            <CardHeader>
              <CardTitle>Display Status</CardTitle>
              <CardDescription>
                Determines whether the category is displayed on the website.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormItem
                label={""}
                name={"display"}
                removeFormControl={true}
                render={({ field }) => {
                  return (
                    <Select
                      value={field.value?.toString() ?? "false"}
                      onValueChange={(value) => {
                        field.onChange(value === "true");
                      }}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={"Select Status"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={"true"}>Website</SelectItem>
                        <SelectItem value={"false"}>Admin Only</SelectItem>
                      </SelectContent>
                    </Select>
                  );
                }}
              />
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardHeader>
              <CardTitle>Image</CardTitle>
              <CardDescription>
                The image that will be displayed for the category.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageIntake
                images={image ? [image] : []}
                single={true}
                onUploaded={(image) => attachImage(image)}
                onDeleted={(image) => detachImage(image.id)}
                remove={true}
              />
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
      <Button type="submit" variant={"primary"} size={"md"} className={"mt-2"}>
        Save
      </Button>
    </Form>
  );
};

export default CategoryForm;
