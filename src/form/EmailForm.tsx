import { EMAIL_SCHEMA, EmailDetail } from "~/query/email";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form } from "~/components/ui/form";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormLayout from "~/components/FormLayout";
import FormCard from "~/components/FormLayout/FormCard";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import Editor from "~/components/RichText/editor";
import React, { useState } from "react";
import { JSONContent } from "@tiptap/react";
import { Button } from "~/components/ui/button";
import EmailRenderDialog from "~/components/email/EmailRenderDialog";
import { allowedVariables } from "~/lib/email/maily";
import { Package, SmileIcon } from "lucide-react";
import { CommandProps } from "~/components/RichText/extensions/slash-command";

type EmailFormProps = {
  initialValues?: EmailDetail;
  onSubmit: (values: EmailDetail) => void;
};

const EmailForm = (props: EmailFormProps) => {
  const form = useForm<EmailDetail>({
    defaultValues: {
      text: JSON.stringify({ type: "doc", content: [] }),
      ...props?.initialValues,
    },
    resolver: yupResolver(EMAIL_SCHEMA),
  });
  const [open, setOpen] = useState(false);

  return (
    <div>
      <EmailRenderDialog
        open={open}
        setOpen={setOpen}
        jsonContent={JSON.parse(form.watch("text")) as JSONContent}
      />
      <Form
        {...form}
        onSubmit={props.onSubmit}
        formItemProps={{
          id: "email-form",
        }}
      >
        <FormLayout>
          <FormLayoutSection style={"full"}>
            <FormCard>
              <FormItem
                label={"Name"}
                name={"name"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Subject"}
                name={"subject"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Preview"}
                name={"previewText"}
                description={{
                  position: "bottom",
                  label:
                    "The optional preview text is the first thing recipients see in their inbox.",
                }}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
            </FormCard>
            <FormCard>
              <FormItem
                label={
                  <div className={"flex justify-between"}>
                    <span>Email Body</span>
                    <Button
                      variant={"secondary"}
                      type={"button"}
                      onClick={() => {
                        setOpen(true);
                      }}
                    >
                      Preview
                    </Button>
                  </div>
                }
                name={"text"}
                description={{
                  position: "top",
                  label: (
                    <p className={"mb-2"}>
                      Type '/' or enter your email body to get started!
                      <br />
                      Use '@' to see all available variables.
                    </p>
                  ),
                }}
                render={({ field }) => {
                  return (
                    <Editor
                      content={JSON.parse(field.value) as JSONContent}
                      onChange={(_, json) => {
                        form.setValue("text", json);
                      }}
                      variables={allowedVariables.map((variable) => ({
                        name: variable,
                      }))}
                      commands={[
                        {
                          title: "Order Products",
                          description:
                            "Used for Order related emails only to show the current receipt.",
                          searchTerms: ["template", "order", "products"],
                          icon: <Package className="h-4 w-4" />,
                          command: ({ editor, range }: CommandProps) => {
                            editor
                              .chain()
                              .focus()
                              .deleteRange(range)
                              .setOrderProducts()
                              .run();
                          },
                        },
                        {
                          title: "Customer Survey",
                          description:
                            "Send your customers a survey to get feedback on their experience.",
                          searchTerms: ["csat", "survey"],
                          icon: <SmileIcon className={"w-4 h-4"} />,
                          command: ({ editor, range }: CommandProps) => {
                            editor
                              .chain()
                              .focus()
                              .deleteRange(range)
                              .setCsatSurvey()
                              .run();
                          },
                        },
                      ]}
                    />
                  );
                }}
              />
            </FormCard>
          </FormLayoutSection>
        </FormLayout>
      </Form>
    </div>
  );
};

export default EmailForm;
