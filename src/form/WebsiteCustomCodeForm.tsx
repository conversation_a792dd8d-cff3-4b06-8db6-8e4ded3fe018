import { array, boolean, object, string } from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import React, { useEffect } from "react";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { toast } from "sonner";
import { Textarea } from "~/components/ui/textarea";
import { DEFAULT_WEBSITE_CONFIG } from "@prp/blocks";
import { Button } from "~/components/ui/button";
import { FormList } from "~/components/FormLayout/FormList";
import ColorPicker from "~/components/ColorPicker";
import { Switch } from "~/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormGroup from "~/components/FormLayout/FormGroup";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import { useWebsiteSettings, WEBSITE_QUERY } from "~/query/website";
import { LoadingSpinner } from "~/pages/_app";
import { useQueryClient } from "@tanstack/react-query";

export const WebsiteSettingsSchema = object().shape({
  background: string().default("#FFF").nonNullable(),
  backgroundImage: string().nullable().optional(),
  nav: object().shape({
    showSearch: boolean().default(true).required().optional(),
    backgroundColor: string().default("#FFF").nullable().optional(),
    textColor: string().default("#000").nullable().optional(),
    links: array()
      .of(
        object()
          .shape({
            label: string().required(),
            href: string().required(),
          })
          .optional(),
      )
      .required(),
  }),
  footer: object().shape({
    backgroundColor: string().nullable().required(),
    textColor: string().nullable().optional(),
    showGoogleBusinessName: boolean().default(false).required().optional(),
    showAddress: boolean().default(false).required(),
  }),
});

export const WebsiteCustomCodeSchema = object().shape({
  googleAnalytics: string()
    .matches(/^G-[A-Z0-9]{7,12}$/, {
      excludeEmptyString: true,
      message: "Invalid Google Analytics ID, should be G-XXXXXXX",
    })
    .nullable(),
  postHogId: string()
    .matches(/^phc_[a-zA-Z0-9]+$/, {
      excludeEmptyString: true,
      message: "Invalid PostHog ID, should be phc_XXXXXXX",
    })
    .optional(),
  afterHead: string().optional().nullable(),
  afterBody: string().optional().nullable(),
  settings: WebsiteSettingsSchema.required(),
});

export type WebsiteCustomCodeValues =
  typeof WebsiteCustomCodeSchema.__outputType;

type WebsiteCustomCodeFormProps = {
  showSaveButton?: boolean;
};

const WebsiteCustomCodeForm = ({
  showSaveButton,
}: WebsiteCustomCodeFormProps) => {
  const { data, isPending } = useWebsiteSettings();
  const queryClient = useQueryClient();
  const form = useForm<WebsiteCustomCodeValues>({
    defaultValues: {
      googleAnalytics: "",
      postHogId: "",
      afterHead: "",
      afterBody: "",
      settings: {
        ...DEFAULT_WEBSITE_CONFIG,
        background: "#FFF",
      },
    },
    resolver: yupResolver(WebsiteCustomCodeSchema),
  });

  useEffect(() => {
    if (isPending) {
      return;
    }
    if (data) {
      form.reset({
        googleAnalytics: data.googleAnalytics,
        postHogId: data.postHogId,
        afterHead: data.afterHead,
        afterBody: data.afterBody,
        settings: {
          ...data.settings,
          background: data.settings.background || "#FFF",
        },
      });
    }
  }, [data, isPending]);

  const fields = [
    {
      name: "label",
      label: "Label",
      placeholder: "Home",
    },
    {
      name: "href",
      label: "URL",
      placeholder: "/",
    },
  ];

  const onSubmit = async (data: WebsiteCustomCodeValues) => {
    const request = fetch("/api/websites/settings/custom-code/save", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    toast.promise(request, {
      loading: "Saving...",
      success: () => {
        queryClient.invalidateQueries({
          queryKey: [WEBSITE_QUERY],
        });
        return "Settings saved";
      },
      error: "Failed to save settings",
    });
  };

  return (
    <Form
      {...form}
      onSubmit={onSubmit}
      formItemProps={{
        id: "custom-code-form",
      }}
    >
      <FormLayout>
        <FormLayoutSection style={"full"}>
          {isPending && <LoadingSpinner />}
          <Card>
            <CardHeader>
              <CardTitle>Site Settings</CardTitle>
              <CardDescription>
                Customize the look and feel of your website.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormGroup>
                <FormItem
                  label={"Background Color"}
                  name={"settings.background"}
                  render={({ field }) => {
                    return (
                      <ColorPicker
                        background={field.value}
                        setBackground={(newColor) => {
                          field.onChange(newColor);
                        }}
                      />
                    );
                  }}
                />
                <FormItem
                  label={"Background Image URL"}
                  name={"settings.backgroundImage"}
                  render={({ field }) => {
                    return (
                      <Input {...field} type={"url"} className={"h-8 py-1.5"} />
                    );
                  }}
                />
              </FormGroup>

              <CardHeader className={"ml-0 pl-0"}>
                <CardTitle className={"ml-0"}>Nav Settings</CardTitle>
                <CardDescription>Customize the nav bar</CardDescription>
              </CardHeader>
              <FormItem
                label={"Show Search"}
                name={"settings.nav.showSearch"}
                render={({ field }) => {
                  return (
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  );
                }}
              />
              <div>
                <p className="text-xs font-normal text-slate-400">Nav Color</p>
                <FormGroup>
                  <FormItem
                    label={"Background Color"}
                    name={"settings.nav.backgroundColor"}
                    render={({ field }) => {
                      return (
                        <ColorPicker
                          background={field.value}
                          setBackground={(newColor) => {
                            field.onChange(newColor);
                          }}
                        />
                      );
                    }}
                  />
                  <FormItem
                    label={"Text Color"}
                    name={"settings.nav.textColor"}
                    render={({ field }) => {
                      return (
                        <ColorPicker
                          background={field.value}
                          setBackground={(newColor) => {
                            field.onChange(newColor);
                          }}
                        />
                      );
                    }}
                  />
                </FormGroup>
                <CardHeader className={"ml-0 pl-0"}>
                  <CardTitle className={"ml-0"}>Nav Links</CardTitle>
                </CardHeader>
                <FormList
                  name="settings.nav.links"
                  control={form.control}
                  defaultValue={{
                    label: "",
                    href: "",
                  }}
                  fields={fields}
                />
              </div>
              <CardHeader className={"ml-0 pl-0"}>
                <CardTitle className={"ml-0"}>Footer Settings</CardTitle>
              </CardHeader>
              <FormGroup>
                <FormItem
                  label={"Footer Background Color"}
                  name={"settings.footer.backgroundColor"}
                  render={({ field }) => {
                    return (
                      <ColorPicker
                        background={field.value}
                        setBackground={(newColor) => {
                          field.onChange(newColor);
                        }}
                      />
                    );
                  }}
                />
                <FormItem
                  label={"Footer Text Color"}
                  name={"settings.footer.textColor"}
                  render={({ field }) => {
                    return (
                      <ColorPicker
                        background={field.value}
                        setBackground={(newColor) => {
                          field.onChange(newColor);
                        }}
                      />
                    );
                  }}
                />
              </FormGroup>
              <FormGroup>
                <FormItem
                  label={"Show Google Business Name"}
                  name={"settings.footer.showGoogleBusinessName"}
                  render={({ field }) => {
                    return (
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    );
                  }}
                />
                <FormItem
                  label={"Show Address"}
                  name={"settings.footer.showAddress"}
                  render={({ field }) => {
                    return (
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    );
                  }}
                />
              </FormGroup>
              <CardTitle>Custom Code</CardTitle>
              <CardDescription>
                Add custom code to your website. This can be used to add
                analytics, chat widgets, or other custom code.
              </CardDescription>
              <FormItem
                label={"Google Analytics Stream ID"}
                name={"googleAnalytics"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"PostHog Id (optional)"}
                name={"postHogId"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"End of <head> tag"}
                name={"afterHead"}
                render={({ field }) => {
                  return (
                    <Textarea
                      {...field}
                      className={
                        "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold"
                      }
                    />
                  );
                }}
              />
              <FormItem
                label={"End of <body> tag"}
                name={"afterBody"}
                render={({ field }) => {
                  return (
                    <Textarea
                      {...field}
                      className={
                        "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold"
                      }
                    />
                  );
                }}
              />

              <Button
                variant={"primary"}
                type={"submit"}
                form={"custom-code-form"}
                hidden={!showSaveButton}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                Save
              </Button>
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
    </Form>
  );
};

export default WebsiteCustomCodeForm;
