import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    POSTGRES_PRISMA_URL: z
      .string()
      .url()
      .refine(
        (str) => !str.includes("YOUR_MYSQL_URL_HERE"),
        "You forgot to change the default URL",
      ),
    NODE_ENV: z
      .enum(["development", "test", "production"])
      .default("development"),
    NEXTAUTH_SECRET:
      process.env.NODE_ENV === "production"
        ? z.string()
        : z.string().optional(),
    SELF_URL: z
      .string()
      .optional()
      .default(
        `https://dash.${
          process.env.NODE_ENV === "production" ? "" : "staging."
        }partyrentalplatform.com`,
      ),
    NEXTAUTH_URL: z.string(),
    R2_SECRET_ACCESS_KEY: z.string().optional(),
    R2_ACCESS_KEY_ID: z.string().optional(),
    R2_BUCKET_NAME: z.string().optional(),
    R2_ACCOUNT_ID: z.string().optional(),
    CLOUDFLARE_IMAGES_API_KEY: z.string().optional(),
    CLOUDFLARE_WORKERS_KEY: z.string().optional(),
    IMAGE_BASE_URL: z.string().optional(),
    STRIPE_SECRET_KEY: z
      .string()
      .default(
        "sk_test_51OEdrxH0Jv34YGonDllKs7DkQkLupCIr4Y2iv0xYyiF46yKR69es3ENeKRfRdGdIJVJnIeIsIoncMBEvL0guHAZQ0047TgbmTK",
      ),
    STRIPE_WEBHOOK_SECRET: z.string().optional(),
    PRP_STRIPE_WEBHOOK_SECRET: z.string().optional(),
    PRP_STRIPE_PAYMENT_WEBHOOK_SECRET: z.string().optional(),
    STRIPE_PUBLIC_KEY: z
      .string()
      .default(
        "pk_test_51OEdrxH0Jv34YGonW5d2KA7RjkFKX1dJEAiq9G6LhrO8jG0uwMSQ5HVZqwMrtLbQOBMYlOrSucQPzzaWZ8k4I5bh000zGkpaE1",
      ),
    PRP_STRIPE_PRIVATE_KEY: z.string().optional(),
    GOOGLE_MAPS_API_KEY: z.string().optional(),
    RESEND_EMAIL_DOMAIN: z.string().optional(),
    RESEND_API_KEY: z.string().optional(),
    HUBSPOT_API_KEY: z.string().optional(),
    SHARED_SECRET: z.string().optional(),
    TWILIO_ACCOUNT_SID: z.string().optional(),
    TWILIO_AUTH_TOKEN: z.string().optional(),
    INFINITY_GRID_SECRET: z.string().default("312737-1237n-h41-312adanh-3127f"), // old secret
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    NEXT_PUBLIC_STRIPE_PUBLIC_KEY: z.string(),
    NEXT_PUBLIC_PRP_STRIPE_KEY: z.string(),
    NEXT_PUBLIC_SELF_URL: z.string(),
    NEXT_PUBLIC_POSTHOG_KEY: z.string(),
    NEXT_PUBLIC_POSTHOG_HOST: z.string(),
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  runtimeEnv: {
    NEXT_PUBLIC_POSTHOG_KEY:
      process.env.NEXT_PUBLIC_POSTHOG_KEY ||
      "phc_EjU2fvlBYyfpoyftWsFFnjk9FTYxUNQ0xBQndLR97Xe",
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    POSTGRES_PRISMA_URL: process.env.POSTGRES_PRISMA_URL,
    NODE_ENV: process.env.NODE_ENV,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    SELF_URL: `https://dash.${
      process.env.NODE_ENV === "production" ? "" : "staging."
    }partyrentalplatform.com`,
    NEXT_PUBLIC_SELF_URL: `https://dash.${
      process.env.NODE_ENV === "production" ? "" : "staging."
    }partyrentalplatform.com`,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    R2_SECRET_ACCESS_KEY: process.env.R2_SECRET_ACCESS_KEY,
    R2_ACCESS_KEY_ID: process.env.R2_ACCESS_KEY_ID,
    R2_BUCKET_NAME: process.env.R2_BUCKET_NAME,
    R2_ACCOUNT_ID: process.env.R2_ACCOUNT_ID,
    CLOUDFLARE_IMAGES_API_KEY: process.env.CLOUDFLARE_IMAGES_API_KEY,
    CLOUDFLARE_WORKERS_KEY: process.env.CLOUDFLARE_WORKERS_KEY,
    IMAGE_BASE_URL: process.env.IMAGE_BASE_URL,
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    PRP_STRIPE_WEBHOOK_SECRET: process.env.PRP_STRIPE_WEBHOOK_SECRET,
    PRP_STRIPE_PAYMENT_WEBHOOK_SECRET:
      process.env.PRP_STRIPE_PAYMENT_WEBHOOK_SECRET,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    NEXT_PUBLIC_STRIPE_PUBLIC_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY,
    NEXT_PUBLIC_PRP_STRIPE_KEY: process.env.NEXT_PUBLIC_PRP_STRIPE_KEY,
    PRP_STRIPE_PRIVATE_KEY: process.env.PRP_STRIPE_PRIVATE_KEY,
    STRIPE_PUBLIC_KEY: process.env.STRIPE_PUBLIC_KEY,
    GOOGLE_MAPS_API_KEY: process.env.GOOGLE_MAPS_API_KEY,
    RESEND_EMAIL_DOMAIN: process.env.RESEND_EMAIL_DOMAIN,
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    HUBSPOT_API_KEY: process.env.HUBSPOT_API_KEY,
    SHARED_SECRET: process.env.SHARED_SECRET,
    TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
    INFINITY_GRID_SECRET: process.env.INFINITY_GRID_SECRET,
  },
  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  /**
   * Makes it so that empty strings are treated as undefined.
   * `SOME_VAR: z.string()` and `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
});
