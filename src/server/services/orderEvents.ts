import { db } from "~/server/db";
import {
  CustomEmailDelivery,
  EmailAction,
  ScheduledEmailConfig,
} from "@prisma/client";
import { captureMessage } from "@sentry/nextjs";
import { Maily } from "~/lib/email/maily";
import { JSONContent } from "@tiptap/react";
import { Account, Order } from ".prisma/client";
import { sendScheduledEmails } from "~/server/services/scheduledEmailCron";
import { ReceiptEmailProps } from "../../../emails/ReceiptEmail";
import {
  ContractSharedItems,
  OrderContractItems,
  OrderPaidItems,
} from "~/components/Contract/Top/types";

export type CancelEmailProps = {
  contractSharedItems: ContractSharedItems;
  contractItems: OrderContractItems[];
  paidItems: OrderPaidItems[];
};

export type QuoteEmailProps = {
  shoppingId: string;
} & ReceiptEmailProps;

export const handleOrderPayment = async (
  orderId: number,
  accountId: number,
  variables: ReceiptEmailProps,
): Promise<void> => {
  const emailConfig = await db.scheduledEmailConfig.findMany({
    where: {
      accountId: accountId,
      enabled: true,
      OR: [
        {
          action: EmailAction.RECEIPT_EMAIL,
        },
        {
          action: EmailAction.ORDER_FOLLOW_UP,
        },
      ],
    },
  });

  await db.scheduledEmail.deleteMany({
    where: {
      accountId: accountId,
      orderId: orderId,
      sent: false,
      OR: [
        { action: EmailAction.RECEIPT_EMAIL },
        { action: EmailAction.ORDER_FOLLOW_UP },
      ],
    },
  });

  for (const config of emailConfig) {
    if (config.action === EmailAction.RECEIPT_EMAIL) {
      await createScheduleFromConfig(
        orderId,
        accountId,
        config,
        {
          receipt: variables,
        },
        undefined,
        false,
      );
      continue;
    }

    if (config.action === EmailAction.ORDER_FOLLOW_UP) {
      const deliveryDate = new Date(
        new Date(variables.contractSharedItems.rentalEndDate).getTime() +
          config.minutesAfter * 60_000,
      );

      await createScheduleFromConfig(
        orderId,
        accountId,
        config,
        { receipt: variables },
        deliveryDate,
        false,
      );
    }
  }
  await sendScheduledEmails();
};

export async function handleNewOrder(
  order: Order,
  accountId: number,
  variables?: ReceiptEmailProps,
): Promise<void> {
  const emailConfig = await db.scheduledEmailConfig.findMany({
    where: {
      accountId: accountId,
      enabled: true,
      OR: [
        { action: EmailAction.ANNIVERSARY_DATE },
        { action: EmailAction.ORDER_REMINDER },
      ],
    },
  });

  for (const config of emailConfig) {
    const deliveryDate = new Date(
      new Date(order.startTime).getTime() - config.minutesAfter * 60_000,
    );

    if (deliveryDate.getTime() < new Date().getTime()) {
      continue;
    }

    await createScheduleFromConfig(
      order.id,
      accountId,
      config,
      { receipt: variables },
      deliveryDate,
      false,
    );
  }
  await sendScheduledEmails();
}

export const handleOrderDeletion = async (
  orderId: number,
  accountId: number,
  variables?: CancelEmailProps,
  sendCancellationEmail = true,
) => {
  await db.scheduledEmail.deleteMany({
    where: {
      orderId: orderId,
      sent: false,
      OR: [
        { action: EmailAction.RECEIPT_EMAIL },
        { action: EmailAction.ORDER_UPDATE },
        { action: EmailAction.ORDER_REMINDER },
        { action: EmailAction.ORDER_FOLLOW_UP },
      ],
    },
  });

  if (sendCancellationEmail) {
    const email = await db.scheduledEmailConfig.findFirst({
      where: {
        accountId: accountId,
        action: EmailAction.ORDER_CANCELLATION,
        enabled: true,
      },
    });

    if (email) {
      await createScheduleFromConfig(orderId, accountId, email, {
        cancel: variables,
      });
    }
  }
};

export async function handleQuoteCreation(
  orderId: number,
  accountId: number,
  props: QuoteEmailProps,
): Promise<void> {
  const email = await db.scheduledEmailConfig.findFirst({
    where: {
      accountId: accountId,
      action: EmailAction.ABANDON_CART,
      enabled: true,
    },
  });

  if (email) {
    const deliveryDate = new Date(
      new Date().getTime() + email.minutesAfter * 60_000,
    );

    await createScheduleFromConfig(
      orderId,
      accountId,
      email,
      { quote: props },
      deliveryDate,
    );
  }
}

export async function handleOrderUpdate(
  orderId: number,
  accountId: number,
  variables?: ReceiptEmailProps,
): Promise<void> {
  const email = await db.scheduledEmailConfig.findFirst({
    where: {
      accountId: accountId,
      action: EmailAction.ORDER_UPDATE,
      enabled: true,
    },
  });

  if (email) {
    await createScheduleFromConfig(orderId, accountId, email, {
      receipt: variables,
    });
  }
}

const createScheduleFromConfig = async (
  orderId: number,
  accountId: number,
  scheduledEmail: ScheduledEmailConfig,
  variables?: {
    receipt?: ReceiptEmailProps;
    cancel?: CancelEmailProps;
    quote?: QuoteEmailProps;
  },
  deliveryAt?: Date,
  proc?: boolean,
) => {
  if (scheduledEmail?.enabled !== true) {
    return;
  }
  if (!deliveryAt) {
    deliveryAt = new Date(Date.now() + scheduledEmail.minutesAfter * 60_000);
  }

  let order: Order & {
    account: Account;
    Contract: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      signed: boolean;
      signedDate: Date | null;
      signedIpAddress: string | null;
      signedStorageUrl: string | null;
      signedUserAgent: string | null;
      inPerson: boolean;
      orderId: number | null;
      customerId: string;
    }[];
    Customer: { id: string };
  };
  let customerEmail: CustomEmailDelivery;

  try {
    const lookup = await db.$transaction(async (tx) => {
      const order = await tx.order.findUnique({
        where: { id: orderId, accountId: accountId },
        include: { account: true, Customer: true, Contract: true },
      });

      if (!order) {
        throw new Error(`Order with ID ${orderId} not found.`);
      }

      const customerEmail = await tx.customEmailDelivery.findFirst({
        where: {
          accountId: accountId,
          id: scheduledEmail.customEmailDeliveryId,
        },
      });

      if (!customerEmail) {
        throw new Error(
          `Custom email delivery with ID ${scheduledEmail.customEmailDeliveryId} not found.`,
        );
      }
      return { order, customerEmail };
    });
    order = lookup.order;
    customerEmail = lookup.customerEmail;
  } catch (e) {
    if (e instanceof Error) {
      captureMessage(
        `Error looking up order and email: ${e?.message ?? "Unknown"}`,
      );
    } else {
      captureMessage("Unknown error looking up order and email");
    }
    return;
  }

  const emailContent: JSONContent = JSON.parse(customerEmail.text);

  if (!emailContent) {
    captureMessage("Invalid email content");
    return;
  }

  const mail = new Maily(emailContent);
  console.log("scheduling content...");

  if (variables?.receipt) {
    mail.setContractItems({
      depositPercentage: order.account.minimumOrderPaymentPercentage,
      contractItems: variables.receipt.contractItems,
      paidItems: variables.receipt.paidItems,
      contractSharedItems: variables.receipt.contractSharedItems,
      refundAmount: variables.receipt.refundedAmount,
      accountTimezone: order.account.businessTimezone,
    });
  }
  if (variables?.cancel) {
    mail.setContractItems({
      depositPercentage: order.account.minimumOrderPaymentPercentage,
      contractItems: variables.cancel.contractItems,
      paidItems: variables.cancel.paidItems,
      contractSharedItems: variables.cancel.contractSharedItems,
      refundAmount: 0,
      accountTimezone: order.account.businessTimezone,
    });
  }
  if (variables?.quote) {
    mail.setVariableValues({
      QUOTE_LINK: variables?.quote.shoppingId,
    });
    mail.setContractItems({
      depositPercentage: order.account.minimumOrderPaymentPercentage,
      contractItems: variables?.quote.contractItems,
      paidItems: variables?.quote.paidItems,
      contractSharedItems: variables?.quote.contractSharedItems,
      refundAmount: variables?.quote.refundedAmount,
      accountTimezone: order.account.businessTimezone,
    });
  }
  mail.setVariableValuesFromCustomer(order.Customer);
  mail.setVariableValuesFromOrder(order);
  mail.setVariableValues({
    ORDER_PAYMENT_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/orders/${order.id}/${order.customerId}`,
    ORDER_CONTRACT_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/contract/${order.id}/${order.Contract?.at(0)?.id}`,
    ORDER_SURVEY_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/survey/${order.id}/${order.customerId}`,
  });
  if (customerEmail.previewText) {
    mail.setPreviewText(customerEmail.previewText);
  }

  const email = await mail.renderAsync({});
  await db.scheduledEmail.create({
    data: {
      sent: false,
      deliveryAt,
      subject: customerEmail.subject,
      body: email,
      accountId: accountId,
      orderId: orderId,
      action: scheduledEmail.action,
    },
  });
  if (proc !== false) {
    // proc the schedule
    await sendScheduledEmails();
  }
};
