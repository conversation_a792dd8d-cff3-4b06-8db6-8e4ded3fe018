import { Customer, EmailAction, Order } from "@prisma/client";
import { db } from "../db";
import { OrderState } from ".prisma/client";
import { captureException, captureMessage } from "@sentry/nextjs";
import { sendMail } from "~/server/email/sendMail";

const shouldSendOrderEmail = (
  order: Order,
  customer: Customer,
  action: EmailAction,
): boolean => {
  switch (action) {
    case EmailAction.ORDER_REMINDER:
      return (
        order.state === OrderState.ACTIVE &&
        order.startTime.getTime() > new Date().getTime() &&
        !order.delivered
      );
    case EmailAction.ORDER_CANCELLATION:
      return order.state === OrderState.CANCELLED;
    case EmailAction.ABANDON_CART:
      return (
        order.totalPaid === 0 &&
        (order.state === OrderState.ABANDONED_QUOTE ||
          order.state === OrderState.QUOTE)
      );
    case EmailAction.ORDER_FOLLOW_UP:
      return (
        (order.state === OrderState.ACTIVE ||
          order.state === OrderState.COMPLETED) &&
        order.totalPaid > 0 &&
        order.endTime.getTime() < new Date().getTime()
      );
    case EmailAction.ORDER_UPDATE:
    case EmailAction.ANNIVERSARY_DATE:
    case EmailAction.RECEIPT_EMAIL:
      return true;

    default:
      return false;
  }
};

export async function sendScheduledEmails(): Promise<void> {
  // Fetch emails that are due to be sent
  const emailsToSend = await db.scheduledEmail.findMany({
    where: {
      deliveryAt: {
        lte: new Date(),
      },
      sent: false,
    },
    include: {
      order: {
        include: {
          Customer: true,
        },
      },
      account: true,
    },
  });

  const emailsToMark: number[] = [];
  const emailsToDelete: number[] = [];
  const emailsRecentlySent: {
    email: string;
    type: EmailAction;
  }[] = [];

  for (const email of emailsToSend) {
    const order = email.order;

    if (!order) {
      captureMessage(`Order with ID ${email.orderId} not found.`);
      continue;
    }

    if (!shouldSendOrderEmail(order, order.Customer, email.action)) {
      captureMessage(
        `Order with ID ${email.orderId} ${email.action} not sent.`,
        {
          extra: {
            order: JSON.stringify(order),
          },
        },
      );
      emailsToDelete.push(email.id);
      continue;
    }

    // Check if email was already sent
    const recentlySent = emailsRecentlySent.find(
      (sent) =>
        sent.email.toLowerCase() === order.Customer.email.toLowerCase() &&
        sent.type === email.action,
    );
    if (recentlySent) {
      emailsToDelete.push(email.id);
      continue;
    }

    // Prepare email content
    const subject = email.subject;
    const body = email.body;

    emailsRecentlySent.push({
      email: order.Customer.email,
      type: email.action,
    });

    // Send the email
    try {
      await sendMail(
        true,
        order.accountId,
        email.account.name,
        email.account.businessEmail,
        order.Customer.email,
        { subject, emailSender: "orders", sendAdminCopy: true },
        body,
        undefined,
        [],
        [],
      );

      // Mark the email as sent
      emailsToMark.push(email.id);
    } catch (error) {
      captureException(error);
    }
  }

  await db.$transaction(async (tx) => {
    // Mark the emails as sent
    if (emailsToMark.length > 0) {
      await tx.scheduledEmail.updateMany({
        where: {
          id: {
            in: emailsToMark,
          },
        },
        data: {
          sent: true,
        },
      });
    }

    // Delete the emails
    if (emailsToDelete.length > 0) {
      await tx.scheduledEmail.deleteMany({
        where: {
          id: {
            in: emailsToDelete,
          },
        },
      });
    }
  });
}
