import { Account } from ".prisma/client";
import { db } from "~/server/db";
import { Resend } from "resend";

const resend = new Resend(process.env.RESEND_API_KEY);
const DOMAIN =
  process.env.RESEND_EMAIL_DOMAIN || "mail.partyrentalplatform.com";

export type EmailTemplate = {
  emailSender: string;
  subject: string;
  sendAdminCopy?: boolean;
};

export type ReactEmailTemplate<T> = {
  html: (props: T) => string;
  text?: (props: T) => string;
  subjectOverride?: (props: T) => string;
} & EmailTemplate;

export type AccountShell = {
  id: number;
  name: string;
  businessEmail: string | null;
};

export const sendReactEmail = async <T>(
  customerDefined: boolean,
  account: AccountShell | Account,
  to: string,
  emailTemplate: ReactEmailTemplate<T>,
  props: T,
  cc?: string | string[],
  bcc?: string | string[],
  stack?: string,
) => {
  const html = emailTemplate.html(props);
  const text = emailTemplate.text ? emailTemplate.text(props) : undefined;
  const subject = emailTemplate.subjectOverride
    ? emailTemplate.subjectOverride(props)
    : emailTemplate.subject;

  return await sendMail(
    customerDefined,
    account.id,
    account.name,
    account.businessEmail,
    to,
    { ...emailTemplate, subject },
    html,
    text,
    cc,
    bcc,
  );
};

export const sendMail = async (
  customerDefined: boolean,
  accountId: number,
  accountName: string,
  accountEmail: string | null,
  to: string,
  emailTemplate: EmailTemplate,
  html: string,
  text: string | undefined,
  cc?: string | string[],
  bcc?: string | string[],
  stack?: string,
) => {
  const bccList = bcc ? (Array.isArray(bcc) ? bcc : [bcc]) : [];
  const ccList = cc ? (Array.isArray(cc) ? cc : [cc]) : [];
  if (emailTemplate.sendAdminCopy === true && accountEmail !== null) {
    bccList.push(accountEmail);
  }
  const { data, error } = await resend.emails.send({
    from: `${accountName} <${emailTemplate.emailSender}@${DOMAIN}>`,
    to: to,
    bcc: bccList,
    cc: ccList,
    subject: emailTemplate.subject,
    html: html,
    text: text || html,
    reply_to: accountEmail ?? undefined,
    headers: {
      "X-Entity-Ref-ID": `Random-UUID-${stack ?? Math.random() * 100000}`,
    },
  });

  await db.emailSentLog.create({
    data: {
      sentId: data?.id || "Unknown",
      allRecipient: [to, ...bccList, ...ccList],
      success: error === undefined,
      error: error?.message,
      customerDefinedEmail: customerDefined,
      accountId: accountId,
    },
  });

  if (error) {
    console.error(error);
    throw new Error("Error sending email");
  }
};
