// These likely won't change

export const fullImageUrl = (
  imageUpload: { url: string } | undefined | string,
): string => {
  if (!imageUpload) {
    return "";
  }
  if (typeof imageUpload === "string" && imageUpload.startsWith("http")) {
    return imageUpload;
  }
  const url = typeof imageUpload == "string" ? imageUpload : imageUpload.url;

  if (url.includes("/")) {
    // This is a legacy image, pre-cloudflare image delivery
    return `https://static.dash.partyrentalplatform.com/${encodeURIComponent(
      url,
    )}`;
  }

  return `https://static.dash.partyrentalplatform.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/${encodeURIComponent(
    url,
  )}/format=auto`;
};

export const legacyImageUrl = (
  imageUpload: { url: string } | undefined | string,
): string => {
  if (!imageUpload) {
    return "";
  }
  if (typeof imageUpload === "string" && imageUpload.startsWith("http")) {
    return imageUpload;
  }
  const url = typeof imageUpload == "string" ? imageUpload : imageUpload.url;

  return `https://static.dash.partyrentalplatform.com/${encodeURIComponent(
    url,
  )}`;
};
