import { number, object, string } from "yup";
import { AddressValidation } from "~/server/lib/location/types";
import { EMAIL_TYPE } from "~/lib/regex";

export const ACCOUNT_SETTINGS = object().shape({
  damageWaiverRate: number().min(0).max(100).nullable(),
  travelFeePerMile: number().min(0).nullable(),
  freeTravelRadius: number().min(0).nullable(),
  businessPhone: string().nullable(),
  businessEmail: EMAIL_TYPE.nullable(),
  customDomain: string().nullable(),
  billingAddress: AddressValidation,
  businessTimezone: string().required(),
  googleReviewLink: string().nullable().optional(),
  minimumOrderPaymentPercentage: number().min(0).max(100).nullable(),
});

export type AccountSettings = typeof ACCOUNT_SETTINGS.__outputType;

export type AccountSettingsWithStripe = AccountSettings & {
  stripeConfigured: boolean;
};
