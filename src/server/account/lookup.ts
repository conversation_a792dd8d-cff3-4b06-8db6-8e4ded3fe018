import { unstable_cache } from "next/cache";
import { db } from "../db";
import { getAccountLogo } from "~/server/lib/logo";

export const getCachedAccountFromId = async (id: number) => {
  return unstable_cache(
    () => {
      return db.account.findUnique({
        where: {
          id,
        },
      });
    },
    [`account-${id}`],
    {
      tags: [`account-${id}`],
      revalidate: 900,
    },
  )();
};

export const getCachedAccountLogoUrl = async (id: number) => {
  return unstable_cache(
    async () => {
      const account = await getAccountLogo(id);
      if (!account) {
        return null;
      }
      return account;
    },
    [`account-logo-${id}`],
    {
      tags: [`account-logo-${id}`],
      revalidate: 60 * 24 * 60, // 24 hours
    },
  )();
};

export const getCachedAccountFromNameOrDomain = async (name: string) => {
  return unstable_cache(
    () => {
      return db.account.findFirst({
        where: {
          OR: [
            {
              name: name,
            },
            {
              customDomain: {
                equals: name.toLowerCase(),
                mode: "insensitive",
              },
            },
          ],
        },
      });
    },
    [`account-name-${name}`],
    {
      tags: [`account-name-${name}`],
      revalidate: 24 * 60 * 60,
    },
  )();
};
