import { type GetServerSidePropsContext } from "next";
import {
  DefaultSession,
  getServerSession,
  type NextAuthOptions,
  User,
} from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

import { db } from "~/server/db";
import { comparePlainTextPassword } from "~/server/lib/password";
import { SubscriptionStatus } from ".prisma/client";
import { PlanLevel, planLevelFromString } from "~/server/lib/entitlement/types";
import { captureException } from "@sentry/core";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
type SessionUser = {
  commonValue: CommonValues;
} & User;

export type CommonValues = {
  accountId: number;
  organizationId: string;
  id: string;
  userId: string;

  name: string;
  email: string;
  roles: string[];
  lastLogin: Date;
  lastPermissionCheck?: Date;
  planLevel?: PlanLevel;
  trialEndDate?: Date;
};

declare module "next-auth" {
  interface Session {
    user: {
      accountId: number;
      organizationId: string;
      planLevel: PlanLevel;
      trialEndDate?: Date;
      roles: string[];
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT extends CommonValues {}
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authOptions: NextAuthOptions = {
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/logout",
    error: "/auth/login",
    verifyRequest: "/auth/login",
    newUser: "/auth/login",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async session({ session, user, token }) {
      session.user = {
        ...session.user,
        ...token,
      };
      session.user.accountId = token.accountId;
      session.user.organizationId = token?.organizationId;

      session.user.planLevel = token.planLevel || PlanLevel.Free;
      session.user.trialEndDate = token.trialEndDate;
      session.user.roles = token.roles;

      if (!session.user.accountId || !session.user.organizationId) {
        console.error("Missing accountId or organizationId in session");
        throw new Error("Missing accountId or organizationId in session");
      }

      return session;
    },
    jwt: async ({ token, user, trigger, session }) => {
      if (trigger === "update") {
        const currentUser = await db.user.findFirst({
          where: {
            id: token.userId,
          },
          include: {
            staff: {
              select: {
                accountId: true,
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        if (!currentUser) {
          return token;
        }

        if (!currentUser.staff) {
          console.error("Staff User not found for user");
          throw new Error("User not found for user");
        }

        const newStaff = currentUser.staff.find(
          (item) => item.accountId === session.user.accountId,
        );

        if (!newStaff) {
          console.error("Staff not found for user");
          throw new Error("Staff not found for user");
        }

        console.log("newStaff", newStaff);
        try {
          const newStaffGenerated = await generateUser(
            currentUser.id,
            newStaff.id,
          );
          token = {
            ...token,
            ...newStaffGenerated.commonValue,
          };

          return token;
        } catch (ex) {
          console.error("Error in generateUser", ex);
          captureException(ex);
          return token;
        }
      }
      const dUser = user as SessionUser;

      if (dUser && dUser.commonValue) {
        token = {
          ...token,
          ...dUser.commonValue,
        };
      }
      return token;
    },
  },
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: "Email", type: "email", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },

      async authorize(credentials): Promise<SessionUser | null> {
        if (!credentials?.email || !credentials.password) {
          throw new Error("Email and password are required");
        }

        const userObject = await db.user.findFirst({
          where: {
            email: {
              equals: credentials.email,
              mode: "insensitive",
            },
          },
        });

        if (!userObject) {
          throw new Error("Invalid Email or Password, please try again!");
        }

        const valid = await comparePlainTextPassword(
          credentials.password,
          userObject.password,
        );
        if (!valid) {
          console.error("Invalid Email or Password, please try again.");
          throw new Error("Invalid Email or Password, please try again.");
        }

        try {
          return generateUser(userObject.id, undefined);
        } catch (ex) {
          console.error("Error in authorize", ex);
          captureException(ex);
          return null;
        }
      },
    }),
  ],
};

export const generateUser = async (
  userId: string,
  staffId: string | undefined,
): Promise<SessionUser> => {
  console.log("Generating user for userId:", userId, "and staffId:", staffId);
  return db.$transaction(async (tx) => {
    const staffDb = await tx.staff.findFirst({
      where: {
        id: staffId || undefined,
        userId: userId,
      },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        account: true,
        StaffRole: {
          select: {
            roleId: true,
          },
        },
      },
    });
    if (!staffDb) {
      throw new Error("Staff not found");
    }

    const roleIds = staffDb.StaffRole.map((ur) => ur.roleId);

    const permissions = await tx.rolePermission.findMany({
      where: {
        roleId: {
          in: roleIds,
        },
      },
      select: {
        permissionId: true,
      },
    });

    const permNodes = await tx.permission.findMany({
      where: {
        id: {
          in: permissions.map((p) => p.permissionId),
        },
      },
      select: {
        policy: true,
      },
    });

    const subscription = await tx.subscription.findFirst({
      where: {
        accountId: staffDb.account.id,
        OR: [
          {
            status: SubscriptionStatus.ACTIVE,
          },
          {
            status: SubscriptionStatus.TRIALING,
          },
        ],
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const roles = permNodes.map((p) => p.policy);

    const planLevel = planLevelFromString(subscription?.planLevel || "Free");

    const user: SessionUser = {
      id: staffDb.id,
      commonValue: {
        organizationId: staffDb.account.organizationId,
        userId: userId,
        id: staffDb.id,
        name: staffDb.name || staffDb.email,
        email: staffDb.email,
        accountId: staffDb.account.id,
        roles: roles || [],
        lastLogin: new Date(),
        lastPermissionCheck: new Date(),
        trialEndDate:
          subscription?.trialEndsAt &&
          subscription.status === SubscriptionStatus.TRIALING
            ? new Date(subscription?.trialEndsAt)
            : undefined,
        planLevel: planLevel,
      },
    };

    tx.staff.update({
      where: {
        id: staffDb.id,
      },
      data: {
        lastLogin: new Date(),
        user: {
          update: {
            lastLogin: new Date(),
          },
        },
      },
    });
    return user;
  });
};

/**
 * Wrapper for `getServerSession` so that you don't need to import the `authOptions` in every file.
 *
 * @see https://next-auth.js.org/configuration/nextjs
 */
export const getServerAuthSession = (ctx: {
  req: GetServerSidePropsContext["req"];
  res: GetServerSidePropsContext["res"];
}) => {
  return getServerSession(ctx.req, ctx.res, authOptions);
};
