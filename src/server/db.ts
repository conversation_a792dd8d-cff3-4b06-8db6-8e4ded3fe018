import { PrismaClient } from "@prisma/client";

export const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const db =
  process.env.NODE_ENV !== "production" && globalForPrisma.prisma
    ? globalForPrisma.prisma
    : new PrismaClient({
        log:
          process.env.NODE_ENV === "development"
            ? ["query", "info", "error", "warn"]
            : ["info", "error"],
      });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = db;
