import { array, boolean, date, number, object, string } from "yup";

export const ScheduleSchema = object().shape({
  name: string().required("Required"),
  description: string().required("Required"),
  priority: number().default(0),
  openTime: date().default(new Date()),
  closeTime: date().default(new Date()),
  startTime: date(),
  endTime: date(),
  closed: boolean().default(false),
  active: boolean().default(true),
  days: array().of(string()).default([]),
  SUNDAY: boolean(),
  MONDAY: boolean(),
  TUESDAY: boolean(),
  WEDNESDAY: boolean(),
  THURSDAY: boolean(),
  FRIDAY: boolean(),
  SATURDAY: boolean(),
});

export type ScheduleValues = typeof ScheduleSchema.__outputType;
