export const formatStandardPhone = (phoneNumber: string): string => {
  const rawNumber = phoneNumber.replace(/\D/g, "");
  if (rawNumber.length === 10) {
    return `+1${rawNumber}`;
  }
  return `+${rawNumber}`;
};

export const displayPrettyPhone = (phone: string): string => {
  if (!phone || phone === "") {
    return "";
  }
  // convert to standard phone format
  phone = formatStandardPhone(phone);
  // remove country code if Us
  if (phone.startsWith("+1")) {
    phone = phone.slice(2);
  }
  // format
  if (phone.length === 10) {
    return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;
  }
  if (phone.length === 11) {
    return `+${phone.slice(0, 1)} (${phone.slice(1, 4)}) ${phone.slice(
      4,
      7,
    )}-${phone.slice(7)}`;
  }

  return phone;
};
