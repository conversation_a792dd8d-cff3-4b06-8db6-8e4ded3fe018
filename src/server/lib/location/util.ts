import { Address } from "@prisma/client";
import { db } from "~/server/db";
import { AddressType } from "~/server/lib/location/types";

export const toAddressType = (address: Address | null): AddressType => {
  if (!address) {
    return {
      line1: undefined,
      line2: undefined,
      city: undefined,
      postalCode: undefined,
      state: undefined,
      country: undefined,
    };
  }
  return {
    line1: address.line1 || undefined,
    line2: address.line2 || undefined,
    city: address.city || undefined,
    postalCode: address.postalCode || undefined,
    state: address.state || undefined,
    country: address.country || undefined,
  };
};

export const areAddressesEqual = (
  a: AddressType | Address | null,
  b: AddressType | Address | null,
): boolean => {
  if (!a && !b) {
    return true;
  }
  if (!a || !b) {
    return false;
  }
  return (
    a.line1 === b.line1 &&
    a.line2 === b.line2 &&
    a.city === b.city &&
    a.postalCode === b.postalCode &&
    a.state === b.state &&
    a.country === b.country
  );
};

export const findOrInsertAddress = async (
  address: AddressType,
  database: any = db,
): Promise<Address> => {
  const lineTwoIfPresent = address.line2 ? address.line2 : undefined;
  const country = address.country || "US";
  // console.log("Print Address", address);

  const existingAddress = await database.address.findFirst({
    where: {
      line1: address.line1,
      line2: lineTwoIfPresent,
      city: address.city,
      postalCode: address.postalCode,
      state: address.state,
      country: country,
    },
  });

  if (existingAddress) {
    return existingAddress;
  }

  return database.address.create({
    data: {
      line1: address.line1,
      line2: address.line2,
      city: address.city,
      postalCode: address.postalCode,
      state: address.state,
      country: country,
    },
  });
};
