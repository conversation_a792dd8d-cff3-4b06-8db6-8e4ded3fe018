import {
  AddressComponent,
  Client,
  PlaceAutocompleteResult,
  PlaceAutocompleteType,
  TravelMode,
} from "@googlemaps/google-maps-services-js";
import process from "process";
import { captureEvent, captureException } from "@sentry/core";

export const MAPS_KEY = process.env.GOOGLE_MAPS_API_KEY || "";
const METERS_TO_MILES = 0.*********;

const MAPS_CLIENT = new Client({});

export const getAddressSuggestions = async (
  addressPart: string,
): Promise<PlaceAutocompleteResult[]> => {
  const input = {
    params: {
      input: addressPart,
      types: PlaceAutocompleteType.address,
      components: ["country:us", "country:ca"],
      key: MAPS_KEY,
    },
    timeout: 1000,
  };
  const placeComplete = await MAPS_CLIENT.placeAutocomplete(input);
  const { status, data } = placeComplete;
  if (data?.predictions && status === 200) {
    return data.predictions;
  }
  return [];
};

/**
 * Returns the miles between two locations, strictly an authenticated wrapped around google maps
 *
 * @returns miles between the two locations
 * @param origin Starting location (either lat/long or address as a string)
 * @param destination Ending location (either lat/long or address as a string)
 */
export const getMilesBetweenLocations = async (
  origin: [number, number] | string,
  destination: [number, number] | string,
): Promise<
  | {
      miles: number;
      travelTimeMinutes: number;
      originLatLang: [number, number];
      destinationLatLang: [number, number];
    }
  | undefined
> => {
  // todo it'd be cool to create a node graph of all the locations and then just do a dijkstra's algorithm to find the shortest path
  // todo but for now, we'll just use google maps
  // todo please cache this...
  let directions;
  let originLatLang: [number, number] | undefined;
  if (!Array.isArray(origin)) {
    const short = await getLongitudeAndLatitude(origin);
    if (!short) {
      return undefined;
    }
    originLatLang = [short.latitude, short.longitude];
  } else {
    originLatLang = origin;
  }
  if (!originLatLang) {
    return undefined;
  }
  let destinationLatLang: [number, number] | undefined;
  if (!Array.isArray(destination)) {
    const short = await getLongitudeAndLatitude(destination);
    if (!short) {
      return undefined;
    }
    destinationLatLang = [short.latitude, short.longitude];
  } else {
    destinationLatLang = destination;
  }
  if (!destinationLatLang) {
    return undefined;
  }
  try {
    directions = await MAPS_CLIENT.directions({
      params: {
        alternatives: true,
        origin: originLatLang,
        destination: destinationLatLang,
        mode: TravelMode.driving,
        key: MAPS_KEY,
      },
    });
  } catch (e) {
    captureException(e);
    return undefined;
  }
  if (directions.status !== 200) {
    captureEvent({
      message: "Failed to get directions",
      extra: {
        origin,
        destination,
        directions,
      },
    });
    return undefined;
  }
  const routes = directions?.data?.routes?.map((route) => {
    return {
      distance: route?.legs?.reduce((acc, leg) => acc + leg.distance.value, 0),
      duration: route?.legs?.reduce((acc, leg) => acc + leg.duration.value, 0),
    };
  });
  const bestRoute = routes?.reduce((acc, route) => {
    if (route.distance < acc.distance) {
      return route;
    }
    return acc;
  });
  return {
    miles: bestRoute.distance * METERS_TO_MILES,
    travelTimeMinutes: bestRoute.duration / 60,
    destinationLatLang: destinationLatLang,
    originLatLang: originLatLang,
  };
};

export const getLongitudeAndLatitude = async (
  address: string,
): Promise<
  | {
      longitude: number;
      latitude: number;
      components: AddressComponent[];
    }
  | undefined
> => {
  const geocodeResponse = await MAPS_CLIENT.geocode({
    params: {
      address,
      key: MAPS_KEY,
    },
    timeout: 1000,
  });
  if (geocodeResponse.status !== 200) {
    return undefined;
  }
  const firstResult = geocodeResponse.data.results[0];
  if (!firstResult) {
    return undefined;
  }
  const { lat, lng } = firstResult.geometry.location;
  return {
    longitude: lng,
    latitude: lat,
    components: firstResult.address_components,
  };
};
