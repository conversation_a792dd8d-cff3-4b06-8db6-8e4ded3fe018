import bcrypt from "bcrypt";

export const SALT_ROUNDS = 10;

export type GeneratedPassword = {
  password: string;
  hash: string;
};

export const generatePassword = async (): Promise<GeneratedPassword> => {
  const password = Math.random().toString(36).slice(-8);
  return { password, hash: await handlePlainTextPassword(password) };
};

export const handlePlainTextPassword = async (
  password: string,
): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

export const comparePlainTextPassword = async (
  password: string,
  hash: string,
): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};
