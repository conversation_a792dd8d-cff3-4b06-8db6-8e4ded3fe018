export enum PlanLevel {
  Free = "free",
  Lite = "lite",
  Core = "core",
  Pro = "pro",
  Unlimited = "unlimited",
}

export const planLevelOrder: Record<PlanLevel, number> = {
  [PlanLevel.Free]: 0,
  [PlanLevel.Lite]: 0,
  [PlanLevel.Core]: 2,
  [PlanLevel.Pro]: 3,
  [PlanLevel.Unlimited]: 4,
};

export const getNextPlanLevel = (
  planLevel: PlanLevel,
): PlanLevel | undefined => {
  switch (planLevel) {
    case PlanLevel.Free:
      return PlanLevel.Core;
    case PlanLevel.Core:
      return PlanLevel.Pro;
    case PlanLevel.Pro:
      return PlanLevel.Unlimited;
    case PlanLevel.Lite:
      return PlanLevel.Core;
    default:
      return undefined;
  }
};

export const planLevelFromString = (planLevel: string): PlanLevel => {
  switch (planLevel.toLowerCase()) {
    case "core":
      return PlanLevel.Core;
    case "pro":
      return PlanLevel.Pro;
    case "unlimited":
      return PlanLevel.Unlimited;
    case "lite":
      return PlanLevel.Lite;
    default:
      return PlanLevel.Free;
  }
};
