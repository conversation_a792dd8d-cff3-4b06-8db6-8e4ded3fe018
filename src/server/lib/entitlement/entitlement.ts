import { Subscription, SubscriptionStatus } from ".prisma/client";
import { db } from "~/server/db";
import { PlanLevel, planLevelFromString } from "~/server/lib/entitlement/types";
import { unstable_cache } from "next/cache";

type BestEntitlement = {
  level: PlanLevel;
  subscription: Subscription | null;
};

const getContent = async (accountId: number): Promise<BestEntitlement> => {
  const subscription = await db.subscription.findFirst({
    where: {
      accountId: accountId,
      OR: [
        {
          status: SubscriptionStatus.ACTIVE,
        },
        {
          status: SubscriptionStatus.TRIALING,
        },
      ],
    },
  });

  if (!subscription) {
    return {
      level: PlanLevel.Free,
      subscription: null,
    };
  }
  const planLevel = planLevelFromString(subscription.planLevel);

  return {
    level: planLevel,
    subscription,
  };
};

export const findBestEntitlement = async (
  accountId: number,
): Promise<BestEntitlement> => {
  return unstable_cache(
    async () => {
      return getContent(accountId);
    },
    [`entitlement-${accountId}`],
    {
      revalidate: 190,
      tags: [`entitlement-${accountId}`],
    },
  )();
};
