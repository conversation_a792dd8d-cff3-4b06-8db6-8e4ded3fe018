import {
  GetObjectCommand,
  NoS<PERSON><PERSON>ey,
  PutO<PERSON><PERSON>ommand,
  S3Client,
} from "@aws-sdk/client-s3";

export const r2 = new S3Client({
  region: "auto",
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
});

export const putItem = async (
  bucket: string,
  key: string,

  buffer: string | Buffer | Blob,
  contentType?: string,
) => {
  return await r2.send(
    new PutObjectCommand({
      Bucket: bucket,
      Key: key,
      Body: buffer,
      ContentType: contentType ?? "application/json",
    }),
  );
};

export const getJsonItem = async <T>(
  bucket: string,
  key: string,
): Promise<T | undefined> => {
  try {
    const data = await r2.send(
      new GetObjectCommand({
        Bucket: bucket,
        Key: key,
      }),
    );

    const body = await data?.Body?.transformToString();

    return JSON.parse(body || "{}") as T;
  } catch (error) {
    if (error instanceof NoSuchKey) {
      return undefined; // Return empty object on error
    }
    throw error; // Rethrow other errors
  }
};
