import twilio, { Twi<PERSON> } from "twilio";

export const getTwilio = async (): Promise<Twilio> => {
  return twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
};

export const getTwilioForSubAccount = (
  accountSid: string,
  authToken: string,
): Twilio => {
  return twilio(accountSid, authToken, {
    accountSid: accountSid,
  });
};

export const formatPhoneNumber = (phoneNumber: string): string => {
  const rawNumber = phoneNumber.replace(/\D/g, "");
  if (rawNumber.length === 10) {
    return `+1${rawNumber}`;
  }
  return `+${rawNumber}`;
};
