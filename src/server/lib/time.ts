import { Schedule, ScheduleDay } from "@prisma/client";
import { differenceInMilliseconds, startOfDay } from "date-fns";

// Jul 20, 2024, 10:00 AM
const formattingOptions: any = {
  weekday: "short",
  month: "short",
  day: "numeric",
  hour: "2-digit",
  minute: "numeric",
  year: "numeric",
};

// Jul 20, 01:00 PM
const compactFormattingOptions: any = {
  month: "short",
  day: "numeric",
  hour: "2-digit",
  minute: "numeric",
  hour12: true, // 12-hour format with AM/PM
};

export const COMPACT_FORMATTER = new Intl.DateTimeFormat("en", {
  ...compactFormattingOptions,
});

export const SAME_DAY_TIME_RANGE = new Intl.DateTimeFormat("en", {
  ...formattingOptions,
});
export const SANE_FORMATTER = new Intl.DateTimeFormat("en", {
  ...formattingOptions,
  weekday: undefined,
});
export const HOUR_FORMAT = new Intl.DateTimeFormat("en", {
  hour: "2-digit",
  minute: "2-digit",
});

export const REPORT_DATE_FORMAT = new Intl.DateTimeFormat("en", {
  month: "short",
  day: "numeric",
  year: "numeric",
});

const formatter = new Intl.RelativeTimeFormat("en", {
  numeric: "auto",
});

export function formatMessageDate(date: Date): string {
  const now = new Date();
  const inputDate = new Date(date);

  const optionsTime: Intl.DateTimeFormatOptions = {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  };
  const optionsDay: Intl.DateTimeFormatOptions = { weekday: "long" };
  const optionsDate: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
  };
  const optionsFullDate: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
    year: "numeric",
  };

  const isToday = inputDate.toDateString() === now.toDateString();
  const isYesterday =
    new Date(now.setDate(now.getDate() - 1)).toDateString() ===
    inputDate.toDateString();
  const isSameWeek =
    now.getTime() - inputDate.getTime() < 7 * 24 * 60 * 60 * 1000 &&
    inputDate.getDay() < now.getDay() &&
    inputDate.getFullYear() === now.getFullYear();
  const isSameYear = inputDate.getFullYear() === now.getFullYear();

  if (isToday) {
    return inputDate.toLocaleTimeString(undefined, optionsTime);
  } else if (isYesterday) {
    return "Yesterday";
  } else if (isSameWeek) {
    return inputDate.toLocaleDateString(undefined, optionsDay);
  } else if (isSameYear) {
    return inputDate.toLocaleDateString(undefined, optionsDate);
  } else {
    return inputDate.toLocaleDateString(undefined, optionsFullDate);
  }
}

const DIVISIONS = [
  { amount: 60, name: "seconds" },
  { amount: 60, name: "minutes" },
  { amount: 24, name: "hours" },
  { amount: 7, name: "days" },
  { amount: 4.34524, name: "weeks" },
  { amount: 12, name: "months" },
  { amount: Number.POSITIVE_INFINITY, name: "years" },
];

export const formatDuration = (ms: number) => {
  if (ms < 0) ms = -ms;
  const time = {
    day: Math.floor(ms / 86400000),
    hour: Math.floor(ms / 3600000) % 24,
    minute: Math.floor(ms / 60000) % 60,
  };
  return Object.entries(time)
    .filter((val) => val[1] !== 0)
    .map(([key, val]) => `${val} ${key}${val !== 1 ? "s" : ""}`)
    .join(", ");
};

export function formatTimeAgo(date: Date): string | undefined {
  let duration = (date.getTime() - new Date().getTime()) / 1000;

  for (const division of DIVISIONS) {
    if (Math.abs(duration) < division.amount) {
      return formatter.format(
        Math.round(duration),
        division.name as
          | "year"
          | "years"
          | "quarter"
          | "quarters"
          | "month"
          | "months"
          | "week"
          | "weeks"
          | "day"
          | "days"
          | "hour"
          | "hours"
          | "minute"
          | "minutes"
          | "second"
          | "seconds",
      );
    }
    duration /= division.amount;
  }
}

export function clampWindow(
  timestampMs: number,
  targetMidnightMs: number,
): number {
  const date = new Date(timestampMs);
  const targetDate = new Date(targetMidnightMs);

  const hours = date.getHours();
  const isSameOrBefore = date.getDate() <= targetDate.getDate();
  const isAfter = date.getDate() > targetDate.getDate();

  // Before 5am OR on/before target date -> 5am of target date
  if (hours < 5 || isSameOrBefore) {
    return targetMidnightMs + 5 * 60 * 60 * 1000; // +5 hours
  }

  // After target date -> 11:59pm of target date
  if (isAfter) {
    return targetMidnightMs + 23 * 60 * 60 * 1000 + 59 * 60 * 1000; // +23h59m
  }

  return timestampMs;
}

export function convertToClientTimezone(
  timestampMs: number,
  clientMidnightMs: number,
): number {
  // Get what midnight should be in UTC for this client
  const clientMidnightDate = new Date(clientMidnightMs);
  const utcMidnight = startOfDay(clientMidnightDate);

  // Calculate the offset
  const timezoneOffsetMs = differenceInMilliseconds(
    clientMidnightDate,
    utcMidnight,
  );

  // Apply offset to your timestamp
  return timestampMs + timezoneOffsetMs;
}

export const formatTimeRange = (
  start: Date,
  end: Date | null,
  timeZone?: string,
): string => {
  if (!end) {
    return SAME_DAY_TIME_RANGE.format(start);
  }
  const sameYear = new Date().getFullYear() === end.getFullYear();
  const formatOptions = { ...formattingOptions };
  if (sameYear) {
    formatOptions.year = undefined;
  }
  if (timeZone) {
    formatOptions.timeZone = timeZone;
  }

  return new Intl.DateTimeFormat("en-US", formatOptions).formatRange(
    start,
    end,
  );
};

export const formatHourRange = (start: Date, end: Date | null): string => {
  if (!end) {
    return HOUR_FORMAT.format(start);
  }
  return HOUR_FORMAT.formatRange(start, end);
};

/**
 * regular expression to check for valid hour format (01-23)
 */
export function isValidHour(value: string) {
  return /^(0[0-9]|1[0-9]|2[0-3])$/.test(value);
}

/**
 * regular expression to check for valid 12 hour format (01-12)
 */
export function isValid12Hour(value: string) {
  return /^(0[1-9]|1[0-2])$/.test(value);
}

/**
 * regular expression to check for valid minute format (00-59)
 */
export function isValidMinuteOrSecond(value: string) {
  return /^[0-5][0-9]$/.test(value);
}

type GetValidNumberConfig = { max: number; min?: number; loop?: boolean };

export function getValidNumber(
  value: string,
  { max, min = 0, loop = false }: GetValidNumberConfig,
) {
  let numericValue = parseInt(value, 10);

  if (!isNaN(numericValue)) {
    if (!loop) {
      if (numericValue > max) numericValue = max;
      if (numericValue < min) numericValue = min;
    } else {
      if (numericValue > max) numericValue = min;
      if (numericValue < min) numericValue = max;
    }
    return numericValue.toString().padStart(2, "0");
  }

  return "00";
}

export function getValidHour(value: string) {
  if (isValidHour(value)) return value;
  return getValidNumber(value, { max: 23 });
}

export function getValid12Hour(value: string) {
  if (isValid12Hour(value)) return value;
  return getValidNumber(value, { min: 1, max: 12 });
}

export function getValidMinuteOrSecond(value: string) {
  if (isValidMinuteOrSecond(value)) return value;
  return getValidNumber(value, { max: 59 });
}

type GetValidArrowNumberConfig = {
  min: number;
  max: number;
  step: number;
};

export function getValidArrowNumber(
  value: string,
  { min, max, step }: GetValidArrowNumberConfig,
) {
  let numericValue = parseInt(value, 10);
  if (!isNaN(numericValue)) {
    numericValue += step;
    return getValidNumber(String(numericValue), { min, max, loop: true });
  }
  return "00";
}

export function getValidArrowHour(value: string, step: number) {
  return getValidArrowNumber(value, { min: 0, max: 23, step });
}

export function getValidArrow12Hour(value: string, step: number) {
  return getValidArrowNumber(value, { min: 1, max: 12, step });
}

export function getValidArrowMinuteOrSecond(value: string, step: number) {
  return getValidArrowNumber(value, { min: 0, max: 59, step });
}

export function setMinutes(date: Date, value: string) {
  const minutes = getValidMinuteOrSecond(value);
  date.setMinutes(parseInt(minutes, 10));
  return date;
}

export function setSeconds(date: Date, value: string) {
  const seconds = getValidMinuteOrSecond(value);
  date.setSeconds(parseInt(seconds, 10));
  return date;
}

export function setHours(date: Date, value: string) {
  const hours = getValidHour(value);
  date.setHours(parseInt(hours, 10));
  return date;
}

export function set12Hours(date: Date, value: string, period: Period) {
  const hours = parseInt(getValid12Hour(value), 10);
  const convertedHours = convert12HourTo24Hour(hours, period);
  date.setHours(convertedHours);
  return date;
}

export type TimePickerType = "minutes" | "seconds" | "hours" | "12hours";
export type Period = "AM" | "PM";

export function setDateByType(
  date: Date,
  value: string,
  type: TimePickerType,
  period?: Period,
) {
  switch (type) {
    case "minutes":
      return setMinutes(date, value);
    case "seconds":
      return setSeconds(date, value);
    case "hours":
      return setHours(date, value);
    case "12hours": {
      if (!period) return date;
      return set12Hours(date, value, period);
    }
    default:
      return date;
  }
}

export function getDateByType(date: Date, type: TimePickerType) {
  switch (type) {
    case "minutes":
      return getValidMinuteOrSecond(String(date.getMinutes()));
    case "seconds":
      return getValidMinuteOrSecond(String(date.getSeconds()));
    case "hours":
      return getValidHour(String(date.getHours()));
    case "12hours":
      const hours = display12HourValue(date.getHours());
      return getValid12Hour(String(hours));
    default:
      return "00";
  }
}

export function getArrowByType(
  value: string,
  step: number,
  type: TimePickerType,
) {
  switch (type) {
    case "minutes":
      return getValidArrowMinuteOrSecond(value, step);
    case "seconds":
      return getValidArrowMinuteOrSecond(value, step);
    case "hours":
      return getValidArrowHour(value, step);
    case "12hours":
      return getValidArrow12Hour(value, step);
    default:
      return "00";
  }
}

/**
 * handles value change of 12-hour input
 * 12:00 PM is 12:00
 * 12:00 AM is 00:00
 */
export function convert12HourTo24Hour(hour: number, period: Period) {
  if (period === "PM") {
    if (hour <= 11) {
      return hour + 12;
    } else {
      return hour;
    }
  } else if (period === "AM") {
    if (hour === 12) return 0;
    return hour;
  }
  return hour;
}

/**
 * time is stored in the 24-hour form,
 * but needs to be displayed to the user
 * in its 12-hour representation
 */
export function display12HourValue(hours: number) {
  if (hours === 0 || hours === 12) return "12";
  if (hours >= 22) return `${hours - 12}`;
  if (hours % 12 > 9) return `${hours}`;
  return `0${hours % 12}`;
}

export const isDateBeforeOrEqual = (date: Date, otherDate: Date): boolean => {
  return date.getTime() <= otherDate.getTime();
};

export const isDateAfterOrEqual = (date: Date, otherDate: Date): boolean => {
  return date.getTime() >= otherDate.getTime();
};

export const isDateBetweenOrEqual = (
  date: Date,
  startDate: Date,
  endDate: Date,
): boolean => {
  return (
    isDateBeforeOrEqual(startDate, date) && isDateAfterOrEqual(endDate, date)
  );
};

export const doDateRangesOverlap = (
  startDate1: Date,
  endDate1: Date,
  startDate2: Date,
  endDate2: Date,
): boolean => {
  return startDate1 <= endDate2 && startDate2 <= endDate1;
};

export const isDateBetweenOrDuring = (
  date: Date,
  startDate: Date,
  endDate: Date,
): boolean => {
  const endOfTheDay = new Date(date.getTime() + 24 * 60 * 60 * 1000);

  // Checks to see if it's just a normal between
  const isEventNormallyDuring = isDateBetweenOrEqual(date, startDate, endDate);
  // Checks to see if the event started today and is still going on
  // It makes sure it's after the start of today, but before the end of today while the end is after the start of the day
  const isEventDuringToday =
    isDateAfterOrEqual(startDate, date) &&
    isDateBeforeOrEqual(startDate, endOfTheDay) &&
    isDateAfterOrEqual(endDate, date);
  return isEventNormallyDuring || isEventDuringToday;
};

export const doesScheduleApply = (schedule: Schedule, date: Date) => {
  if (!schedule.active) {
    return false;
  }
  const start = new Date(schedule.startTime);
  const end = new Date(schedule.endTime);
  const withinScope = date >= start && date <= end;
  if (!withinScope) {
    return false;
  }
  const dayOfWeek = date.getDay();
  const dayOfTheWeekSchedule = Object.values(ScheduleDay).at(dayOfWeek);
  if (!dayOfTheWeekSchedule) {
    return false;
  }
  return schedule.days.includes(dayOfTheWeekSchedule);
};

export const endOfDay = (date: Date): Date => {
  return new Date(date.getTime() + 24 * 60 * 60 * 1000);
};
