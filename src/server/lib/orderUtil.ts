import { db } from "~/server/db";
import { Account, ChargeType, Coupon, Order, OrderState } from ".prisma/client";
import {
  Address,
  DiscountType,
  OrderFeeType,
  SetupSurface,
} from "@prisma/client";
import {
  ContractSharedItems,
  OrderContractItems,
  OrderPaidItems,
} from "~/components/Contract/Top/types";
import {
  DiscountRateBasedPaymentItem,
  getCurrencyString,
  getPaymentInfo,
  PaymentInfo,
  PaymentInfoLineItem,
  RateBasedPaymentItemAmount,
  RateBasedPaymentItemPercentage,
} from "~/server/lib/currency";
import { ReceiptEmailProps } from "../../../emails/ReceiptEmail";
import { getFullAddress } from "~/server/lib/location/types";
import { getMilesBetweenLocations } from "~/server/lib/location/maps";
import { doDateRangesOverlap } from "~/server/lib/time";
import { getAccountLogo } from "~/server/lib/logo";
import { captureEvent } from "@sentry/nextjs";
import { getCachedAccountFromId } from "~/server/account/lookup";
import { captureException } from "@sentry/core";

export type ProductCharge = {
  orderId: number;
  productId: number;
  quantity: number;
  pricePaid: number;
};

export type ProductChargeInput = {
  productId: number;
  quantity: number;
  pricePaid: number;
};

export type OrderFeeInput = {
  amount: number;
  name: string | null;
  type: OrderFeeType;
  taxable: boolean;
};

export type OrderDiscountInputAmount = {
  chargeType: "DOLLAR";
  type: DiscountType;
  amount: number;
  entityId: number | null;
};

export type OrderDiscountInputPercentage = {
  chargeType: "PERCENTAGE";
  type: DiscountType;
  amount: number;
  entityId: number | null;
};

export type OrderDiscountInput =
  | OrderDiscountInputAmount
  | OrderDiscountInputPercentage;

export type NamedProductChargeInput = {
  productName: string;
} & ProductChargeInput;

type SuccessProps = {
  displayItems: PaymentInfoLineItem[];
  order: Order;
  emailProps?: ReceiptEmailProps;
};

export const handleCoupon = async (
  accountId: number,
  couponId: number | undefined | null,
): Promise<{
  couponInfo: RateBasedPaymentItemPercentage | RateBasedPaymentItemAmount;
  couponObject: Coupon;
} | null> => {
  if (!couponId) {
    return null;
  }
  const coupon = await db.coupon.findFirst({
    where: {
      accountId: accountId,
      id: couponId,
    },
  });
  if (!coupon) {
    return null;
  }
  let couponInfo: RateBasedPaymentItemPercentage | RateBasedPaymentItemAmount;
  if (coupon.discountType === ChargeType.PERCENTAGE) {
    couponInfo = {
      percentage: coupon.discount,
    };
  } else {
    couponInfo = {
      amount: coupon.discount,
    };
  }

  return { couponInfo, couponObject: coupon };
};

export const handleOrderDiscounts = async (
  accountId: number,
  couponId: number | null,
): Promise<OrderDiscountInput[]> => {
  const discounts: OrderDiscountInput[] = [];

  if (couponId) {
    const coupon = await handleCoupon(accountId, couponId);
    if (coupon) {
      let discount: OrderDiscountInput;
      if (coupon.couponInfo.percentage !== undefined) {
        discount = {
          chargeType: "PERCENTAGE",
          type: DiscountType.COUPON,
          entityId: coupon.couponObject.id,
          amount: coupon.couponInfo.percentage,
        };
      } else {
        discount = {
          chargeType: "DOLLAR",
          type: DiscountType.COUPON,
          entityId: coupon.couponObject.id,
          amount: coupon.couponInfo.amount,
        };
      }
      discounts.push(discount);
    }
  }

  return discounts;
};

export const updateOrderTotalValues = async (
  accountId: number,
  products: ProductChargeInput[],
  discountInput: OrderDiscountInput[],
  fee: OrderFeeInput[],
  damageWaiver: { enabled: boolean; percentage: number | null },
  taxRate: number | null,
  paymentsMadeAmount: number,
): Promise<{
  paymentInfo: PaymentInfo;
  fees: OrderFeeInput[];
  discounts: DiscountRateBasedPaymentItem[];
  order: {
    taxRate: number | null;
    totalPaid: number;
    damageWaiverRate: number | null;
    damageWaiverAmount: number | null;
    taxAmount: number | null;
    baseTotal: number;
    finalTotal: any;
  };
}> => {
  const paymentInfo = await calculateOrderTotal(
    accountId,
    products,
    discountInput,
    fee,
    damageWaiver,
    taxRate,
    paymentsMadeAmount,
  );

  const {
    baseTotal,
    discounts,
    damageWaiver: damageWaiverAmount,
    fees,
    tax,
    totalPaid,
    finalTotal: totalAmount,
  } = paymentInfo;

  return {
    order: {
      baseTotal: baseTotal,
      damageWaiverRate: damageWaiverAmount
        ? damageWaiverAmount.percentage
        : null,
      damageWaiverAmount: damageWaiverAmount ? damageWaiverAmount.amount : null,
      taxRate: tax ? tax.percentage : null,
      taxAmount: tax ? tax.amount : null,
      finalTotal: totalAmount,
      totalPaid: totalPaid,
    },
    discounts: discounts,
    fees: fees,
    paymentInfo: paymentInfo,
  };
};

export const calculateOrderTotal = async (
  accountId: number,
  products: ProductChargeInput[],
  orderDiscount: OrderDiscountInput[] | null,
  orderFee: OrderFeeInput[] | null,
  damageWaiver: { enabled: boolean; percentage: number | null },
  taxRate: number | null,
  paymentsMadeAmount: number,
): Promise<PaymentInfo> => {
  let productAmount = 0;
  products.forEach((product: ProductChargeInput) => {
    const price = product.pricePaid;
    productAmount += price * product.quantity;
  });

  let damageWaiverReturn: RateBasedPaymentItemPercentage | null = null;
  if (damageWaiver.enabled && damageWaiver.percentage) {
    damageWaiverReturn = {
      percentage: damageWaiver.percentage,
    };
  }

  return getPaymentInfo({
    baseTotal: productAmount,
    discounts: orderDiscount || [],
    damageWaiver: damageWaiverReturn,
    fees: orderFee || [],
    taxRate: taxRate
      ? {
          percentage: taxRate,
        }
      : null,
    totalPaid: paymentsMadeAmount,
  });
};

export const createOrder = async (
  accountId: number,
  organizationId: string,
  addressId: number,
  setupSurfaceId: number,
  products: NamedProductChargeInput[],
  discountInput: OrderDiscountInput[],
  fee: OrderFeeInput[],
  damageWaiver: { enabled: boolean; percentage: number },
  taxRate: number | null,
  startTime: Date,
  endTime: Date,
  customerId: string,
  customerNotes: string | undefined,
  internalNotes: string | undefined,
  createContract: boolean,
  state: OrderState = "ACTIVE",
): Promise<{ error?: string; success?: SuccessProps }> => {
  let productAmount = 0;
  products.forEach((product: ProductChargeInput) => {
    const price = product.pricePaid || 0;
    productAmount += price * product.quantity;
  });

  let damageWaiverReturn: RateBasedPaymentItemPercentage | null = null;
  if (damageWaiver.enabled && damageWaiver.percentage) {
    damageWaiverReturn = {
      percentage: damageWaiver.percentage,
    };
  }

  const paymentInfo = getPaymentInfo({
    baseTotal: productAmount,
    discounts: discountInput,
    damageWaiver: damageWaiverReturn,
    fees: fee,
    taxRate:
      taxRate === null
        ? null
        : {
            percentage: taxRate,
          },
    totalPaid: 0,
  });
  const {
    baseTotal,
    discounts,
    damageWaiver: damageWaiverAmount,
    fees,
    tax,
    totalPaid,
    finalTotal: totalAmount,
    displayItems,
  } = paymentInfo;

  const order = await db.order.create({
    data: {
      setupSurfaceId: setupSurfaceId,
      accountId: accountId,
      organizationId: organizationId,
      customerId: customerId,
      startTime: startTime,
      endTime: endTime,
      customerNotes: customerNotes,
      internalNotes: internalNotes,
      state: state,

      baseTotal: baseTotal,
      damageWaiverRate: damageWaiverAmount
        ? damageWaiverAmount.percentage
        : null,
      damageWaiverAmount: damageWaiverAmount ? damageWaiverAmount.amount : null,
      taxRate: tax ? tax.percentage : null,
      taxAmount: tax ? tax.amount : null,
      taxExempt: taxRate === null,
      finalTotal: totalAmount,
      totalPaid: totalPaid,
      eventAddressId: addressId,
    },
  });

  if (discounts.length > 0) {
    try {
      await db.orderDiscount.createMany({
        data: discounts.map((discount) => {
          return {
            orderId: order.id,
            amount: discount.amount,
            chargeType:
              discount.chargeType === "PERCENTAGE"
                ? ChargeType.PERCENTAGE
                : ChargeType.DOLLAR,
            type: discount.type,
            couponId:
              discount.type === "COUPON" ? discount.entityId : undefined,
          };
        }),
      });
    } catch (ex) {
      console.warn(ex);
      captureException(ex);
    }
  }

  if (fees.length > 0) {
    try {
      await db.orderFee.createMany({
        data: fees.map((fee) => ({
          orderId: order.id,
          amount: fee.amount,
          type: fee.type,
          name: fee.name,
          taxable: fee.taxable,
        })),
      });
    } catch (e) {
      console.warn(e);
      captureException(e);
    }
  }

  try {
    const productCreate = await db.orderProduct.createMany({
      data: products.map((product) => {
        return {
          orderId: order.id,
          productId: product.productId,
          quantity: product.quantity,
          pricePaid: product.pricePaid,
        };
      }),
    });
    console.log(productCreate.count, " created!");
  } catch (e) {
    console.warn(e);
    captureException(e);
    return { error: "Failed to create order products" };
  }

  let emailProps: ReceiptEmailProps | undefined = undefined;
  if (createContract) {
    const contract = await db.contract.create({
      data: {
        orderId: order.id,
        customerId: customerId,
      },
    });

    const account = await getCachedAccountFromId(accountId);

    if (!account) {
      return { error: "Account not found" };
    }

    const contractSharedItems = {
      rentalStartDate: order.startTime,
      rentalEndDate: order.endTime,
      ...paymentInfo,
    };
    emailProps = {
      newOrder: true,
      accountMinimumDeposit: account.minimumOrderPaymentPercentage,
      orderId: order.id.toString(),
      accountName: account.name,
      accountLogo: await getAccountLogo(order.accountId),
      contractItems: orderContractItems(
        products,
        products.map((product) => ({
          id: product.productId,
          name: product.productName,
        })),
      ),
      paidItems: [],
      contractSharedItems: contractSharedItems,
      contractLink: `https://${
        account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
      }/post-order/contract/${order.id}/${contract.id}`,
      paymentLink: `https://${
        account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
      }/post-order/orders/${order.id}/${order.customerId}`,
      accountTimezone: account.businessTimezone,
    };
  }

  return { success: { emailProps, order, displayItems } };
};

export const orderContractItems = (
  orderProduct: { productId: number; quantity: number; pricePaid: number }[],
  products: { id: number; name: string }[],
): OrderContractItems[] => {
  const contractItems: OrderContractItems[] = [];
  orderProduct.forEach((orderProduct) => {
    contractItems.push({
      name:
        products.find((product) => product.id === orderProduct.productId)
          ?.name || "",
      quantity: orderProduct.quantity,
      pricePerUnit: getCurrencyString(orderProduct.pricePaid),
      total: getCurrencyString(orderProduct.pricePaid * orderProduct.quantity),
    });
  });

  return contractItems;
};

export const orderPaidItems = (
  orderPayments: {
    method: string;
    amount: number;
    methodId: string;
    tip: number | null;
    refundAmount?: number | null;
  }[],
): OrderPaidItems[] => {
  const paidItems: OrderPaidItems[] = [];
  orderPayments.forEach((orderPayment) => {
    paidItems.push({
      method: orderPayment.method,
      amount: orderPayment.amount,
      methodId: orderPayment.methodId,
    });
    if (orderPayment.tip !== null) {
      paidItems.push({
        method: "Tip",
        amount: orderPayment.tip,
        methodId: orderPayment.methodId,
      });
    }
    if (orderPayment.refundAmount) {
      paidItems.push({
        method: "Refund",
        amount: orderPayment.refundAmount,
        methodId: orderPayment.methodId,
      });
    }
  });
  return paidItems;
};

export const orderInfoToPaymentInfo = (
  order: Order,
  fees: OrderFeeInput[],
  discounts: {
    amount: number;
    couponId: number | null;
    saleId: number | null;
    chargeType: ChargeType;
    type: DiscountType;
  }[],
): PaymentInfo => {
  const discountItems: OrderDiscountInput[] = discounts?.map((discount) => {
    if (discount.chargeType === "PERCENTAGE") {
      return {
        type: discount.type,
        chargeType: "PERCENTAGE",
        amount: discount.amount,
        entityId: discount.couponId ?? discount.saleId ?? -1,
      };
    }

    return {
      type: discount.type,
      chargeType: "DOLLAR",
      amount: discount.amount,
      entityId: discount.couponId ?? discount.saleId ?? -1,
    };
  });

  return getPaymentInfo({
    baseTotal: order.baseTotal,
    discounts: discountItems || [],
    damageWaiver: order.damageWaiverRate
      ? {
          percentage: order.damageWaiverRate,
        }
      : null,
    fees: fees,
    taxRate: order.taxRate
      ? {
          percentage: order.taxRate,
        }
      : null,
    totalPaid: order.totalPaid,
  });
};

export const orderInfoToSharedItems = (
  order: Order,
  fees: OrderFeeInput[],
  discounts: {
    amount: number;
    couponId: number | null;
    saleId: number | null;
    chargeType: ChargeType;
    type: DiscountType;
  }[],
): ContractSharedItems => {
  return {
    rentalStartDate: order.startTime,
    rentalEndDate: order.endTime,
    ...orderInfoToPaymentInfo(order, fees, discounts),
  };
};

export const getSetupSurfaceFee = async (
  accountId: number,
  setupSurfaceId: number | SetupSurface,
  lineItems: number,
): Promise<number | undefined> => {
  let surface: SetupSurface | null = null;
  if (typeof setupSurfaceId === "number") {
    surface = await db.setupSurface.findFirst({
      where: {
        id: setupSurfaceId,
        accountId: accountId,
        archived: false,
      },
    });
  } else {
    surface = setupSurfaceId;
  }

  if (!surface) {
    return undefined;
  }

  if (surface.feeAmount === null || surface.feeAmount <= 0) {
    return undefined;
  }

  // TODO scale fee with quantity
  // if (surface.scaleFeeWithQuantity) {
  //   return surface.feeAmount * lineItems;
  // }

  return surface.feeAmount;
};

export const getTravelFee = async (
  account: Account | number,
  endingPoint: Address,
  startingPoint?: Address,
): Promise<number | undefined> => {
  // todo please cache this...
  let accountData: Account | null;
  if (typeof account === "number") {
    accountData = await db.account.findFirst({
      where: {
        id: account,
      },
    });
  } else {
    accountData = account;
  }

  if (!accountData) {
    return undefined;
  }

  if (accountData.travelFeePerMile === 0) {
    return 0;
  }

  let startingLocation: Address | undefined = startingPoint;
  let originLocation: string | [number, number] | undefined;
  let destinationLocation: string | [number, number] | undefined;
  if (!startingPoint) {
    if (!accountData.billingAddressId) {
      return undefined;
    }
    const accountAddress = await db.address.findFirst({
      where: {
        id: accountData.billingAddressId,
      },
    });
    if (!accountAddress) {
      return undefined;
    }
    startingLocation = accountAddress;
  }

  if (!startingLocation) {
    return undefined;
  }

  if (startingLocation?.id === endingPoint.id) {
    return 0;
  }

  if (startingLocation?.latitude && startingLocation?.longitude) {
    originLocation = [startingLocation.latitude, startingLocation.longitude];
  } else {
    originLocation = getFullAddress(startingLocation).replace(" ", "+");
  }

  if (endingPoint.latitude && endingPoint.longitude) {
    destinationLocation = [endingPoint.latitude, endingPoint.longitude];
  } else {
    destinationLocation = getFullAddress(endingPoint).replace(" ", "+");
  }

  if (!originLocation || !destinationLocation) {
    return undefined;
  }

  const milesBetween = await getMilesBetweenLocations(
    originLocation,
    destinationLocation,
  );
  if (milesBetween === undefined) {
    captureEvent({
      message: "Failed to get miles between locations",
      extra: {
        originLocation,
        destinationLocation,
      },
    });
    return undefined;
  }
  const { miles, originLatLang, destinationLatLang } = milesBetween;
  const promises = [];
  if (originLatLang && !Array.isArray(originLocation)) {
    promises.push(
      db.address.update({
        where: {
          id: startingLocation?.id,
        },
        data: {
          latitude: originLatLang[0],
          longitude: originLatLang[1],
        },
      }),
    );
  }
  if (destinationLatLang && !Array.isArray(destinationLocation)) {
    promises.push(
      db.address.update({
        where: {
          id: endingPoint.id,
        },
        data: {
          latitude: destinationLatLang[0],
          longitude: destinationLatLang[1],
        },
      }),
    );
  }
  if (promises.length > 0) {
    await Promise.all(promises);
  }
  const milesBetweenRounded = Math.ceil(miles);

  const freeTravelRadius = accountData.freeTravelRadius;
  const travelFeePerMile = accountData.travelFeePerMile;

  if (!freeTravelRadius || !travelFeePerMile) {
    return undefined;
  }

  if (milesBetweenRounded <= freeTravelRadius) {
    // actually free
    return 0;
  }

  const milesOver = milesBetweenRounded - freeTravelRadius;
  return milesOver * travelFeePerMile;
};

export const getActiveOrders = async (
  organizationId: string,
  startTime: Date,
  endTime: Date,
  extraWhere?: object,
  extraInclude?: object,
) => {
  return db.order.findMany({
    where: {
      AND: {
        ...extraWhere,
        organizationId,
        state: OrderState.ACTIVE,
      },
      OR: [
        {
          startTime: {
            lte: startTime,
          },
          endTime: {
            gte: startTime,
          },
        },
        {
          startTime: {
            gte: startTime,
            lte: endTime,
          },
          endTime: {
            gte: startTime,
          },
        },
      ],
    },
    include: extraInclude,
  });
};

export const getActiveOrCompleteOrders = async (
  organizationId: string,
  startTime: Date,
  endTime: Date,
  extraWhere?: object,
  extraInclude?: object,
) => {
  return db.order.findMany({
    where: {
      AND: {
        ...extraWhere,
        organizationId,
      },
      OR: [
        {
          startTime: {
            lte: startTime,
          },
          endTime: {
            gte: startTime,
          },
        },
        {
          startTime: {
            gte: startTime,
            lte: endTime,
          },
          endTime: {
            gte: startTime,
          },
        },
      ],
      NOT: [
        {
          state: OrderState.CANCELLED,
        },
        {
          state: OrderState.QUOTE,
        },
        {
          state: OrderState.ABANDONED_QUOTE,
        },
      ],
    },
    include: extraInclude,
  });
};

type ProductAvailability = {
  available: number[];
  taken: number[];
};

export type ProductWithBuffer = {
  productId: number;
  quantity: number;
  beforeRentalBufferMinutes: number;
  afterRentalBufferMinutes: number;
};

export const areProductsAvailable = async (
  organizationId: string,
  products: ProductWithBuffer[],
  startTime: Date,
  endTime: Date,
): Promise<ProductAvailability> => {
  const availability = await getQuantityOfProductsAvailableBulk(
    organizationId,
    products,
    startTime,
    endTime,
    30 * 60, // 30 minutes
  );

  const available: number[] = [];
  const taken: number[] = [];

  products.forEach((product) => {
    const isAvailable = availability.every((interval) => {
      const productInfo = interval.productInfo.find(
        (info) => info.productId === product.productId,
      );
      const quantity = productInfo?.quantity ?? product.quantity;
      return quantity >= product.quantity;
    });

    if (isAvailable) {
      available.push(product.productId);
    } else {
      taken.push(product.productId);
    }
  });
  return { available, taken };
};

/**
 * Gets the quantity of products available at any given time.
 *
 * @param organizationId The organization to check
 * @param productWithQuantityAndBuffer The products that we are checking against, the quantity is how many products are being requested
 * @param startDate The start date of the availability
 * @param endDate The end date of the availability, everything from the start date to the end date is checked
 * @param intervalSeconds The frequency of which we check for availability in that given period.
 *                        The more frequent the interval, the more accurate the availability will be but the slower the response time.
 *                        If you request a realy long interval then there is a chance that an order will be missed because it started after the last check and ended before the next check.
 *                        If you're wanting to look at a daily availability I would suggest no more than 1 hour intervals.
 *                        If you're wanting to check a high level monthly availability then I would suggest no more than 8 hour intervals.
 */
export const getQuantityOfProductsAvailableBulk = async (
  organizationId: string,
  productWithQuantityAndBuffer: ProductWithBuffer[],
  startDate: Date,
  endDate: Date,
  intervalSeconds: number,
): Promise<
  {
    start: Date;
    end: Date;
    productInfo: {
      productId: number;
      quantity: number;
    }[];
  }[]
> => {
  const productIds = productWithQuantityAndBuffer.map(
    (product) => product.productId,
  );
  const orders = await getActiveOrders(
    organizationId,
    startDate,
    endDate,
    {
      OrderProduct: {
        some: {
          productId: {
            in: productIds,
          },
        },
      },
    },
    {
      OrderProduct: {
        select: {
          productId: true,
          quantity: true,
        },
      },
    },
  );

  const dateAvailability: {
    start: Date;
    end: Date;
    productInfo: ProductWithBuffer[];
  }[] = [];
  const datePointer = new Date(startDate);

  datePointer.setUTCMinutes(0);
  datePointer.setUTCSeconds(0);
  datePointer.setUTCMilliseconds(0);
  while (datePointer < endDate) {
    const dateRange = {
      start: new Date(datePointer),
      end: new Date(datePointer.getTime() + intervalSeconds * 1000),
      productInfo: productWithQuantityAndBuffer.map((product) => {
        return {
          ...product,
        };
      }),
    };
    dateAvailability.push(dateRange);
    datePointer.setTime(datePointer.getTime() + intervalSeconds * 1000);
  }

  dateAvailability.forEach((dateRange) => {
    orders.forEach((order: any) => {
      for (const orderProduct of order.OrderProduct) {
        const productInfo = dateRange.productInfo.find(
          (product) => product.productId === orderProduct.productId,
        );
        if (!productInfo) {
          continue;
        }
        if (
          !doDateRangesOverlap(
            dateRange.start,
            dateRange.end,
            new Date(
              order.startTime.getTime() -
                productInfo.beforeRentalBufferMinutes * 60 * 1000,
            ),
            new Date(
              order.endTime.getTime() +
                productInfo.afterRentalBufferMinutes * 60 * 1000,
            ),
          )
        ) {
          continue;
        }

        if (productInfo) {
          productInfo.quantity -= orderProduct.quantity;
        }
      }
    });
  });

  // reduce all the times when the product info is the same
  const relevantDateAvailability = dateAvailability.filter(
    (dateRange) => dateRange.productInfo !== productWithQuantityAndBuffer,
  );

  // finally remove the product info that is the same as the original quantity
  return relevantDateAvailability.map((dateRange) => {
    return {
      ...dateRange,
      productInfo: dateRange.productInfo
        .filter(
          (prod) =>
            prod.quantity !==
            productWithQuantityAndBuffer.find(
              (pro) => pro.productId === prod.productId,
            )?.quantity,
        )
        .map((prod) => {
          return {
            productId: prod.productId,
            quantity: prod.quantity,
          };
        }),
    };
  });
};

export const getQuantityOfProductsAvailable = async (
  organizationId: string,
  productId: number,
  startingQuantity: number,
  startDate: Date,
  endDate: Date,
  intervalSeconds: number,
  beforeRentalBufferMinutes: number,
  afterRentalBufferMinutes: number,
): Promise<
  {
    start: Date;
    end: Date;
    quantity: number;
  }[]
> => {
  const bulkAvailability = await getQuantityOfProductsAvailableBulk(
    organizationId,
    [
      {
        productId,
        quantity: startingQuantity,
        beforeRentalBufferMinutes: beforeRentalBufferMinutes,
        afterRentalBufferMinutes: afterRentalBufferMinutes,
      },
    ],
    startDate,
    endDate,
    intervalSeconds,
  );

  return bulkAvailability
    .filter((interval) => interval.productInfo.length === 1)
    .map((interval) => ({
      start: interval.start,
      end: interval.end,
      quantity: interval.productInfo[0]!.quantity,
    }));
};
