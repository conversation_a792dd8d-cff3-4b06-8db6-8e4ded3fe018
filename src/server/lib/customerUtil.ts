import { Customer } from "@prisma/client";
import { findOrInsertAddress } from "./location/util";
import { db } from "../db";
import { formatStandardPhone } from "~/server/lib/phone";

interface onboardingCustomerSchema {
  // TODO: Make this actually use the ERS fields if theyre present
  // ERS Fields
  mobile_phone?: string;
  work_phone?: string;
  phone?: string;
  parent_email?: string;
  secondary_email?: string;
  billing_address?: string;
  billing_city?: string;
  billing_state?: string;
  billing_zip?: string;
  firstname?: string;
  lastname?: string;
  ///////////////////

  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  internalNotes: string;
  address: {
    line1: string;
    line2: string | null;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  // Add other fields as needed, with or without the `?` to indicate optional fields
}

interface CustomerSearch {
  email?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  accountId: number;
}

async function doesEmailExist(
  email: string,
  accountId: number,
): Promise<boolean> {
  const customer = await db.customer.findFirst({
    where: {
      email: email,
      accountId: accountId,
    },
  });

  return customer !== null;
}

export async function findOneCustomer(
  searchCriteria: CustomerSearch,
): Promise<Customer | null> {
  // console.log("Search Criteria", searchCriteria);
  if (searchCriteria.email) {
    // Search by email if it exists
    return await db.customer.findFirst({
      where: {
        email: searchCriteria.email,
        accountId: searchCriteria.accountId,
      },
    });
  } else if (
    searchCriteria.firstName &&
    searchCriteria.lastName &&
    searchCriteria.phoneNumber
  ) {
    // Search by first name, last name, and phone number if email does not exist
    return await db.customer.findFirst({
      where: {
        firstName: searchCriteria.firstName,
        lastName: searchCriteria.lastName,
        phoneNumber: searchCriteria.phoneNumber,
        accountId: searchCriteria.accountId,
      },
    });
  }
  // If neither email nor all other fields are provided, return null
  return null;
}

export async function digestCustomerData(
  accountId: number,
  customers: onboardingCustomerSchema[],
): Promise<any> {
  const customersToCreate = [];
  const seenEmails = new Set();

  for (const customer1 of customers.filter((customer) => {
    return customer.email !== "";
  })) {
    const address = await findOrInsertAddress({
      line1: customer1.address.line1,
      line2: customer1.address.line2 === "" ? null : customer1.address.line2,
      city: customer1.address.city,
      state: customer1.address.state,
      postalCode: customer1.address.postalCode,
      country: "US",
    });
    if (!address) {
      console.log(
        `Address not found for customer: ${customer1.firstName} ${customer1.lastName}`,
      );
      continue;
    }
    if (!customer1.firstName || !customer1.lastName) {
      console.log(
        `Name not found for customer: ${customer1.firstName} ${customer1.lastName}`,
      );
      continue;
    }
    const email = customer1.email //||
      // customer1.parent_email ||
      // customer1.secondary_email
      .toLowerCase();
    if (!email) {
      console.log(
        `Email not found for customer: ${customer1.firstName} ${customer1.lastName}`,
      );
      continue;
    } else if (seenEmails.has(email)) {
      console.log(
        `Duplicate email found for customer: ${customer1.firstName} ${customer1.lastName}`,
      );
      continue;
    } else if (await (async () => doesEmailExist(email, accountId))()) {
      console.log(
        `Email already exists\nEmail: ${email}\nFirst Name: ${customer1.firstName}\nLast Name: ${customer1.lastName}\nAccount ID: ${accountId}`,
      );
      continue;
    }
    seenEmails.add(email);

    const phone = formatStandardPhone(customer1.phoneNumber);

    customersToCreate.push({
      accountId: accountId,
      addressId: address.id,
      firstName: customer1.firstName,
      lastName: customer1.lastName,
      email: email,
      phoneNumber: phone,
      internalNotes: customer1.internalNotes,
    });
  }
  return customersToCreate;
}
