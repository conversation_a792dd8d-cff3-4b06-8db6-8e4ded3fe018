import { Account } from ".prisma/client";
import { Address } from "@prisma/client";
import { AddressType } from "~/server/lib/location/types";
import { db } from "~/server/db";
import { captureException } from "@sentry/core";

const SHARED_SECRET = "fb17fd92-99c2-41e0-841b-2b2c95483d1a";

export const getTaxRate = async (
  account: Account | number,
  address: Address | AddressType,
): Promise<number | null> => {
  let accountData: Account | null;
  if (typeof account === "number") {
    accountData = await db.account.findFirst({
      where: {
        id: account,
      },
    });
  } else {
    accountData = account;
  }

  if (!accountData) {
    return null;
  }

  if (!address.state || !address.country) {
    return null;
  }

  const staging =
    process.env.NODE_ENV !== "production" ||
    process.env.NEXT_PUBLIC_SELF_URL?.includes("staging");

  const request = await fetch("https://prp-tax.ib4est123.workers.dev/taxRate", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${SHARED_SECRET}`,
    },
    body: JSON.stringify({
      accountId: accountData.id,
      stripeAccountId:
        staging || accountData.name === "CentralArkansasInflatables"
          ? null
          : accountData.stripeAccountId,
      isCai: staging || accountData.name === "CentralArkansasInflatables",
      address: {
        line1: address.line1 ?? undefined,
        line2: address.line2 ?? undefined,
        city: address.city ?? undefined,
        country: address.country,
        postalCode: address.postalCode ?? undefined,
        state: address.state,
      },
    }),
  });

  if (!request.ok) {
    console.log(
      `Error getting tax rate for account ${accountData.id}: ${request.status} ${request.statusText}`,
    );
    captureException(
      new Error(
        `Error getting tax rate for account ${accountData.id}: ${request.status} ${request.statusText}`,
      ),
    );
    return null;
  }

  const response = await request.json();
  if (response.error || !response.taxRate) {
    console.log(
      `Error getting tax rate for account ${accountData.id}: ${JSON.stringify(
        response,
      )}`,
    );
    captureException(
      new Error(
        `Error getting tax rate for account ${accountData.id}: ${JSON.stringify(
          response,
        )}`,
      ),
    );
    return null;
  }

  if (typeof response.taxRate !== "number") {
    console.log(
      `Invalid tax rate for account ${accountData.id}: ${response.taxRate}`,
    );
    captureException(
      new Error(
        `Invalid tax rate for account ${accountData.id}: ${response.taxRate}`,
      ),
    );
    return null;
  }

  return response.taxRate;
};
