import { Account } from "@prisma/client";
import { Stripe } from "stripe";
import { db } from "~/server/db";

export const platformStripeServer = new Stripe(
  process.env.PRP_STRIPE_PRIVATE_KEY || "",
  {
    apiVersion: "2023-10-16",
    appInfo: {
      name: "Platform",
      url: `${process.env.NEXTAUTH_URL}/`,
    },
  },
);

export const getStripeAccount = (account: Account): Stripe => {
  if (account.name === "CentralArkansasInflatables") {
    return centralArkansasInflatablesStripe;
  }
  if (account.stripeAccountId) {
    return new Stripe(process.env.PRP_STRIPE_PRIVATE_KEY || "", {
      apiVersion: "2023-10-16",
      appInfo: {
        name: account.name,
        url: `${process.env.NEXTAUTH_URL}/`,
      },
      stripeAccount: account.stripeAccountId,
    });
  }
  throw new Error(
    "Attempting to grab stripe account for account without stripe account id",
  );
};

export const centralArkansasInflatablesStripe = new Stripe(
  process.env.STRIPE_SECRET_KEY || "",
  {
    // https://github.com/stripe/stripe-node#configuration
    apiVersion: "2023-10-16",
    appInfo: {
      name: "Platform",
      url: `${process.env.NEXTAUTH_URL}/`,
    },
  },
);

export const findOrCreateStripeCustomerPlatform = async (
  account: Account,
): Promise<Stripe.Customer> => {
  let stripeCustomer: Stripe.Customer | undefined;

  if (!account.stripeCustomerId) {
    // try and get the stripe account from querying stripe by the account id
    const stripeCustomerRetry = await platformStripeServer.customers.search({
      query: `metadata['accountId']:'${account.id}'`,
    });

    if (stripeCustomerRetry.data.length !== 0) {
      const foundStripeCustomer = stripeCustomerRetry.data[0];
      if (foundStripeCustomer?.metadata?.accountId === account.id.toString()) {
        await db.account.update({
          where: {
            id: account.id,
          },
          data: {
            stripeCustomerId: foundStripeCustomer.id,
          },
        });
        stripeCustomer = foundStripeCustomer;
      }
    }
  } else {
    const foundCustomer = await platformStripeServer.customers.retrieve(
      account.stripeCustomerId,
    );
    if (foundCustomer && !foundCustomer.deleted) {
      stripeCustomer = foundCustomer;
    }
  }

  // no luck finding the stripe account, create a new one
  if (!stripeCustomer) {
    stripeCustomer = await platformStripeServer.customers.create({
      name: account.name,
      email: account.businessEmail || undefined,
      metadata: {
        accountId: account.id.toString(),
      },
    });
    await db.account.update({
      where: {
        id: account.id,
      },
      data: {
        stripeCustomerId: stripeCustomer.id,
      },
    });
  }
  return stripeCustomer;
};

export const findStripeCustomer = async (
  account: Account,
  email: string,
  customerId: string,
): Promise<Stripe.Customer | undefined> => {
  if (!account.stripeCustomerId) {
    return;
  }
  let customerStripe: Stripe.Customer | undefined;
  const stripeCustomer = await getStripeAccount(account).customers.search({
    query: `metadata['customerId']:'${customerId}' or email:"${email.toLowerCase()}"`,
    limit: 1,
  });

  if (stripeCustomer.data.length > 0) {
    customerStripe = stripeCustomer.data[0];
  }

  return customerStripe;
};

export const getStripeDisplayPaymentMethod = (
  paymentMethod: Stripe.PaymentMethod,
): { method: string; methodId: string } => {
  const method = paymentMethod.type.replace("_", " ");
  let methodId = method;
  if (paymentMethod.card) {
    methodId = `${paymentMethod.card.brand} ${paymentMethod.card.last4}`;
  }
  if (paymentMethod.cashapp) {
    methodId = paymentMethod.cashapp.cashtag ?? "";
  }
  if (paymentMethod.link) {
    methodId = "Link Payment";
  }
  return { method, methodId };
};
