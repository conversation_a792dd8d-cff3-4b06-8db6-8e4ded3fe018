export type ersOrder = {
  success: boolean;
  order_id: string;
  start_datetime: string;
  end_datetime: string;
  status: string;
  total: string;
  paid: string;
  customer_comments: string;
  internal_order_notes: string;
  driver: string;
  driverid: string;
  invoice_group: string;
  order_importid: string;
  created_by: string;
  address: string;
  city_name: string;
  state_name: string;
  zip: string;
  order_status: string;
  customer: {
    company_name: string;
    firstname: string;
    lastname: string;
    email: string;
    phone: string;
    work_phone: string;
    mobile_phone: string;
    internal_customer_notes: string;
    id: string;
  };
  contents: {
    item_name: string;
    item_id: string;
    picture: string;
    unit_cost: string;
    quantity: string;
    cost: string;
    start_datetime: string;
    end_datetime: string;
    service_addons: {
      name: string;
      id: string;
      set_price: string;
      qtytype: string;
    }[];
    serial_info: any[];
    serial_present: boolean;
    serial_status: string;
    available_inventory: any[];
    inventory_history: any[];
    inventory_status: any[];
    is_sku: boolean;
    snscount: number;
    swapout_last_used: string;
    inventory_info: {
      item_id: string;
      quantity: string;
      serial_info: any[];
      serial_present: boolean;
      serial_status: string;
      item_name: string;
      available_inventory: any[];
      inventory_history: any[];
      inventory_status: any[];
      is_sku: boolean;
      snscount: number;
      swapout_last_used: string;
    }[];
  }[];
  photos: any[];
  guests: any[];
  custom_fields: {
    name: string;
    type: string;
    description: string;
    value: any[];
    display: string;
    scope: string;
    taxable: string;
    id: string;
  }[];
  custom_checklist: {
    name: string;
    content: string;
    color: string;
    checked: string;
    display: string;
    id: string;
  }[];
  options: {
    id: string;
    name: string;
    value: string;
  }[];
  weather: {
    dt: number;
    main: {
      temp: number;
      feels_like: number;
      temp_min: number;
      temp_max: number;
      pressure: number;
      sea_level: number;
      grnd_level: number;
      humidity: number;
      temp_kf: number;
    };
    weather: {
      id: number;
      main: string;
      description: string;
      icon: string;
    }[];
    clouds: {
      all: number;
    };
    wind: {
      speed: number;
      deg: number;
      gust: number;
    };
    visibility: number;
    pop: number;
    sys: {
      pod: string;
    };
    dt_txt: string;
  };
  lastfour: string;
  payments: any[];
}


export type ersOrderContract = {
  success: boolean
  content: string;
  message: string;
  status: string;
  elapsed: string;
}

export type ersExpandedOrderContract = {
  success: boolean
  content: string;
  message: string;
  status: string;
  elapsed: string;
  customerId: string;
  orderId: string;
}

export type ersCustomer = {
  company_id: string;
  org_id: string;
  created_source: string;
  created_datetime: string;
  alternate_contact_name: string;
  account_status: string;
  parentid: string;
  secondary_email: string;
  primeid: string;
  signature_datetime: string;
  parent_email: string;
  parent_phone: string;
  parent_name: string;
  birth_date: string;
  signature: string;
  group_order_id: string;
  poslavu_id: string;
  customer_notes: string;
  status: string;
  groupid: string;
  importid: string;
  reference: string;
  billing_zip: string;
  billing_state: string;
  billing_city: string;
  billing_address: string;
  fax_phone: string;
  mobile_phone: string;
  work_phone: string;
  phone: string;
  company_name: string;
  email: string;
  lastname: string;
  firstname: string;
  id: string;
  _order: string;
  crm_customer_tags: null | string[]; // This allows for either null or an array of strings
  reference_name: string;
}

export type ersCustomerResponse = {
  success: boolean;
  customers: ersCustomer[];
  status: string;
  elapsed: string;
}

export type ersEmailTemplate = {
  id: string;
  name: string;
  content: string;
  subject: string;
}

export type ersSchedule = {
  row_count: number;
  rows: {
    name: string;
    mode: string;
    properties: {
      sunday?: { start: string; end: string };
      monday?: { start: string; end: string };
      tuesday?: { start: string; end: string };
      wednesday?: { start: string; end: string };
      thursday?: { start: string; end: string };
      friday?: { start: string; end: string };
      saturday?: { start: string; end: string };
      slotsize?: string;
      earliest?: string;
      latest?: string;
    };
    collect_guest_info: string;
    time_in_time: string;
    printers: string;
    id: string;
    alternate_profiles: string;
  }[];
  status: string;
  elapsed: string;
}

export type ersProduct = {
  //endpoint also has status, count, and elapsed fields
  hide_from_routing: string;
  exclude_from_top_items: string;
  additional_media: string;
  takedown_time: string;
  setup_time: string;
  path: string;
  revenue_account: string;
  service_addons: string;
  award_points: string;
  reduce_qty: string;
  serial_number: string;
  year_made: string;
  manufacturer: string;
  extra_categoryid: string;
  receipt_reminders: string;
  purchase_price: string;
  cogs: string;
  display_name: string;
  setupfee: string;
  subcontracted: string;
  _deleted: string;
  tic: string;
  availability_rules: string;
  required_addons: string;
  meta_info: string;
  shared_cost: string;
  surface_load_options: string;
  taxable: string;
  customer_display: string;
  shareid: string;
  type: string;
  payrate: string;
  notes: string;
  suggested_addons: string;
  reminders: string;
  age_group: string;
  monitors: string;
  outlets: string;
  actual_size: string;
  setup_area: string;
  importid: string;
  schedule_profileid: string;
  date_acq: string;
  sku: string;
  contents: string;
  picture: string;
  cost: string;
  dimensions: string;
  categoryid: string;
  qty: string;
  description: string;
  name: string;
  id: string;
};

export type ersProductResponse = {
  status: "Success";
  count: number;
  items: ersProduct[];
  elapsed: string;
};
export type ersCategory = {
  name: string;
  picture: string;
  display: "yes" | "admin_only";
  parent: string;
  quickitems: "Yes" | "No";
  description: string;
  id: string;
  display_name: string;
  link: string;
  sort_items?: string[]; // Including the missing field
};

export type ersCategoryResponse = {
  row_count: number;
  rows: ersCategory[];
  status: string;
  elapsed: string;
};

export type ersUser = {
  username: string;
  name: string;
  email: string;
  phone: string;
  groupid: string;
  eula: string;
}

export type ersWebpage = {
  id: string;
  name: string;
  path: string;
};

export type ersWebpageResponse = {
  success: boolean;
  webpages: ersWebpage[];
  status: string;
  elapsed: string;
};

export type ersSingleWebpageResponse = {
  success: boolean;
  webpage: ersSingleWebpage;
  status: string;
  elapsed: string;
};

export type ersSingleWebpage = {
  rsp_page_type: string;
  nav_type: string;
  page_goal: string;
  in_rotation: string;
  parent_id: string;
  version_name: string;
  icon: string;
  rsp_page_mode: string;
  rsp_editor_content: string;
  rsp_content: string;
  mobile_content: string;
  meta_description: string;
  meta_keywords: string;
  html_title: string;
  importid: string;
  use_template: string;
  alias: string;
  content: string;
  path: string;
  name: string;
  id: string;
  _order: string;
};