/**
 * Utility functions for sending notifications to Slack
 */

/**
 * Send a message to a Slack channel using a webhook URL
 * @param webhookUrl The Slack webhook URL
 * @param message The message to send
 * @param channel Optional channel override
 * @returns Promise that resolves when the message is sent
 */
export async function sendSlackMessage(
  webhookUrl: string,
  message: string,
  channel?: string
): Promise<Response> {
  const payload = {
    text: message,
    ...(channel && { channel }),
  };

  return fetch(webhookUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });
}

/**
 * Send a notification about a new signup to Slack
 * @param businessName The name of the business that signed up
 * @param email The email of the user who signed up
 * @param referrer How the user heard about the platform (optional)
 * @returns Promise that resolves when the notification is sent
 */
export async function sendSignupNotification(
  businessName: string,
  email: string,
  referrer?: string
): Promise<void> {
  const webhookUrl = process.env.SLACK_SIGNUP_WEBHOOK_URL;

  if (!webhookUrl) {
    console.warn("SLACK_SIGNUP_WEBHOOK_URL not set, skipping Slack notification");
    return;
  }

  // Format the date in ISO format with UTC time
  const now = new Date();
  const formattedTime = now.toISOString();

  // Also include a human-readable time in US format
  const readableTime = now.toLocaleString('en-US', {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short'
  });

  const message = `🎉 *New Signup!* 🎉\n\n*Business:* ${businessName}\n*Email:* ${email}${
    referrer ? `\n*Referrer:* ${referrer}` : ""
  }\n*Time (ET):* ${readableTime}\n*Time (ISO):* ${formattedTime}`;

  try {
    const response = await sendSlackMessage(webhookUrl, message);

    if (!response.ok) {
      throw new Error(`Failed to send Slack notification: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error sending Slack notification:", error);
    // Don't throw the error - we don't want to fail the signup process if Slack notification fails
  }
}
