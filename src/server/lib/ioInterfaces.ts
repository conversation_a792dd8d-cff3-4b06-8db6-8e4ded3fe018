export type ioCategoryResponse = {
  offset: number;
  limit: number;
  first: string;
  next: string;
  previous: string;
  items: ioCategory[];
  locations_lastmodified: number;
  paymenttypes_lastmodified: number;
  categories_lastmodified: number;
  deliverymethods_lastmodified: number;
  surfaces_lastmodified: number;
  rentals_lastmodified: string;
  statuses_lastmodified: number;
  positions_lastmodified: number;
  customertypes_lastmodified: number;
  referrals_lastmodified: number;
  filters_lastmodified: number;
  emailtemplates_lastmodified: string;
  request_time: number;
};

export type ioCategory = {
  id: string;
  href: string;
  name: string;
  imageloc: string;
  imagelocbig: string;
  description: string;
  order: string;
};

export type ioProductResponse = {
  offset: number;
  limit: number;
  count: string;
  first: string;
  next: string;
  previous: string;
  items: ioProduct[];
  locations_lastmodified: number;
  paymenttypes_lastmodified: number;
  categories_lastmodified: number;
  deliverymethods_lastmodified: number;
  surfaces_lastmodified: number;
  rentals_lastmodified: string;
  statuses_lastmodified: number;
  positions_lastmodified: number;
  customertypes_lastmodified: number;
  referrals_lastmodified: number;
  filters_lastmodified: number;
  emailtemplates_lastmodified: string;
  request_time: number;
};

export type ioProduct = {
  id: string;
  createtime: string;
  modifiedtime: string;
  parentid: string;
  isoption: string;
  ridename: string;
  rideorder: string;
  rideactive: string;
  quantity: string;
  description: string;
  imagelocbig: string;
  imageloc: string;
  weight: string;
  electric: string;
  dimensions: string;
  footprint: string;
  ties: string;
  tieweight: string;
  staff: string;
  volunteer: string;
  setupstaff: string;
  setuptime: string;
  tdowntime: string;
  pl: string;
  category: string;
  serialnumber: string;
  contract: string;
  operateinst: string;
  inspectionlist: string;
  ohioitinerary: string;
  qtytype: string;
  increment: string;
  tic: string;
  inputtype: string;
  notes: string;
  separateunits: string;
  structure: string;
  appendname: string;
  contractorpay: string;
  manufacturer: string;
  modelnumber: string;
  reqdepamt: string;
  reqdeptype: string;
  showcontract: string;
  showemail: string;
  showpackinglist: string;
  showquoteform: string;
  showquoteconfirm: string;
  recoverytime: string;
  qbaccount: string;
  custrestrict: string;
  qboupdatetime: string;
  qboid: string;
  imageloc2: string;
  imagelocbig2: string;
  imageloc3: string;
  imagelocbig3: string;
  imageloc4: string;
  imagelocbig4: string;
  vendorid: string;
  qboname: string;
  shared: string;
  sharedid: string;
  ispackage: string;
  accessorylimit: string;
  accessorydd: string;
  separatevehicle: string;
  alertwind: string;
  alertgust: string;
  replacementcost: string;
  separateonpl: string;
  giftcardvalue: string;
  allowdiscounts: string;
  vendorcompare: string;
  operationcost: string;
  showleadspage: string;
  plbehavior: string;
  ismanufacturer: string;
  isheading: string;
  aataxable: string;
  tentlength: string;
  istent: string;
  tentsection: string;
  tentwidth: string;
  returnprice: string;
  splitcontractorpay: string;
  deliveryprice: string;
  contractordeliverypay: string;
  contractorreturnpay: string;
  options: string[];
  option_required: Record<string, string>;
  option_linkqty: Record<string, string>;
  option_overrideprice: Record<string, string>;
  option_description: Record<string, string>;
  option_imageloc: Record<string, string>;
  option_imagelocbig: Record<string, string>;
  category_name: string;
  locations: string[];
  location_names: string[];
  lcategories: string[];
  qpages: string[];
  qcategories: string[];
  allcats: Record<string, string>;
  upsells: string[];
  wppages: ioProductWPPage[];
  images: Record<string, ioProductImage>;
  href: string;
  createtime_ts: string;
  createtime_utc: string;
  modifiedtime_ts: string;
  modifiedtime_utc: string;
  PackingList: string | null;
  prices: Record<string, string | null>;
};

type ioProductWPPage = {
  site: string;
  page_id: string;
  url: string;
};

type ioProductImage = {
  rentalimage_id: string;
  rentalimage_createtime: string;
  rentalimage_modifiedtime: string;
  rentalimage_itemid: string;
  rentalimage_order: string;
  rentalimage_imageloc: string;
  rentalimage_imagelocbig: string;
};

export type ioCustomer = {
  id: string;
  fromCustomerProvider: boolean;
  saveAsCopyId: string;
  createtime: string;
  modifiedtime: string;
  lastcontacttime: string;
  lastactiontime: string;
  organization: string;
  firstname: string;
  lastname: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  homephone: string;
  officephone: string;
  cellphone: string;
  fax: string;
  email: string;
  notes: string;
  customertype: string;
  locationid: string;
  isblacklisted: string;
  score: string;
  qboupdatetime: string;
  qboid: string;
  title: string;
  active: string;
  address2: string;
  qboname: string;
  qbosendinvoice: string;
  referral: string;
  oktotext: string;
  repeatcustomer: string;
  taxexemptid: string;
  tags: string[];
  contact_phone: string;
  href: string;
  createtime_ts: string;
  createtime_utc: string;
  modifiedtime_ts: string;
  modifiedtime_utc: string;
};

export type ioCustomerResponse = {
  offset: number;
  limit: number;
  first: string;
  next: string;
  previous: string;
  items: ioCustomer[];
  locations_lastmodified: number;
  paymenttypes_lastmodified: number;
  categories_lastmodified: number;
  deliverymethods_lastmodified: number;
  surfaces_lastmodified: number;
  rentals_lastmodified: string;
  statuses_lastmodified: number;
  positions_lastmodified: number;
  customertypes_lastmodified: number;
  referrals_lastmodified: number;
  filters_lastmodified: number;
  emailtemplates_lastmodified: string;
  request_time: number;
};

export type ioOrderResponse = {
    offset: number;
    limit: number;
    first: string;
    next: string;
    previous: string;
    items: ioOrder[];
};

export type ioOrder = {
    id: string;
    tz: string;
    createtime: string;
    modifiedtime: string;
    eventname: string;
    eventorganization: string;
    eventstreet: string;
    eventcity: string;
    eventstate: string;
    eventzip: string;
    eventstarttime: string;
    eventduration: string;
    deliverytype: string;
    surface: string;
    taxexempt: string;
    taxexemptid: string;
    taxcert: string;
    rentaldct: string;
    fromLeadsProvider: boolean;
    eventcountry: string;
    eventcounty: string;
    contactid: string;
    taxrate: string;
    salestax: string;
    total: string;
    taxcertid: string;
    notes: string;
    statusid: string;
    viewed: string;
    followup: string;
    conflag: string;
    fullstart: string;
    fullend: string;
    cushstart: string;
    cushend: string;
    custrequest: string;
    custreqtime: string;
    stafftosend: string;
    volsreqd: string;
    setupduration: string;
    setupwindowstart: string;
    setupwindowend: string;
    tdownduration: string;
    tdownwindowstart: string;
    tdownwindowend: string;
    contractsent: string;
    directionsfrom: string;
    ttimefrom: string;
    contractrecd: string;
    depositamount: string;
    amountpaid: number;
    balancerecd: string;
    ipaddress: string;
    numguests: string;
    reqdepamt: string;
    reqdeptype: string;
    qboupdatetime: string;
    qboid: string;
    referral: string;
    locationid: string;
    salesrep: string;
    agent: string;
    os: string;
    browser: string;
    gaclickid: string;
    warning: string;
    venueid: string;
    venuecontact: string;
    additionalnotes1: string;
    additionalnotes2: string;
    vendor: string;
    lattitude: string;
    longitude: string;
    salesreppaidtime: string;
    autocontract: string;
    venueaddress2: string;
    ionotes: string;
    uspsVerified: string;
    ersid: string;
    erslastupdate: string;
    eventzip4: string;
    qboinvoicesent: string;
    venuename: string;
    venuenotes: string;
    aataxsubdivision: string;
    aataxrate: string;
    elecreqd: string;
    type: string;
    recurringparent: string;
    aataxgross: string;
    setupstart: string;
    fbclid: string;
    tdownstart: string;
    qbostatus: string;
    ttimeto: string;
    setupwindowlocked: string;
    tdownwindowlocked: string;
    ttimefromlocked: string;
    ttimetolocked: string;
    utmsource: string;
    utmmedium: string;
    utmcampaign: string;
    profit: string;
    eventendtime: string;
    subtotal: number;
    selectedrides: string[];
    leadrental_taxrates: Record<string, string>;
    rentalqty: Record<string, string>;
    rentalholdtime: Record<string, string>;
    ridequotes: Record<string, string>;
    optionparent: Record<string, string[]>;
    isoption: number[];
    vendorcost: Record<string, string | null>;
    vendoravailable: Record<string, string | null>;
    vendorall: Record<string, number>;
    ridequotesorig: Record<string, string>;
    rentalstarttime: Record<string, string>;
    rentalendtime: Record<string, string>;
    item_order: Record<string, string>;
    leadrental_conflict: Record<string, string>;
    rentalqty_option: Record<string, Record<string, string>>;
    ridequotes_option: Record<string, Record<string, string>>;
    ridequotesorig_option: Record<string, Record<string, string>>;
    rentalstarttime_option: Record<string, Record<string, string>>;
    rentalendtime_option: Record<string, Record<string, string>>;
    leadrental_volunteers: Record<string, string | null>;
    leadrental_staff: Record<string, string | null>;
    leadrental_notes: Record<string, string | null>;
    leadrental_aataxrate: Record<string, string | null>;
    leadrental_discount: Record<string, string | null>;
    accessory_paths: string[];
    leadrental_tbl: ioOrderLeadRental[];
    vendorallids: Record<string, string | null>;
    rentalstaff: Record<string, string | null>;
    rentalstaff_option: Record<string, Record<string, string | null>>;
    feeorder: number[];
    feeorigprice: string[];
    feeprice: string[];
    feetaxrate: string[];
    feename: string[];
    feenote: string[];
    feetype: string[];
    feecouponid: string[];
    feeisdiscount: string[];
    feedescription: (string | null)[];
    status: ioOrderStatus;
    cust: ioOrderCustomer;
    admin_warning: string[];
    fields: Record<string, string>;
    fields_all: Record<string, string>;
    fields_type: Record<string, string>;
    fields_options: Record<string, string | null>;
    fields_ids: Record<string, ioOrderFieldInfo>;
    customfields: Record<string, string>;
    customfields_ids: Record<string, ioOrderFieldInfo>;
    shifts: string[];
    rentals: Record<string, ioOrderRental>;
    balancedue: number;
    totalamountpaid: string;
    href: string;
    createtime_formatted: string;
    createtime_ts: string;
    createtime_utc: string;
    modifiedtime_formatted: string;
    modifiedtime_ts: string;
    modifiedtime_utc: string;
    eventstarttime_formatted: string;
    eventstarttime_ts: string;
    eventstarttime_utc: string;
    fullstart_formatted: string;
    fullstart_ts: string;
    fullstart_utc: string;
    fullend_formatted: string;
    fullend_ts: string;
    fullend_utc: string;
    cushstart_formatted: string;
    cushstart_ts: string;
    cushstart_utc: string;
    cushend_formatted: string;
    cushend_ts: string;
    cushend_utc: string;
    setupwindowstart_formatted: string;
    setupwindowstart_ts: string;
    setupwindowstart_utc: string;
    contractsent_formatted: string;
    contractsent_ts: string;
    contractsent_utc: string;
    contractrecd_formatted: string;
    contractrecd_ts: string;
    contractrecd_utc: string;
    salesreppaidtime_formatted: string;
    salesreppaidtime_ts: string;
    salesreppaidtime_utc: string;
    eventendtime_formatted: string;
    eventendtime_ts: number;
    eventendtime_utc: string;
  };
  
  type ioOrderLeadRental = {
    leadrental_id: string;
    leadrental_username: string;
    leadrental_leadid: string;
    leadrental_order: string;
    leadrental_rentalid: string;
    leadrental_rentalqty: string;
    leadrental_holdtime: string;
    leadrental_origprice: string;
    leadrental_price: string;
    leadrental_optionparent: string | null;
    leadrental_rentalstarttime: string;
    leadrental_rentalendtime: string;
    leadrental_conflict: string;
    leadrental_overbooked: string | null;
    leadrental_conleads: string | null;
    leadrental_conleadscush: string | null;
    leadrental_taxrate: string;
    leadrental_vendorcost: string | null;
    leadrental_vendoravailable: string | null;
    leadrental_vendorall: string;
    leadrental_vendorallids: string | null;
    leadrental_rentalstaff: string | null;
    leadrental_accessorypath: string;
    leadrental_volunteers: string | null;
    leadrental_staff: string | null;
    leadrental_discount: string | null;
    leadrental_notes: string | null;
    leadrental_aataxrate: string | null;
    leadrental_surfaceid: string | null;
  };
  
  type ioOrderStatus = {
    id: string;
    name: string;
    isactive: string;
    order: string;
    color: string;
    newlead: string;
    newquote: string;
    contract: string;
    confirmed: string;
    complete: string;
    cancel: string;
    postpone: string;
    quickbooks: string;
    preventautoemail: string;
    reports: string;
    conflicts: string;
    googlecal: string;
    workercalendar: string;
    salesreppayroll: string;
    routing: string;
    action_copy: string;
    action_eventdatepast: string;
    action_balancedatepast: string;
    action_contractsigned: string;
    action_balancepaid: string;
    action_depositpaid: string;
    action_setbookdate: string;
    lastmodified: string;
    autobook: string;
    newpartial: string;
    action_paymentorcontract: string;
    lastmodified_ts: string;
    lastmodified_utc: string;
  };
  
  type ioOrderCustomer = {
    id: string;
    fromCustomerProvider: boolean;
    saveAsCopyId: string;
    createtime: string;
    modifiedtime: string;
    lastcontacttime: string;
    lastactiontime: string;
    organization: string;
    firstname: string;
    lastname: string;
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    homephone: string;
    officephone: string;
    cellphone: string;
    fax: string;
    email: string;
    notes: string;
    customertype: string;
    locationid: string;
    isblacklisted: string;
    score: string;
    qboupdatetime: string;
    qboid: string;
    title: string;
    active: string;
    address2: string;
    qboname: string;
    qbosendinvoice: string;
    referral: string;
    oktotext: string;
    repeatcustomer: string;
    taxexemptid: string;
    tags: string[];
    contact_phone: string;
    href: string;
    createtime_ts: string;
    createtime_utc: string;
    modifiedtime_ts: string;
    modifiedtime_utc: string;
  };
  
  type ioOrderFieldInfo = {
    name: string;
    value: string;
    type: string;
    options: string | null;
  };
  
  type ioOrderRental = {
    id: string;
    createtime: string;
    modifiedtime: string;
    parentid: string;
    isoption: string;
    ridename: string;
    rideorder: string;
    rideactive: string;
    quantity: string;
    description: string;
    imagelocbig: string;
    imageloc: string;
    weight: string;
    electric: string;
    dimensions: string;
    footprint: string;
    ties: string;
    tieweight: string;
    staff: string;
    volunteer: string;
    setupstaff: string;
    setuptime: string;
    tdowntime: string;
    pl: string;
    serialnumber: string;
    contract: string;
    operateinst: string;
    inspectionlist: string;
    ohioitinerary: string;
    qtytype: string;
    increment: string;
    tic: string;
    inputtype: string;
    notes: string;
    separateunits: string;
    structure: string;
    appendname: string;
    contractorpay: string;
    manufacturer: string;
    modelnumber: string;
    reqdepamt: string;
    reqdeptype: string;
    showcontract: string;
    showemail: string;
    showpackinglist: string;
    showquoteform: string;
    showquoteconfirm: string;
    recoverytime: string;
    qbaccount: string;
    custrestrict: string;
    qboupdatetime: string;
    qboid: string;
    imageloc2: string;
    imagelocbig2: string;
    imageloc3: string;
    imagelocbig3: string;
    imageloc4: string;
    imagelocbig4: string;
    vendorid: string;
    qboname: string;
    shared: string;
    sharedid: string;
    ispackage: string;
    accessorylimit: string;
    accessorydd: string;
    separatevehicle: string;
    alertwind: string;
    alertgust: string;
    replacementcost: string;
    separateonpl: string;
    giftcardvalue: string;
    allowdiscounts: string;
    vendorcompare: string;
    operationcost: string;
    showleadspage: string;
    plbehavior: string;
    ismanufacturer: string;
    isheading: string;
    aataxable: string;
    tentlength: string;
    istent: string;
    tentsection: string;
    tentwidth: string;
    returnprice: string;
    splitcontractorpay: string;
    deliveryprice: string;
    contractordeliverypay: string;
    contractorreturnpay: string;
    leadid: string;
    order: string;
    rentalid: string;
    rentalqty: string;
    holdtime: string;
    origprice: string;
    price: string;
    optionparent: string;
    rentalstarttime: string;
    rentalendtime: string;
    conflict: string;
    overbooked: string;
    conleads: string;
    conleadscush: string;
    taxrate: string;
    vendorcost: string;
    vendoravailable: string;
    vendorall: string;
    vendorallids: string;
    rentalstaff: string;
    accessorypath: string;
    volunteers: string;
    discount: string;
    aataxrate: string;
    surfaceid: string;
    options: string[];
    option_required: Record<string, string>;
    href: string;
    createtime_ts: string;
    createtime_utc: string;
    modifiedtime_ts: string;
    modifiedtime_utc: string;
  };
  