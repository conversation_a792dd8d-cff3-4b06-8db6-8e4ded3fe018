import { Data } from "@measured/puck";
import { Account } from ".prisma/client";
import { getJsonItem, r2 } from "~/server/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { getWebsiteData } from "~/pages/api/backend/siteInfo";
import { db } from "~/server/db";
import { getConciseAddress } from "~/server/lib/location/types";
import { getCachedAccountFromId } from "~/server/account/lookup";
import { productImageValidator } from "~/server/product/types";
import { fullImageUrl } from "~/server/globalTypes";
import slugify from "slugify";
import { PublicProduct } from "~/pages/api/public/[accountName]/products";
import { PublicCategory } from "~/pages/api/public/[accountName]/categories";
import { PublicRule } from "~/lib/rules";
import { getAccountLogo } from "~/server/lib/logo";
import { PuckRootProps } from "~/pages/website";

type InternalPageData = {
  title: string;
  description: string;
  content: string;
  logo: string;
};

export type SeoPageData = {
  title: string;
  description: string;
  beforeContent: string;
  afterContent?: string;
  logo: string;
};

const BUCKET =
  process.env.NEXT_PUBLIC_SELF_URL?.includes("staging") ||
  process.env.NODE_ENV !== "production"
    ? "staging-websites"
    : "websites";

export const updateWebsitePage = async (
  domain: string,
  slug: string,
  data: Data<any, PuckRootProps>,
  account: Account,
) => {
  const pageData: InternalPageData = {
    title: data?.root?.props?.title ?? data?.root?.title ?? account.name,
    description:
      data?.root?.props?.description ?? data?.root?.description ?? account.name,
    content: JSON.stringify(data),
    logo:
      (await getAccountLogo(account.id)) ??
      "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon",
  };

  return await r2.send(
    new PutObjectCommand({
      Bucket: BUCKET,
      Key: `${domain}/pages/${encodeURIComponent(slug)}.json`,
      Body: JSON.stringify(pageData),
      ContentType: "application/json",
    }),
  );
};

export const updateSimplePage = async ({
  domain,
  slug,
  beforeContent,
  afterContent,
  title,
  description,
  account,
}: {
  domain: string;
  slug: string;
  beforeContent: string;
  afterContent?: string;
  title: string;
  description: string;
  account: Account;
}) => {
  const pageData: SeoPageData = {
    title: title ?? account.name,
    description: description,
    beforeContent: beforeContent,
    afterContent: afterContent,
    logo:
      (await getAccountLogo(account.id)) ??
      "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon",
  };

  return await r2.send(
    new PutObjectCommand({
      Bucket: BUCKET,
      Key: `${domain}/seo-pages/${encodeURIComponent(slug)}.json`,
      Body: JSON.stringify(pageData),
      ContentType: "application/json",
    }),
  );
};

export const getWebsiteInfo = async (domain: string) => {
  return await getJsonItem(BUCKET, `${domain.toLowerCase()}/info.json`);
};

export const getWebsiteCollection = async (domain: string) => {
  return await getJsonItem(BUCKET, `${domain.toLowerCase()}/collections.json`);
};

export const updateWebsiteCollection = async (
  accountId: number,
  organizationId: string,
  validationTag?: string,
) => {
  try {
    if (validationTag) {
      // revalidateTag(validationTag);
    }
  } catch (e) {
    console.log(e);
  }

  try {
    const account = await getCachedAccountFromId(accountId);

    if (!account?.customDomain) {
      return;
    }

    const [products, categories, bestSellers, shareSettings, rules] =
      await db.$transaction([
        db.product.findMany({
          where: {
            organizationId: organizationId,
            archived: false,
          },
          select: {
            accountId: true,
            id: true,
            display: true,
            name: true,
            description: true,
            price: true,
            quantity: true,
            slug: true,
            ProductCategory: {
              select: {
                categoryId: true,
              },
            },
            ProductRule: {
              select: {
                ruleId: true,
              },
            },
            ProductShareSettings: {
              select: {
                accountId: true,
                shared: true,
              },
            },
            categoryId: true,
            subCategoryId: true,
            metaTitle: true,
            metaDescription: true,
            ProductImageUpload: {
              select: productImageValidator,
            },
          },
        }),
        db.category.findMany({
          where: {
            organizationId: organizationId,
          },
          select: {
            id: true,
            display: true,
            slug: true,
            name: true,
            description: true,
            accountId: true,
            CategoryImageUpload: {
              select: {
                imageUpload: {
                  select: {
                    name: true,
                    url: true,
                  },
                },
              },
            },
          },
        }),
        db.product.findMany({
          where: {
            organizationId: organizationId,
            archived: false,
            display: true,
          },
          select: {
            id: true,
          },
          take: 10,
          orderBy: {
            OrderProduct: {
              _count: "desc",
            },
          },
        }),
        db.accountInventoryShareSettings.findFirst({
          where: {
            accountId: accountId,
          },
        }),
        db.rule.findMany({
          where: {
            accountId: accountId,
            isActive: true,
          },
        }),
      ]);

    const publicProducts: PublicProduct[] = [];

    products
      .filter((product) => {
        if (
          product.accountId !== accountId &&
          product.ProductShareSettings.find(
            (settings) => settings.accountId === accountId,
          ) !== undefined
        ) {
          return (
            product.ProductShareSettings.find(
              (settings) => settings.accountId === accountId,
            )?.shared === true
          );
        }
        if (shareSettings?.receiveSharedProducts === false) {
          return product.accountId === accountId;
        }
        return true;
      })
      .forEach((product) => {
        const images = product.ProductImageUpload.sort(
          (a, b) => a.priority - b.priority,
        );

        const heroImage = fullImageUrl(images.at(0)?.imageUpload);

        const otherImages = images
          .slice(1)
          .map((image) => fullImageUrl(image.imageUpload));

        const categoryIds = product.ProductCategory.map(
          (category) => category.categoryId,
        );

        publicProducts.push({
          id: product.id,
          rules: product.ProductRule.map((rule) => rule.ruleId),
          display: product.display,
          name: product.name,
          slug: product.slug ?? slugify(product.name, { lower: true }),
          description: product.description,
          price: product.price,
          quantity: product.quantity,
          categories: categoryIds,
          altImages: otherImages,
          heroImage: heroImage,
          metaTitle: product.metaTitle ?? undefined,
          metaDescription: product.metaDescription ?? undefined,
          ProductImageUpload: [],
        });
      });

    const publicCategories: PublicCategory[] = [];

    categories
      .filter((category) => {
        if (shareSettings?.receiveSharedCategories === false) {
          return category.accountId === accountId;
        }
        return true;
      })
      .forEach((category) => {
        const heroImage = fullImageUrl(
          category.CategoryImageUpload?.at(0)?.imageUpload,
        );
        const altImages =
          category.CategoryImageUpload?.slice(1).map((imageUpload) =>
            fullImageUrl(imageUpload.imageUpload),
          ) || [];
        publicCategories.push({
          id: category.id,
          display: category.display,
          slug: category.slug ?? slugify(category.name, { lower: true }),
          name: category.name,
          description: category.description,
          heroImage,
          altImages,
        });
      });

    const publicRules: PublicRule[] = [];

    rules.forEach((rule) => {
      publicRules.push({
        id: rule.id,
        name: rule.name,
        ruleJson: rule.ruleJson,
        priority: rule.priority,
      });
    });

    return await r2.send(
      new PutObjectCommand({
        Bucket: BUCKET,
        Key: `${account?.customDomain?.toLowerCase()}/collections.json`,
        Body: JSON.stringify({
          products: publicProducts,
          categories: publicCategories,
          bestSellers,
          rules: publicRules,
        }),
        ContentType: "application/json",
      }),
    );
  } catch (e) {
    console.log(e);
  }
};

export const updateWebsite = async (
  account: number | (Account & { address: string }),
  domain?: string,
) => {
  try {
    let data;
    let domainFromDb: string | undefined = domain;
    if (typeof account === "number") {
      const accountFromDb = await db.account.findFirst({
        where: {
          id: account,
        },
        include: {
          billingAddress: true,
        },
      });
      if (!accountFromDb) {
        return;
      }

      data = await getWebsiteData(
        {
          ...accountFromDb,
          address: accountFromDb.billingAddress
            ? getConciseAddress(accountFromDb.billingAddress)
            : "",
        },
        domain ?? "",
      );
      if (!domain) {
        domainFromDb = accountFromDb.customDomain ?? "";
      }
    } else {
      data = await getWebsiteData(account, domain ?? "");
      if (!domain) {
        domainFromDb = account.customDomain ?? "";
      }
    }

    if (!data || !domainFromDb) {
      console.log("No data or domain found");
      return;
    }

    return await r2.send(
      new PutObjectCommand({
        Bucket: BUCKET,
        Key: `${domainFromDb}/info.json`,
        Body: JSON.stringify(data, (key, value) => {
          if (value instanceof Date) {
            return value.toISOString();
          }

          // Handle undefined values explicitly
          if (value === undefined) {
            return null; // Convert undefined to null for JSON.stringify
          }

          // Return the value as is for all other cases
          return value;
        }),
        ContentType: "application/json",
      }),
    );
  } catch (e) {
    console.log(`Saving Data`, e);
  }
};
