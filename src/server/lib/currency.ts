import { DiscountType, OrderFeeType } from "@prisma/client";
import { OrderDiscountInput, OrderFeeInput } from "~/server/lib/orderUtil";

export const getCurrencyValue = (amount: number): number => {
  return Math.ceil(amount * 100) / 100;
};

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export const getCurrencyString = (amount: number): string => {
  if (amount < 0 && amount > -0.01) {
    return formatter.format(0);
  }
  if (isNaN(amount)) {
    return formatter.format(0);
  }
  return formatter.format(getCurrencyValue(amount));
};

const getStripeAmountFromPlatform = (amount: number): number => {
  return Math.ceil(getCurrencyValue(amount) * 100);
};

const getPlatformAmountFromStripe = (amount: number): number => {
  return getCurrencyValue(amount / 100);
};

export class CurrencyValue {
  amount: number;
  stripeAmount: number;
  origin: "stripe" | "platform";

  private constructor(
    amount: number,
    stripeAmount: number,
    origin: "stripe" | "platform",
  ) {
    this.amount = getCurrencyValue(amount);
    this.stripeAmount = Math.ceil(stripeAmount);
    this.origin = origin;
  }

  static fromStripe = (amount: number): CurrencyValue => {
    return new CurrencyValue(
      getPlatformAmountFromStripe(amount),
      amount,
      "stripe",
    );
  };

  static fromPlatform = (amount: number): CurrencyValue => {
    return new CurrencyValue(
      amount,
      getStripeAmountFromPlatform(amount),
      "platform",
    );
  };

  add = (other: CurrencyValue): CurrencyValue => {
    return new CurrencyValue(
      this.amount + other.amount,
      this.stripeAmount + other.stripeAmount,
      this.origin,
    );
  };

  subtract = (other: CurrencyValue): CurrencyValue => {
    return new CurrencyValue(
      this.amount - other.amount,
      this.stripeAmount - other.stripeAmount,
      this.origin,
    );
  };

  divide = (other: number): CurrencyValue => {
    return new CurrencyValue(
      this.amount / other,
      this.stripeAmount / other,
      this.origin,
    );
  };

  toPlatformString = (): string => {
    return getCurrencyString(this.amount);
  };
}

export const enum PaymentInfoLineItemName {
  Product = "Product",
  CouponCode = "Coupon Code",
  Sale = "Sale",
  GeneralDiscount = "General Discount",
  TravelFee = "Travel Fee",
  SurfaceFee = "Surface Fee",
  CustomFee = "Order Fee",
  DamageWaiver = "Rental Protection",
  Tax = "Tax",
}

export type RateBasedPaymentItem = {
  amount: number;
  percentage: number;
};

export type RateBasedPaymentItemAmount = {
  amount: number;
  percentage?: never;
};

export type RateBasedPaymentItemPercentage = {
  amount?: never;
  percentage: number;
};

export type NamedRateBasedPaymentItemAmount = RateBasedPaymentItemAmount & {
  type: OrderFeeType;
  name: string | null;
  taxable: boolean;
};

export type DiscountRateBasedPaymentItemPercentage = {
  chargeType: "PERCENTAGE";
  amount: number;
  percentage: number;
  type: DiscountType;
  entityId: number | null;
};
export type DiscountRateBasedPaymentItemAmount = {
  chargeType: "DOLLAR";
  amount: number;
  type: DiscountType;
  entityId: number | null;
};

export type DiscountRateBasedPaymentItem =
  | DiscountRateBasedPaymentItemAmount
  | DiscountRateBasedPaymentItemPercentage;

export type PaymentInfoInput = {
  /**
   * What the customer has selected, generally this will just be the product price total
   */
  baseTotal: number;

  discounts: OrderDiscountInput[] | null;

  /**
   * The amount to add to the baseTotal for a Rental Protection. This is not linked to the account so that if the business changes the rental protection amount, it will not affect past orders.
   * If no rental protection was applied, this should be null.
   * This amount will be added to the baseTotal less any coupon, sale, or general discounts, thus not subject to any discounts.
   */
  damageWaiver: RateBasedPaymentItemPercentage | null;

  /**
   * The fees to apply to the order charged for various reasons. These are usually calculated amounts based on the order.
   * If no fees were applied, this should be an empty array.
   */
  fees: NamedRateBasedPaymentItemAmount[];

  /**
   * The tax to apply to the order.
   */
  taxRate: RateBasedPaymentItemPercentage | null;

  /**
   * The amount of payments made on the order. This can be zero.
   */
  totalPaid: number;
};

export type PaymentInfo = {
  displayItems: PaymentInfoLineItem[];
  baseTotal: number;
  discounts: DiscountRateBasedPaymentItem[];
  damageWaiver: RateBasedPaymentItem | null;
  fees: NamedRateBasedPaymentItemAmount[];
  tax: RateBasedPaymentItem | null;
  totalPaid: number;
  finalTotal: number;
  amountRemaining: number;
};

export type PaymentInfoLineItem = {
  amountBefore: number;
  amountAfter: number;
  itemAmount: number;
  name: PaymentInfoLineItemName;
  detail?: {
    name?: string;
    taxable?: boolean;
  };
};

export const convertFeeTypeToPaymentInfoLineItemName = (
  feeType: OrderFeeType,
): PaymentInfoLineItemName => {
  switch (feeType) {
    case OrderFeeType.TRAVEL_FEE:
      return PaymentInfoLineItemName.TravelFee;
    case OrderFeeType.SURFACE_FEE:
      return PaymentInfoLineItemName.SurfaceFee;
    case OrderFeeType.CUSTOM_FEE:
      return PaymentInfoLineItemName.CustomFee;
  }
};

export const convertDiscountTypeToPaymentInfoLineItemName = (
  discountType: DiscountType,
): PaymentInfoLineItemName => {
  switch (discountType) {
    case DiscountType.COUPON:
      return PaymentInfoLineItemName.CouponCode;
    case DiscountType.MANUAL:
      return PaymentInfoLineItemName.GeneralDiscount;
    case DiscountType.SALE:
      return PaymentInfoLineItemName.Sale;
  }
};

export const convertPaymentInfoLineItemNameToFeeType = (
  name: PaymentInfoLineItemName,
): OrderFeeType | null => {
  switch (name) {
    case PaymentInfoLineItemName.TravelFee:
      return OrderFeeType.TRAVEL_FEE;
    case PaymentInfoLineItemName.SurfaceFee:
      return OrderFeeType.SURFACE_FEE;
    case PaymentInfoLineItemName.CustomFee:
      return OrderFeeType.CUSTOM_FEE;
    default:
      return null;
  }
};

export const convertPaymentInfoLineItemNameToDiscountType = (
  name: PaymentInfoLineItemName,
): DiscountType | null => {
  switch (name) {
    case PaymentInfoLineItemName.CouponCode:
      return DiscountType.COUPON;
    case PaymentInfoLineItemName.GeneralDiscount:
      return DiscountType.MANUAL;
    case PaymentInfoLineItemName.Sale:
      return DiscountType.SALE;
    default:
      return null;
  }
};

const DISCOUNT_ORDER: DiscountType[] = ["COUPON", "SALE", "MANUAL"];

export const getPaymentInfo = (options: PaymentInfoInput): PaymentInfo => {
  const { baseTotal, discounts, damageWaiver, fees, taxRate, totalPaid } =
    options;
  let subTotal = 0;

  const displayItems: PaymentInfoLineItem[] = [];
  const baseTotalLineItem: PaymentInfoLineItem = {
    amountBefore: subTotal,
    amountAfter: (subTotal += baseTotal),
    itemAmount: baseTotal,
    name: PaymentInfoLineItemName.Product,
  };
  displayItems.push(baseTotalLineItem);

  const discountItems: DiscountRateBasedPaymentItem[] = [];
  for (const item of (discounts || [])?.sort((a, b) => {
    return (
      DISCOUNT_ORDER.indexOf(a.type as DiscountType) -
      DISCOUNT_ORDER.indexOf(b.type as DiscountType)
    );
  })) {
    const namedItem = convertDiscountTypeToPaymentInfoLineItemName(
      item.type as DiscountType,
    );
    const discountLineItem: PaymentInfoLineItem = {
      amountBefore: subTotal,
      amountAfter: subTotal,
      itemAmount: 0,
      name: namedItem,
    };
    let amount: number;
    let discountItem: DiscountRateBasedPaymentItem;
    if (item.chargeType === "PERCENTAGE") {
      amount = getCurrencyValue(subTotal * (item.amount / 100));
      discountItem = {
        entityId: item.entityId,
        chargeType: "PERCENTAGE",
        amount,
        percentage: item.amount,
        type: item.type as DiscountType,
      };
    } else {
      amount = getCurrencyValue(item.amount);
      discountItem = {
        entityId: item.entityId,
        chargeType: "DOLLAR",
        amount,
        type: item.type as DiscountType,
      };
    }
    discountItem.amount = amount;
    discountLineItem.itemAmount = amount;
    discountLineItem.amountAfter = subTotal -= amount;
    discountItems.push(discountItem);
    displayItems.push(discountLineItem);
  }

  let damageWaiverReturn: RateBasedPaymentItem | null = null;
  if (damageWaiver) {
    const damageWaiverLineItem: PaymentInfoLineItem = {
      amountBefore: subTotal,
      amountAfter: subTotal,
      itemAmount: 0,
      name: PaymentInfoLineItemName.DamageWaiver,
    };
    const damageWaiverAmount = getCurrencyValue(
      subTotal * (damageWaiver.percentage / 100),
    );

    damageWaiverReturn = {
      amount: damageWaiverAmount,
      percentage: damageWaiver.percentage,
    };
    damageWaiverLineItem.itemAmount = damageWaiverAmount;
    damageWaiverLineItem.amountAfter = subTotal += damageWaiverAmount;
    displayItems.push(damageWaiverLineItem);
  }

  const feeReturn: OrderFeeInput[] = [];
  if (fees.length > 0) {
    fees
      .filter((fee) => fee.taxable)
      .forEach((fee) => {
        const feeName = convertFeeTypeToPaymentInfoLineItemName(fee.type);
        if (!feeName) {
          return;
        }
        const feeLineItem: PaymentInfoLineItem = {
          amountBefore: subTotal,
          amountAfter: subTotal,
          itemAmount: getCurrencyValue(fee.amount),
          name: feeName,
          detail: {
            name: fee.name ?? undefined,
            taxable: true,
          },
        };
        subTotal += feeLineItem.itemAmount;
        feeLineItem.amountAfter = subTotal;
        feeReturn.push({ ...fee, taxable: true });
        displayItems.push(feeLineItem);
      });
  }

  let taxReturn: RateBasedPaymentItem | null = null;
  if (taxRate) {
    const taxLineItem: PaymentInfoLineItem = {
      amountBefore: subTotal,
      amountAfter: subTotal,
      itemAmount: getCurrencyValue(subTotal * (taxRate.percentage / 100)),
      name: PaymentInfoLineItemName.Tax,
    };
    taxLineItem.amountAfter = subTotal += taxLineItem.itemAmount;
    displayItems.push(taxLineItem);
    taxReturn = {
      amount: taxLineItem.itemAmount,
      percentage: taxRate.percentage,
    };
  }

  if (fees.length > 0) {
    fees
      .filter((fee) => !fee.taxable)
      .forEach((fee) => {
        const feeName = convertFeeTypeToPaymentInfoLineItemName(fee.type);
        if (!feeName) {
          return;
        }
        const feeLineItem: PaymentInfoLineItem = {
          amountBefore: subTotal,
          amountAfter: subTotal,
          itemAmount: getCurrencyValue(fee.amount),
          name: feeName,
          detail: {
            name: fee.name ?? undefined,
            taxable: false,
          },
        };
        subTotal += feeLineItem.itemAmount;
        feeLineItem.amountAfter = subTotal;
        feeReturn.push({ ...fee, taxable: false });
        displayItems.push(feeLineItem);
      });
  }

  return {
    displayItems,
    baseTotal,
    discounts: discountItems,
    damageWaiver: damageWaiverReturn,
    fees: feeReturn,
    tax: taxReturn,
    totalPaid: getCurrencyValue(totalPaid),
    finalTotal: getCurrencyValue(subTotal),
    amountRemaining: getCurrencyValue(subTotal - totalPaid),
  };
};

export const getMinimumDeposit = (
  finalTotal: number,
  minimumDepositPercentage: number,
): number => {
  if (isNaN(finalTotal)) {
    return 0;
  }
  if (minimumDepositPercentage >= 1) {
    minimumDepositPercentage = minimumDepositPercentage / 100;
  }
  const amount = getCurrencyValue(finalTotal * minimumDepositPercentage);
  return Math.max(amount, 1);
};
