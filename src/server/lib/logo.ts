import { Account } from "@prisma/client";
import { db } from "~/server/db";
import { fullImageUrl } from "~/server/globalTypes";

export const getAccountLogo = async (
  account: Account | number,
): Promise<string | undefined> => {
  const accountId = typeof account === "number" ? account : account.id;
  const logoImage = await db.imageUpload.findFirst({
    where: {
      accountId: accountId,
      type: "LOGO",
    },
  });

  if (!logoImage) {
    return undefined;
  }

  return fullImageUrl(logoImage?.url);
};

export const getLogoForAccounts = async (
  accounts: Account[] | { id: number }[],
): Promise<{ [accountId: number]: string | undefined }> => {
  const logos = await db.imageUpload.findMany({
    where: {
      accountId: {
        in: accounts.map((account) => account.id),
      },
      type: "LOGO",
    },
  });

  return accounts.reduce((acc, account) => {
    const logo = logos.find((logo) => logo.accountId === account.id);
    return {
      ...acc,
      [account.id]: logo ? fullImageUrl(logo.url) : undefined,
    };
  }, {});
};
