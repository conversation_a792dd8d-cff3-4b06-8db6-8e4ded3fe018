import { Prisma } from ".prisma/client";
import { array, boolean, number, object, string } from "yup";

export const ProductOrderItemSchema = object().shape({
  id: number().required(),
  name: string().required(),
  available: number().required(),
  quantity: number().required(),
  price: number().required(),
  productThumbnail: string().optional(),
});

export type ProductOrderItem = typeof ProductOrderItemSchema.__outputType;

export const productImageValidator =
  Prisma.validator<Prisma.ProductImageUploadSelect>()({
    priority: true,
    imageUpload: {
      select: {
        id: true,
        name: true,
        url: true,
      },
    },
  });

export const ProductSchema = object().shape({
  name: string().required("Product Name is required"),
  description: string().required("Product Description is required"),
  setupTimeMinutes: number().required("Setup Time is required"),
  takeDownTimeMinutes: number().required("Take Down Time is required"),
  beforeRentalBufferMinutes: number()
    .min(0, "A positive number is required.")
    .default(0)
    .required("Before Rental Buffer is required"),
  deliverable: boolean()
    .default(true)
    .required("Deliverable status is required"),
  afterRentalBufferMinutes: number()
    .min(0, "A positive number is required.")
    .default(0)
    .required("After Rental Buffer is required"),
  price: number()
    .min(0, "A positive price is required")
    .required("Price is required"),
  display: boolean().required("Display status is required"),
  categories: array().of(number().required()).required("Category is required"),
  quantity: number()
    .moreThan(0, "Product Quantity must be above 0")
    .required("Quantity is required"),
  slug: string().nullable().optional(),
  metaTitle: string().nullable().optional(),
  metaDescription: string().nullable().optional(),
});

export type ProductSchemaType = typeof ProductSchema.__outputType;
