import { type NextRequest, NextResponse } from "next/server";

const corsOptions: {
  allowedMethods: string[];
  allowedOrigins: string[];
  allowedHeaders: string[];
  maxAge?: number;
} = {
  allowedMethods: (
    process.env?.ALLOWED_METHODS || "GET, POST, PUT, DELETE, OPTIONS"
  ).split(","),
  allowedOrigins: (process.env?.ALLOWED_ORIGIN || "*").split(","),
  allowedHeaders: (process.env?.ALLOWED_HEADERS || "*").split(","),
  maxAge: (process.env?.MAX_AGE && parseInt(process.env?.MAX_AGE)) || undefined, // 60 * 60 * 24 * 30, // 30 days
};

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();

  const headers: Record<string, string> = {};
  // Allowed origins check
  const origin = request.headers.get("origin") ?? "";
  if (
    corsOptions.allowedOrigins.includes("*") ||
    corsOptions.allowedOrigins.includes(origin)
  ) {
    headers["Access-Control-Allow-Origin"] = origin;
  }

  // Set default CORS headers
  headers["Access-Control-Allow-Methods"] =
    corsOptions.allowedMethods.join(",");
  headers["Access-Control-Allow-Headers"] =
    corsOptions.allowedHeaders.join(",");
  headers["Access-Control-Max-Age"] =
    corsOptions.maxAge?.toString() ?? (60 * 60 * 24 * 30).toString();

  if (request.method === "OPTIONS") {
    return NextResponse.json({}, { headers: headers });
  }

  for (const headersKey in headers) {
    response.headers.set(headersKey, headers[headersKey] || "");
  }

  // Return
  return response;
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/api/backend/admin/:path*",
    "/api/backend/signup/:path*",
    "/api/public/:path*",
    "/api/public/backend/stripe/:path*",
    "/api/public/backend/emailCron",
    "/api/backend/siteInfo/:path/documents/:title/save",
    "/api/backend/siteInfo/:path/documents/:title/:id/upload",
  ],
};
