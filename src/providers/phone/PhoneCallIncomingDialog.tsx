import { AlertDialog, AlertDialogContent } from "~/components/ui/alert-dialog";
import { Button } from "~/components/ui/button";
import { Call } from "@twilio/voice-sdk";

type PhoneCallIncomingDialogProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  pendingCall: Call | null;
  onResponse: (call: Call, accepted: boolean) => void;
};

const PhoneCallIncomingDialog = ({
  open,
  setOpen,
  pendingCall,
  onResponse,
}: PhoneCallIncomingDialogProps) => {
  return (
    <AlertDialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen && pendingCall) {
          pendingCall.ignore();
        }
        setOpen(newOpen);
      }}
    >
      <AlertDialogContent>
        {pendingCall && (
          <div className={"text-center"}>
            <h1 className={"text-2xl"}>Incoming Call</h1>
            <h2
              className={"text-lg"}
            >{`From ${pendingCall.parameters.From}`}</h2>
            <div className={"mt-2 flex flex-row justify-evenly"}>
              <Button
                onClick={() => {
                  pendingCall.accept();
                  setOpen(false);
                  onResponse(pendingCall, true);
                }}
                variant={"primary"}
              >
                Accept
              </Button>
              <Button
                variant={"destructive"}
                onClick={() => {
                  pendingCall.disconnect();
                  setOpen(false);
                  onResponse(pendingCall, false);
                }}
              >
                Reject
              </Button>
            </div>
          </div>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default PhoneCallIncomingDialog;
