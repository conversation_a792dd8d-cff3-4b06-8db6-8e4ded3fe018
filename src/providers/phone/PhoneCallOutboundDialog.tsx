import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog";
import { Call, Device } from "@twilio/voice-sdk";
import { Input } from "~/components/ui/input";
import { useState } from "react";
import { cn } from "~/lib/utils";

type PhoneCallOutboundDialogProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  device: Device | null;
  outboundCall: (Call: Call) => void;
};

const PhoneCallOutboundDialog = ({
  open,
  setOpen,
  device,
  outboundCall,
}: PhoneCallOutboundDialogProps) => {
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  return (
    <AlertDialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Outbound Call</AlertDialogTitle>
        </AlertDialogHeader>
        <Input
          className={cn("mt-4", error && "border-red-500")}
          placeholder={"Phone Number"}
          onChange={(e) => {
            setPhoneNumber(e.target.value);
          }}
        />
        {error && <p className={"text-red-500"}>{error}</p>}
        <div className={"flex flex-row gap-2 justify-end"}>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            variant={"primary"}
            onClick={() => {
              if (!phoneNumber) {
                setError("Phone number is required");
                return;
              }
              // check if the phone number is valid, if it's close we'll make it valid
              if (phoneNumber.length < 10) {
                setError("Phone number is invalid");
                return;
              }
              setError(null);
              if (phoneNumber.length === 10) {
                setPhoneNumber(`+1${phoneNumber}`);
              }
              if (device) {
                const params = {
                  To: phoneNumber,
                };
                device.connect({ params }).then((call) => {
                  outboundCall(call);
                });
              }
              setOpen(false);
            }}
          >
            Call
          </AlertDialogAction>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default PhoneCallOutboundDialog;
