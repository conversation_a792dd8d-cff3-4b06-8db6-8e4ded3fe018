import React, {
  createContext,
  ReactElement,
  useContext,
  useEffect,
  useState,
} from "react";
import { Call, Device } from "@twilio/voice-sdk";
import { captureEvent } from "@sentry/core";
import PhoneCallIncomingDialog from "~/components/phone/call/PhoneCallIncomingDialog";
import PhoneCallOutboundDialog from "~/components/phone/call/PhoneCallOutboundDialog";
import { PhoneResponse } from "~/pages/api/phone";
import { toast } from "sonner";
import { usePhoneProviderInfo } from "~/query/phone";
import Codec = Call.Codec;

export const PhoneContext = createContext({
  twilioDevice: null as Device | null,
  pendingCall: null as Call | null,
  activeCall: null as Call | null,
  token: null as string | null,
  phoneInfo: undefined as PhoneResponse | undefined,
  requestVoiceToken: async (): Promise<Device | null> => {
    /* do nothing */
    return null;
  },
  unregisterDevice: async () => {
    /* do nothing */
  },
  openOutboundCall: () => {
    /* do nothing */
  },
  makeOutboundCall: (phoneNumber: string) => {
    /* do nothing */
  },
  isMessagingEnabled: (): boolean => {
    return false;
  },
  hasPhoneService: (): boolean => {
    return false;
  },
});

export const usePhone = () => useContext(PhoneContext);

export const PhoneProvider = ({ children }: { children: ReactElement }) => {
  const [outboundOpen, setOutboundOpen] = useState<string | undefined>(
    undefined,
  );
  const [open, setOpen] = useState(false);
  const [twilioDevice, setTwilioDevice] = useState<Device | null>(null);
  const [pendingCall, setPendingCall] = useState<Call | null>(null);
  const [activeCall, setActiveCall] = useState<Call | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const { data: phoneInfo } = usePhoneProviderInfo();

  const openOutboundCall = () => {
    setOutboundOpen("");
  };

  useEffect(() => {
    if (activeCall) {
      activeCall.on("disconnect", () => {
        setActiveCall(null);
      });
      activeCall.on("cancel", () => {
        setActiveCall(null);
      });
      activeCall.on("disconnect", () => {
        setActiveCall(null);
      });
      activeCall.on("reject", () => {
        setActiveCall(null);
      });
    }
  }, [activeCall]);

  const makeOutboundCall = (phoneNumber: string) => {
    console.log("Making outbound call", phoneNumber);
    setTimeout(() => {
      setOutboundOpen(phoneNumber);
    }, 100);
  };

  const _makeOutboundCall = (phoneNumber: string, forwardToMobile: boolean) => {
    if (phoneNumber.length === 10) {
      phoneNumber = `+1${phoneNumber}`;
    }

    const params = {
      To: phoneNumber,
    };

    if (forwardToMobile) {
      fetch("/api/phone/voice/call", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber,
        }),
      }).then((response) => {
        if (!response.ok) {
          captureEvent({
            extra: {
              status: response.status,
              statusText: response.statusText,
            },
            message: "Failed to make outbound call",
          });
          return;
        }
        toast.success("Sending call request to your phone.");
      });
      return;
    }

    if (twilioDevice) {
      twilioDevice.connect({ params }).then((call) => {
        setActiveCall(call);
      });
    } else {
      requestVoiceToken().then((device) => {
        if (!device) {
          return;
        }
        device.connect({ params }).then((call) => {
          setActiveCall(call);
        });
      });
    }
  };

  const requestVoiceToken = async (): Promise<Device | null> => {
    const response = await fetch("/api/phone/voice/token", {
      method: "GET",
    });
    if (!response.ok) {
      captureEvent({
        extra: {
          status: response.status,
          statusText: response.statusText,
        },
        message: "Failed to get voice token",
      });
      return null;
    }
    const { token } = await response.json();
    const newDevice = await registerNewDevice(token);
    setToken(token);
    return newDevice;
  };

  const unregisterDevice = async () => {
    if (twilioDevice) {
      await twilioDevice.unregister();
    }
    setActiveCall(null);
    setPendingCall(null);
    setTwilioDevice(null);
    setToken(null);
  };

  const addDeviceListeners = async (device: Device) => {
    await device.register();
    device.on("error", (error) => {
      console.error(error);
      unregisterDevice();
    });

    device.on("incoming", (call: Call) => {
      console.log("Incoming call", call);
      setOpen(true);
      setPendingCall(call);
    });

    const handleDisconnect = (call: Call) => {
      setOpen(false);
      setPendingCall(null);
      setActiveCall(null);
      call.reject();
    };

    device.on("cancel", handleDisconnect);
    device.on("disconnect", handleDisconnect);
    device.on("reject", handleDisconnect);
  };

  const registerNewDevice = async (token: string): Promise<Device> => {
    if (twilioDevice) {
      twilioDevice.updateToken(token);
      return twilioDevice;
    }
    const device = new Device(token, {
      logLevel: "debug",

      codecPreferences: [Codec.Opus, Codec.PCMU],
    });

    setTwilioDevice(device);

    await addDeviceListeners(device);
    return device;
  };

  const isMessagingEnabled = (): boolean => {
    if (!phoneInfo?.enabled) {
      return false;
    }
    return (
      phoneInfo.messagingStatus === "approved" ||
      phoneInfo.messagingStatus === "success"
    );
  };

  const hasPhoneService = (): boolean => {
    return phoneInfo?.enabled || false;
  };

  return (
    <PhoneContext.Provider
      value={{
        twilioDevice,
        pendingCall,
        activeCall,
        token,
        phoneInfo: phoneInfo ?? undefined,
        requestVoiceToken,
        unregisterDevice,
        openOutboundCall,
        makeOutboundCall,
        isMessagingEnabled,
        hasPhoneService,
      }}
    >
      <PhoneCallIncomingDialog
        open={open}
        setOpen={setOpen}
        pendingCall={pendingCall}
        onResponse={(call, accepted) => {
          if (accepted) {
            setActiveCall(call);
          }
          setPendingCall(null);
        }}
      />
      <PhoneCallOutboundDialog
        open={outboundOpen !== undefined}
        setOpen={() => setOutboundOpen(undefined)}
        outboundCall={(phoneNumber: string, forwardToMobile) => {
          _makeOutboundCall(phoneNumber, forwardToMobile);
        }}
        forwardToMobileConfigured={
          phoneInfo?.mobileClickToCall === true &&
          phoneInfo.currentUserPhoneNumber !== null
        }
        prefillPhoneNumber={outboundOpen}
      />
      {children}
    </PhoneContext.Provider>
  );
};
