import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useSession } from "next-auth/react";
import { useQueryClient } from "@tanstack/react-query";
import { useTeams } from "~/query/account/query";

export type Team = {
  id: number;
  name: string | undefined;
  logo: string;
  plan: string | undefined;
};

type TeamContextType = {
  currentTeam: Team;
  setCurrentTeam: (team: number) => Promise<void>;
  teams: Team[];
};

const TeamContext = createContext<TeamContextType | undefined>(undefined);

export const TeamProvider = ({ children }: { children: ReactNode }) => {
  const [currentTeam, setCurrentTeam] = useState<Team>({
    id: 0,
    name: undefined,
    logo: "",
    plan: undefined,
  });
  const { data, isLoading } = useTeams();

  const session = useSession();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!session.data?.user.accountId) {
      return;
    }
    const team = Object.values(data || {})
      ?.flatMap((item) => item)
      .find((team) => team.id === session.data?.user.accountId);
    if (!team) {
      return;
    }
    setCurrentTeam({
      id: team.id,
      name: team.name,
      logo: team.logo,
      plan: session.data?.user?.planLevel
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase()),
    });
  }, [session, data]);

  const setTeam = async (teamId: number) => {
    const team = teams.find((team) => team.id === teamId);
    if (!team) {
      return;
    }
    await session.update({
      user: {
        ...session.data?.user,
        accountId: team.id,
      },
    });

    void queryClient.invalidateQueries();
    setCurrentTeam(team);
  };

  const teams = useMemo(() => {
    return Object.values(data || {})
      ?.flatMap((item) => item)
      .map((team) => ({
        id: team.id,
        name: team.name,
        logo: team.logo,
        plan: session.data?.user?.planLevel
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
      }));
  }, [data, session]);

  return (
    <TeamContext.Provider
      value={{
        currentTeam,
        setCurrentTeam: setTeam,
        teams: teams,
      }}
    >
      {children}
    </TeamContext.Provider>
  );
};

export const useAccountSwitcher = () => {
  const context = useContext(TeamContext);
  if (!context) {
    throw new Error("useAccountSwitcher must be used within a TeamProvider");
  }
  return context;
};
