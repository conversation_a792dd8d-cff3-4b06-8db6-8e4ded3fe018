import React, { createContext, useContext, useState, ReactNode } from "react";
import { PageProps } from "~/components/Page";

type HistoryEntry = {
  path: string;
  title: string;
};

type PageContextType = {
  path: string;
  pageProps?: PageProps;
  history: HistoryEntry[];
  setPath: (path: string) => void;
  setPageProps: (pageProps: PageProps) => void;
  pushToHistory: (entry: HistoryEntry) => void;
  updateLastHistory: (entry: HistoryEntry) => void;
  clearHistory: (currentPath: string) => void;
};

const PageContext = createContext<PageContextType | undefined>(undefined);

export const PageProvider = ({ children }: { children: ReactNode }) => {
  const [pageProps, setPageProps] = useState<PageProps | undefined>(undefined);
  const [path, setPath] = useState<string>("");
  const [history, setHistory] = useState<HistoryEntry[]>([]);

  const pushToHistory = (entry: HistoryEntry) => {
    setHistory((prevHistory) => {
      if (
        prevHistory.length > 0 &&
        prevHistory[prevHistory.length - 1]?.path === entry.path
      ) {
        return prevHistory;
      }
      return [...prevHistory, entry];
    });
  };

  const updateLastHistory = (entry: HistoryEntry) => {
    setHistory((prevHistory) => {
      const newHistory = [...prevHistory];
      newHistory[newHistory.length - 1] = entry;
      return newHistory;
    });
  };

  const clearHistory = (currentPath: string) => {
    setHistory((prevHistory) => {
      return prevHistory.filter((entry) => currentPath.includes(entry.path));
    });
  };

  return (
    <PageContext.Provider
      value={{
        path,
        pageProps,
        history,
        setPath,
        setPageProps,
        pushToHistory,
        clearHistory,
        updateLastHistory,
      }}
    >
      {children}
    </PageContext.Provider>
  );
};

export const usePageContext = () => {
  const context = useContext(PageContext);
  if (!context) {
    throw new Error("usePageContext must be used within a PageProvider");
  }
  return context;
};
