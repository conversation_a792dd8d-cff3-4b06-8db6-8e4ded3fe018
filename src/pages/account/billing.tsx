import React, { useEffect, useState } from "react";
import { AccountSettingsWithStripe } from "~/server/account/types";
import Page from "~/components/Page";
import BillingTab from "~/components/settings/BillingTab";

const BillingSettingsPage = () => {
  const [planLevel, setPlanLevel] = useState<string | undefined>(undefined);
  const [settings, setSettings] = useState<
    AccountSettingsWithStripe | undefined
  >(undefined);

  const requestCurrentAccountData = async (): Promise<{
    settings: AccountSettingsWithStripe | undefined;
    plan: {
      level: string;
    };
  }> => {
    const response = await fetch("/api/account");
    if (!response.ok) {
      return { settings: undefined, plan: { level: "" } };
    }
    return await response.json();
  };

  useEffect(() => {
    requestCurrentAccountData().then((data) => {
      setPlanLevel(data.plan.level);
      setSettings(data.settings);
    });
  }, []);

  return (
    <Page title={"Billing Settings"}>
      <BillingTab planLevel={planLevel} accountSettings={settings} />
    </Page>
  );
};

export default BillingSettingsPage;
