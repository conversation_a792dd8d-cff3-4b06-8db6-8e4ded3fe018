import React, { useEffect, useState } from "react";
import { AccountSettingsWithStripe } from "~/server/account/types";
import Page from "~/components/Page";
import SettingsTab from "~/components/settings/SettingsTab";

const AccountSettingsPage = () => {
  const [settings, setSettings] = useState<
    AccountSettingsWithStripe | undefined
  >(undefined);

  const requestCurrentAccountData = async (): Promise<{
    settings: AccountSettingsWithStripe | undefined;
    plan: {
      level: string;
    };
  }> => {
    const response = await fetch("/api/account");
    if (!response.ok) {
      return { settings: undefined, plan: { level: "" } };
    }
    return await response.json();
  };

  useEffect(() => {
    requestCurrentAccountData().then((data) => {
      setSettings(data.settings);
    });
  }, []);

  return (
    <Page title={"General Settings"}>
      <SettingsTab defaultSettings={settings} />
    </Page>
  );
};

export default AccountSettingsPage;
