import React, { useEffect, useState } from "react";
import { AccountSettingsWithStripe } from "~/server/account/types";
import Page from "~/components/Page";
import { useRouter } from "next/router";
import BillingTab from "~/components/settings/BillingTab";
import SettingsTab from "~/components/settings/SettingsTab";
import WebsiteSettingsTab from "~/components/settings/WebsiteTab";
import VoiceTab from "~/components/settings/VoiceTab";

const AccountSettingsPage = () => {
  const router = useRouter();

  const [planLevel, setPlanLevel] = useState<string | undefined>(undefined);
  const [settings, setSettings] = useState<
    AccountSettingsWithStripe | undefined
  >(undefined);

  const requestCurrentAccountData = async (): Promise<{
    settings: AccountSettingsWithStripe | undefined;
    plan: {
      level: string;
    };
  }> => {
    const response = await fetch("/api/account");
    if (!response.ok) {
      return { settings: undefined, plan: { level: "" } };
    }
    return await response.json();
  };

  useEffect(() => {
    requestCurrentAccountData().then((data) => {
      setPlanLevel(data.plan.level);
      setSettings(data.settings);
    });
  }, []);

  // Determine which tab to show based on URL parameter
  const currentTab = (router.query?.tab as string) ?? "settings";

  const renderTabContent = () => {
    switch (currentTab) {
      case "billing":
        return <BillingTab planLevel={planLevel} accountSettings={settings} />;
      case "website":
        return <WebsiteSettingsTab />;
      case "voice":
        return <VoiceTab />;
      default:
        return <SettingsTab defaultSettings={settings} />;
    }
  };

  return (
    <Page title={"Settings"}>
      {renderTabContent()}
    </Page>
  );
};

export default AccountSettingsPage;
