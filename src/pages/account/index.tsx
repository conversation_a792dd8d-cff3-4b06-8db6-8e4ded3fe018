import React, { useEffect, useState } from "react";
import { AccountSettingsWithStripe } from "~/server/account/types";
import Page from "~/components/Page";
import { Tabs, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useRouter } from "next/router";
import BillingTab from "~/components/settings/BillingTab";
import SettingsTab from "~/components/settings/SettingsTab";
import WebsiteSettingsTab from "~/components/settings/WebsiteTab";
import VoiceTab from "~/components/settings/VoiceTab";

const AccountSettingsPage = () => {
  const router = useRouter();

  const [planLevel, setPlanLevel] = useState<string | undefined>(undefined);
  const [settings, setSettings] = useState<
    AccountSettingsWithStripe | undefined
  >(undefined);

  const requestCurrentAccountData = async (): Promise<{
    settings: AccountSettingsWithStripe | undefined;
    plan: {
      level: string;
    };
  }> => {
    const response = await fetch("/api/account");
    if (!response.ok) {
      return { settings: undefined, plan: { level: "" } };
    }
    return await response.json();
  };

  useEffect(() => {
    requestCurrentAccountData().then((data) => {
      setPlanLevel(data.plan.level);
      setSettings(data.settings);
    });
  }, []);

  return (
    <Page title={"Settings"}>
      <Tabs defaultValue={(router.query?.tab as string) ?? "settings"}>
        <TabsList>
          <TabsTrigger value={"settings"}>General</TabsTrigger>
          <TabsTrigger value={"billing"}>Billing</TabsTrigger>
          <TabsTrigger value={"website"}>Website</TabsTrigger>
          <TabsTrigger value={"voice"}>Voice</TabsTrigger>
        </TabsList>
        <SettingsTab defaultSettings={settings} />
        <BillingTab planLevel={planLevel} accountSettings={settings} />
        <WebsiteSettingsTab />

        <VoiceTab />
      </Tabs>
    </Page>
  );
};

export default AccountSettingsPage;
