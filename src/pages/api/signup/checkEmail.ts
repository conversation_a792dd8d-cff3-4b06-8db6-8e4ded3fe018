import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { db } from "~/server/db";

const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const result = schema.safeParse(req.body);

  if (!result.success) {
    return res.status(400).json({ error: "Invalid request body" });
  }

  const { email, password } = result.data;

  // todo ensure there is no existing user with this email

  const user = await db.user.findFirst({
    where: {
      email,
    },
  });

  if (user) {
    return res.status(400).json({ error: "Email already exists" });
  }

  return res.status(200).json({ email, password });
};

export default POST;
