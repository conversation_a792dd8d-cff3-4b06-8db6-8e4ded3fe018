import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { db } from "~/server/db";
import { platformStripeServer } from "~/server/lib/stripe";
import slugify from "slugify";
import { handlePlainTextPassword } from "~/server/lib/password";
import { Client } from "@hubspot/api-client";
import { createDefaultWebsite } from "~/lib/signup/website";
import { createDefaultEmailTemplates } from "../backend/signup";
import { ScheduleDay } from "@prisma/client";
import { sendSignupNotification } from "~/server/lib/slack";

const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  businessName: z.string(),
  referrer: z.string().optional(),
});

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const data = schema.safeParse(req.body);

  if (!data.success) {
    return res.status(400).json({ error: "Invalid request body" });
  }

  const currentAccount = await db.account.findFirst({
    where: {
      name: data.data.businessName,
    },
  });

  if (!!currentAccount) {
    return res.status(400).json({ error: "Account name already exists" });
  }

  const stripeCustomer = await platformStripeServer.customers.create({
    name: data.data.businessName,
    email: data.data.email || undefined,
    metadata: {
      customerFlow: "true",
    },
  });

  const slugifiedName = slugify(data.data.businessName.toLowerCase());

  const organization = await db.organization.create({
    data: {},
  });

  const account = await db.account.create({
    data: {
      name: data.data.businessName,
      businessEmail: data.data.email,
      customDomain: `${slugifiedName}.evntflow.com`,
      stripeCustomerId: stripeCustomer.id,
      organizationId: organization.id,
    },
  });

  await platformStripeServer.customers.update(stripeCustomer.id, {
    metadata: {
      customerFlow: "true",
      accountId: account.id,
    },
  });

  try {
    const hubspot = new Client({
      accessToken: process.env.HUBSPOT_API_KEY,
    });

    const companyCreate = {
      properties: {
        name: data.data.businessName,
        domain: `${slugifiedName}.evntflow.com` ?? "",
        reference_id: data.data.referrer ?? "",
        closedate: new Date().toISOString(),
      },
    };

    await hubspot.crm.companies.basicApi.create(companyCreate);
  } catch (e) {
    console.error("Error creating company in HubSpot", e);
  }
  const customerSession = await platformStripeServer.customerSessions.create({
    customer: stripeCustomer.id,

    components: {
      pricing_table: {
        enabled: true,
      },
    },
  });

  const hashedPassword = await handlePlainTextPassword(data.data.password);

  const user = await db.user.create({
    data: {
      email: data.data.email,
      password: `unpaid-${hashedPassword}`,
    },
  });

  if (!user) {
    return res.status(500).json({ message: "Failed to create user" });
  }

  const staff = await db.staff.create({
    data: {
      email: data.data.email,
      name: data.data.email,
      password: `unpaid-${hashedPassword}`,
      accountId: account.id,
      userId: user.id,
    },
  });

  const defaultRoles = await db.role.findFirst({
    where: {
      name: "Owner",
    },
  });

  if (!defaultRoles) {
    return res.status(500).json({ message: "No default roles found" });
  }

  await db.staffRole.create({
    data: {
      roleId: defaultRoles.id,
      staffId: staff.id,
    },
  });

  // create default setup surfaces
  const defaultSetupSurface = ["grass", "indoor", "concrete"];
  const boilerPlatePromises = [];
  const defaultSetupSurfacePromises = defaultSetupSurface.map((surface) => {
    return db.setupSurface.create({
      data: {
        name: surface,
        description: `Default setup surface for ${surface}`,
        accountId: account.id,
      },
    });
  });
  boilerPlatePromises.push(...defaultSetupSurfacePromises);

  const now = new Date();

  // create default schedule
  boilerPlatePromises.push({
    data: {
      name: "Default Schedule",
      description: "Default Schedule",
      startTime: now,
      endTime: new Date(9999, 12, 31),
      openTime: new Date(1970, 1, 1, 8, 0, 0),
      closeTime: new Date(1970, 1, 1, 17, 0, 0),
      accountId: account.id,
      days: [
        ScheduleDay.MONDAY,
        ScheduleDay.TUESDAY,
        ScheduleDay.WEDNESDAY,
        ScheduleDay.THURSDAY,
        ScheduleDay.FRIDAY,
        ScheduleDay.SATURDAY,
        ScheduleDay.SUNDAY,
      ],
    },
  });
  await Promise.all(boilerPlatePromises);

  try {
    await createDefaultEmailTemplates(account.id, data.data.businessName);
  } catch (e) {
    console.error("Error creating default email templates", e);
  }

  try {
    // create default pages
    await createDefaultWebsite(account.id, data.data.businessName);
  } catch (e) {
    console.error("Error creating default website", e);
  }

  // Send notification to Slack
  try {
    await sendSignupNotification(
      data.data.businessName,
      data.data.email,
      data.data.referrer
    );
  } catch (e) {
    console.error("Error sending Slack notification:", e);
    // Don't fail the signup process if Slack notification fails
  }

  res.status(200).json({
    accountId: account.id,
    sessionId: customerSession.client_secret,
  });
};

export default POST;
