import { NextApiResponse } from "next";
import { getActiveOrCompleteOrders } from "~/server/lib/orderUtil";
import { Prisma } from ".prisma/client";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const orderWithCustomerAndPaymentsAndAddress =
  Prisma.validator<Prisma.OrderInclude>()({
    Customer: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        company: true,
      },
    },
    Contract: {
      select: {
        id: true,
        signed: true,
      },
    },
    eventAddress: true,
    setupSurface: {
      select: {
        id: true,
        name: true,
      },
    },
    OrderProduct: {
      select: {
        productId: true,
        quantity: true,
      },
    },
  });

export type OrderWithCustomerAndPaymentsAndAddress = Prisma.OrderGetPayload<{
  include: typeof orderWithCustomerAndPaymentsAndAddress;
}>;
const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startTime } = req.query;
  const date = startTime as string;
  const startDate = new Date(date);
  startDate.setUTCSeconds(0);
  startDate.setUTCMilliseconds(0);
  const endOfStartDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);

  const orders = await getActiveOrCompleteOrders(
    req.organizationId,
    startDate,
    endOfStartDate,
    {},
    {
      Customer: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          company: true,
        },
      },
      Contract: {
        select: {
          id: true,
          signed: true,
        },
      },
      eventAddress: true,
      setupSurface: {
        select: {
          id: true,
          name: true,
        },
      },
      OrderProduct: {
        select: {
          productId: true,
          quantity: true,
        },
      },
    },
  );
  if (!orders) {
    res.status(200).json({ orders: [] });
    return;
  }
  res.status(200).json({ orders });
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "read",
  },
  handler,
);
