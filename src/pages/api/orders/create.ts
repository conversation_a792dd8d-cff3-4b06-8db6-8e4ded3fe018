import { NextApiResponse } from "next";
import { array, boolean, date, mixed, number, object, string } from "yup";
import { db } from "~/server/db";
import { sendReactEmail } from "~/server/email/sendMail";
import { RECEIPT_EMAIL } from "../../../../emails/ReceiptEmail";
import { createOrder, OrderDiscountInput } from "~/server/lib/orderUtil";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { AddressValidation } from "~/server/lib/location/types";
import {
  handleNewOrder,
  handleOrderUpdate,
} from "~/server/services/orderEvents";
import { ChargeType, DiscountType, OrderFeeType } from "@prisma/client";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { getCachedAccountFromId } from "~/server/account/lookup";
import { sendMessage } from "~/pages/api/phone/text/[conversationId]/sendMessage";
import { formatTimeRange } from "~/server/lib/time";

const OrderCreateRequestBodySchema = object().shape({
  customerId: string().required(),
  products: array()
    .of(
      object().shape({
        id: number().required(),
        pricePaid: number().min(-1).required(),
        quantity: number().min(0).required(),
      }),
    )
    .required(),
  eventStartTime: date().required(),
  eventEndTime: date().required(),
  customerNotes: string().nullable().optional(),
  internalNotes: string().nullable().optional(),

  damageWaiverApplied: boolean().required(),
  taxRate: number().nullable().required(),
  fees: array(
    object().shape({
      type: mixed<OrderFeeType>().oneOf(Object.values(OrderFeeType)).required(),
      amount: number().required(),
      name: string().required().nullable(),
      taxable: boolean().required(),
    }),
  ).required(),
  discount: array(
    object().shape({
      type: string().oneOf(["COUPON", "DISCOUNT"]).required(),
      entityId: number().required(),
      amount: number().required(),
      chargeType: string().oneOf(["PERCENTAGE", "DOLLAR"]).required(),
    }),
  ),
  setupSurface: number().required(),
  taxExempt: boolean().required().default(false),

  location: AddressValidation.required(),
  sendNotification: boolean().optional().default(true),
});

export type OrderCreateRequest =
  typeof OrderCreateRequestBodySchema.__outputType;

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const account = await getCachedAccountFromId(req.accountId);

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const body: OrderCreateRequest = req.body;

  const [products, customer, setupSurface] = await db.$transaction([
    db.product.findMany({
      where: {
        id: {
          in: body.products.map((p) => p.id),
        },
        archived: false,
        organizationId: account.organizationId,
      },
    }),
    db.customer.findFirst({
      where: {
        account: account,
        id: body.customerId,
      },
    }),
    db.setupSurface.findFirst({
      where: {
        accountId: account.id,
        archived: false,
        id: body.setupSurface,
      },
    }),
  ]);

  if (!products) {
    res.status(404).json({ error: "Product not found" });
    return;
  }

  if (!customer) {
    res.status(404).json({ error: "Customer not found" });
    return;
  }

  if (!setupSurface) {
    res.status(404).json({ error: "Setup surface not found" });
    return;
  }

  const bodyDiscounts: OrderDiscountInput[] =
    body.discount?.map((item) => {
      const itemDiscount: OrderDiscountInput = {
        type: item.type === "COUPON" ? DiscountType.COUPON : DiscountType.SALE,
        entityId: item.entityId,
        amount: item.amount,
        chargeType:
          item.chargeType === "PERCENTAGE"
            ? ChargeType.PERCENTAGE
            : ChargeType.DOLLAR,
      };
      return itemDiscount;
    }) ?? [];

  const result = await createOrder(
    account.id,
    account.organizationId,
    (await findOrInsertAddress(body.location)).id,
    setupSurface.id,
    body.products.map((p) => ({
      productId: p.id,
      quantity: p.quantity,
      pricePaid: p.pricePaid,
      productName: products.find((prod) => prod.id === p.id)?.name || "",
    })),
    bodyDiscounts,
    body.fees,
    {
      enabled: body.damageWaiverApplied,
      percentage: account.damageWaiverRate || 0,
    },
    body.taxExempt ? null : body.taxRate,
    body.eventStartTime,
    body.eventEndTime,
    customer.id,
    body.customerNotes ?? undefined,
    body.internalNotes ?? undefined,
    true,
    "ACTIVE",
  );
  const success = result.success;

  if (result.error) {
    res.status(400).json({ error: result.error });
    return;
  }

  if (!success) {
    res.status(500).json({ error: "Error creating order" });
    return;
  }

  if (success?.emailProps) {
    if (body.sendNotification) {
      await handleOrderUpdate(
        success.order.id,
        account.id,
        success?.emailProps,
      );
      await handleNewOrder(success.order, account.id, success?.emailProps);
      if (customer?.phoneNumber) {
        const accountData = await db.phoneAccount.findFirst({
          where: {
            accountId: account.id,
          },
          include: {
            account: true,
          },
        });

        if (accountData?.phoneNumber && accountData?.messagingApproved) {
          await sendMessage(
            customer?.phoneNumber,
            account.id,
            `Your order #${success.order.id} with ${
              account.name
            } for ${formatTimeRange(
              success.order.startTime,
              success.order.endTime,
              account.businessTimezone,
            )} has been created. You can view your order details here: ${
              success.emailProps.paymentLink
            }`,
          );
        }
      }
    } else if (account.businessEmail) {
      success.emailProps.silent = true;
      await sendReactEmail(
        false,
        account,
        account.businessEmail,
        RECEIPT_EMAIL,
        success?.emailProps,
        [],
        [],
      );
    }
  }
  return res.status(200).json({ success: true, orderId: success.order.id });
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "write",
  },
  withMethods(["POST"], withValidation(OrderCreateRequestBodySchema, handler)),
);
