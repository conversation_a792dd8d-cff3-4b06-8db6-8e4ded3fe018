import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState, Prisma } from ".prisma/client";
import { checkPermission } from "~/pages/api/permissions";

const orderWithCustomerAndPayments = Prisma.validator<Prisma.OrderInclude>()({
  Customer: {
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      company: true,
    },
  },
  Contract: {
    select: {
      id: true,
      signed: true,
    },
  },
});

export type OrderWithCustomerAndPayments = Prisma.OrderGetPayload<{
  include: typeof orderWithCustomerAndPayments;
}>;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.orders",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const data = await db.order.findMany({
    where: {
      accountId: permission.accountId,
      OR: [
        {
          state: OrderState.ACTIVE,
        },
        {
          state: OrderState.CANCELLED,
        },
        {
          state: OrderState.COMPLETED,
        },
      ],
    },
    include: orderWithCustomerAndPayments,
  });

  return res.status(200).json({ orders: data });
};

export default handler;
