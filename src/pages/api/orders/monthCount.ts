import { NextApiResponse } from "next";
import { getActiveOrCompleteOrders } from "~/server/lib/orderUtil";
import { Order, Schedule } from ".prisma/client";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

export type MonthCountResponse = {
  activeOrders: Order[];
  startOfMonth: Date;
  endOfMonth: Date;
  activeSchedule: Schedule[];
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { specificDate } = req.query;

  let startOfMonth = new Date();
  if (specificDate) {
    startOfMonth = new Date(specificDate as string);
  }

  startOfMonth.setTime(startOfMonth.getTime() - 7 * 24 * 60 * 60 * 1000);

  const endOfMonth = new Date(
    startOfMonth.getTime() + 45 * 24 * 60 * 60 * 1000,
  );
  const activeOrders = await getActiveOrCompleteOrders(
    req.organizationId,
    startOfMonth,
    endOfMonth,
  );

  // const activeSchedule = await db.schedule.findMany({
  //   where: {
  //     accountId: permission.accountId,
  //   },
  //   orderBy: {
  //     priority: "asc",
  //   },
  // });

  res.json({
    activeOrders,
    startOfMonth,
    endOfMonth,
    // activeSchedule,
  } as MonthCountResponse);
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "read",
  },
  handler,
);
