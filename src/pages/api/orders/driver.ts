import { NextApiResponse } from "next";
import { OrderState, Prisma } from ".prisma/client";
import { db } from "~/server/db";
import { LogisticTimeline } from "~/pages/api/driver/timelines";
import { toAddressType } from "~/server/lib/location/util";
import { LogisticTaskItem } from "~/components/driver/types";
import { createId } from "@paralleldrive/cuid2";
import { ITEM_LIST_TYPE } from "~/pages/orders/driver/calendar";
import { endOfDay, isDateBetweenOrEqual } from "~/server/lib/time";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { Address } from "@prisma/client";

const orderWithAddress = Prisma.validator<Prisma.OrderInclude>()({
  eventAddress: true,
  setupSurface: {
    select: {
      id: true,
      name: true,
    },
  },
  OrderProduct: {
    select: {
      product: {
        select: {
          id: true,
          setupTimeMinutes: true,
          takeDownTimeMinutes: true,
        },
      },
    },
  },
});

export type OrderWithAddress = Prisma.OrderGetPayload<{
  include: typeof orderWithAddress;
}>;

export type DriverOrderType = OrderWithAddress & {
  totalSetupTimeMinutes: number;
  totalTakedownTimeMinutes: number;
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startTime } = req.query;
  const date = startTime as string;
  const startDate = new Date(date);

  try {
    const {
      tasks,
      timelines: logisticalTimeline,
      orders: orderResponse,
    } = await getTasksForDate(req.organizationId, startDate);

    if (!orderResponse || orderResponse.length === 0) {
      return res.status(200).json({
        orders: [],
        timelines: [],
        tasks: [],
      });
    }

    res.status(200).json({
      orders: orderResponse,
      timelines: logisticalTimeline,
      tasks: tasks,
    });
  } catch (err) {
    console.error("Error fetching tasks for date:", err);
    return res.status(200).json({
      orders: [],
      timelines: [],
      tasks: [],
    });
  }
};

export const getTasksForDate = async (
  organizationId: string,
  date: Date,
): Promise<{
  tasks: LogisticTaskItem[];
  timelines: LogisticTimeline[];
  orders: DriverOrderType[];
}> => {
  const startDate = new Date(date);
  const endOfStartDate = endOfDay(new Date(date));

  const orderResponse = await getAndFormatOrders(
    organizationId,
    startDate,
    endOfStartDate,
  );

  if (!orderResponse) {
    throw new Error("No orders found for the given date");
  }

  const { tasks, timelines: logisticalTimeline } = await getTasksFromTimelines(
    organizationId,
    startDate,
    endOfStartDate,
    orderResponse,
  );

  // find orders that aren't in any timeline and add them as tasks
  orderResponse
    .filter(
      (order) => tasks.filter((item) => item.orderId === order.id).length !== 2,
    )
    .forEach((order) => {
      const tasksForThisOrder = tasks.filter(
        (item) => item.orderId === order.id,
      );
      if (
        isDateBetweenOrEqual(order.endTime, startDate, endOfStartDate) &&
        tasksForThisOrder.find(
          (item) => item.actionTime.getTime() === order.endTime.getTime(),
        ) === undefined
      ) {
        tasks.push(createPickupTaskFromOrder(order));
      }
      const taskForDropoff = tasksForThisOrder.find(
        (item) => item.actionTime.getTime() === order.startTime.getTime(),
      );
      if (
        isDateBetweenOrEqual(order.startTime, startDate, endOfStartDate) &&
        taskForDropoff === undefined
      ) {
        tasks.push(createDropoffTaskFromOrder(order));
      }
    });

  return {
    tasks: tasks,
    timelines: logisticalTimeline,
    orders: orderResponse,
  };
};

export const getTasksFromTimelines = async (
  organizationId: string,
  startDate: Date,
  endOfStartDate: Date,
  orderResponse: { id: number; eventAddress: Address }[],
): Promise<{
  tasks: LogisticTaskItem[];
  timelines: LogisticTimeline[];
}> => {
  const timelines = await db.logisticTimeline.findMany({
    where: {
      organizationId: organizationId,
      startTime: {
        gte: startDate,
        lt: endOfStartDate,
      },
    },
    select: {
      LogicistcTimelineStaff: true,
      id: true,
      startTime: true,
      origin: true,
      tasks: true,
    },
  });

  const tasks: LogisticTaskItem[] = [];

  for (const timeline of timelines ?? []) {
    if (!timeline.tasks || !Array.isArray(timeline.tasks)) {
      continue;
    }
    const timelineTasks = timeline.tasks as Prisma.JsonArray;
    for (const task of timelineTasks) {
      if (typeof task !== "object") {
        continue;
      }
      const timelineTask = task as Prisma.JsonObject;

      // handle the address, if it's an order, get the address from the order
      // otherwise look it up on the fly.
      const orderId = timelineTask?.orderId
        ? (timelineTask.orderId as number)
        : null;

      let address = null;
      if (!orderId) {
        const addressId = timelineTask.address as number;
        address = await db.address.findFirst({
          where: {
            id: addressId,
          },
        });
      } else {
        address = orderResponse.find((order) => order.id === orderId)
          ?.eventAddress;
      }

      if (!address) {
        continue;
      }

      tasks.push({
        id: timelineTask.id as string,
        index: timelineTask.index as number,
        displayAction: timelineTask.displayAction as string,
        timelineId: timelineTask.timelineId as string,
        durationMinutes: timelineTask.durationMinutes as number,
        durationAffectsTime: timelineTask.durationAffectsTime as boolean,
        actionTimeFlexibilityMinutes:
          timelineTask.actionTimeFlexibilityMinutes as number,
        actionTime: new Date(timelineTask.actionTime as string),
        orderId: orderId,
        notes: timelineTask?.notes ? (timelineTask?.notes as string) : null,
        address: address,
      });
    }
  }

  const logisticalTimeline: LogisticTimeline[] = timelines
    .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
    .map((timeline, index) => ({
      ...timeline,
      tasks: undefined,
      staff: timeline?.LogicistcTimelineStaff?.map((s) => s.staffId) || [],
      origin: toAddressType(timeline.origin),
      index,
    }));

  return {
    tasks,
    timelines: logisticalTimeline,
  };
};

export const getAndFormatOrders = async (
  organizationId: string,
  startDate: Date,
  endOfStartDate: Date,
): Promise<DriverOrderType[] | null> => {
  const orders = await db.order.findMany({
    where: {
      OR: [
        {
          startTime: {
            gte: startDate,
            lte: endOfStartDate,
          },
        },
        {
          endTime: {
            gte: startDate,
            lte: endOfStartDate,
          },
        },
      ],
      AND: {
        organizationId: organizationId,
        state: OrderState.ACTIVE,
      },
    },
    include: {
      eventAddress: true,
      setupSurface: {
        select: {
          id: true,
          name: true,
        },
      },
      OrderProduct: {
        select: {
          product: {
            select: {
              id: true,
              takeDownTimeMinutes: true,
              setupTimeMinutes: true,
            },
          },
        },
      },
    },
  });

  if (!orders || orders.length === 0) {
    return null;
  }

  return orders.map((item: any) => {
    const order = item as OrderWithAddress;
    let takeDownTimeMinutes = 0;
    let totalSetupTimeMinutes = 0;
    order.OrderProduct.forEach((orderProduct) => {
      totalSetupTimeMinutes += orderProduct.product.setupTimeMinutes;
      takeDownTimeMinutes += orderProduct.product.takeDownTimeMinutes;
    });
    return {
      ...order,
      totalSetupTimeMinutes,
      totalTakedownTimeMinutes: takeDownTimeMinutes,
    };
  });
};

export const createDropoffTaskFromOrder = (
  order: DriverOrderType,
): LogisticTaskItem => {
  return {
    id: createId(),
    index: 0,
    displayAction: "Drop Off",
    timelineId: ITEM_LIST_TYPE,
    durationMinutes: order.totalSetupTimeMinutes,
    durationAffectsTime: true,
    actionTimeFlexibilityMinutes: -8 * 60,
    actionTime: new Date(order.startTime),
    orderId: order.id,
    notes:
      order.customerNotes || order.internalNotes
        ? `${order.internalNotes ? `${order.internalNotes} ` : ""}${
            order.customerNotes ?? ""
          }`
        : null,
    address: order.eventAddress,
  };
};

export const createPickupTaskFromOrder = (
  order: DriverOrderType,
): LogisticTaskItem => {
  return {
    id: createId(),
    index: 0,
    displayAction: "Pickup",
    timelineId: ITEM_LIST_TYPE,
    durationMinutes: order.totalTakedownTimeMinutes,
    durationAffectsTime: false,
    actionTimeFlexibilityMinutes: 6 * 60,
    actionTime: new Date(order.endTime),
    orderId: order.id,
    notes:
      order.customerNotes || order.internalNotes
        ? `${order.internalNotes ? `${order.internalNotes} ` : ""}${
            order.customerNotes ?? ""
          }`
        : null,
    address: order.eventAddress,
  };
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "read",
  },
  handler,
);
