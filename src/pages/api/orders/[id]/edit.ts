import { NextApiRequest, NextApiResponse } from "next";
import { array, boolean, date, mixed, number, object, string } from "yup";
import { db } from "~/server/db";
import { sendReactEmail } from "~/server/email/sendMail";
import {
  RECEIPT_EMAIL,
  ReceiptEmailProps,
} from "../../../../../emails/ReceiptEmail";
import {
  orderContractItems,
  OrderDiscountInput,
  ProductCharge,
  updateOrderTotalValues,
} from "~/server/lib/orderUtil";
import { AddressValidation } from "~/server/lib/location/types";
import {
  areAddressesEqual,
  findOrInsertAddress,
} from "~/server/lib/location/util";
import { handleBasicOrder } from "~/pages/api/orders/[id]/index";
import { getAccountLogo } from "~/server/lib/logo";
import { handleOrderUpdate } from "~/server/services/orderEvents";
import { ChargeType, DiscountType, OrderFeeType } from "@prisma/client";
import { getCachedAccountFromId } from "~/server/account/lookup";
import { sendMessage } from "~/pages/api/phone/text/[conversationId]/sendMessage";

const OrderCreateRequestBodySchema = object().shape({
  products: array()
    .of(
      object().shape({
        id: number().required(),
        pricePaid: number().min(-1).required(),
        quantity: number().min(0).required(),
      }),
    )
    .required(),
  eventStartTime: date().required(),
  eventEndTime: date().required(),
  customerNotes: string().nullable().optional(),
  internalNotes: string().nullable().optional(),

  damageWaiverApplied: boolean().required(),
  taxRate: number().nullable().required(),
  fees: array(
    object().shape({
      type: mixed<OrderFeeType>().oneOf(Object.values(OrderFeeType)).required(),
      amount: number().required(),
      name: string().required().nullable(),
      taxable: boolean().default(true).required(),
    }),
  ).required(),
  discount: array(
    object().shape({
      type: string().oneOf(["COUPON", "DISCOUNT"]).required(),
      entityId: number().required(),
      amount: number().required(),
      chargeType: string().oneOf(["PERCENTAGE", "DOLLAR"]).required(),
    }),
  ),
  setupSurface: number().required(),
  taxExempt: boolean().required().default(false),

  location: AddressValidation.required(),
  sendNotification: boolean().optional().default(true),
});

export type OrderCreateRequest =
  typeof OrderCreateRequestBodySchema.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicOrderRequest = await handleBasicOrder(req, res, "write");

  if (!basicOrderRequest) {
    return;
  }

  const account = await getCachedAccountFromId(basicOrderRequest.accountId);
  if (!account) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: basicOrderRequest.orderId,
      accountId: account.id,
    },
    include: {
      eventAddress: true,
      Customer: {
        select: {
          email: true,
          phoneNumber: true,
        },
      },
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const data = await req.body;

  let body: OrderCreateRequest;
  try {
    body = await OrderCreateRequestBodySchema.validate(data);
  } catch (e: any) {
    console.log(e);
    res.status(400).json({ error: `Invalid request body ${e.message}` });
    return;
  }

  if (Object.values(body.location).length === 0) {
    res.status(400).json({ error: "Invalid location" });
    return;
  }

  let newAddressId: number | undefined;
  if (!areAddressesEqual(order.eventAddress, body.location)) {
    const newAddress = await findOrInsertAddress(body.location);
    newAddressId = newAddress.id;
  }

  const bodyDiscounts: OrderDiscountInput[] =
    body.discount?.map((item) => {
      const itemDiscount: OrderDiscountInput = {
        type: item.type === "COUPON" ? DiscountType.COUPON : DiscountType.SALE,
        entityId: item.entityId,
        amount: item.amount,
        chargeType:
          item.chargeType === "PERCENTAGE"
            ? ChargeType.PERCENTAGE
            : ChargeType.DOLLAR,
      };
      return itemDiscount;
    }) ?? [];

  const {
    order: orderValues,
    paymentInfo,
    discounts,
    fees,
  } = await updateOrderTotalValues(
    account.id,
    body.products.map((product) => {
      return {
        productId: product.id,
        quantity: product.quantity,
        pricePaid: product.pricePaid,
      };
    }),
    bodyDiscounts || [],
    body.fees,
    { enabled: body.damageWaiverApplied, percentage: account.damageWaiverRate },
    body.taxExempt ? null : body.taxRate,
    order.totalPaid,
  );

  const updatedOrder = await db.order.update({
    where: {
      id: order.id,
    },
    data: {
      ...orderValues,
      eventAddressId: newAddressId,
      startTime: body.eventStartTime,
      endTime: body.eventEndTime,
      customerNotes: body.customerNotes ?? undefined,
      internalNotes: body.internalNotes ?? undefined,
      setupSurfaceId: body.setupSurface,
      taxExempt: body.taxExempt,
    },
  });

  // We DO NOT want to check for archival here because we want to be able to edit historical orders
  const products = await db.product.findMany({
    where: {
      id: {
        in: body.products.map((product) => product.id),
      },
      organizationId: account.organizationId,
    },
  });
  if (products.length !== body.products.length) {
    res.status(400).json({ error: "Invalid products" });
    return;
  }
  // eventually this will cause locking issues we'll want to do diffbased updates
  await db.orderProduct.deleteMany({
    where: {
      orderId: order.id,
    },
  });

  await db.orderFee.deleteMany({
    where: {
      orderId: order.id,
    },
  });

  await db.orderDiscount.deleteMany({
    where: {
      orderId: order.id,
    },
  });

  const orderProductData: ProductCharge[] = [];
  data.products.forEach(
    (product: { id: number; quantity: number; pricePaid: number }) => {
      orderProductData.push({
        orderId: order.id,
        productId: product.id,
        quantity: product.quantity,
        pricePaid: product.pricePaid,
      });
    },
  );

  await db.orderFee.createMany({
    data: fees.map((fee) => {
      return {
        orderId: order.id,
        amount: fee.amount,
        name: fee.name,
        type: fee.type,
        taxable: fee.taxable,
      };
    }),
  });

  if (discounts.length > 0) {
    await db.orderDiscount.createMany({
      data: discounts.map((discount) => {
        return {
          orderId: order.id,
          amount: discount.amount,
          chargeType:
            discount.chargeType === "PERCENTAGE"
              ? ChargeType.PERCENTAGE
              : ChargeType.DOLLAR,
          type: discount.type,
          couponId: discount.type === "COUPON" ? discount.entityId : undefined,
          saleId: discount.type === "SALE" ? discount.entityId : undefined,
        };
      }),
    });
  }

  await db.orderProduct.createMany({
    data: orderProductData,
  });

  const contract = await db.contract.findFirst({
    where: {
      orderId: order.id,
    },
  });

  if (contract) {
    const contractSharedItems = {
      rentalStartDate: updatedOrder.startTime,
      rentalEndDate: updatedOrder.endTime,
      ...paymentInfo,
    };
    const emailProps: ReceiptEmailProps = {
      accountTimezone: account.businessTimezone,
      newOrder: false,
      orderId: order.id.toString(),
      accountName: account.name,
      accountLogo: await getAccountLogo(account),
      contractItems: orderContractItems(orderProductData, products),
      paidItems: [],
      contractSharedItems: contractSharedItems,
      contractLink: `https://${
        account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
      }/post-order/contract/${order.id}/${contract.id}`,
      paymentLink: `https://${
        account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
      }/post-order/orders/${order.id}/${order.customerId}`,
      accountMinimumDeposit: account.minimumOrderPaymentPercentage,
    };
    if (body.sendNotification) {
      await handleOrderUpdate(
        updatedOrder.id,
        updatedOrder.accountId,
        emailProps,
      );
      if (order.Customer?.phoneNumber) {
        const accountData = await db.phoneAccount.findFirst({
          where: {
            accountId: account.id,
          },
          include: {
            account: true,
          },
        });

        if (accountData?.phoneNumber && accountData?.messagingApproved) {
          await sendMessage(
            order.Customer.phoneNumber,
            account.id,
            `Your order #${updatedOrder.id} with ${account.name} has been updated. You can view your order details here: ${emailProps.paymentLink}`,
          );
        }
      }
    } else if (account.businessEmail) {
      emailProps.silent = true;
      await sendReactEmail(
        false,
        account,
        account.businessEmail,
        RECEIPT_EMAIL,
        emailProps,
        [],
        [],
      );
    }
  }
  return res.json({ success: true, orderId: order.id });
};

export default handler;
