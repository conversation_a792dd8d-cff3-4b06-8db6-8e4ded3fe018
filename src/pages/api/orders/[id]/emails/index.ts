import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";

export type OrderEmailLog = {
  id: number;
  orderId: number;
  sentAt: Date;
  name: string;
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({
      error: "Missing id",
    });
  }
  const numberId = parseInt(id as string, 10);

  const order = await db.order.findFirst({
    where: {
      accountId: req.accountId,
      id: numberId,
    },
  });

  if (!order) {
    return res.status(404).json({
      error: "Order not found",
    });
  }

  const orderEmails = await db.orderEmailLog.findMany({
    where: {
      orderId: order.id,
    },
    orderBy: {
      sentAt: "desc",
    },
    include: {
      customEmailDelivery: {
        select: {
          name: true,
        },
      },
    },
  });

  const emails: OrderEmailLog[] = orderEmails.map((email) => ({
    id: email.customEmailDeliveryId,
    orderId: email.orderId,
    sentAt: email.sentAt,
    name: email.customEmailDelivery?.name ?? "Unknown",
  }));

  res.status(200).json(emails);
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "read",
  },
  handler,
);
