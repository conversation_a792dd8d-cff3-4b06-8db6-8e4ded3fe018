import {
  AuthorizedNextApiRequest,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { number, object } from "yup";
import { db } from "~/server/db";
import { Maily } from "~/lib/email/maily";
import { JSONContent } from "@tiptap/react";
import { sendMail } from "~/server/email/sendMail";
import { captureException } from "@sentry/core";
import {
  orderContractItems,
  orderInfoToSharedItems,
  orderPaidItems,
} from "~/server/lib/orderUtil";

export const SEND_EMAIL_SCHEMA = object().shape({
  templateId: number().required(),
});

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const orderId = req.query.id as string;

  if (!orderId) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const numberId = parseInt(orderId);

  if (isNaN(numberId)) {
    res.status(400).json({ error: "Invalid order id" });
    return;
  }

  const order = await db.order.findUnique({
    where: {
      id: numberId,
      accountId: req.accountId,
    },
    include: {
      Customer: true,
      account: true,
      Contract: true,
      PaymentDetails: true,
      OrderFee: true,
      OrderDiscount: true,
      OrderProduct: {
        include: {
          product: true,
        },
      },
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const template = await db.customEmailDelivery.findUnique({
    where: {
      accountId: req.accountId,
      id: req.body.templateId,
    },
  });

  if (!template) {
    res.status(404).json({ error: "Template not found" });
    return;
  }

  const emailContent: JSONContent = JSON.parse(template.text);

  if (!emailContent) {
    res.status(400).json({ error: "Invalid email content" });
    return;
  }

  const mail = new Maily(emailContent);

  const paymentDetails = order.PaymentDetails;
  // intentionally not checking for archived products because we want to show the customer what they ordered even
  // if the product is no longer available
  const products = await db.product.findMany({
    where: {
      accountId: req.accountId,
    },
  });
  const orderProducts = order.OrderProduct;

  mail.setContractItems({
    depositPercentage: order.account.minimumOrderPaymentPercentage,
    contractItems: orderContractItems(orderProducts, products),
    paidItems: orderPaidItems(paymentDetails),
    contractSharedItems: orderInfoToSharedItems(
      order,
      order.OrderFee,
      order.OrderDiscount,
    ),
    refundAmount: 0,
    accountTimezone: order.account.businessTimezone,
  });
  mail.setVariableValuesFromCustomer(order.Customer);
  mail.setVariableValuesFromOrder(order);
  mail.setVariableValues({
    ORDER_PAYMENT_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/orders/${order.id}/${order.customerId}`,
    ORDER_CONTRACT_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/contract/${order.id}/${order.Contract?.at(0)?.id}`,
    ORDER_SURVEY_LINK: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/survey/${order.id}/${order.customerId}`,
  });

  const email = await mail.renderAsync({});
  try {
    await sendMail(
      true,
      req.accountId,
      order.account.name,
      order.account.businessEmail,
      order.Customer.email,
      {
        emailSender: "receipt",
        subject: template.subject,
        sendAdminCopy: true,
      },
      email,
      undefined,
      undefined,
      undefined,
    );
  } catch (e) {
    captureException(e);
    res.status(500).json({ error: "Failed to send email" });
    return;
  }

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.orders",
    action: "execute",
  },
  withValidation(SEND_EMAIL_SCHEMA, handler),
);
