import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicOrder,
  handleCancelOrder,
} from "~/pages/api/orders/[id]/index";
import { db } from "~/server/db";
import { mixed, object } from "yup";
import { OrderState } from ".prisma/client";
import { EmailAction } from "@prisma/client";

export const ORDER_UPDATE_STATE = object().shape({
  state: mixed<OrderState>().oneOf(Object.values(OrderState)).required(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "write");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  const order = await db.order.findUnique({
    where: {
      id: orderId,
      accountId: accountId,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  let state: OrderState | undefined = undefined;
  try {
    const data = await ORDER_UPDATE_STATE.validate(req.body);

    state = data.state;
  } catch (e) {
    res.status(400).json({ error: "Invalid state" });
    return;
  }

  if (!state) {
    res.status(400).json({ error: "Invalid state" });
    return;
  }

  if (state === OrderState.CANCELLED) {
    await handleCancelOrder(req, res, orderBasics);
    return;
  }

  // don't send out abandoned cart emails if the order is no longer in that state
  if (
    order.state === OrderState.ABANDONED_QUOTE ||
    order.state === OrderState.QUOTE
  ) {
    await db.scheduledEmail.deleteMany({
      where: {
        accountId: accountId,
        orderId: order.id,
        sent: false,
        action: EmailAction.ABANDON_CART,
      },
    });
  }

  await db.order.update({
    where: {
      accountId: accountId,
      id: orderId,
    },
    data: {
      state: state,
    },
  });

  res.status(200).json({ success: true });
};

export default handler;
