import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]/index";
import { db } from "~/server/db";
import {
  RECEIPT_EMAIL,
  ReceiptEmailProps,
} from "../../../../../emails/ReceiptEmail";
import { getAccountLogo } from "~/server/lib/logo";
import {
  orderContractItems,
  orderInfoToSharedItems,
  orderPaidItems,
} from "~/server/lib/orderUtil";
import { sendReactEmail } from "~/server/email/sendMail";
import {
  handleNewOrder,
  handleOrderPayment,
} from "~/server/services/orderEvents";
import { sendMessage } from "~/pages/api/phone/text/[conversationId]/sendMessage";
import { formatTimeRange } from "~/server/lib/time";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "execute");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;
  const { silent, newOrder } = req.query;

  const result = await sendOrderReceiptAPI(
    orderId,
    accountId,
    silent === "true",
    newOrder === "true",
  );

  if (!result.success) {
    res.status(400).json({ error: result.message });
    return;
  }

  res.status(200).json({ success: true });
};

type SendProps = {
  order: any;
  accountId: number;
  emailProps: ReceiptEmailProps;
};

export const sendOrderReceiptAPI = async (
  orderId: number,
  accountId: number,
  silent: boolean,
  newOrder: boolean,
): Promise<{ success: boolean; message: string }> => {
  const order = await db.order.findUnique({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      Customer: true,
      account: true,
      OrderFee: true,
      OrderDiscount: true,
      PaymentDetails: {
        include: {
          PaymentRefund: true,
        },
      },
      Contract: true,
      OrderProduct: {
        include: {
          product: true,
        },
      },
    },
  });

  if (!order) {
    return { success: false, message: "Order not found" };
  }

  let refundedAmount: number | undefined = order.PaymentDetails.reduce(
    (acc, pd) => {
      return acc + pd.PaymentRefund.reduce((acc, pr) => acc + pr.amount, 0);
    },
    0,
  );

  if (refundedAmount === 0) {
    refundedAmount = undefined;
  }

  const contract = order.Contract?.at(0);

  const emailProps: ReceiptEmailProps = {
    accountMinimumDeposit: order.account.minimumOrderPaymentPercentage,
    orderId: order.id.toString(),
    accountName: order.account.name,
    accountLogo: await getAccountLogo(order.account),
    silent: silent,
    contractItems: orderContractItems(
      order.OrderProduct,
      order.OrderProduct.map((pd) => pd.product),
    ),
    paidItems: orderPaidItems(order.PaymentDetails),
    refundedAmount: refundedAmount,
    contractSharedItems: orderInfoToSharedItems(
      order,
      order.OrderFee,
      order.OrderDiscount,
    ),
    contractLink: contract
      ? `https://${
          order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
        }/post-order/contract/${order.id}/${contract.id}`
      : "No contract found.",
    paymentLink: `https://${
      order.account?.customDomain ?? process.env.NEXT_PUBLIC_SELF_URL
    }/post-order/orders/${order.id}/${order.customerId}`,
    accountTimezone: order.account.businessTimezone,
    newOrder: newOrder,
  };

  await send({ order, accountId, emailProps });
  return { success: true, message: "Email sent" };
};

const send = async ({ order, accountId, emailProps }: SendProps) => {
  if (emailProps.silent) {
    await sendReactEmail(
      false,
      order.account,
      order.account.businessEmail || "",
      RECEIPT_EMAIL,
      emailProps,
      [],
      [],
    );
  } else {
    await handleOrderPayment(order.id, accountId, emailProps);
    if (emailProps.newOrder) {
      await handleNewOrder(order, accountId, emailProps);
      if (order.customer?.phoneNumber) {
        const accountData = await db.phoneAccount.findFirst({
          where: {
            accountId: order.account.id,
          },
          include: {
            account: true,
          },
        });

        if (accountData?.phoneNumber && accountData?.messagingApproved) {
          await sendMessage(
            order.customer?.phoneNumber,
            accountId,
            `Your order #${order.id} with ${
              order.account.name
            } for ${formatTimeRange(
              order.startTime,
              order.endTime,
              order.account.businessTimezone,
            )} has been created. You can view your order details here: ${
              emailProps.paymentLink
            }`,
          );
        }
      }
    }
  }
};

export default handler;
