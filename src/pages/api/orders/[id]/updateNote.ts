import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]/index";
import { db } from "~/server/db";
import { object, string } from "yup";

export const ORDER_UPDATE_INTERNAL_NOTE = object().shape({
  internalNote: string().required(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "write");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  const order = await db.order.findUnique({
    where: {
      id: orderId,
      accountId: accountId,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  let newNote: string | undefined = undefined;
  try {
    const data = await ORDER_UPDATE_INTERNAL_NOTE.validate(req.body);

    newNote = data.internalNote;
  } catch (e) {
    res.status(400).json({ error: "Invalid state" });
    return;
  }

  if (!newNote) {
    res.status(400).json({ error: "Invalid state" });
    return;
  }

  await db.order.update({
    where: {
      accountId: accountId,
      id: orderId,
    },
    data: {
      internalNotes: newNote,
    },
  });

  res.status(200).json({ success: true });
};

export default handler;
