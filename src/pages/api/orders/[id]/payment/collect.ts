import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]";
import { db } from "~/server/db";
import {
  ORDER_PAYMENT_SCHEMA,
  OrderPaymentValues,
} from "~/components/Order/PayAction";
import { getCurrencyValue } from "~/server/lib/currency";
import { OrderState } from ".prisma/client";
import { sendOrderReceiptAPI } from "~/pages/api/orders/[id]/sendReceipt";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "read");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      Customer: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  let body: OrderPaymentValues | undefined = undefined;
  try {
    body = await ORDER_PAYMENT_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (e) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  if (
    body.paymentMethod !== "cash" &&
    body.paymentMethod !== "check" &&
    body.paymentMethod !== "otherCard"
  ) {
    res.status(400).json({ error: "Invalid payment method" });
    return;
  }

  await db.paymentDetails.create({
    data: {
      method: body.paymentMethod,
      methodId: body.paymentMethodId || "cash",
      amount: getCurrencyValue(body.paymentAmount),
      tip: body.tipAmount ? getCurrencyValue(body.tipAmount) : null,
      orderId: order.id,
      customerId: order.customerId,
    },
  });

  const newTotalPaid = order.totalPaid + body.paymentAmount;
  await db.order.update({
    where: {
      id: order.id,
    },
    data: {
      totalPaid: newTotalPaid,
      state: OrderState.ACTIVE,
    },
  });

  await sendOrderReceiptAPI(order.id, accountId, body.silent, false);

  res.status(200).json({ success: true });
};

export default handler;
