import { NextApiResponse } from "next";
import { db } from "~/server/db";
import {
  findStripeCustomer,
  getStripeAccount,
  getStripeDisplayPaymentMethod,
} from "~/server/lib/stripe";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { id } = req.query;
  if (!id) {
    res.status(400).json({ error: "No order id provided" });
    return;
  }

  const orderId = parseInt(id as string);
  if (isNaN(orderId)) {
    res.status(400).json({ error: "Invalid order id" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: req.accountId,
    },
    include: {
      account: true,
      Customer: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const stripeCustomer = await findStripeCustomer(
    order.account,
    order.Customer.email,
    order.Customer.id,
  );

  // Map payment method Id to human-readable name
  const paymentMethodNameMap: Record<string, string> = {};

  if (!stripeCustomer) {
    // it's likely a guest customer, we need to pull the paymentmethod from the intent
    const paymentDetails = await db.paymentDetails.findMany({
      where: {
        orderId: orderId,
        processor: "stripe",
      },
    });
    for (const paymentDetail of paymentDetails) {
      if (!paymentDetail.processorId) {
        continue;
      }
      const paymentIntent = await getStripeAccount(
        order.account,
      ).paymentIntents.retrieve(paymentDetail.processorId);
      if (!paymentIntent.payment_method) {
        continue;
      }
      let paymentMethodString = paymentIntent.payment_method;
      if (typeof paymentMethodString !== "string") {
        paymentMethodString = paymentMethodString.id;
      }
      const paymentMethod = await getStripeAccount(
        order.account,
      ).paymentMethods.retrieve(paymentMethodString);
      paymentMethodNameMap[paymentMethod.id] =
        getStripeDisplayPaymentMethod(paymentMethod).methodId;

      return res.json({
        success: true,
        payment: paymentMethod.id,
        methods: paymentMethodNameMap,
        paymentFound: true,
      });
    }
    res.status(404).json({ error: "Customer not found" });
    return;
  }

  const paymentMethods = await getStripeAccount(
    order.account,
  ).paymentMethods.list({
    customer: stripeCustomer.id,
  });

  if (paymentMethods.data.length === 0) {
    res
      .status(200)
      .json({ success: true, payment: "none", paymentFound: false });
    return;
  }

  paymentMethods.data.forEach((paymentMethod) => {
    paymentMethodNameMap[paymentMethod.id] =
      getStripeDisplayPaymentMethod(paymentMethod).methodId;
  });

  return res.json({
    success: true,
    payment: paymentMethods.data[0]?.id,
    methods: paymentMethodNameMap,
    paymentFound: true,
  });
};

export default withMethods(
  ["GET"],
  withPermissions(
    {
      policy: "api.orders",
      action: "read",
    },
    handler,
  ),
);
