import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]";
import { db } from "~/server/db";
import { findStripeCustomer, getStripeAccount } from "~/server/lib/stripe";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "read");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      account: true,
      Customer: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const stripeCustomer = await findStripeCustomer(
    order.account,
    order.Customer.email,
    order.Customer.id,
  );

  if (!stripeCustomer) {
    // it's likely a guest customer, we need to pull the paymentmethod from the intent
    const paymentDetails = await db.paymentDetails.findMany({
      where: {
        orderId: orderId,
        processor: "stripe",
      },
    });
    for (const paymentDetail of paymentDetails) {
      if (!paymentDetail.processorId) {
        continue;
      }
      const paymentIntent = await getStripeAccount(
        order.account,
      ).paymentIntents.retrieve(paymentDetail.processorId);
      if (!paymentIntent.payment_method) {
        continue;
      }
      let paymentMethodString = paymentIntent.payment_method;
      if (typeof paymentMethodString !== "string") {
        paymentMethodString = paymentMethodString.id;
      }
      const paymentMethod = await getStripeAccount(
        order.account,
      ).paymentMethods.retrieve(paymentMethodString);
      return res.json({
        success: true,
        payment: paymentMethod.id,
        paymentFound: true,
      });
    }
    res.status(404).json({ error: "Customer not found" });
    return;
  }

  const paymentIntents = await getStripeAccount(
    order.account,
  ).paymentIntents.list({
    customer: stripeCustomer.id,
  });

  const successfulPaymentIntents = paymentIntents.data.filter(
    (paymentIntent) => paymentIntent.status === "succeeded",
  );

  if (successfulPaymentIntents.length === 0) {
    res
      .status(200)
      .json({ success: true, payment: "none", paymentFound: false });
    return;
  }

  return res.json({
    success: true,
    payment: successfulPaymentIntents[0]?.payment_method,
    cards: successfulPaymentIntents.map((paymentIntent) => ({
      methodId: paymentIntent.payment_method,
      intentId: paymentIntent.id,
    })),
    paymentFound: true,
  });
};

export default handler;
