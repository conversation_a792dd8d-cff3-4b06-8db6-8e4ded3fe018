import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]";
import { db } from "~/server/db";

export type PaymentsResponse = {
  id: number;
  orderId: number;
  createdAt: Date;
  method: string | null;
  methodId: string | null;
  processor: string | null;
  processorId: string | null;
  tip: number | null;
  amount: number;
  PaymentRefund: {
    id: string;
    reason: string;
    amount: number;
    paymentId: number;
  }[];
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "read");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const payments: PaymentsResponse[] = await db.paymentDetails.findMany({
    where: {
      orderId: orderId,
    },
    include: {
      PaymentRefund: true,
    },
  });

  console.log(payments);

  return res.status(200).json({
    payments,
  });
};

export default handler;
