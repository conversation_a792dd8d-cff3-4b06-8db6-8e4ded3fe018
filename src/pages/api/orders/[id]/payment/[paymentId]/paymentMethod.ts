import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { number, object } from "yup";
import { getStripeAccount } from "~/server/lib/stripe";

const COLLECT_CARD_ON_FILE_SCHEMA = object().shape({
  amount: number().required(),
  tipAmount: number().required().max(500, "Tip amount too high"),
});

export type CollectCardOnFileValues =
  typeof COLLECT_CARD_ON_FILE_SCHEMA.__outputType;

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.order",
      action: "execute",
    },
  });

  if (!permission) {
    return;
  }

  const account = await db.account.findFirst({
    where: {
      id: permission.accountId,
    },
  });

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const { id, paymentId } = req.query;

  const order = await db.order.findFirst({
    where: {
      id: Number(id),
      accountId: account.id,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const payment = await db.paymentDetails.findFirst({
    where: {
      id: Number(paymentId),
      orderId: order.id,
    },
    include: {
      PaymentRefund: true,
    },
  });

  if (!payment) {
    res.status(404).json({ error: "Payment not found" });
    return;
  }

  let body: CollectCardOnFileValues | undefined = undefined;
  try {
    body = await COLLECT_CARD_ON_FILE_SCHEMA.validate(req.body);
  } catch (e: any) {
    res.status(400).json({ error: `Invalid body: ${e.message}` });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  if (payment.processor !== "stripe") {
    res.status(500).json({
      error: "Payment method not supported.",
    });
    return;
  }
  const paymentIntentId = payment.processorId;
  if (!paymentIntentId) {
    res.status(500).json({
      error: "Payment intent not found, you'll have to refund them manually.",
    });
    return;
  }

  const paymentIntent =
    await getStripeAccount(account).paymentIntents.retrieve(paymentIntentId);
  if (paymentIntent.status !== "succeeded") {
    res.status(500).json({
      error: "Payment intent not found.",
    });
    return;
  }
  if (!paymentIntent?.payment_method) {
    res.status(500).json({
      error: "Payment method not found.",
    });
    return;
  }

  let paymentMethodString = paymentIntent.payment_method;
  if (typeof paymentMethodString !== "string") {
    paymentMethodString = paymentMethodString.id;
  }
  const paymentMethod =
    await getStripeAccount(account).paymentMethods.retrieve(
      paymentMethodString,
    );
  return res.json({
    success: true,
    payment: paymentMethod.id,
    paymentFound: true,
  });
};

export default POST;
