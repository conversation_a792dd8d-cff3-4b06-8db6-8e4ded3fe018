import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { CurrencyValue } from "~/server/lib/currency";
import {
  ORDER_PAYMENT_REFUND_SCHEMA,
  OrderPaymentRefund,
} from "~/components/Order/RefundAction";
import { getStripeAccount } from "~/server/lib/stripe";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.order",
      action: "execute",
    },
  });

  if (!permission) {
    return;
  }

  const account = await db.account.findFirst({
    where: {
      id: permission.accountId,
    },
  });

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const { id, paymentId } = req.query;

  const order = await db.order.findFirst({
    where: {
      id: Number(id),
      accountId: account.id,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const payment = await db.paymentDetails.findFirst({
    where: {
      id: Number(paymentId),
      orderId: order.id,
    },
    include: {
      PaymentRefund: true,
    },
  });

  if (!payment) {
    res.status(404).json({ error: "Payment not found" });
    return;
  }

  let body: OrderPaymentRefund | undefined = undefined;
  try {
    body = await ORDER_PAYMENT_REFUND_SCHEMA.validate(req.body);
  } catch (e: any) {
    res.status(400).json({ error: `Invalid body: ${e.message}` });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  const paymentTotal = CurrencyValue.fromPlatform(
    payment.amount + (payment.tip || 0),
  );

  if (body.amount > paymentTotal.amount) {
    res.status(400).json({ error: "Refund amount exceeds payment total" });
    return;
  }

  const amountAlreadyRefunded = payment.PaymentRefund.reduce(
    (total, refund) => total + refund.amount,
    0,
  );

  if (body.amount + amountAlreadyRefunded > paymentTotal.amount) {
    res.status(400).json({
      error: `Refund amount exceeds payment total, max refund: ${
        paymentTotal.amount - amountAlreadyRefunded
      }`,
    });
    return;
  }

  const refund = await db.paymentRefund.create({
    data: {
      amount: body.amount,
      reason: body.reason,
      paymentId: payment.id,
    },
  });

  await db.order.update({
    where: {
      id: order.id,
    },
    data: {
      totalPaid: order.totalPaid - Math.min(body.amount, paymentTotal.amount),
    },
  });

  if (payment.processor === null) {
    res.status(200).json({
      message:
        "Payment processor not found, you'll have to refund them manually.",
    });
    return;
  }

  if (payment.processor === "stripe") {
    const paymentIntentId = payment.processorId;
    if (!paymentIntentId) {
      res.status(500).json({
        error: "Payment intent not found, you'll have to refund them manually.",
      });
      return;
    }

    const paymentIntent =
      await getStripeAccount(account).paymentIntents.retrieve(paymentIntentId);
    if (paymentIntent.status !== "succeeded") {
      res.status(500).json({
        error: "Payment intent not found, you'll have to refund them manually.",
      });
      return;
    }

    const refundIntent = await getStripeAccount(account).refunds.create({
      payment_intent: paymentIntentId,
      amount: CurrencyValue.fromPlatform(body.amount).stripeAmount,
      metadata: {
        refundId: refund.id.toString(),
        reason: body.reason,
      },
    });

    if (refundIntent.status !== "succeeded") {
      res.status(500).json({
        error: "Refund failed, you'll have to refund them manually.",
      });
      return;
    } else {
      res.status(200).json({
        success: true,
      });
      return;
    }
  }
};

export default handler;
