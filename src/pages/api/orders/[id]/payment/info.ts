import { handleBasicOrder } from "~/pages/api/orders/[id]";
import { db } from "~/server/db";
import { orderInfoToPaymentInfo, orderPaidItems } from "~/server/lib/orderUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { OrderPaidItems } from "~/components/Contract/Top/types";
import { PaymentInfo } from "~/server/lib/currency";

export type OrderPaymentInfoResponse = {
  paymentInfo: PaymentInfo;
  paidItems: OrderPaidItems[];
  due: number;
  orderId: number;
  accountId: number;
  customerId: string;
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "read");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;
  const { includePaidItems } = req.query;

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      PaymentDetails: {
        include: {
          PaymentRefund: true,
        },
      },
      OrderFee: true,
      OrderDiscount: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }
  const paymentInfo = orderInfoToPaymentInfo(
    order,
    order.OrderFee,
    order.OrderDiscount,
  );

  let paidItems: OrderPaidItems[] = [];
  if (includePaidItems !== "false") {
    paidItems = orderPaidItems(
      order.PaymentDetails.map((pd) => {
        return {
          ...pd,
          refundAmount: pd.PaymentRefund.reduce(
            (acc, refund) => acc + refund.amount,
            0,
          ),
        };
      }),
    );
  }

  const response: OrderPaymentInfoResponse = {
    paymentInfo,
    paidItems,
    orderId: order.id,
    accountId: order.accountId,
    customerId: order.customerId,
    due: order.finalTotal - order.totalPaid,
  };

  return res.status(200).json(response);
};

export default handler;
