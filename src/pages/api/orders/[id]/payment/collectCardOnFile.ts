import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicOrder } from "~/pages/api/orders/[id]";
import { number, object, string } from "yup";
import { findStripeCustomer, getStripeAccount } from "~/server/lib/stripe";
import { db } from "~/server/db";
import { CurrencyValue } from "~/server/lib/currency";
import { captureException } from "@sentry/core";

const COLLECT_CARD_ON_FILE_SCHEMA = object().shape({
  paymentMethodId: string().required(),
  amount: number().required(),
  tipAmount: number().required().max(500, "Tip amount too high"),
});

export type CollectCardOnFileValues =
  typeof COLLECT_CARD_ON_FILE_SCHEMA.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const orderBasics = await handleBasicOrder(req, res, "execute");

  if (!orderBasics) {
    return;
  }

  const { orderId, accountId } = orderBasics;

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  let body: CollectCardOnFileValues | undefined = undefined;
  try {
    body = await COLLECT_CARD_ON_FILE_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (e) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      account: true,
      Customer: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const stripePaymentMethod = await getStripeAccount(
    order.account,
  ).paymentMethods.retrieve(body.paymentMethodId);

  if (!stripePaymentMethod) {
    res.status(404).json({ error: "Payment method not found" });
    return;
  }

  let stripeCustomerId;
  const stripeCustomer = await findStripeCustomer(
    order.account,
    order.Customer.email,
    order.Customer.id,
  );

  if (!stripeCustomer) {
    const foundStripeCustomer = await getStripeAccount(
      order.account,
    ).customers.create({
      email: order.Customer.email,
      metadata: {
        customerId: order.Customer.id.toString(),
      },
    });
    stripeCustomerId = foundStripeCustomer.id;
  } else {
    stripeCustomerId = stripeCustomer.id;
  }

  try {
    await getStripeAccount(order.account).paymentIntents.create({
      amount: CurrencyValue.fromPlatform(body.amount + (body.tipAmount || 0))
        .stripeAmount,
      currency: "usd",
      customer: stripeCustomerId,
      payment_method: stripePaymentMethod.id,
      off_session: true,
      confirm: true,
      metadata: {
        orderId: order.id,
        accountId: order.accountId,
        tipAmount: body.tipAmount,
      },
    });
  } catch (err: any) {
    const errorCode = err?.code || "";
    if (errorCode === "payment_intent_authentication_failure") {
      res
        .status(400)
        .json({
          error: `Payment authentication failed ${errorCode}`,
          stripeError: true,
        });
      return;
    }
    captureException(err);
    console.log(err);
    res.status(500).json({ error: `Error creating payment intent ${err}` });
    return;
  }

  return res.status(200).json({ success: true });
};

export default handler;
