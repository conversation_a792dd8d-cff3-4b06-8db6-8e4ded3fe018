import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import {
  orderContractItems,
  orderInfoToSharedItems,
  orderPaidItems,
} from "~/server/lib/orderUtil";
import { OrderState } from "@prisma/client";
import { checkPermission, PermissionAction } from "~/pages/api/permissions";
import { OrderFormSchema } from "~/form/OrderForm";
import { toAddressType } from "~/server/lib/location/util";
import {
  CancelEmailProps,
  handleOrderDeletion,
} from "~/server/services/orderEvents";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicOrderRequest = await handleBasicOrder(req, res, "read");

  if (!basicOrderRequest) {
    return;
  }

  if (req.method === "DELETE") {
    return await handleCancelOrder(req, res, basicOrderRequest);
  }

  const order = await db.order.findFirst({
    where: {
      id: basicOrderRequest.orderId,
      accountId: basicOrderRequest.accountId,
    },
    include: {
      eventAddress: true,
      OrderFee: true,
      OrderDiscount: true,
      OrderProduct: {
        include: {
          product: {
            select: {
              quantity: true,
              ProductImageUpload: {
                select: {
                  imageUpload: {
                    select: {
                      url: true,
                    },
                  },
                  priority: true,
                },
                orderBy: {
                  priority: "asc",
                },
              },
              name: true,
              id: true,
            },
          },
        },
      },
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  const orderResponse: OrderFormSchema = {
    discount: order.OrderDiscount.map((discount) => {
      return {
        type: discount.type,
        chargeType: discount.chargeType,
        amount: discount.amount,
        entityId: discount.couponId || -1,
      };
    }),
    setupSurface: order.setupSurfaceId,
    eventLocation: toAddressType(order.eventAddress),
    startDate: order.startTime,
    endDate: order.endTime,
    products: order.OrderProduct.map((op) => {
      return {
        id: op.productId,
        name: op.product.name,
        productThumbnail:
          op.product.ProductImageUpload.at(0)?.imageUpload?.url || undefined,
        available: op.product.quantity - op.quantity,
        quantity: op.quantity,
        price: op.pricePaid,
      };
    }),
    customerId: order.customerId,
    damageWaiver: order.damageWaiverRate !== null,
    fees: order.OrderFee.map((fee) => {
      return {
        type: fee.type,
        name: fee.name,
        amount: fee.amount,
        taxable: fee.taxable,
      };
    }),
    taxRate: order.taxRate ?? 0,
    taxExempt: order.taxExempt,
  };

  res.status(200).json({
    order: orderResponse,
    raw: order,
    extraInfo: {
      internalNote: order.internalNotes,
      customerNote: order.customerNotes,
      state: order.state,
    },
    internalNote: order.internalNotes,
    damageWaiver: {
      rate: order.damageWaiverRate,
    },
  });
};

export default handler;

export type BasicOrderRequest = {
  orderId: number;
  accountId: number;
};

export const handleBasicOrder = async (
  req: NextApiRequest,
  res: NextApiResponse,
  action: PermissionAction,
): Promise<BasicOrderRequest | null> => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.orders",
      action: action,
    },
  });

  if (!permission) {
    return null;
  }

  const { id } = req.query;
  if (!id) {
    res.status(400).json({ error: "No order id provided" });
    return null;
  }

  const orderId = parseInt(id as string);
  if (isNaN(orderId)) {
    res.status(400).json({ error: "Invalid order id" });
    return null;
  }

  return { orderId, accountId: permission.accountId };
};

export const handleCancelOrder = async (
  req: NextApiRequest,
  res: NextApiResponse,
  basicOrderRequest: BasicOrderRequest | null,
) => {
  if (!basicOrderRequest) {
    return;
  }

  const { orderId, accountId } = basicOrderRequest;

  const order = await db.order.findFirst({
    where: {
      id: orderId,
      accountId: accountId,
    },
    include: {
      account: {
        select: {
          businessTimezone: true,
          damageWaiverRate: true,
          name: true,
          businessEmail: true,
          businessPhone: true,
        },
      },
      Customer: {
        select: {
          email: true,
          firstName: true,
          lastName: true,
        },
      },
      couponApplied: true,
      PaymentDetails: true,
      OrderProduct: {
        include: {
          product: {
            select: {
              name: true,
            },
          },
        },
      },
      OrderDiscount: true,
      OrderFee: true,
    },
  });

  if (!order) {
    res.status(404).json({ error: "Order not found" });
    return;
  }

  await db.order.update({
    where: {
      id: orderId,
    },
    data: {
      state: OrderState.CANCELLED,
    },
  });

  const products: { id: number; name: string }[] = [];
  order.OrderProduct.forEach((op) => {
    products.push({ id: op.productId, name: op.product.name });
  });

  const contractItems = orderContractItems(order.OrderProduct, products);

  const paidItems = orderPaidItems(order.PaymentDetails);

  const sharedItems = orderInfoToSharedItems(
    order,
    order.OrderFee,
    order.OrderDiscount,
  );

  const cancelProps: CancelEmailProps = {
    contractItems,
    paidItems,
    contractSharedItems: sharedItems,
  };

  await handleOrderDeletion(orderId, accountId, cancelProps);

  res.status(200).json({ success: true });
};
