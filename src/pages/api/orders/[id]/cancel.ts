import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicOrder,
  handleCancelOrder,
} from "~/pages/api/orders/[id]/index";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicOrderRequest = await handleBasicOrder(req, res, "write");

  if (!basicOrderRequest) {
    return;
  }

  return await handleCancelOrder(req, res, basicOrderRequest);
};

export default handler;
