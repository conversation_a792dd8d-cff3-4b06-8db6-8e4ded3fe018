import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { Prisma } from ".prisma/client";

const categoryWithThumbnailAndProductCount =
  Prisma.validator<Prisma.CategoryInclude>()({
    CategoryImageUpload: {
      select: {
        imageUpload: {
          select: {
            id: true,
            url: true,
          },
        },
      },
    },
    Product: {
      select: {
        _count: true,
      },
    },
  });

export type CategoryWithThumbnailAndProductCount = Prisma.CategoryGetPayload<{
  include: typeof categoryWithThumbnailAndProductCount;
}>;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.categories",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const data: CategoryWithThumbnailAndProductCount[] =
    await db.category.findMany({
      where: {
        accountId: permission.accountId,
      },
      include: categoryWithThumbnailAndProductCount,
    });
  return res.status(200).json({ categories: data });
};

export default handler;
