import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { CategorySchema, CategorySchemaType } from "~/query/category";
import slugify from "slugify";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { updateWebsiteCollection } from "~/server/lib/website";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const category: CategorySchemaType = req.body;
  const metaTitle = category.metaTitle ?? category.name;
  const slug = category.slug ?? slugify(category.name, { lower: true });

  const createdCategory = await db.category.create({
    data: {
      organizationId: req.organizationId,
      accountId: req.accountId,
      name: category.name,
      description: category.description,
      display: category.display,
      slug,
      metaDescription: category.metaDescription ?? null,
      metaTitle,
    },
  });

  await updateWebsiteCollection(
    req.accountId,
    req.organizationId,
    `categories-${req.organizationId}`,
  );

  return res.json({ success: true, id: createdCategory.id });
};

export default withPermissions(
  {
    policy: "api.category",
    action: "write",
  },
  withMethods(["POST"], withValidation(CategorySchema, handler)),
);
