import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { CategorySchema } from "~/query/category";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const { id } = req.query;

  const category = await db.category.findUnique({
    where: {
      accountId: permissions.accountId,
      id: Number(id),
    },
  });

  if (!category) {
    res.status(404).json({ error: "Category not found" });
    return;
  }

  const strippedCategory = CategorySchema.cast(category, {
    stripUnknown: true,
  });

  res.status(200).json({ success: true, category: strippedCategory });
};

export default GET;
