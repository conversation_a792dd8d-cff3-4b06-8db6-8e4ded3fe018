import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { ImageUploadSimpleType } from "~/components/image/image";
import { fullImageUrl } from "~/server/globalTypes";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const { id } = req.query;

  if (!id) {
    res.status(400).json({ error: "Missing categoryId" });
    return;
  }

  const category = await db.category.findUnique({
    where: {
      id: Number(id),
      accountId: permission.accountId,
    },
  });

  if (!category) {
    res.status(404).json({ error: "Category not found" });
    return;
  }

  const image = await db.categoryImageUpload.findFirst({
    where: {
      categoryId: Number(id),
    },
    include: {
      imageUpload: {
        select: {
          id: true,
          name: true,
          url: true,
        },
      },
    },
  });

  if (!image) {
    return res.status(200).json({ images: null });
  }

  const simpleImage: ImageUploadSimpleType = {
    id: image.imageUpload.id,
    name: image.imageUpload.name,
    url: fullImageUrl(image.imageUpload.url),
  };

  return res.status(200).json({ image: simpleImage });
};

export default GET;
