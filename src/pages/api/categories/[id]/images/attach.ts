import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { ImageUploadSimpleType } from "~/components/image/image";
import { fullImageUrl } from "~/server/globalTypes";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const { id } = req.query;

  if (!id) {
    res.status(400).json({ error: "Missing id or imageId" });
    return;
  }

  const { imageId } = req.body;

  const category = await db.category.findUnique({
    where: {
      id: Number(id),
      accountId: permission.accountId,
    },
  });

  if (!category) {
    res.status(404).json({ error: "Category not found" });
    return;
  }

  if (!imageId) {
    res.status(400).json({ error: "Missing imageId" });
    return;
  }

  const imageUpload = await db.imageUpload.findUnique({
    where: {
      id: imageId,
      accountId: permission.accountId,
    },
  });

  if (!imageUpload) {
    res.status(404).json({ error: "Image not found" });
    return;
  }

  await db.categoryImageUpload.create({
    data: {
      category: {
        connect: {
          id: category.id,
        },
      },
      imageUpload: {
        connect: {
          id: imageUpload.id,
        },
      },
    },
  });

  const imageResponse: ImageUploadSimpleType = {
    id: imageUpload.id,
    url: fullImageUrl(imageUpload.url),
    name: imageUpload.name,
  };

  res.status(200).json({ success: true, image: imageResponse });
};

export default POST;
