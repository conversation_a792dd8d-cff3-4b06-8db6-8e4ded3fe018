import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const { id, imageId } = req.query;

  if (!id || !imageId) {
    res.status(400).json({ message: "Missing id or imageId" });
    return;
  }

  const category = await db.category.findUnique({
    where: {
      id: Number(id),
      accountId: permission.accountId,
    },
  });

  if (!category) {
    res.status(404).json({ message: "Category not found" });
    return;
  }

  const imageUpload = await db.imageUpload.findUnique({
    where: {
      id: imageId as string,
      accountId: permission.accountId,
    },
    include: {
      CategoryImageUpload: {
        select: {
          categoryId: true,
        },
      },
    },
  });

  if (!imageUpload) {
    res.status(404).json({ message: "Image not found" });
    return;
  }

  if (
    imageUpload.CategoryImageUpload.length === 0 ||
    imageUpload.CategoryImageUpload.find(
      (piu) => piu.categoryId === category.id,
    ) === undefined
  ) {
    res.status(404).json({ message: "Image not found" });
    return;
  }

  const result = await db.categoryImageUpload.delete({
    where: {
      categoryId_imageUploadId: {
        imageUploadId: imageUpload.id,
        categoryId: category.id,
      },
    },
  });

  if (!result) {
    res.status(500).json({ message: "Something went wrong" });
    return;
  }

  res.status(200).json({ success: true });
};

export default GET;
