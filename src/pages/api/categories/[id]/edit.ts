import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { CategorySchema } from "~/query/category";
import { checkPermission } from "~/pages/api/permissions";
import { captureException } from "@sentry/core";
import { updateWebsiteCollection } from "~/server/lib/website";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const data = req.body;
  const categoryId = req.query.id as string;
  const categoryNumber = parseInt(categoryId);

  if (!categoryId || isNaN(categoryNumber)) {
    res.status(400).json({ error: "Missing id" });
    return;
  }
  try {
    const category = await CategorySchema.validate(data);
    // todo add redirect for slug changes, until then, slug is not updated
    await db.category.update({
      where: {
        accountId: permission.accountId,
        id: categoryNumber,
      },
      data: {
        name: category.name,
        description: category.description,
        display: category.display,
        metaTitle: category.metaTitle,
        metaDescription: category.metaDescription,
      },
    });

    await updateWebsiteCollection(
      permission.accountId,
      permission.organizationId,
      `categories-${permission.organizationId}`,
    );
  } catch (e) {
    captureException(e);
    console.log(e);
    res.status(400).json({ error: "Invalid request body" });
    return;
  }

  res.json({ success: true });
};

export default POST;
