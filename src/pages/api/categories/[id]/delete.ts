import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { updateWebsiteCollection } from "~/server/lib/website";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.category",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const { id } = req.query;

  const category = await db.category.findUnique({
    where: {
      accountId: permissions.accountId,
      id: Number(id),
    },
    include: {
      Product: {
        select: {
          _count: true,
        },
      },
    },
  });

  if (!category) {
    res.status(404).json({ error: "Category not found" });
    return;
  }

  if (category.Product.length > 0) {
    res.status(409).json({ error: "Category has products" });
    return;
  }

  // manually handle the category images because I forgot to add the cascade delete
  await db.categoryImageUpload.deleteMany({
    where: {
      categoryId: category.id,
    },
  });

  await db.category.delete({
    where: {
      accountId: permissions.accountId,
      id: Number(id),
    },
  });

  await updateWebsiteCollection(
    permissions.accountId,
    permissions.organizationId,
    `categories-${permissions.organizationId}`,
  );

  res.status(200).json({ success: true });
};

export default GET;
