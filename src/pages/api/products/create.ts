import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { ProductSchema, ProductSchemaType } from "~/server/product/types";
import slugify from "slugify";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { updateWebsiteCollection } from "~/server/lib/website";

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const product: ProductSchemaType = req.body;

  const validCategories = product.categories.flatMap((categoryId) => {
    if (!categoryId) {
      return [];
    }
    return [categoryId];
  });

  if (validCategories.length !== 0) {
    const categoryValidation = await db.category.findMany({
      where: {
        id: {
          in: validCategories,
        },
        accountId: req.accountId,
      },
    });

    if (categoryValidation.length !== product.categories.length) {
      res.status(400).json({ error: "Invalid category" });
      return;
    }
  }

  const metaTitle = product.metaTitle ?? product.name;
  const slug = product.slug ?? slugify(product.name, { lower: true });

  const productCreated = await db.product.create({
    data: {
      accountId: req.accountId,
      organizationId: req.organizationId,
      archived: false,
      name: product.name,
      deliverable: product.deliverable,
      description: product.description,
      setupTimeMinutes: product.setupTimeMinutes,
      takeDownTimeMinutes: product.takeDownTimeMinutes,
      beforeRentalBufferMinutes: product.beforeRentalBufferMinutes,
      afterRentalBufferMinutes: product.afterRentalBufferMinutes,
      price: product.price,
      display: product.display,
      quantity: product.quantity,
      slug,
      metaDescription: product.metaDescription ?? null,
      metaTitle,
    },
  });

  if (validCategories.length > 0) {
    await db.productCategory.createMany({
      data: validCategories.map((categoryId) => ({
        categoryId,
        productId: productCreated.id,
      })),
    });
  }

  await updateWebsiteCollection(
    req.accountId,
    req.organizationId,
    `products-${req.organizationId}`,
  );

  res.status(200).json({ success: true, id: productCreated.id });
};

export default withPermissions(
  {
    policy: "api.product",
    action: "write",
  },
  withMethods(["POST"], withValidation(ProductSchema, POST)),
);
