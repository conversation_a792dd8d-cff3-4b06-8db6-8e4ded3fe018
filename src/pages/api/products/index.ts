import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { Prisma } from ".prisma/client";
import { getQuantityOfProductsAvailableBulk } from "~/server/lib/orderUtil";
import { ProductOrderItem } from "~/server/product/types";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import ProductWhereInput = Prisma.ProductWhereInput;

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const productSearch = req.query.search;

  const excludedIds: number[] = [];
  const exclude = req.query.exclude;
  if (exclude && typeof exclude === "string") {
    const ids = exclude.split(",");
    for (const id of ids) {
      const parsedId = parseInt(id);
      if (isNaN(parsedId)) {
        continue;
      }
      excludedIds.push(parsedId);
    }
  }

  let orSearch: ProductWhereInput[] | undefined = undefined;
  if (
    productSearch &&
    typeof productSearch === "string" &&
    productSearch !== ""
  ) {
    orSearch = [
      {
        name: {
          contains: productSearch,
        },
      },
      {
        description: {
          contains: productSearch,
        },
      },
    ];
  }

  let limit = undefined;

  const limitQuery = req.query.limit;
  if (limitQuery && typeof limitQuery === "string") {
    const parsedLimit = parseInt(limitQuery);
    if (!isNaN(parsedLimit)) {
      limit = parsedLimit;
    }
  }

  const products: ProductOrderItem[] = [];
  const searchedProducts = await db.product.findMany({
    where: {
      organizationId: req.organizationId,
      OR: orSearch,
      archived: false,
      NOT: {
        quantity: 0,
        id: {
          in: excludedIds,
        },
      },
    },
    select: {
      accountId: true,
      id: true,
      name: true,
      price: true,
      quantity: true,
      display: true,
      beforeRentalBufferMinutes: true,
      afterRentalBufferMinutes: true,
      ProductImageUpload: {
        select: {
          priority: true,
          imageUpload: {
            select: {
              url: true,
            },
          },
        },
      },
    },
    take: limit,
  });

  const { startTime, endTime } = req.query;

  const availabilityMap: Record<number, number> = {};
  if (
    startTime &&
    typeof startTime === "string" &&
    endTime &&
    typeof endTime === "string"
  ) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const productQuantity = await getQuantityOfProductsAvailableBulk(
      req.organizationId,
      searchedProducts.map((product) => {
        availabilityMap[product.id] = product.quantity;
        return {
          productId: product.id,
          quantity: product.quantity,
          beforeRentalBufferMinutes: product.beforeRentalBufferMinutes,
          afterRentalBufferMinutes: product.afterRentalBufferMinutes,
        };
      }),
      start,
      end,
      60 * 60,
    );

    productQuantity.forEach((dateInfo) => {
      dateInfo.productInfo.forEach((productInfo) => {
        availabilityMap[productInfo.productId] = Math.min(
          availabilityMap[productInfo.productId] ?? 0,
          productInfo.quantity,
        );
      });
    });
  }
  products.push(
    ...searchedProducts.map((product) => {
      return {
        accountId: product.accountId,
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: product.quantity,
        display: product.display,
        available: availabilityMap[product.id] ?? product.quantity,
        productThumbnail:
          product.ProductImageUpload.sort((a: any, b: any) => {
            return a.priority - b.priority;
          }).at(0)?.imageUpload?.url || undefined,
      };
    }),
  );

  return res.status(200).json({ products });
};

export default withPermissions(
  {
    policy: "api.product",
    action: "read",
  },
  handler,
);
