import { NextApiResponse } from "next";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { ProductSchema, ProductSchemaType } from "~/server/product/types";
import { db } from "~/server/db";
import { updateWebsiteCollection } from "~/server/lib/website";

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const productID = req.query.id;
  if (!productID) {
    res.status(400).json({ error: "Invalid product id" });
    return;
  }

  const productId = Number(productID);
  if (isNaN(productId)) {
    res.status(400).json({ error: "Invalid product id" });
    return;
  }

  const currentProduct = await db.product.findUnique({
    where: {
      id: productId,
      accountId: req.accountId,
      archived: false,
    },
    include: {
      ProductCategory: true,
    },
  });

  if (!currentProduct) {
    res.status(404).json({ error: "Product not found" });
    return;
  }

  const product: ProductSchemaType = req.body;

  const validCategories = Array.from(
    new Set(
      product.categories.flatMap((categoryId) => {
        if (!categoryId) {
          return [];
        }
        return [categoryId];
      }),
    ),
  );

  if (validCategories.length !== 0) {
    const categoryValidation = await db.category.findMany({
      where: {
        id: {
          in: validCategories,
        },
        accountId: req.accountId,
      },
    });

    if (
      categoryValidation.length !==
      validCategories.filter((item) => !!item).length
    ) {
      res.status(400).json({ error: "Invalid category" });
      return;
    }

    const currentCategories = currentProduct.ProductCategory.map(
      (pc) => pc.categoryId,
    );

    const categoriesToAdd = validCategories.filter(
      (categoryId) => !currentCategories.includes(categoryId),
    );

    const categoriesToRemove = currentCategories.filter(
      (categoryId) => !validCategories.includes(categoryId),
    );

    await db.$transaction(async (t) => {
      if (categoriesToRemove.length > 0) {
        await t.productCategory.deleteMany({
          where: {
            productId: productId,
            categoryId: {
              in: categoriesToRemove,
            },
          },
        });
      }

      if (categoriesToAdd.length > 0) {
        await t.productCategory.createMany({
          data: categoriesToAdd.map((categoryId) => ({
            categoryId,
            productId,
          })),
        });
      }
    });
  }

  await db.product.update({
    where: {
      id: productId,
      accountId: req.accountId,
    },
    data: {
      name: product.name,
      description: product.description,
      setupTimeMinutes: product.setupTimeMinutes,
      takeDownTimeMinutes: product.takeDownTimeMinutes,
      beforeRentalBufferMinutes: product.beforeRentalBufferMinutes,
      afterRentalBufferMinutes: product.afterRentalBufferMinutes,
      price: product.price,
      display: product.display,
      quantity: product.quantity,
      metaTitle: product.metaTitle,
      metaDescription: product.metaDescription,
      deliverable: product.deliverable,
    },
  });

  await updateWebsiteCollection(
    req.accountId,
    req.organizationId,
    `products-${req.organizationId}`,
  );

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.product",
    action: "write",
  },
  withMethods(["POST"], withValidation(ProductSchema, POST)),
);
