import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { ImageUploadSimpleType } from "~/components/image/image";
import { fullImageUrl } from "~/server/globalTypes";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.product",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const { id } = req.query;

  if (!id) {
    res.status(400).json({ error: "ProductId Missing" });
    return;
  }

  const product = await db.product.findUnique({
    where: {
      id: Number(id),
      accountId: permission.accountId,
      archived: false,
    },
  });

  if (!product) {
    res.status(404).json({ error: "Product not found" });
    return;
  }

  const images = await db.productImageUpload.findMany({
    where: {
      productId: Number(id),
    },
    include: {
      imageUpload: {
        select: {
          id: true,
          name: true,
          url: true,
        },
      },
    },
  });

  const simpleImages: ImageUploadSimpleType[] = images
    .sort((a, b) => a.priority - b.priority)
    .map((image) => {
      return {
        id: image.imageUploadId,
        url: fullImageUrl(image.imageUpload.url),
        name: image.imageUpload.name,
      };
    });

  return res.status(200).json({ images: simpleImages });
};

export default GET;
