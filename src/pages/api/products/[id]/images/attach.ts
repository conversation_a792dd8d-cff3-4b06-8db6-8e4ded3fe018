import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { ImageUploadSimpleType } from "~/components/image/image";
import { fullImageUrl } from "~/server/globalTypes";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { number, object, string } from "yup";
import { updateWebsiteCollection } from "~/server/lib/website";

const ATTACH_IMAGE_SCHEMA = object().shape({
  imageId: string().required(),
  priority: number().required(),
});

type AttachImageBody = typeof ATTACH_IMAGE_SCHEMA.__outputType;

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { id } = req.query;

  if (!id) {
    res.status(400).json({ error: "Missing id or imageId" });
    return;
  }

  if (isNaN(Number(id))) {
    res.status(400).json({ error: "Invalid product id value" });
    return;
  }

  const bodyJson: AttachImageBody = req.body;

  const product = await db.product.findUnique({
    where: {
      id: Number(id),
      accountId: req.accountId,
      archived: false,
    },
    select: {
      id: true,
    },
  });

  if (!product) {
    res.status(400).json({ error: "Product not found" });
    return;
  }

  const imageUpload = await db.imageUpload.findUnique({
    where: {
      id: bodyJson.imageId,
      accountId: req.accountId,
    },
  });

  if (!imageUpload) {
    res.status(400).json({ error: "Image not found" });
    return;
  }

  await db.productImageUpload.create({
    data: {
      product: {
        connect: {
          id: product.id,
        },
      },
      imageUpload: {
        connect: {
          id: imageUpload.id,
        },
      },
      priority: bodyJson.priority,
    },
  });

  const imageResponse: ImageUploadSimpleType = {
    id: imageUpload.id,
    url: fullImageUrl(imageUpload.url),
    name: imageUpload.name,
  };

  await updateWebsiteCollection(
    req.accountId,
    req.organizationId,
    `products-${req.organizationId}`,
  );

  res.status(200).json({ success: true, image: imageResponse });
};

export default withPermissions(
  {
    policy: "api.product",
    action: "write",
  },
  withMethods(["POST"], withValidation(ATTACH_IMAGE_SCHEMA, POST)),
);
