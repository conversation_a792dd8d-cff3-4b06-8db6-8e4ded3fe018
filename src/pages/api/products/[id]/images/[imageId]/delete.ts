import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.product",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const { id, imageId } = req.query;

  if (!id || !imageId) {
    res.status(400).json({ message: "Missing id or imageId" });
    return;
  }

  const product = await db.product.findUnique({
    where: {
      id: Number(id),
      accountId: permission.accountId,
      archived: false,
    },
  });

  if (!product) {
    res.status(404).json({ message: "Product not found" });
    return;
  }

  const imageUpload = await db.imageUpload.findUnique({
    where: {
      id: imageId as string,
      accountId: permission.accountId,
    },
    include: {
      ProductImageUpload: {
        select: {
          productId: true,
        },
      },
    },
  });

  if (!imageUpload) {
    res.status(404).json({ message: "Image not found" });
    return;
  }

  if (
    imageUpload.ProductImageUpload.length === 0 ||
    imageUpload.ProductImageUpload.find(
      (piu) => piu.productId === product.id,
    ) === undefined
  ) {
    res.status(404).json({ message: "Image not found" });
    return;
  }

  const result = await db.productImageUpload.delete({
    where: {
      productId_imageUploadId: {
        imageUploadId: imageUpload.id,
        productId: product.id,
      },
    },
  });

  if (!result) {
    res.status(500).json({ message: "Something went wrong" });
    return;
  }

  res.status(200).json({ success: true });
};

export default handler;
