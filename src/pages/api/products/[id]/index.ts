import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { ProductSchema } from "~/server/product/types";
import { checkPermission, PermissionAction } from "~/pages/api/permissions";
import { captureException } from "@sentry/core";
import { updateWebsiteCollection } from "~/server/lib/website";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const items = await handleBasicProduct(
    req,
    res,
    req.method === "GET"
      ? "read"
      : req.method === "DELETE"
        ? "execute"
        : "write",
  );
  if (!items) {
    return;
  }
  if (req.method === "DELETE") {
    return handleDeleteProduct(req, res, items);
  }

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }
  const product = await db.product.findFirst({
    where: {
      id: items.productId,
      accountId: items.accountId,
      archived: false,
    },
    include: {
      ProductCategory: {
        select: {
          categoryId: true,
        },
      },
    },
  });
  if (!product) {
    res.status(404).json({ error: "Product not found" });
    return;
  }

  const categories = [
    ...product.ProductCategory.map((category) => category.categoryId),
  ];
  if (categories.length === 0) {
    if (product.categoryId) {
      categories.push(product.categoryId);
    }
    if (product.subCategoryId) {
      categories.push(product.subCategoryId);
    }
  }
  const productClone = {
    ...product,
    categories: categories,
  };

  const productOutput = ProductSchema.cast(productClone, {
    stripUnknown: true,
  });

  return res.status(200).json({ success: true, product: productOutput });
};

export default handler;

type BasicProductResponse = {
  productId: number;
  accountId: number;
};

export const handleBasicProduct = async (
  req: NextApiRequest,
  res: NextApiResponse,
  action: PermissionAction,
): Promise<BasicProductResponse | null> => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.product",
      action: action,
    },
  });

  if (!permission) {
    return null;
  }

  const productID = req.query.id;
  if (!productID) {
    res.status(400).json({ error: "Invalid product id" });
    return null;
  }

  const numberID = Number(productID);
  if (isNaN(numberID)) {
    res.status(400).json({ error: "Invalid product id" });
    return null;
  }

  return {
    productId: numberID,
    accountId: permission.accountId,
  };
};

export const handleDeleteProduct = async (
  req: NextApiRequest,
  res: NextApiResponse,
  { productId, accountId }: BasicProductResponse,
) => {
  const product = await db.product.findUnique({
    where: {
      id: productId,
      accountId: accountId,
      archived: false,
    },
    include: {
      OrderProduct: {
        select: {
          orderId: true,
        },
      },
    },
  });
  if (!product) {
    res.status(404).json({ error: "Product not found" });
    return;
  }

  // If there are no orders tied to this product, we can safely delete it.
  if (product.OrderProduct.length === 0) {
    try {
      await db.product.delete({
        where: {
          id: productId,
          accountId: accountId,
        },
      });
    } catch (e) {
      captureException(e);
      res.status(500).json({ error: "Error deleting product" });
    }
  } else {
    // If there are orders tied to this product, we should not delete it.
    try {
      await db.$transaction([
        db.product.update({
          where: {
            id: productId,
            accountId: accountId,
          },
          data: {
            display: false,
            archived: true,
          },
        }),
        db.cartItem.deleteMany({
          where: {
            productId: productId,
          },
        }),
        db.productCategory.deleteMany({
          where: {
            productId: productId,
          },
        }),
        db.productSetupSurface.deleteMany({
          where: {
            productId: productId,
          },
        }),
        db.productImageUpload.deleteMany({
          where: {
            productId: productId,
          },
        }),
      ]);
    } catch (e) {
      captureException(e);
      res.status(500).json({ error: "Error archiving product" });
    }
  }

  await updateWebsiteCollection(
    product.accountId,
    product.organizationId,
    `products-${product.organizationId}`,
  );
  return res.json({ success: true });
};
