import { AuthorizedNextApiRequest, withPermissions } from "../apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { getLogoForAccounts } from "~/server/lib/logo";
import { TeamResponse } from "~/query/account/query";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const user = await db.user.findUnique({
    where: {
      id: req.userId,
    },
    include: {
      staff: {
        include: {
          account: true,
        },
      },
    },
  });

  if (!user) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const staffs = user.staff;

  if (staffs.length === 0) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const logos = await getLogoForAccounts(
    staffs.map((staff) => ({
      id: staff.accountId,
    })),
  );

  const orgToAccount: TeamResponse = {};
  staffs.forEach((staff) => {
    if (!orgToAccount[staff.account.organizationId]) {
      orgToAccount[staff.account.organizationId] = [];
    }
    orgToAccount[staff.account.organizationId]!.push({
      id: staff.accountId,
      name: staff.account.name,
      logo: logos[staff.accountId] ?? "",
    });
  });

  return res.status(200).json({
    organizations: orgToAccount,
  });
};

export default withPermissions(
  {
    bypass: true,
  },
  handler,
);
