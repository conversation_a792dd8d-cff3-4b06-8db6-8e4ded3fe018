import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import {
  findOrCreateStripeCustomerPlatform,
  platformStripeServer,
} from "~/server/lib/stripe";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.account",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const account = await db.account.findFirst({
    where: {
      id: permission.accountId,
    },
  });

  if (!account) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const stripeCustomer = await findOrCreateStripeCustomerPlatform(account);

  const session = await platformStripeServer.billingPortal.sessions.create({
    customer: stripeCustomer.id,
    return_url: `${process.env.NEXT_PUBLIC_SELF_URL}/account`,
  });

  res.status(200).json({
    url: session.url,
  });
};

export default handler;
