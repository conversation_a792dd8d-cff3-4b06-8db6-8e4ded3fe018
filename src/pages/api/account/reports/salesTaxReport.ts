import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState } from ".prisma/client";
import {
  SalesTaxLineItem,
  SalesTaxResponse,
} from "~/pages/reports/sales-tax-report";
import { getCurrencyValue } from "~/server/lib/currency";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { endOfDay } from "~/server/lib/time";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startTime, endTime } = req.query;
  const date = startTime as string;
  const startDate = new Date(date);

  const endDate = endOfDay(new Date(endTime as string));

  const orders = await db.order.findMany({
    where: {
      accountId: req.accountId,
      state: OrderState.COMPLETED,
      endTime: {
        gte: startDate,
        lte: endDate,
      },
      taxExempt: false,
    },
    include: {
      eventAddress: true,
    },
  });
  const salesTaxLineItems: SalesTaxLineItem[] = [];

  const response: SalesTaxResponse = {
    startDate: startDate.toISOString(),
    salesTaxLineItems: salesTaxLineItems,
  };

  const salesByCity: Record<
    string,
    {
      taxableSales: number;
      taxOwed: number;
    }
  > = {};

  orders.forEach((order) => {
    const tax = order.taxAmount || 0;
    const city = (order.eventAddress.city || "Unknown").toLowerCase();
    let currentTaxableSales = salesByCity[city];

    if (!currentTaxableSales) {
      currentTaxableSales = {
        taxableSales: 0,
        taxOwed: 0,
      };
    }

    const orderSubtotal = order.finalTotal - tax;
    currentTaxableSales.taxableSales += orderSubtotal;
    currentTaxableSales.taxOwed += tax;
    salesByCity[city] = currentTaxableSales;
  });

  Object.entries(salesByCity).forEach(([city, value]) => {
    const capitalizedCityName = `${city.at(0)?.toUpperCase()}${city.substring(
      1,
    )}`;
    salesTaxLineItems.push({
      name: capitalizedCityName,
      taxableSales: getCurrencyValue(value.taxableSales),
      taxOwed: getCurrencyValue(value.taxOwed),
    });
  });

  res.status(200).json(response);
};

export default withPermissions({ bypass: true }, handler);
