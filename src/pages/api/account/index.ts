import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { AccountSettingsWithStripe } from "~/server/account/types";
import { toAddressType } from "~/server/lib/location/util";
import { checkPermission } from "~/pages/api/permissions";
import { SubscriptionStatus } from ".prisma/client";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.account",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const account = await db.account.findFirst({
    where: {
      id: permission.accountId,
    },
    include: {
      billingAddress: true,
    },
  });

  if (!account) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const settings: AccountSettingsWithStripe = {
    damageWaiverRate: account.damageWaiverRate,
    travelFeePerMile: account.travelFeePerMile,
    freeTravelRadius: account.freeTravelRadius,
    businessPhone: account.businessPhone,
    businessEmail: account.businessEmail,
    googleReviewLink: account.googleReviewLink,
    customDomain: account.customDomain,
    stripeConfigured: account.stripeAccountId !== null,
    billingAddress: toAddressType(account.billingAddress),
    businessTimezone: account.businessTimezone,
    minimumOrderPaymentPercentage: account.minimumOrderPaymentPercentage,
  };

  const activePlan = await db.subscription.findFirst({
    where: {
      accountId: account.id,
      OR: [
        {
          status: SubscriptionStatus.ACTIVE,
        },
        {
          status: SubscriptionStatus.TRIALING,
        },
      ],
    },
  });

  res.status(200).json({
    settings,
    plan: {
      level: (activePlan?.planLevel || "free").toUpperCase(),
    },
  });
};

export default handler;
