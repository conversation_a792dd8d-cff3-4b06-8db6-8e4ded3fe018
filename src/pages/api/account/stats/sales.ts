import { db } from "~/server/db";
import { NextApiResponse } from "next";
import { BarChartItem } from "~/components/Graph/BarChart";
import { CurrencyValue } from "~/server/lib/currency";
import { OrderState } from ".prisma/client";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { addQuarters, subQuarters, subYears } from "date-fns";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const currentDate = new Date();
  const orders = await db.order.findMany({
    where: {
      accountId: req.accountId,
      startTime: {
        gt: subYears(subQuarters(currentDate, 1), 1),
        lt: addQuarters(currentDate, 1),
      },
      OR: [
        { state: OrderState.ACTIVE },
        { state: OrderState.COMPLETED },
        { totalPaid: { gt: 0 } },
      ],
    },
  });

  const items: Record<string, { amount: number; committed: number }> = {};
  let overallTotal = 0;

  for (const order of orders) {
    const date = new Date(order.startTime);
    const key = `${date.getMonth() + 1}/${date.getFullYear()}`;
    const item = items[key];
    const orderAmount = order.totalPaid;
    const committedAmountSubPayment = Math.max(
      0,
      order.finalTotal - orderAmount,
    );
    if (item) {
      item.amount += orderAmount;
      item.committed += committedAmountSubPayment;
    } else {
      items[key] = {
        amount: orderAmount,
        committed: committedAmountSubPayment,
      };
    }
    overallTotal += orderAmount;
  }

  const currentPayment: BarChartItem[] = [];
  for (const itemsKey in items) {
    const item = items[itemsKey];
    if (!item) {
      continue;
    }

    currentPayment.push({
      name: itemsKey,
      totalReceived: CurrencyValue.fromPlatform(item.amount).amount,
      totalCommitted: CurrencyValue.fromPlatform(item.committed).amount,
      total: CurrencyValue.fromPlatform(item.amount + item.committed).amount,
    });
  }

  currentPayment.sort((a, b) => {
    const splitA = a.name.split("/");
    const splitB = b.name.split("/");
    if (splitA.length !== 2 || splitB.length !== 2) {
      return 0;
    }
    if (splitA[1] !== splitB[1]) {
      return Number(splitA[1]) > Number(splitB[1]) ? 1 : -1;
    }
    const aDate = new Date(a.name.split("/")?.at(0) ?? 0);
    const bDate = new Date(b.name.split("/")?.at(0) ?? 0);
    if (aDate > bDate) {
      return 1;
    } else if (aDate < bDate) {
      return -1;
    }
    return 0;
  });

  return res.status(200).json({
    payments: currentPayment,
    totalAmount: CurrencyValue.fromPlatform(overallTotal).amount,
  });
};

export default withPermissions({ bypass: true }, handler);
