import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { endOfDay, startOfDay, subDays } from "date-fns";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const now = new Date();

  // This usage of startOfDay is fine because it's not related to orders.
  const currentWeekStart = startOfDay(subDays(now, 7));
  const currentWeekEnd = endOfDay(now);

  // Calculate the start and end of the previous 7 days
  const previousPeriodStart = startOfDay(subDays(now, 14));
  const previousPeriodEnd = endOfDay(subDays(now, 8));

  // get the count of all the orders for this upcoming week
  const [current, previous] = await db.$transaction([
    db.shoppingSession.count({
      where: {
        accountId: req.accountId,
        createdAt: {
          gte: currentWeekStart,
          lte: currentWeekEnd,
        },
      },
    }),
    db.shoppingSession.count({
      where: {
        accountId: req.accountId,
        createdAt: {
          gte: previousPeriodStart,
          lte: previousPeriodEnd,
        },
      },
    }),
  ]);

  return res.status(200).json({
    current: current || 0,
    previous: previous || 0,
  });
};

export default withPermissions({ bypass: true }, handler);
