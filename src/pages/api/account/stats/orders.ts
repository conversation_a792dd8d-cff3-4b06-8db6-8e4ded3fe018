import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState } from ".prisma/client";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { endOfWeek, startOfWeek, subWeeks } from "date-fns";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const now = new Date();

  // Get the start and end of the current week (Monday to Sunday)
  const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 }); // Week starts on Monday
  const currentWeekEnd = endOfWeek(now, { weekStartsOn: 1 });

  // Get the start and end of the last week (Monday to Sunday)
  const lastWeekStart = startOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });
  const lastWeekEnd = endOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });

  // get the count of all the orders for this upcoming week
  const [upcomingOrders, lastWeekOrders] = await db.$transaction([
    db.order.count({
      where: {
        accountId: req.accountId,
        state: OrderState.ACTIVE,
        startTime: {
          gte: currentWeekStart,
          lte: currentWeekEnd,
        },
      },
    }),
    db.order.count({
      where: {
        accountId: req.accountId,
        state: OrderState.COMPLETED,
        startTime: {
          gte: lastWeekStart,
          lte: lastWeekEnd,
        },
      },
    }),
  ]);

  return res.status(200).json({
    current: upcomingOrders || 0,
    previous: lastWeekOrders || 0,
  });
};

export default withPermissions({ bypass: true }, handler);
