import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState } from ".prisma/client";
import { checkPermission } from "~/pages/api/permissions";
import { getCurrencyString } from "~/server/lib/currency";

export type RecentSaleObject = {
  customer: {
    id: string;
    displayName: string;
  };
  id: number;
  amount: string;
  startTime: string;
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      bypass: true,
    },
  });

  if (!permission) {
    return;
  }

  const startDateObj = new Date(Date.now() - 1000 * 60 * 60 * 24 * 7);

  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  const [monthlyOrders, orders] = await db.$transaction([
    db.order.count({
      where: {
        accountId: permission.accountId,
        createdAt: {
          gt: startOfMonth,
        },
        OR: [{ state: OrderState.COMPLETED }, { state: OrderState.ACTIVE }],
      },
    }),
    db.order.findMany({
      where: {
        accountId: permission.accountId,
        createdAt: {
          gt: startDateObj,
        },
        state: OrderState.ACTIVE,
      },
      include: {
        Customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            company: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    }),
  ]);

  const orderList: RecentSaleObject[] = orders.map((order) => {
    const surName = order.Customer.company
      ? `@ ${order.Customer.company}`
      : order.Customer.lastName;
    const displayName = `${order.Customer.firstName} ${surName}`;
    return {
      customer: {
        id: order.Customer.id,
        displayName,
      },
      id: order.id,
      amount: getCurrencyString(order.finalTotal),
      startTime: order.startTime.toISOString(),
    };
  });

  return res.status(200).json({
    orders: orderList,
    orderCount: monthlyOrders,
  });
};

export default handler;
