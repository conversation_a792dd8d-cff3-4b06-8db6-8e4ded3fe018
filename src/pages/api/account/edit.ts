import { NextApiResponse } from "next";
import { ACCOUNT_SETTINGS, AccountSettings } from "~/server/account/types";
import { db } from "~/server/db";
import {
  areAddressesEqual,
  findOrInsertAddress,
} from "~/server/lib/location/util";
import {
  findOrCreateStripeCustomerPlatform,
  platformStripeServer,
} from "~/server/lib/stripe";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { updateWebsite } from "~/server/lib/website";
import { getConciseAddress } from "~/server/lib/location/types";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const account = await db.account.findFirst({
    where: {
      id: req.accountId,
    },
    include: {
      billingAddress: true,
    },
  });

  if (!account) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const bodyJson: AccountSettings = req.body;

  const { billingAddress, businessEmail, customDomain, ...rest } = bodyJson;

  if (businessEmail && businessEmail !== account.businessEmail) {
    const stripeCustomer = await findOrCreateStripeCustomerPlatform(account);

    await platformStripeServer.customers.update(stripeCustomer.id, {
      email: businessEmail,
    });
  }

  if (
    account.customDomain &&
    customDomain &&
    account.customDomain !== customDomain
  ) {
    let shouldCreate = false;
    if (account.customDomain !== null) {
      const updateResponse = await fetch(
        "https://cloud.approximated.app/api/vhosts/update/by/incoming",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "api-key": "ea23c2ce-4b7a-4c68-85b8-fad3ad4cbca4-**********",
          },
          body: JSON.stringify({
            current_incoming_address: account.customDomain,
            incoming_address: customDomain,
            target_address: "evntflow.com",
          }),
        },
      );

      if (updateResponse.status === 404) {
        shouldCreate = true;
      }
    } else {
      shouldCreate = true;
    }
    if (shouldCreate) {
      await fetch("https://cloud.approximated.app/api/vhosts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "api-key": "ea23c2ce-4b7a-4c68-85b8-fad3ad4cbca4-**********",
        },
        body: JSON.stringify({
          incoming_address: customDomain,
          target_address: "evntflow.com",
        }),
      });
    }
  }

  if (
    billingAddress &&
    !areAddressesEqual(billingAddress, account.billingAddress)
  ) {
    const address = await findOrInsertAddress(billingAddress);
    if (address) {
      await db.account.update({
        where: {
          id: account.id,
        },
        data: {
          ...rest,
          billingAddressId: address.id,
          businessEmail: businessEmail,
          businessTimezone: rest.businessTimezone,
          googleReviewLink: rest.googleReviewLink,
          customDomain: customDomain?.toLowerCase(),
          minimumOrderPaymentPercentage:
            rest.minimumOrderPaymentPercentage ?? 0,
        },
      });
    }
  } else {
    await db.account.update({
      where: {
        id: account.id,
      },
      data: {
        ...rest,
        customDomain: customDomain?.toLowerCase(),
        businessEmail: businessEmail,
        businessTimezone: rest.businessTimezone,
        minimumOrderPaymentPercentage: rest.minimumOrderPaymentPercentage ?? 0,
      },
    });
  }

  await updateWebsite(
    {
      ...account,
      address: account.billingAddress
        ? getConciseAddress(account.billingAddress)
        : "",
    },
    customDomain?.toLowerCase() ?? "",
  );

  res.status(200).json({ message: "success" });
};

export default withPermissions(
  {
    policy: "api.account",
    action: "write",
  },
  withMethods(["POST"], withValidation(ACCOUNT_SETTINGS, handler)),
);
