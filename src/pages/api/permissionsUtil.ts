import { PermissionAction } from "~/pages/api/permissions";

export const stringPermissionCheck = (
  totalPermissions: string[],
  permissionName: string,
  action: PermissionAction,
) => {
  const qualifiedPermissionName = `${permissionName}.${action}`;

  return totalPermissions.some(
    (p) =>
      p === "*" ||
      p.toLowerCase() === `${permissionName.toLowerCase()}.*` ||
      p.toLowerCase() === qualifiedPermissionName.toLowerCase(),
  );
};

export const stringPermissionCheckAll = (
  totalPermissions: string[],
  qualifiedPermissionName: string,
) => {
  const splitPermission = qualifiedPermissionName.split(".");
  const permissionName = splitPermission.slice(0, -1).join(".");
  return totalPermissions.some(
    (p) =>
      p === "*" ||
      p.toLowerCase() === `${permissionName.toLowerCase()}.*` ||
      p.toLowerCase() === qualifiedPermissionName.toLowerCase(),
  );
};
