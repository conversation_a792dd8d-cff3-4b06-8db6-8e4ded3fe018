import { NextApiResponse } from "next";
import { Staff } from "@prisma/client";
import { getToken, JWT } from "next-auth/jwt";
import { db } from "~/server/db";
import type { IncomingMessage } from "http";
import type { NextApiRequestCookies } from "next/dist/server/api-utils";
import { stringPermissionCheck } from "~/pages/api/permissionsUtil";
import { unstable_cache } from "next/cache";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/server/auth";
import { redirect } from "next/navigation";

/**
 * policy: The policy to check against. This is reverse-dns style, e.g. "api.account"
 * action: The action to check for. This is one of "read", "write", or "execute"
 * prefix: This will override the default prefix with a custom one, the default is "com.PartyRentalPlatform" (nothing is case-sensitive)
 *
 * So for example, if you wanted to check if the user has permission to read the account, you would use:
 * {
 *  policy: "api.account",
 *  action: "read",
 * }
 * and the permission it would be checking for is "com.PartyRentalPlatform.api.account.read" or "com.partyrentalplatform.api.account.read"
 * This would match for users who have EXACTLY that permission, or users who have a permission that is a superset of that permission such as "com.partyrentalplatform.api.account.*"
 * Keep in mind this will not traverse the permission tree and so the wildcard will not match "com.partyrentalplatform.api.*.read" or "com.partyrentalplatform.*.read" or "com.partyrentalplatform.api.account.read.zone.*"
 */
export type PermissionCheck = {
  policy: string;
  action: PermissionAction;
  prefix?: string;
  bypass?: never;
};

export type PermissionAction = "read" | "write" | "execute";

/**
 * This permission check will bypass all permission checks and return the account and user
 */
export type OpenPermissionCheck = {
  policy?: never;
  action?: never;
  prefix?: never;
  bypass: true;
};

export type PermissionRequest = {
  req: IncomingMessage & {
    cookies: NextApiRequestCookies;
  };
  res: NextApiResponse | null;
  permission: PermissionCheck | OpenPermissionCheck;
};

export type PermissionCheckResult = {
  organizationId: string;
  accountId: number;
  staffId: string;
  userId: string;
  totalPermissions: string[];
  token: JWT;
};

export const checkPermission = async ({
  req,
  res,
  permission,
}: PermissionRequest): Promise<PermissionCheckResult | undefined> => {
  const token = await getToken({ req });
  if (!token) {
    if (res) {
      res.status(401).json({ error: "Unauthorized" });
    }
    return;
  }

  const totalPermissions = token.roles ?? [];

  if (permission.bypass) {
    return {
      token: token,
      organizationId: token.organizationId,
      accountId: token.accountId,
      staffId: token.id,
      userId: token.userId,
      totalPermissions: totalPermissions,
    };
  }

  const prefix = permission.prefix || "com.partyrentalplatform";
  const hasPermission = stringPermissionCheck(
    totalPermissions,
    `${prefix}.${permission.policy}`,
    permission.action,
  );

  if (!hasPermission) {
    if (res) {
      res
        .status(403)
        .json({ error: "Ask your administrator for permissions!" });
    }
    return;
  }

  return {
    token: token,
    accountId: token.accountId,
    organizationId: token.organizationId,
    staffId: token.id,
    userId: token.userId,
    totalPermissions: totalPermissions,
  };
};

const getStaffAndRole = async (accountId: number, staffId: string) => {
  return unstable_cache(
    async () => {
      return db.staff.findFirst({
        where: {
          accountId: accountId,
          id: staffId,
        },
        include: {
          StaffRole: {
            include: {
              role: {
                include: {
                  permissions: {
                    include: {
                      permission: {
                        select: {
                          policy: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      });
    },
    [`staff-${accountId}-${staffId}`],
    {
      revalidate: 900,
      tags: [`staff-${accountId}-${staffId}`],
    },
  )();
};
