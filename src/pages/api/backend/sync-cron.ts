import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import {
  updateWebsite,
  updateWebsiteCollection,
  updateWebsitePage,
} from "~/server/lib/website";
import { getConciseAddress } from "~/server/lib/location/types";
import { captureException } from "@sentry/core";

const SHARED_SECRET = "fb17fd92-99c2-41e0-841b-2b2c95483d1a";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  await syncCronItems();
  res.status(200).json({ message: "Success" });
};

export const syncCronItems = async () => {
  const accounts = await db.account.findMany({
    where: {
      deleted: false,
    },
    include: {
      billingAddress: true,
    },
  });

  const recentlyUpdatedWebsitePages = await db.websitePage.findMany({
    where: {
      accountId: {
        in: accounts.map((account) => account.id),
      },
      updatedAt: {
        lte: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
    },
  });

  const toMark = [];

  if (recentlyUpdatedWebsitePages.length !== 0) {
    console.log(
      `Updating website data for ${recentlyUpdatedWebsitePages.length} pages`,
    );
    for (const websitePage of recentlyUpdatedWebsitePages) {
      try {
        const account = accounts.find(
          (account) => account.id === websitePage.accountId,
        );

        if (!account?.customDomain) {
          continue;
        }

        await updateWebsitePage(
          account.customDomain,
          websitePage.slug,
          websitePage.content as any,
          account,
        );

        toMark.push(websitePage.id);
      } catch (error) {
        console.error(
          `Error updating website data for page ${websitePage.id}`,
          error,
        );
        captureException(error);
      }
    }

    await db.websitePage.updateMany({
      where: {
        id: {
          in: toMark,
        },
      },
      data: {
        updatedAt: new Date(),
      },
    });
  }

  for (const account of accounts) {
    if (!account.customDomain) {
      continue;
    }

    try {
      await updateWebsiteCollection(account.id, account.organizationId);
      await updateWebsite(
        {
          ...account,
          address: account.billingAddress
            ? getConciseAddress(account.billingAddress)
            : "",
        },
        account.customDomain ?? "",
      );
    } catch (error) {
      console.error(
        `Error updating website data for account ${account.id}`,
        error,
      );
      captureException(error);
    }
  }
};

export default handler;
