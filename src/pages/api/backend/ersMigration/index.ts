import { NextApiRequest, NextApiResponse } from "next";
import {
  ersEmailTemplate,
  ersSchedule,
  ersSingleWebpage,
  ersSingleWebpageResponse,
  ersUser,
  ersWebpageResponse,
} from "~/server/lib/ersInterfaces";
import { db } from "~/server/db";
import { trace } from "@opentelemetry/api";
import { putItem } from "~/server/lib/r2";
import {
  getCategoriesFromErs,
  getCategoryErsMapping,
  storeCategory,
} from "~/pages/api/backend/ersMigration/categories";
import {
  getProductsFromErs,
  storeProducts,
} from "~/pages/api/backend/ersMigration/products";
import { boolean, date, number, object, string } from "yup";
import { withValidation } from "~/pages/api/apiMiddleware";
import {
  getCustomersFromR2,
  migrateCustomers,
  storeCustomers,
} from "~/pages/api/backend/ersMigration/customers";
import { migrateOrders } from "./orders";
import { Product } from "@prisma/client";

const MigrationSchema = object().shape({
  accountId: number().required(),
  ersFolder: string().required(),
  ersKey: string().required(),
  ersToken: string().required(),
  wipeProductsAndCategories: boolean().required(),

  saveDocuments: boolean().required(),
  saveWebpages: boolean().required(),
  saveReferences: boolean().required(),
  saveUsers: boolean().required(),
  saveSchedules: boolean().required(),
  saveEmailTemplates: boolean().required(),

  migrateCategories: boolean().required(),
  migrateProducts: boolean().required(),
  migrateCustomers: object().shape({
    status: boolean().required(),
    limit: number().required(),
  }),
  migrateOrders: object().shape({
    status: boolean().required(),
    failedOnly: boolean().required(),
    earliestOrderDate: date().required(),
    latestOrderDate: date().required(),
  }),
});

export type ERSAuthentication = {
  folderName: string;
  key: string;
  token: string;
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const tracer = trace.getTracer("migration");
  // .getTracer('migration') can also make part of function chain
  return await tracer.startActiveSpan("auth", async (span) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const secret = authHeader.split(" ")[1];

      if (secret !== process.env.SHARED_SECRET) {
        return res.status(401).json({ error: "Unauthorized" });
      }
    } finally {
      span.end();
    }

    const schema: typeof MigrationSchema.__outputType = req.body;

    const { accountId, ersFolder } = schema;

    if (!accountId) {
      return res.status(400).json({ error: "No account specified" });
    }

    const account = await db.account.findFirst({
      where: {
        id: Number(accountId),
      },
    });

    if (!account) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const ersAuth: ERSAuthentication = {
      folderName: ersFolder,
      key: schema.ersKey,
      token: schema.ersToken,
    };

    if (schema.wipeProductsAndCategories) {
      console.log("Wiping products and categories");
      await db.product.deleteMany({
        where: {
          accountId: account.id,
        },
      });

      await db.category.deleteMany({
        where: {
          accountId: account.id,
        },
      });
    }

    // API documented at https://www.notion.so/ERS-API-Migration-80e40c20a9914f3cad0db0bc1b747160

    const success: string[] = [];

    // TODO: Figure out how to get document names and migrate them
    if (schema.saveDocuments) {
      console.log("migrating documents");
      try {
        await requestERS(ersAuth, `api/read/document/`, {
          name: " ",
        });

        success.push("documents");
      } catch (ex) {
        console.log(ex);
      }
    }

    if (schema.saveEmailTemplates) {
      console.log("migrating email templates");
      try {
        await migrateEmailTemplates(ersAuth);
        success.push("emailTemplates");
      } catch (ex) {
        console.log(ex);
      }
    }

    if (schema.saveWebpages) {
      console.log("migrating webpages");

      try {
        await migrateWebpages(ersAuth);

        success.push("webpages");
      } catch (ex) {
        console.log(ex);
      }
    }

    if (schema.saveReferences) {
      console.log("migrating references");

      try {
        await migrateReferences(ersAuth);
        success.push("references");
      } catch (ex) {
        console.log(ex);
      }
    }

    if (schema.saveUsers) {
      console.log("migrating users");

      try {
        await migrateUsers(ersAuth);
        success.push("users");
      } catch (ex) {
        console.log(ex);
      }
    }

    if (schema.saveSchedules) {
      console.log("migrating schedules");

      try {
        await migrateSchedules(ersAuth);

        success.push("schedules");
      } catch (ex) {
        console.log(ex);
      }
    }

    let categoryErsToName = undefined;
    if (schema.migrateCategories) {
      try {
        console.log("migrating categories");

        const categoriesFromErs = await getCategoriesFromErs(ersAuth);

        categoryErsToName = await storeCategory(
          ersAuth,
          categoriesFromErs,
          account,
        );

        success.push("categories");
      } catch (ex) {
        console.log(ex);
      }
    } else {
      console.log("Pulling Category ERS Mapping from R2");

      categoryErsToName = await getCategoryErsMapping(ersAuth);
    }

    if (!categoryErsToName) {
      return res.status(500).json({
        error:
          "No categories found, you must either migrate the categories or pull them",
      });
    }

    let productsDb: Product[] | undefined;
    if (schema.migrateProducts) {
      try {
        console.log("migrating products");
        const products = await getProductsFromErs(ersAuth);

        productsDb = await storeProducts(
          ersAuth,
          products,
          categoryErsToName,
          account,
        );

        const surfaces = await db.setupSurface.findMany({
          where: {
            accountId: account.id,
          },
        });

        for (const productsDbElement of productsDb) {
          try {
            await db.productSetupSurface.createMany({
              data: surfaces.map((surface) => {
                return {
                  productId: productsDbElement.id,
                  setupSurfaceId: surface.id,
                };
              }),
            });
          } catch (ex) {
            console.log("Setup Surface", ex);
          }
        }

        success.push("products");
      } catch (ex) {
        console.log(ex);
      }
    } else {
      console.log("Pulling Products from database.");

      productsDb = await db.product.findMany({
        where: {
          accountId: account.id,
        },
      });
    }

    if (!productsDb) {
      return res.status(500).json({
        error:
          "No products found, you must either migrate the products or pull them",
      });
    }

    let customers;
    let customersDb;
    if (schema.migrateCustomers.status) {
      try {
        console.log("Migrating Customers");
        customers = await migrateCustomers(
          ersAuth,
          schema.migrateCustomers.limit,
        );

        customersDb = await storeCustomers(accountId, customers);

        success.push("customers");
      } catch (ex) {
        console.log("Error migrating customers", ex);
      }
    } else {
      console.log("Pulling Customers from R2");
      customers = await getCustomersFromR2(ersFolder);

      customersDb = await db.customer.findMany({
        where: {
          accountId: account.id,
        },
      });
    }

    if (!customersDb || !customers) {
      return res.status(500).json({
        error:
          "No customers found, you must either migrate the customers or pull them",
      });
    }

    if (schema.migrateOrders.status) {
      console.log("Migrating Orders");
      try {
        const surfaceId = await db.setupSurface.findFirst({
          where: {
            accountId: account.id,
          },
        });

        if (!surfaceId) {
          console.log("No surface found");
          return;
        }

        await migrateOrders(
          accountId,
          account.organizationId,
          surfaceId.id,
          schema.migrateOrders.failedOnly,
          ersAuth,
          customers,
          customersDb,
          productsDb,
          schema.migrateOrders.earliestOrderDate,
          schema.migrateOrders.latestOrderDate,
        );
        success.push("orders");
      } catch (ex) {
        console.log("Error migrating orders", ex);
      }
    }

    res.status(200).json({
      status: 200,
      message: "Migration Successful",
      models_migrated: success,
    });
  });
};
const migrateWebpages = async (
  folderName: ERSAuthentication,
): Promise<ersSingleWebpage[]> => {
  const webpages = (await requestERS(
    folderName,
    `api/read/webpage/`,
  )) as ersWebpageResponse;
  // return webpages;
  const justSingleWebpages = [];
  for (const webpage of webpages.webpages) {
    const singlePage = (await requestERS(
      folderName,
      "api/read/webpage/" + webpage.id,
    )) as ersSingleWebpageResponse;
    // return singlePage;
    justSingleWebpages.push(singlePage.webpage as ersSingleWebpage);
  }
  return justSingleWebpages;
};
const migrateReferences = async (
  folderName: ERSAuthentication,
): Promise<any> => {
  const references = await requestERS(folderName, `api/read/references`);
};

/**
 * Parses a meta_info string with custom separators into a structured object
 * @param metaInfoString The raw meta_info string to parse
 * @returns An object containing the parsed metadata fields
 */
export function parseMetaInfo(metaInfoString: string): Record<string, string> {
  // Create an object to store the parsed values
  const result: Record<string, string> = {};

  // Split the string by the field separator
  const fields = metaInfoString.split("[_sep_]");

  // Process each field
  fields.forEach((field) => {
    // Split each field by the label/value separator, limiting to 2 parts
    const parts: string[] = field.split("[_sep2_]", 2);

    if (parts.length === 2) {
      // Extract the key (lowercase for consistency) and value
      const key = parts[0]!.trim().toLowerCase();
      // Add to our result object
      result[key] = parts[1]!.trim();
    }
  });

  return result;
}

const migrateUsers = async (ersFolder: ERSAuthentication): Promise<any> => {
  // this shit is wack as hell
  //{
  // "success": true,
  // "accounts": {
  //   "19": {
  //     "username": "Example Employee",
  //     "name": "Example Employee",
  //     "email": "",
  //     "phone": "",
  //     "group": "Employee",
  //     "eula": ""
  //   },
  //   "20": {
  //     "username": "Example Manager ",
  //     "name": "Example Manager",
  //     "email": "",
  //     "phone": "",
  //     "group": "Manager",
  //     "eula": ""
  //   },
  //   "status": "Success",
  //   "elapsed": "0.00"
  // }
  const companyLogin = await requestERS(ersFolder, `api/read/accounts/`);
  const justUsers = [];
  for (const personKey in companyLogin.accounts) {
    justUsers.push(companyLogin.accounts[personKey] as ersUser);
  }
  return justUsers;
};

// const migrateCustomerOrderContracts = async (
//   folderName: string,
//   customersOrders: ersOrder[],
// ): Promise<ersExpandedOrderContract[]> => {
//   // Looks like this doesnt work well either. The message field always says "Customer not found for order"
//   const customersOrdersContracts = [];
//   for (const customerOrder of customersOrders) {
//     const customerOrderContract = await requestERS(
//       folderName,
//       `api/read/contract/${customerOrder.order_id}`,
//     );
//     customerOrderContract.customer_id = customerOrder.customer.id;
//     customerOrderContract.order_id = customerOrder.order_id;
//     customersOrdersContracts.push(
//       customerOrderContract as ersExpandedOrderContract,
//     );
//     console.log(customerOrderContract);
//   }
//   return customersOrdersContracts;
// };

const migrateEmailTemplates = async (
  folderName: ERSAuthentication,
): Promise<ersEmailTemplate[]> => {
  //https://www.notion.so/api-read-ersemail_templates-f32253bd969c4457a2e92dcb3bea219b?pvs=4
  // ALL OF STEVES TEMPLATES SHOW AS EMPTY
  const emailTemplates = await requestERS(
    folderName,
    `api/read/ersmail_templates/`,
  );
  // TODO: Push Email Templates into DB
  return emailTemplates;
};

// const migrateCRMNotes = async (fileName: string, customers: any[]) => {
//   // TODO: Find a working example of this things payload and build a type
//   for (const customer of customers) {
//     const customer_crm_notes = await requestERS(
//       fileName,
//       `api/read/crm_customer_notes/${customer.id}`,
//     );
//   }
// };

const migrateSchedules = async (
  ersFolder: ERSAuthentication,
): Promise<ersSchedule[]> => {
  const schedules = await requestERS(ersFolder, `api/read/profiles`);
  return schedules;
};

export const requestERS = async (
  ersAuth: ERSAuthentication,
  path: string,
  body?: Record<string, string>,
) => {
  //"984068dedd5176726ce01abc9dc1e92d1ffd373a0cce68f81c77e63a70e4d87cc4672727028ea4417c2af568c3cd763961973951bb4601de845f09c583720d3c$99f77b29", // from /cp/api_keys
  // "centralarkansasinflatables_726cf0389e1b49124a2f870724439a89", // From the normal API view
  const fullUrl = `https://${ersAuth.folderName}.ourers.com/${path}${
    path.endsWith("/") ? "" : "/"
  }`;
  console.log("requesting", fullUrl);
  //https://inflatetheparty.ourers.com/api/read/webpage/
  //https://inflatetheparty.ourers.com/api/read/document/
  const response = await fetch(fullUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    body: new URLSearchParams({
      ...(body ?? {}),
      key: ersAuth.key,
      token: ersAuth.token,
    }),
  });
  const resp = await response.text();

  const responseObject: any = JSON.parse(resp);

  await putItem(
    "migration",
    `ers/${ersAuth.folderName}/${encodeURIComponent(path)}.json`,

    JSON.stringify(responseObject),
  );

  if (
    response.status < 500 &&
    response.status !== 429 &&
    responseObject?.status?.toLowerCase() === "success"
  ) {
    return responseObject;
  }
  // console.log(responseObject);
  throw new Error(`Request failed ${response.status} ${resp}`);
};

export default withValidation(MigrationSchema, handler);
