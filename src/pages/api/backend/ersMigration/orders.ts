import { findOrInsertAddress } from "~/server/lib/location/util";
import { db } from "~/server/db";
import { Customer, OrderState, Product } from "@prisma/client";
import { ersCustomer, ersOrder } from "~/server/lib/ersInterfaces";
import {
  ERSAuthentication,
  requestERS,
} from "~/pages/api/backend/ersMigration/index";
import { getJsonItem, putItem } from "~/server/lib/r2";

const migrateCustomerOrders = async (
  folderName: ERSAuthentication,
  customers: ersCustomer[],
  earliestDate: Date,
  latestDate: Date,
): Promise<ersOrder[]> => {
  // returns success, truncated, orders, status, and elapsed
  const customersOrders = [];
  for (const customer of customers) {
    // OG date range was 3 years when migrating CAI
    const customerOrders = await requestERS(
      folderName,
      `api/read/orders-by-customer/${
        customer.id
      }/${earliestDate.toISOString()}/${latestDate.toISOString()}`,
    );
    if (
      customerOrders.success == true &&
      customerOrders.status == "Success" &&
      customerOrders.orders.length > 0
    ) {
      for (const order of customerOrders.orders) {
        customersOrders.push(order as ersOrder);
      }
    } else if (
      customerOrders.success == false ||
      customerOrders.status != "Success"
    ) {
      console.log(
        "Error pulling customer orders for customer ID: " + customer.id,
      );
    }
  }

  return customersOrders;
};

export const migrateOrders = async (
  accountId: number,
  organizationId: string,
  setupSurfaceId: number,
  failedOnly: boolean,
  ersFolder: ERSAuthentication,
  customers: ersCustomer[],
  customersDb: Customer[],
  productsDb: Product[],
  earliestDate: Date,
  latestDate: Date,
) => {
  let customersOrders;
  const ordersCreated: Record<string, number> =
    (await getJsonItem(
      "migration",
      `ers/${ersFolder.folderName}/order-mapping.json`,
    )) ?? {};

  const failedOrderId: string[] =
    (await getJsonItem<string[]>(
      "migration",
      `ers/${ersFolder.folderName}/orders-failed.json`,
    )) ?? [];
  try {
    customersOrders = await migrateCustomerOrders(
      ersFolder,
      failedOnly
        ? customers.filter((customer) => {
            return failedOrderId.includes(customer.id);
          })
        : customers,
      new Date(earliestDate),
      new Date(latestDate),
    );
  } catch (e) {
    console.warn("Failed to migrate orders", e);
    return;
  }

  for (const customerOrder of customersOrders) {
    try {
      if (ordersCreated[customerOrder.order_id]) {
        console.log(
          `Order ${customerOrder.order_id} already migrated, skipping...`,
        );
        continue;
      }

      const customerId = customersDb.find((customer) => {
        return (
          customer.email.toLowerCase() ===
          customerOrder.customer.email.toLowerCase()
        );
      })?.id;

      if (!customerId) {
        console.log(customerId);
        console.log("Customer not found for order: " + customerOrder.order_id);
        if (!failedOnly) {
          failedOrderId.push(customerOrder.customer.id);
        }
        continue;
      }

      const eventAddress = await db.$transaction(async (tx) => {
        return findOrInsertAddress(
          {
            line1: customerOrder.address,
            city: customerOrder.city_name,
            state: customerOrder.state_name,
            postalCode: customerOrder.zip,
            country: "US",
          },
          tx,
        );
      });
      const order = await db.order.create({
        data: {
          accountId: accountId,
          organizationId: organizationId,
          customerId: customerId,
          startTime: new Date(customerOrder.start_datetime),
          endTime: new Date(customerOrder.end_datetime),
          internalNotes: customerOrder.internal_order_notes,
          customerNotes: customerOrder.customer_comments,
          state: OrderState.COMPLETED,
          setupSurfaceId: setupSurfaceId,
          eventAddressId: eventAddress.id,
          baseTotal: parseFloat(customerOrder.total),
          finalTotal: parseFloat(customerOrder.total),
          totalPaid: isNaN(parseFloat(customerOrder.paid))
            ? 0
            : parseFloat(customerOrder.paid),
        },
      });

      ordersCreated[customerOrder.order_id] = order.id;

      const orderProduct = [];
      for (const content of customerOrder.contents) {
        const productId = productsDb?.find((product) => {
          return product.name.toLowerCase() === content.item_name.toLowerCase();
        });

        if (!productId) {
          console.log("Product not found for order: " + customerOrder.order_id);
          continue;
        }

        orderProduct.push({
          orderId: order.id,
          productId: productId.id,
          quantity: parseInt(content.quantity),
          pricePaid: parseFloat(content.cost),
        });
      }

      await db.orderProduct.createMany({
        data: orderProduct,
      });

      if (Object.entries(ordersCreated).length % 40 === 0) {
        console.log(
          `Migrated ${
            Object.entries(ordersCreated).length
          } orders, saving progress...`,
        );

        await putItem(
          "migration",
          `ers/${
            ersFolder.folderName
          }/order-mapping-${new Date().toString()}.json`,
          JSON.stringify(ordersCreated),
        );
      }
    } catch (e) {
      failedOrderId.push(customerOrder.customer.id);
      console.warn(`Failed order creation for ${customerOrder.order_id}`, e);
    }
  }

  await putItem(
    "migration",
    `ers/${ersFolder.folderName}/orders-failed.json`,
    JSON.stringify(failedOrderId),
  );

  await putItem(
    "migration",
    `ers/${ersFolder.folderName}/order-mapping.json`,
    JSON.stringify(ordersCreated),
  );

  console.log(
    `Migrated ${
      Object.entries(ordersCreated).length
    } orders for account ${accountId}`,
  );
};
