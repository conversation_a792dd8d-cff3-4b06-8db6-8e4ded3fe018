import { ersProduct, ersProductResponse } from "~/server/lib/ersInterfaces";
import {
  ERSAuthentication,
  parseMetaInfo,
  requestERS,
} from "~/pages/api/backend/ersMigration/index";
import { db } from "~/server/db";
import { Account } from ".prisma/client";
import { uploadImageInternal } from "~/pages/api/images/upload";
import { getJsonItem, putItem } from "~/server/lib/r2";

function parseCategoryIds(categoryString: string): string[] {
  // Split by separator and filter out empty values
  return categoryString
    .replace("[_sep_]", "")
    .split("[_sep2_]")
    .filter(Boolean);
}

export const getProductsFromErs = async (folderName: ERSAuthentication) => {
  const products = (await requestERS(
    folderName,
    `api/read/iteminfo/`,
  )) as ersProductResponse;
  const justProducts = [];
  for (const product of products.items) {
    justProducts.push(product as ersProduct);
  }
  return justProducts;
};

export const storeProducts = async (
  ersFolder: ERSAuthentication,
  products: ersProduct[],
  categories: Record<
    string,
    {
      name: string;
      prpId: number;
    }
  >,
  account: Account,
) => {
  const productsToCreate: any[] = [];
  const productImageBlob: {
    productName: string;
    image: Blob;
  }[] = [];

  const productToCategory: Record<string, Set<string>> = (await getJsonItem(
    "migration",
    `ers/${ersFolder.folderName}/product-category-mapping.json`,
  )) ?? {};

  for (const product of products) {
    const numberPrice = parseFloat(product.cost.split(".")[0] || "");
    if (isNaN(numberPrice)) {
      console.log("Price not found for product: " + product.name);
      continue;
    }

    const image = await fetch(
      product.picture.includes("https://")
        ? product.picture
        : `https://files.sysers.com/cp/upload/${ersFolder.folderName}/items/full/${product.picture}`,
      {},
    ).then((response) => {
      return response.blob();
    });

    productImageBlob.push({
      productName: product.name,
      image: image,
    });

    // this is so stupid but... here we go...
    let metaTitle: undefined | string;
    let metaDescription: undefined | string;
    try {
      const parsedData = parseMetaInfo(product.meta_info);
      metaTitle = parsedData?.title || "";
      metaDescription = parsedData?.description || "";
    } catch (error) {
      console.warn("Error parsing meta info:", error);
    }

    productsToCreate.push({
      name: product.name,
      description: product.description,
      display: product.customer_display === "yes",
      organizationId: account.organizationId,
      accountId: account.id,
      price: numberPrice,
      quantity: Number(product.qty || "1"),
      metaTitle: metaTitle,
      metaDescription: metaDescription,
    });

    const map = (productToCategory[product.name] ??= new Set<string>());

    map.add(product.categoryid);
    // Parse extra_categoryid and add to the map
    const parsedCategoryIds = parseCategoryIds(product.extra_categoryid);
    if (parsedCategoryIds) {
      for (const categoryId of parsedCategoryIds) {
        map.add(categoryId);
      }
    }

    productToCategory[product.name] = map;
  }

  const productsDb = await db.product.createManyAndReturn({
    data: productsToCreate,
  });

  const productCategoryCreation: {
    productId: number;
    categoryId: number;
  }[] = [];

  for (const product of productsDb) {
    const productCategories = productToCategory[product.name];

    if (!productCategories) {
      console.warn(`Product ${product.name} does not have a category mapping.`);
      continue;
    }
    for (const categoryId of productCategories) {
      const categoryMapping = categories[categoryId];
      if (!categoryMapping) {
        console.warn(
          `Category ${categoryId} for product ${product.name} not found.`,
        );
        continue;
      }

      const productCategoryCreationArray = {
        productId: product.id,
        categoryId: categoryMapping.prpId,
      };

      productCategoryCreation.push(productCategoryCreationArray);
    }
  }

  await putItem(
    "migration",
    `ers/${ersFolder.folderName}/product-category-mapping.json`,
    JSON.stringify(productToCategory),
  );

  try {
    await db.productCategory.createMany({
      data: productCategoryCreation,
    });
  } catch (error) {
    console.error("Error creating product-category mappings:", error);
  }

  try {
    const productImages = await Promise.all(
      productImageBlob.map(async (productImage) => {
        return uploadImageInternal(
          productImage.image,
          productImage.productName,
          account.id,
        );
      }),
    );

    for (const productsDbKey of productsDb) {
      const imageId = productImages.find((productImage) => {
        return productImage?.name === productsDbKey.name;
      });
      if (imageId) {
        await db.productImageUpload.create({
          data: {
            productId: productsDbKey.id,
            imageUploadId: imageId.id,
            priority: 0,
          },
        });
      }
    }
  } catch (ex) {
    console.error("Error uploading product images:", ex);
  }

  return productsDb;
};
