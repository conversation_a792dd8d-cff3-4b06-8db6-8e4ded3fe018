import { ComponentConfig } from "@measured/puck";
import HeaderCarouselBlock from "~/components/WebsiteBuilder/blocks/HeaderCarouselBlock";
import SpacingBlock from "~/components/WebsiteBuilder/blocks/SpacingBlock";
import TextBlock from "~/components/WebsiteBuilder/blocks/TextBlock/field";
import CategoryGridBlock from "~/components/WebsiteBuilder/blocks/CategoryGridBlock";
import { ContainerBlock } from "~/components/WebsiteBuilder/blocks";
import GridBlock from "~/components/WebsiteBuilder/blocks/GridBlock";
import ReviewCarouselBlock from "~/components/WebsiteBuilder/blocks/ReviewCarouselBlock";
import FAQBlock from "~/components/WebsiteBuilder/blocks/FAQBlock";
import { BackgroundHeroBlock } from "~/components/WebsiteBuilder/blocks/BackgroundHeroBlock";

const ELEMENT_MAPPING: Record<string, ComponentConfig<any>> = {
  "elemtype-Bounce House Banner Carousel": HeaderCarouselBlock,
  "elemtype-Spacing": SpacingBlock, // 23px
  "ers-read-more-1": TextBlock, // h1 with text
  "elemtype-Store": CategoryGridBlock,
  "elemtype-Bounce House Specials Divider": ContainerBlock, // With TextBlock
  "elemtype-Bounce House CTA Divider 2": ContainerBlock,
  "elemtype-Text Heading Using Span": TextBlock,
  "elemtype-Bounce House Items 1": GridBlock,
  "elemtype-Bounce House Testimonials 1": ReviewCarouselBlock,
  "elemtype-Bounce House FAQ": FAQBlock,
  "elemtype-Bounce House Ers Hero for Template 2": BackgroundHeroBlock,
  "elemtype-Bounce House CTA Picture 1": ContainerBlock, // With Image and TextBlock
  "elemtype-Bounce House CTA Picture 2": BackgroundHeroBlock,
  // TODO "elemtype-Bounce House Product Categories 3":
};
