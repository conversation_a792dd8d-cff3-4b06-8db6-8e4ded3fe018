import { ersCategory, ersCategoryResponse } from "~/server/lib/ersInterfaces";
import {
  ERSAuthentication,
  requestERS,
} from "~/pages/api/backend/ersMigration/index";
import { getJsonItem, putItem } from "~/server/lib/r2";
import { db } from "~/server/db";
import { Account } from ".prisma/client";
import { uploadImageInternal } from "~/pages/api/images/upload";

export const getCategoriesFromErs = async (folderName: ERSAuthentication) => {
  const categories = (await requestERS(
    folderName,
    `api/read/categories/`,
  )) as ersCategoryResponse;
  const justCategories: ersCategory[] = [];
  for (const category of categories.rows) {
    justCategories.push(category as ersCategory);
  }
  return justCategories;
};

export const getCategoriesFromR2 = async (auth: ERSAuthentication) => {
  const categories = await getJsonItem<ersCategoryResponse>(
    `migration`,
    `ers/${auth.folderName}/${encodeURIComponent("api/read/categories/")}.json`,
  );
  const justCategories: ersCategory[] = [];
  for (const category of categories?.rows ?? []) {
    justCategories.push(category as ersCategory);
  }
  return justCategories;
};

export const getCategoryErsMapping = async (
  folderName: ERSAuthentication,
): Promise<
  | Record<
      string,
      {
        name: string;
        prpId: number;
      }
    >
  | undefined
> => {
  return getJsonItem<
    Record<
      string,
      {
        name: string;
        prpId: number;
      }
    >
  >(`migration`, `ers/${folderName.folderName}/category-mapping.json`);
};

export const storeCategory = async (
  ersFolder: ERSAuthentication,
  categories: ersCategory[],
  account: Account,
): Promise<
  Record<
    string,
    {
      name: string;
      prpId: number;
    }
  >
> => {
  const categoryErsToDb: Record<string, string> = {};
  const categoriesToCreate: {
    name: string;
    description: string;
    organizationId: string;
    accountId: number;
    display: boolean;
  }[] = [];
  const categoryImageBlob: {
    categoryName: string;
    image: Blob;
  }[] = [];
  for (const category of categories) {
    categoryErsToDb[category.id] = category.name;

    const image = await fetch(
      category.picture.includes("https://")
        ? category.picture
        : `https://files.sysers.com/cp/upload/${ersFolder.folderName}/categories/full/${category.picture}`,
      {},
    ).then((response) => {
      return response.blob();
    });

    categoryImageBlob.push({
      categoryName: category.name,
      image: image,
    });
    categoriesToCreate.push({
      name: category.name,
      description: category.description,
      organizationId: account.organizationId,
      accountId: account.id,
      display: category.display === "yes",
    });
  }

  const cat = await db.category.createManyAndReturn({
    data: categoriesToCreate,
  });

  try {
    const categoryImages = await Promise.all(
      categoryImageBlob.map(async (categoryImage) => {
        return uploadImageInternal(
          categoryImage.image,
          categoryImage.categoryName,
          account.id,
        );
      }),
    );

    await db.$transaction(async (tx) => {
      for (const categoryDb of cat) {
        const imageId = categoryImages.find((categoryImage) => {
          return categoryImage?.name === categoryDb.name;
        });
        if (imageId) {
          tx.categoryImageUpload.create({
            data: {
              categoryId: categoryDb.id,
              imageUploadId: imageId.id,
            },
          });
        }
      }
    });
  } catch (error) {
    console.error("Error uploading category images:", error);
  }

  const response: Record<
    string,
    {
      name: string;
      prpId: number;
    }
  > = {};

  for (const [key, value] of Object.entries(categoryErsToDb)) {
    const category = cat.find((category) => category.name === value);
    if (!category) {
      continue;
    }
    response[key] = {
      name: value,
      prpId: category.id,
    };
  }

  // storing the category mapping
  await putItem(
    "migration",
    `ers/${ersFolder.folderName}/category-mapping.json`,
    JSON.stringify(response),
  );

  return response;
};
