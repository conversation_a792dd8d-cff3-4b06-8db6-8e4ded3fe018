import { getJsonItem } from "~/server/lib/r2";
import { ersCustomer, ersCustomerResponse } from "~/server/lib/ersInterfaces";
import {
  ERSAuthentication,
  requestERS,
} from "~/pages/api/backend/ersMigration/index";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { db } from "~/server/db";

export const getCustomersFromR2 = async (folderName: string) => {
  const customers = await getJsonItem<ersCustomerResponse>(
    `migration`,
    `ers/${folderName}/${encodeURIComponent("api/read/customers/")}.json`,
  );
  const just_customers: ersCustomer[] = [];
  for (const customer of customers?.customers || []) {
    just_customers.push(customer as ersCustomer);
  }

  return just_customers;
};

export const storeCustomers = async (
  accountId: number,
  customers: ersCustomer[],
) => {
  const customersToCreate = [];
  const seenEmails = new Set();

  for (const customer1 of customers.filter((customer) => {
    return customer.email !== "";
  })) {
    const address = await findOrInsertAddress({
      line1: customer1.billing_address,
      city: customer1.billing_city,
      state: customer1.billing_state,
      postalCode: customer1.billing_zip,
      country: "US",
    });
    if (!address) {
      console.log(
        "Address not found for customer: " +
          customer1.firstname +
          " " +
          customer1.lastname,
      );
      continue;
    }
    if (!customer1.firstname) {
      console.log(
        "Name not found for customer: " +
          customer1.firstname +
          " " +
          customer1.lastname,
      );
      continue;
    }
    const email = (
      customer1.email ||
      customer1.parent_email ||
      customer1.secondary_email
    ).toLowerCase();
    if (!email) {
      console.log(
        "Email not found for customer: " +
          customer1.firstname +
          " " +
          customer1.lastname,
      );
      continue;
    }
    if (seenEmails.has(email)) {
      console.log(
        "Duplicate email found for customer: " +
          customer1.firstname +
          " " +
          customer1.lastname,
      );
      continue;
    }
    seenEmails.add(email);

    const phone = formatPhoneNumber(
      customer1.mobile_phone ?? customer1.work_phone ?? customer1.phone,
    );

    customersToCreate.push({
      accountId: accountId,
      addressId: address.id,
      firstName: customer1.firstname,
      lastName: customer1.lastname ?? "",
      email: email,
      phoneNumber: phone,
      company: customer1.company_name,
      reference: customer1.reference_name,
      internalNotes: customer1.customer_notes,
    });
  }

  console.log(
    `Creating ${customersToCreate.length} customers for account ${accountId}`,
  );

  return await db.customer.createManyAndReturn({
    data: customersToCreate,
  });
};

export const migrateCustomers = async (
  folderName: ERSAuthentication,
  limit: number,
): Promise<ersCustomer[]> => {
  // https://www.notion.so/api-read-customers-d5e271cef38a490a898681058df5ba95?pvs=4
  const searchTerm = " ";
  const customers = (await requestERS(folderName, `api/read/customers/`, {
    searchterm: searchTerm,
    limit: limit.toString(),
  })) as ersCustomerResponse;
  const just_customers: ersCustomer[] = [];
  for (const customer of customers.customers) {
    just_customers.push(customer as ersCustomer);
  }

  return just_customers;
};
