import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { promRegister } from "~/server/metrics";

const SHARED_SECRET = "3a2e3c82-414a-46cd-992e-23d7b6cf2ae0";

export const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET && secret !== btoa(SHARED_SECRET)) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const prismaMetrics = await db.$metrics.prometheus();
  const appMetrics = await promRegister.metrics();
  res.end(prismaMetrics + appMetrics);
};

export default handler;
