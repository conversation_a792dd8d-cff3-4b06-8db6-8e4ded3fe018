import { NextApiRequest, NextApiResponse } from "next";
import { sendScheduledEmails } from "~/server/services/scheduledEmailCron";
import { syncCronItems } from "~/pages/api/backend/sync-cron";
import { db } from "~/server/db";

const SHARED_SECRET = "fb17fd92-99c2-41e0-841b-2b2c95483d1a";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const time = new Date();
  const minutes = time.getMinutes();
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  // every 10 minutes send scheduled emails
  await sendScheduledEmails();

  // every 30 minutes send sync website items.
  if (
    process.env.NODE_ENV === "production" &&
    !process.env.NEXT_PUBLIC_SELF_URL?.includes("staging")
  ) {
    if (
      minutes > 0 &&
      minutes < 12 &&
      time.getHours() <= 1 &&
      time.getHours() >= 0
    ) {
      await db.order.updateMany({
        where: {
          endTime: {
            lt: new Date(new Date().setDate(new Date().getDate() - 2)),
          },
          state: "ACTIVE",
          totalPaid: {
            gte: db.order.fields.finalTotal,
          },
        },
        data: {
          state: "COMPLETED",
        },
      });
    }

    // if ((minutes > 29 && minutes < 40) || (minutes > 0 && minutes < 10)) {
    //   await syncCronItems();
    // }
  }

  res.status(200).json({ message: "Success" });
};

export default handler;
