import { NextApiRequest, NextApiResponse } from "next";
import { getToken } from "next-auth/jwt";
import { getAddressSuggestions } from "~/server/lib/location/maps";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const token = await getToken({ req });

  if (!token) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  const { query } = req.query;
  if (!query) {
    res.status(400).json({ error: "Missing query" });
    return;
  }

  const placeComplete = await getAddressSuggestions(query as string);
  res.status(200).json({ placeComplete: placeComplete });
};

export default handler;
