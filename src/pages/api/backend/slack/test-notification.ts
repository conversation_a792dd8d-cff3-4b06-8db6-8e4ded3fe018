import { NextApiResponse } from "next";
import { AuthorizedNextApiRequest, withPermissions } from "../../apiMiddleware";
import { db } from "~/server/db";
import { createSlackWebhook } from "~/lib/slack";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  // check if they have a phoneAccount
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: req.accountId,
    },
  });

  if (!phoneAccount) {
    return res.status(400).json({ error: "Phone account not found" });
  }

  // check if they have a slack webhook url
  if (!phoneAccount.forwardMessagesToSlackWebhookUrl) {
    return res.status(400).json({ error: "Slack webhook URL not set" });
  }

  // send a test notification

  await createSlackWebhook(
    phoneAccount.forwardMessagesToSlackWebhookUrl,
  ).test();
  return res.status(200).json({
    status: "success",
    message: "Test notification sent successfully",
  });
};

export default withPermissions(
  {
    bypass: true,
  },
  GET,
);
