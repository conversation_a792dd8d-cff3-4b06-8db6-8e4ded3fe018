import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { Account, OrderState, Prisma } from ".prisma/client";
import { r2 } from "~/server/lib/r2";
import { trace } from "@opentelemetry/api";
import { PutObjectCommand } from "@aws-sdk/client-s3";

import {
  ioCategory,
  ioCategoryResponse,
  ioCustomer,
  ioCustomerResponse,
  ioOrder,
  ioOrderResponse,
  ioProduct,
  ioProductResponse,
} from "~/server/lib/ioInterfaces";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { findOrInsertAddress } from "~/server/lib/location/util";
import {
  calculateOrderTotal,
  OrderFeeInput,
  ProductChargeInput,
} from "~/server/lib/orderUtil";
import { uploadImageInternal } from "~/pages/api/images/upload";
import CategoryCreateManyInput = Prisma.CategoryCreateManyInput;
import ProductCreateManyInput = Prisma.ProductCreateManyInput;
import CustomerCreateManyInput = Prisma.CustomerCreateManyInput;

// https://rental.software/support/knowledge-base/articles/api

const DEFAULT_PRP_LOGO =
  "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const tracer = trace.getTracer("migration");
  // .getTracer('migration') can also make part of function chain
  return await tracer.startActiveSpan("auth", async (span) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const secret = authHeader.split(" ")[1];

      if (secret !== process.env.SHARED_SECRET) {
        return res.status(401).json({ error: "Unauthorized" });
      }
    } finally {
      span.end();
    }

    const accountQuery = req.query.account;
    const apiKey = req.query.apiKey;

    if (!accountQuery) {
      return res.status(400).json({ error: "No account specified" });
    }

    if (typeof accountQuery !== "string") {
      return res.status(400).json({ error: "Account must be a string" });
    }

    if (!apiKey) {
      return res.status(400).json({ error: "No apiKey specified" });
    }

    if (typeof apiKey !== "string") {
      return res.status(400).json({ error: "apiKey must be a string" });
    }

    try {
      const account = await db.account.findFirst({
        where: {
          name: accountQuery,
        },
      });
      if (!account) {
        return res.status(401).json({ error: "Unauthorized" });
      }
      const success = [];

      // await emptyTheAccount(account.id);

      const categories = await migrateCategories(account, apiKey);
      const categoryIOToDb = categories[1];
      success.push("categories");
      const products = await migrateProducts(account, apiKey, categoryIOToDb);
      success.push("products");
      success.push("success");

      const { mapping } = await migrateCustomers(account, apiKey);
      success.push("customers");

      await migrateOrders(account, apiKey, mapping, products.mapping);
      success.push("orders");

      const responseBody = {
        status: 200,
        message: "Migration Successful",
        models_migrated: success,
      };
      res.status(200).send(JSON.stringify(responseBody, null, 2));
    } catch (e) {
      console.log(e);
      res.status(500).send(JSON.stringify(e, null, 2));
    }
  });
};
const emptyTheAccount = async (accountId: number) => {
  await db.$transaction([
    db.order.deleteMany({
      where: {
        accountId: accountId,
      },
    }),
    db.customer.deleteMany({
      where: {
        accountId: accountId,
      },
    }),
    db.category.deleteMany({
      where: {
        accountId: accountId,
      },
    }),
    db.product.deleteMany({
      where: {
        accountId: accountId,
      },
    }),
    db.imageUpload.deleteMany({
      where: {
        accountId: accountId,
      },
    }),
  ]);
};
const migrateCategories = async (
  account: Account,
  apiKey: string,
): Promise<[ioCategory[], Record<string, string>]> => {
  const categories = (await requestIO(
    account.name,
    apiKey,
    `api6/categories_list`,
  )) as ioCategoryResponse;
  const justCategories = [];

  for (const category of categories.items) {
    justCategories.push(category as ioCategory);
  }
  // console.log(justCategories);

  // Mapping of IO's category ID to the categories name
  const categoryIOToDb: Record<string, string> = {};
  const categoriesToCreate: CategoryCreateManyInput[] = [];
  const categoryImageBlob: {
    categoryName: string;
    image: Blob;
  }[] = [];

  for (const category of justCategories) {
    categoryIOToDb[category.id] = category.name;

    const image = await fetch(category.imageloc || DEFAULT_PRP_LOGO, {}).then(
      (response) => {
        return response.blob();
      },
    );
    categoryImageBlob.push({
      categoryName: category.name,
      image: image,
    });
    categoriesToCreate.push({
      name: category.name,
      description: category.description,
      organizationId: account.organizationId,
      accountId: account.id,
      display: true,
    });
  }
  await db.category.createMany({
    data: categoriesToCreate,
  });
  const categoryImages = await Promise.all(
    categoryImageBlob.map((categoryImage) => {
      return uploadImageInternal(
        categoryImage.image,
        categoryImage.categoryName,
        account.id,
      );
    }),
  );
  const categoriesDb = await db.category.findMany({
    where: {
      accountId: account.id,
    },
  });

  for (const categoryDb of categoriesDb) {
    const imageId = categoryImages.find((categoryImage) => {
      return categoryImage?.name === categoryDb.name;
    });
    if (imageId) {
      await db.categoryImageUpload.create({
        data: {
          categoryId: categoryDb.id,
          imageUploadId: imageId.id,
        },
      });
    }
  }
  console.log("CATEGORIES FOUND");
  return [justCategories, categoryIOToDb];
};

const migrateOrders = async (
  account: Account,
  apiKey: string,
  customerMapping: Record<string, string>,
  productMapping: Record<string, number>,
) => {
  const orders = await getOrdersFromIO(account, apiKey);

  const surfaces = await db.setupSurface.findMany({
    where: {
      accountId: account.id,
    },
  });

  let defaultSurface;
  if (surfaces.length === 0) {
    defaultSurface = await db.setupSurface.create({
      data: {
        name: "Default Surface",
        description: "Default Surface",
        accountId: account.id,
      },
    });
  } else {
    defaultSurface = surfaces[0];
  }

  if (!defaultSurface) {
    console.log("Default surface not found");
    return;
  }

  for (const order of orders) {
    if (order.status.name === "Temporary") {
      continue;
    }
    const customer: ioCustomer = order.cust;
    const customerId = customerMapping[customer.id];
    if (!customerId) {
      console.log("Customer not found in mapping: " + customer.id, order.id);
      continue;
    }

    const orderItems: ProductChargeInput[] = [];
    Object.entries(order.rentals).forEach(([key, value]) => {
      const product = productMapping[key];
      if (!product) {
        console.log("Product not found in mapping: " + key, order.id);
        return;
      }
      orderItems.push({
        productId: product,
        quantity: Number(value.quantity),
        pricePaid: Number(value.price) || 0,
      });
    });

    const eventAddress = await findOrInsertAddress({
      city: order.eventcity,
      state: order.eventstate,
      postalCode: order.eventzip,
      line1: order.eventstreet,
      country: order.eventcountry || "US",
    });
    const fees: OrderFeeInput[] = [];
    const surface = order.surface.includes("Grass") ? "grass" : "concrete";
    let setupSurface = surfaces.find((s) => {
      return s.name === surface;
    });
    if (!setupSurface) {
      setupSurface = defaultSurface;
    }
    const travelFeeIndex = order.feetype.indexOf("distance");
    if (travelFeeIndex !== -1) {
      fees.push({
        type: "TRAVEL_FEE",
        amount: Number(order.feeprice[travelFeeIndex]),
        name: null,
        taxable: true,
      });
    }
    const stateMapping: Record<string, OrderState> = {
      Quote: OrderState.QUOTE,
      Partial: OrderState.QUOTE,
      Cancelled: OrderState.CANCELLED,
      Complete: OrderState.COMPLETED,
    };

    const orderTotal = await calculateOrderTotal(
      account.id,
      orderItems,
      [],
      fees,
      { enabled: false, percentage: null },
      Number(order.taxrate || "8"),
      Number(order.amountpaid || "0"),
    );

    const newOrder = await db.order.create({
      data: {
        setupSurfaceId: setupSurface?.id,
        accountId: account.id,
        organizationId: account.organizationId,
        customerId: customerId,
        startTime: new Date(order.eventstarttime_utc),
        endTime: new Date(order.eventendtime_utc),
        customerNotes: order.notes || undefined,
        internalNotes: `Original Order State ${order.status.name}`,
        state: stateMapping[order.status.name] || OrderState.ACTIVE,

        baseTotal: orderTotal.baseTotal,
        couponAmount: 0,
        couponId: null,
        saleAmount: 0,
        generalDiscountAmount: 0,
        damageWaiverRate: null,
        damageWaiverAmount: null,
        taxRate: orderTotal.tax ? orderTotal.tax.percentage : null,
        taxAmount: orderTotal.tax ? orderTotal.tax.amount : null,
        taxExempt: orderTotal.tax === null,
        finalTotal: Number(order.total || orderTotal.finalTotal.toString()),
        totalPaid: Number(order.amountpaid || "0"),
        eventAddressId: eventAddress.id,
      },
    });

    await db.orderFee.createMany({
      data: fees.map((fee) => {
        return {
          orderId: newOrder.id,
          type: fee.type,
          amount: fee.amount,
          name: fee.name,
        };
      }),
    });

    await db.orderProduct.createMany({
      data: orderItems.map((item) => {
        return {
          orderId: newOrder.id,
          productId: item.productId,
          quantity: item.quantity,
          pricePaid: item.pricePaid,
        };
      }),
    });
  }
};

const getOrdersFromIO = async (
  account: Account,
  apiKey: string,
): Promise<ioOrder[]> => {
  const orders = (await requestIO(account.name, apiKey, `api6/leads`, {
    _body: "true",
    limit: "1000",
  })) as ioOrderResponse;
  const justOrders = [];
  for (const order of orders.items) {
    justOrders.push(order as ioOrder);
  }
  let hasMoreOrders = orders.items.length === 1000;
  let offset = 1000;
  while (hasMoreOrders) {
    const newOrders = (await requestIO(account.name, apiKey, `api6/leads`, {
      _body: "true",
      limit: "1000",
      offset: offset,
    })) as ioOrderResponse;
    for (const order of newOrders.items) {
      justOrders.push(order as ioOrder);
    }
    hasMoreOrders = newOrders.items.length === 1000;
    offset += 1000;
  }
  return justOrders;
};

const isGhostCustomer = (customer: ioCustomer): boolean => {
  return customer.email.trim() === "" && customer.contact_phone.trim() === "";
};

const migrateCustomers = async (
  account: Account,
  apiKey: string,
): Promise<{ customers: ioCustomer[]; mapping: Record<string, string> }> => {
  const customers = (await requestIO(account.name, apiKey, `api6/customers`, {
    _body: "true",
    limit: "1000", // defaults to 25
  })) as ioCustomerResponse;
  const justCustomers = [];
  for (const customer of customers.items) {
    justCustomers.push(customer as ioCustomer);
  }
  let hasMoreCustomers = customers.items.length === 1000;
  let offset = 1000;
  while (hasMoreCustomers) {
    const newCustomers = (await requestIO(
      account.name,
      apiKey,
      `api6/customers`,
      {
        _body: "true",
        limit: "1000",
        offset: offset,
      },
    )) as ioCustomerResponse;
    for (const customer of newCustomers.items) {
      justCustomers.push(customer as ioCustomer);
    }
    hasMoreCustomers = newCustomers.items.length === 1000;
    offset += 1000;
  }

  // take all the customers that aren't "ghost" customers
  const justCustomersNoGhosts = justCustomers.filter(
    (c) => !isGhostCustomer(c),
  );

  const addressMap: Record<string, number> = {};
  for (const c of justCustomersNoGhosts) {
    const address = await findOrInsertAddress({
      city: c.city,
      state: c.state,
      postalCode: c.zip,
      line1: c.street,
      country: c.country || "US",
    });
    addressMap[c.id] = address.id;
  }

  const usedEmails = new Map<string, string>();
  // Maps the original id to the duplicate id
  const customerInternalMapping = new Map<string, string[]>();

  await db.customer.createMany({
    data: justCustomersNoGhosts
      .map((c) => {
        const phoneNumber =
          c.contact_phone || c.cellphone || c.homephone || c.officephone;
        const email =
          c.email.toLowerCase() || `void+io-${c.id}@partyrentalplatform.com`;

        if (usedEmails.has(email)) {
          const previousMapped =
            customerInternalMapping.get(usedEmails.get(email) || "") || [];
          previousMapped.push(c.id);
          customerInternalMapping.set(
            usedEmails.get(email) || "",
            previousMapped,
          );
          console.log("Duplicate email found: " + email);
          return null;
        }

        const customer: CustomerCreateManyInput = {
          accountId: account.id,
          firstName: c.firstname || `io-${c.id}`,
          company: c.organization || null,
          lastName: c.lastname,
          email: email,
          phoneNumber:
            phoneNumber.trim() === "" ? null : formatPhoneNumber(phoneNumber),
          internalNotes: c.notes || null,
          points: Number(c.score || "0"),
          addressId: addressMap[c.id],
          banned: c.isblacklisted === "1",
          reference: c.referral || null,
        };
        usedEmails.set(email, c.id);
        return customer;
      })
      .flatMap((c) => (c ? [c] : [])),
  });

  const customersDb = await db.customer.findMany({
    where: {
      accountId: account.id,
    },
  });

  const customerMap: Record<string, string> = {};
  for (const customer of customersDb) {
    const ioCustomer = usedEmails.get(customer.email.toLowerCase());

    if (!ioCustomer) {
      console.log("No customer found for email: " + customer.email);
      continue;
    }

    // map duplicates back to the original
    const ioCustomerOriginal = customerInternalMapping.get(ioCustomer);

    if (ioCustomerOriginal) {
      for (const ioCustomerOriginalId of ioCustomerOriginal) {
        customerMap[ioCustomerOriginalId] = customer.id;
      }
    }

    customerMap[ioCustomer] = customer.id;
  }

  return {
    customers: justCustomers,
    mapping: customerMap,
  };
};

const migrateProducts = async (
  account: Account,
  apiKey: string,
  categoryIOToDb: Record<string, string>,
): Promise<{ products: ioProduct[]; mapping: Record<string, number> }> => {
  const products = (await requestIO(account.name, apiKey, `api6/rentals`, {
    _prices: "true",
    _body: "true",
  })) as ioProductResponse;
  const justProducts = [];
  for (const product of products.items) {
    justProducts.push(product as ioProduct);
  }
  console.log("GOING TO FIND PRODUCTS");
  const productsToCreate: ProductCreateManyInput[] = [];
  const productImageBlob: {
    productName: string;
    image: Blob;
    priority: number;
  }[] = [];

  for (const product of justProducts) {
    const category = await db.category.findFirst({
      where: {
        accountId: account.id,
        name: categoryIOToDb[product.category],
      },
    });
    if (!category) {
      console.log("Category not found for product: " + product.ridename);
      // continue;
    }

    // README: If the prices are all not found, you can go to the customer site /all-items which contains a list of all the products and their prices

    let numberPrice = 0;
    if (!product.prices["1"]) {
      console.log("Price not found for product: " + product.ridename);
      product.rideactive = "0"; // disabling product so that it doesnt show up without a price
      // continue;
    } else {
      numberPrice = Number(product.prices["1"]);
      if (isNaN(numberPrice)) {
        console.log("Price is not a number for product: " + product.ridename);
        numberPrice = 0;
        product.rideactive = "0"; // disabling product so that it doesnt show up without a price
      } else {
        console.log(`Price found for product: ${numberPrice}`);
      }
    }

    for (const image of Object.values(product.images)) {
      if (!image.rentalimage_imageloc) {
        continue;
      }
      const imageBlob = await fetch(image.rentalimage_imageloc, {}).then(
        (response) => {
          return response.blob();
        },
      );
      productImageBlob.push({
        productName: product.ridename,
        image: imageBlob,
        priority: Number(image.rentalimage_order || "0"),
      });
    }

    if (productImageBlob.length === 0) {
      const image = await fetch(product.imageloc || DEFAULT_PRP_LOGO, {}).then(
        (response) => {
          return response.blob();
        },
      );
      productImageBlob.push({
        productName: product.ridename,
        image: image,
        priority: 0,
      });
    }

    productsToCreate.push({
      organizationId: account.organizationId,
      name: product.ridename,
      description: product.description,
      display: numberPrice !== 0,
      setupTimeMinutes: Number(product.setuptime || "0"),
      takeDownTimeMinutes: Number(product.tdowntime || "0"),
      slug: product.ridename,
      accountId: account.id,
      price: numberPrice,
      quantity: Number(product.quantity || "1"),
      categoryId: Number(category?.id),
    });
  }

  await db.product.createMany({
    data: productsToCreate,
  });
  const productsDb = await db.product.findMany({
    where: {
      accountId: account.id,
    },
  });
  const productImages = await Promise.all(
    productImageBlob.map(async (productImage) => {
      return {
        imageId: await uploadImageInternal(
          productImage.image,
          productImage.productName,
          account.id,
        ),
        priority: 0,
      };
    }),
  );

  for (const productsDbKey of productsDb) {
    const imageId = productImages.find((productImage) => {
      return productImage.imageId?.name === productsDbKey.name;
    });
    if (imageId?.imageId) {
      await db.productImageUpload.create({
        data: {
          productId: productsDbKey.id,
          imageUploadId: imageId.imageId.id,
          priority: imageId.priority,
        },
      });
    }
  }

  const productMapping: Record<string, number> = {};
  for (const product of productsDb) {
    const ioProduct = justProducts.find((p) => {
      return p.ridename === product.name;
    });
    if (!ioProduct) {
      console.log("Product not found in IO: " + product.name);
      continue;
    }
    productMapping[ioProduct.id] = product.id;
  }

  console.log("PRODUCTS FOUND");
  return { products: justProducts, mapping: productMapping };
};
const requestIO = async (
  accountName: string,
  apiKey: string,
  path: string,
  queryParams: Record<string, string | number | boolean> = {},
) => {
  const queryString = `?${buildQueryString(queryParams, apiKey)}`;
  const base_url = "https://rental.software/";
  const url = `${base_url}${path}${queryString}`;
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Accept-Encoding": "gzip, deflate, br",
      Accept: "*/*",
    },
  });

  const responseObject: any = await response.json();

  if (
    response.status < 500 &&
    response.status !== 429 &&
    response.statusText.toLowerCase() === "ok"
  ) {
    await r2.send(
      new PutObjectCommand({
        Bucket: "migration",
        Key: `io/${accountName}/${path.replaceAll(
          "/",
          "-",
        )}${queryString.replaceAll("/", "-")}.json`,
        Body: JSON.stringify(responseObject),
        ContentType: "application/json",
      }),
    );
    return responseObject;
  }
  // console.log(responseObject);
  throw new Error(`Request failed ${response.status}`);
};

// Tre if you can think of a better way to do this let me know. This was a GPT special since im rushing
function buildQueryString(
  params: Record<string, string | number | boolean>,
  apiKey: string,
): string {
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, params[key]?.toString() || "");
    }
  }
  query.append("apiKey", apiKey);
  return query.toString();
}

export default handler;
