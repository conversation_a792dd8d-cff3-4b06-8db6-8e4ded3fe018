import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";

const SHARED_SECRET = "********-62ba-42f3-9b7a-d63c8d44ede0";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { accountId } = req.query;

  const account = await db.account.findFirst({
    where: {
      id: Number(accountId as string),
    },
    include: {
      phoneAccount: true,
      Staff: {
        select: {
          id: true,
          email: true,
          name: true,
        },
      },
      _count: {
        select: {
          Order: true,
          Product: true,
          ShoppingSession: true,
        },
      },
    },
  });

  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const accountData = {
    id: account.id,
    name: account.name,
    businessEmail: account.businessEmail,
    customDomain: account.customDomain,
    createdAt: account.createdAt,
    updatedAt: account.updatedAt,
    paymentProviderStripeId: account.stripeAccountId,
    stripeCustomerId: account.stripeCustomerId,
    reach: {
      status: account.phoneAccount === null ? "inactive" : "active",
      twilioAccountSid: account.phoneAccount?.accountSid,
      phoneNumber: account.phoneAccount?.phoneNumber,
      messagingStatus: account.phoneAccount?.messagingStatus,
    },
    paymentProvider: {
      status: account.stripeAccountId === null ? "inactive" : "active",
      stripeConnectId: account.stripeAccountId,
    },
    stripeCustomer: {
      status: account.stripeCustomerId === null ? "inactive" : "active",
      stripeCustomerId: account.stripeCustomerId,
    },
    stats: {
      orders: account._count.Order,
      products: account._count.Product,
      sessions: account._count.ShoppingSession,
    },
    staff: account.Staff.map((staff) => {
      return {
        id: staff.id,
        email: staff.email,
        name: staff.name,
        role: "unknown",
      };
    }),
  };

  return res.status(200).json({ accountData });
};

export default GET;
