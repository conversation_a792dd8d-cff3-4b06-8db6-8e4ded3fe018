import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";

const SHARED_SECRET = "********-62ba-42f3-9b7a-d63c8d44ede0";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { search } = req.query;

  const accounts = await db.account.findMany({
    where: {
      OR: [
        {
          businessEmail: {
            contains: search as string,
          },
        },
        {
          name: {
            contains: search as string,
          },
        },
        {
          customDomain: {
            contains: search as string,
          },
        },
      ],
    },
    select: {
      id: true,
      name: true,
      businessEmail: true,
      customDomain: true,
      createdAt: true,
      updatedAt: true,
      stripeAccountId: true,
      stripeCustomerId: true,
    },
    take: 10,
  });

  return res.status(200).json({
    accounts: accounts.map((item) => {
      return {
        id: item.id,
        name: item.name,
        businessEmail: item.businessEmail,
        customDomain: item.customDomain,
        createdAt: item.createdAt,
        lastUpdated: item.updatedAt,
        paymentProviderStripeId: item.stripeAccountId,
        stripeCustomerId: item.stripeCustomerId,
      };
    }),
  });
};

export default GET;
