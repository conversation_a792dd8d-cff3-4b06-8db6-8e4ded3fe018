import type { Readable } from "stream";
import { fullImageUrl } from "~/server/globalTypes";
import { uploadImageInternal } from "~/pages/api/images/upload";
import { NextApiRequest, NextApiResponse } from "next";

async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export const config = {
  api: {
    bodyParser: false,
  },
};

const SHARED_SECRET = "be0d890c-a512-4925-8028-a2ef1af0b194";

export const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { fileName, accountId } = req.query;

  if (!fileName) {
    res.status(400).json({ error: "No file name provided" });
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const buf = await buffer(req);
  const fileBlob = new Blob([buf], { type: req.headers["content-type"]! });

  if (!fileBlob) {
    res.status(400).json({ error: "No file provided" });
    return;
  }

  const image = await uploadImageInternal(
    fileBlob,
    fileName as string,
    Number(accountId as string),
  );

  if (!image) {
    return res
      .status(500)
      .json({ success: false, error: "Error uploading image!" });
  }

  return res.status(200).json({
    token: image.id,
    image: {
      name: image.name,
      url: fullImageUrl(image.url),
      id: image.id,
    },
  });
};

export default handler;
