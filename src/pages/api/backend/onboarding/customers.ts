import { trace } from "@opentelemetry/api";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { digestCustomerData } from "~/server/lib/customerUtil";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // console.log("Request body", req.body);
  const tracer = trace.getTracer("customers migration");
  // .getTracer('migration') can also make part of function chain
  return await tracer.startActiveSpan("auth", async (span) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const secret = authHeader.split(" ")[1];

      if (secret !== process.env.SHARED_SECRET) {
        return res.status(401).json({ error: "Unauthorized" });
      }
    } finally {
      span.end();
    }
    if (req.method === "POST") {
      // const accounts = await db.account.findMany();
      // console.log("Accounts", accounts);
      // const users = await db.staff.findMany();
      // console.log("Users", users);

      try {
        const data = req.body.customers;

        // Find the Account
        const account = await db.account.findFirst({
          where: {
            name: req.body.account,
          },
        });
        if (!account) {
          return res.status(401).json({ error: "Account not found" });
        }

        if (!Array.isArray(data)) {
          return res
            .status(400)
            .json({ error: "Request body must be an array of JSON objects" });
        }
        const customersToPush = await digestCustomerData(account.id, data);
        await db.customer.createMany({
          data: customersToPush,
        });
        // find customers where the <NAME_EMAIL>
        // const testCustomers = await db.customer.findMany({
        //     where: {
        //         email: {
        //             contains: "wedonthaveone",
        //         }
        //     }
        // });

        // console.log("New Customers", testCustomers);

        // Example response
        res.status(200).json({ message: "Data processed successfully", data });
      } catch (error) {
        res.status(500).json({ error: "Internal Server Error" });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  });
};
// export default handler;
