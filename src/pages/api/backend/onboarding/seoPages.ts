import { NextApiRequest, NextApiResponse } from "next";
import { array, number, object, string } from "yup";
import { withValidation } from "~/pages/api/apiMiddleware";
import { handleSeoPage } from "~/pages/api/websites/seo-pages/[slug]/save";

const WEBSITE_CREATE_SCHEMA = object().shape({
  pages: array()
    .of(
      object()
        .shape({
          name: string().required(),
          beforeContent: string().required(),
          afterContent: string().required(),
          metaTitle: string().required(),
          metaDescription: string().required(),
          slug: string().required(),
        })
        .required(),
    )
    .required(),
  accountId: number().required(),
});

const SHARED_SECRET = "be0d890c-a512-4925-8028-a2ef1af0b194";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const body: typeof WEBSITE_CREATE_SCHEMA.__outputType = req.body;

  const { accountId } = body;

  const pagesCreated: string[] = [];

  for (const pages of body.pages) {
    console.log(`Creating... ${pages.slug}`);
    await handleSeoPage(accountId, pages, pages.slug);
    pagesCreated.push(pages.slug);
  }

  return res
    .status(200)
    .json({ message: "Pages created", pages: pagesCreated });
};

export default withValidation(WEBSITE_CREATE_SCHEMA, handler);
