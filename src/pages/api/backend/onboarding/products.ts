import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { trace } from "@opentelemetry/api";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // console.log("Request body", req.body);
  const tracer = trace.getTracer("products migration");
  // .getTracer('migration') can also make part of function chain
  return await tracer.startActiveSpan("auth", async (span) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const secret = authHeader.split(" ")[1];

      if (secret !== process.env.SHARED_SECRET) {
        return res.status(401).json({ error: "Unauthorized" });
      }
    } finally {
      span.end();
    }

    if (req.method === "POST") {
      try {
        const data = req.body.products;

        // Find the Account
        const account = await db.account.findFirst({
          where: {
            name: req.body.account,
          },
        });
        if (!account) {
          return res.status(401).json({ error: "Account not found" });
        }

        if (!Array.isArray(data)) {
          return res
            .status(400)
            .json({ error: "Request body must be an array of JSON objects" });
        }

        console.log("Processing data", data);
        await db.product.createMany({
          data: data.map((product) => ({
            accountId: account.id,
            organizationId: account.organizationId,
            name: product.name,
            description: product.description,
            setupTimeMinutes: product.setupTimeMinutes,
            takeDownTimeMinutes: product.takeDownTimeMinutes,
            price: product.price,
            display: product.display,
            quantity: product.quantity,
            categoryId: product.category,
          })),
        });

        // Example response
        res.status(200).json({ message: "Data processed successfully", data });
      } catch (error) {
        res.status(500).json({ error: "Internal Server Error" });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  });
};
// export default handler;
