import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { OrderState } from "@prisma/client";
import { findOneCustomer } from "~/server/lib/customerUtil";
import { trace } from "@opentelemetry/api";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // console.log("Request body", req.body);
  // const surfaces = await db.setupSurface.findMany();
  // console.log("Surfaces", surfaces);
  // const addresses = await db.address.findFirst({
  //     orderBy: {
  //       id: 'desc',
  //     },
  //     select: {
  //       id: true,
  //     },
  //   });
  // console.log("Addresses", addresses);
  const tracer = trace.getTracer("orders migration");
  // .getTracer('migration') can also make part of function chain
  return await tracer.startActiveSpan("auth", async (span) => {
    try {
      const authHeader = req.headers.authorization;

      if (!authHeader) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      const secret = authHeader.split(" ")[1];

      if (secret !== process.env.SHARED_SECRET) {
        return res.status(401).json({ error: "Unauthorized" });
      }
    } finally {
      span.end();
    }
    if (req.method === "POST") {
      // const accounts = await db.account.findMany();
      // console.log("Accounts", accounts);
      // const users = await db.staff.findMany();
      // console.log("Users", users);

      try {
        const data = req.body.orders;

        // Find the Account
        const account = await db.account.findFirst({
          where: {
            name: req.body.account,
          },
        });
        if (!account) {
          return res.status(401).json({ error: "Account not found" });
        }

        if (!Array.isArray(data)) {
          return res
            .status(400)
            .json({ error: "Request body must be an array of JSON objects" });
        }
        // grab all surfaces for account
        const surfaces = await db.setupSurface.findMany({
          where: {
            accountId: account.id,
          },
        });
        // find a non null surface id
        const setupSurfaceId = surfaces.find((surface) => {
          return surface?.id !== undefined;
        })?.id;
        if (setupSurfaceId === undefined) {
          throw new Error("No surface with a defined ID found");
        }

        let index = 0;
        for (const order of data) {
          index++;
          console.log(`Processing item ${index}:`, order);

          const customer = await findOneCustomer({
            email: order.customerEmail,
            accountId: account.id,
            firstName: order.customerFirstName,
            lastName: order.customerLastName,
            phoneNumber: order.customerPhone,
          });
          if (!customer) {
            console.log(
              "Customer not found for order: " +
                order.customerFirstName +
                " " +
                order.customerLastName,
            );
            continue;
          }
          await db.order.create({
            data: {
              setupSurfaceId: setupSurfaceId,
              organizationId: account.organizationId,
              accountId: order.accountId,
              customerId: customer.id,
              startTime: order.startTime,
              endTime: order.endTime,
              state: OrderState.ACTIVE,
              baseTotal: order.baseTotal,
              taxExempt: order.taxExempt,
              finalTotal: order.finalTotal,
              totalPaid: order.totalPaid,
              eventAddressId: (await findOrInsertAddress(order.eventAddress))
                .id,
            },
          });
        }
        res.status(200).json({ message: "Data processed successfully", data });
      } catch (error) {
        res.status(500).json({ error: "Internal Server Error" });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  });
};
// export default handler;
