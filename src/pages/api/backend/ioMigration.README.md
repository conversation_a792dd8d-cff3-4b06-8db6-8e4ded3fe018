# Migrating IO Customers 

## Resource Translations

These are IO naming schemes to PRP naming schemes. We are using the DB table name for the PRP name

| IO Name  | PRP Name |
|----------|----------|
| Lead     | Order    |
| Customer | Customer |
| Rental   | Product  |
| Workers  | User     |


## Migrating Customers

Some customers are created without any information, these are created by IO in the Order flow when an eyeball submits a quote
without providing any data. The form will come back with an error to the eyeball, but the data will be created in the database. 

To the Customer (business owner) these customers do not exist, but they do exist in the background. 
IO attempts to map these customers by IP address so if they do come back and create an order then the order will be mapped to the correct customer.

In reality this just creates a ton of "ghost" customers in the database

The orders (leads) also contain the customer information, but it's incomplete customer information.

## Migrating Orders

Similar to Customers we have "ghost" orders. These orders sometimes contains products, but they're all marked as "Temporary" but they do not hold items.

An order is considered a "ghost" order if they have a customer, but it has no data (email and phone number).

You can't create an order without an `eventstreet` from the dashboard but the Customer Website CAN create an order without an `eventstreet`.

### Order State

| IO UI State | IO State  | PRP State | Notes                                                                   |
|-------------|-----------|-----------|-------------------------------------------------------------------------|
| Hold        | Temporary | ACTIVE    | We should leave an internal note saying the order was created as a hold. |
| Contracted  | Contracted | ACTIVE    |                                                                         |
| Confirmed | Confirmed | ACTIVE    |                                                                         |
| Cancelled   | Cancelled | CANCELLED |                                                                         |
| Completed   | Complete | COMPLETED |                                                                         |
| Quote      | Quote     | Quote     |                                                                         |
| Partial    | Partial   | QUOTE     |                                                                         |




