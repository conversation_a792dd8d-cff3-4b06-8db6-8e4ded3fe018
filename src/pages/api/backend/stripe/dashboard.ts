import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { platformStripeServer } from "~/server/lib/stripe";
import { db } from "~/server/db";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const account = await db.account.findFirst({
    where: {
      id: req.accountId,
    },
  });

  if (!account) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  if (!account.stripeAccountId) {
    return res.status(400).json({ message: "Stripe account not found" });
  }

  const loginLink = await platformStripeServer.accounts.createLoginLink(
    account.stripeAccountId,
  );

  res.status(200).json({
    url: loginLink.url,
  });
};

export default withPermissions(
  {
    policy: "api.payments",
    action: "execute",
  },
  withMethods(["GET"], handler),
);
