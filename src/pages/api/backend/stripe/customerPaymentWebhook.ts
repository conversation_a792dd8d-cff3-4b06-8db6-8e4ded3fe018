import { NextApiRequest, NextApiResponse } from "next";
import {
  getStripeDisplayPaymentMethod,
  platformStripeServer,
} from "~/server/lib/stripe";
import { db } from "~/server/db";
import { CurrencyValue } from "~/server/lib/currency";
import { Stripe } from "stripe";
import { OrderState } from ".prisma/client";
import type { Readable } from "stream";
import * as Sentry from "@sentry/nextjs";
import { captureMessage } from "@sentry/nextjs";
import { sendOrderReceiptAPI } from "~/pages/api/orders/[id]/sendReceipt";

const handlePaymentFailed = async (paymentIntent: Stripe.PaymentIntent) => {
  const orderIdString = paymentIntent.metadata.orderId;

  if (!orderIdString) {
    console.log("No orderId in paymentIntentSucceeded");
    return null;
  }

  const orderId = Number(orderIdString);

  if (!orderId) {
    console.log("Invalid orderId in paymentIntentSucceeded");
    return null;
  }
};

export const config = {
  api: {
    bodyParser: false,
  },
};
export async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}
const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const sig = req.headers["stripe-signature"];

  if (!sig || typeof sig !== "string") {
    res.status(400).json({ error: "Missing stripe signature" });
    return;
  }

  let event;
  let buf;
  try {
    buf = await buffer(req);
  } catch (e) {
    Sentry.captureException(e);
    res.status(400).send(`No Buffer`);
    return;
  }

  try {
    event = platformStripeServer.webhooks.constructEvent(
      buf,
      sig,
      process.env.PRP_STRIPE_PAYMENT_WEBHOOK_SECRET || "",
    );
  } catch (err) {
    Sentry.captureException(err);
    console.log(err);
    res.status(400).send(`Webhook Error`);
    return;
  }

  const stripeEvent: Stripe.Event = event;

  const handlePaymentMethod = async (method: string) => {
    return await platformStripeServer.paymentMethods.retrieve(method, {
      stripeAccount: stripeEvent.account,
    });
  };

  await handleCustomerPayment(stripeEvent, handlePaymentMethod);

  res.status(200).send("OK");
};

export const handleCustomerPayment = async (
  event: Stripe.Event,
  paymentMethodCallback: (
    payment_method: string,
  ) => Promise<Stripe.Response<Stripe.PaymentMethod>>,
) => {
  // Handle the event
  switch (event.type) {
    case "payment_intent.payment_failed":
      const paymentIntentPaymentFailed = event.data.object;
      await handlePaymentFailed(paymentIntentPaymentFailed);
      break;
    case "payment_intent.succeeded":
      const paymentIntentSucceeded: Stripe.PaymentIntent = event.data.object;

      let stripeAmountPaid = CurrencyValue.fromStripe(
        paymentIntentSucceeded.amount,
      );

      const tipAmountString = paymentIntentSucceeded.metadata.tipAmount;
      let tipAmount: CurrencyValue | null = null;
      // we'll record the tip as a separate thing
      if (tipAmountString) {
        const tipValue = Number(tipAmountString);
        if (!isNaN(tipValue)) {
          // Convert tip to stripe amount (cents)
          tipAmount = CurrencyValue.fromPlatform(tipValue);
          stripeAmountPaid = stripeAmountPaid.subtract(tipAmount);
        } else {
          // No tip found.
          tipAmount = null;
        }
      }

      const paymentIntentId = paymentIntentSucceeded.id;
      const orderIdString = paymentIntentSucceeded.metadata.orderId;

      if (!orderIdString) {
        captureMessage("No orderId in paymentIntentSucceeded", {
          level: "warning",
          extra: {
            paymentIntentSucceeded,
          },
        });
        console.log("No orderId in paymentIntentSucceeded");
        break;
      }

      const orderId = Number(orderIdString);

      if (!orderId) {
        captureMessage("Invalid orderId in paymentIntentSucceeded", {
          level: "warning",
          extra: {
            paymentIntentSucceeded,
          },
        });
        console.log("Invalid orderId in paymentIntentSucceeded");
        break;
      }

      const order = await db.order.findFirst({
        where: {
          id: orderId,
          NOT: {
            state: OrderState.COMPLETED,
          },
        },
        include: {
          account: true,
          eventAddress: true,
        },
      });

      if (!order) {
        captureMessage("Order Not Found for payment", {
          level: "warning",
          extra: {
            paymentIntentSucceeded,
          },
        });
        console.log("No order found for orderId in paymentIntentSucceeded");
        break;
      }

      let customerId: string | null = null;

      // first we check the order for a customer, it should have one...
      if (order.customerId) {
        customerId = order.customerId;
      } else {
        // if not, we check the shopping session
        const shoppingId = paymentIntentSucceeded.metadata.shoppingId;

        if (shoppingId) {
          const shoppingSession = await db.shoppingSession.findFirst({
            where: {
              id: shoppingId,
            },
          });

          if (shoppingSession?.customerId) {
            customerId = shoppingSession.customerId;
          }
        } else {
          // last resort, we check the payment intent inverse
          const shoppingSession = await db.shoppingSession.findFirst({
            where: {
              paymentIntent: paymentIntentId,
            },
          });

          if (shoppingSession?.customerId) {
            customerId = shoppingSession?.customerId;
          }
        }
      }

      // once again, stripe has already collected the money. We NEED to show that on our system.
      if (!customerId) {
        captureMessage("No Customer found for the order!", {
          level: "error",
          extra: {
            paymentIntentSucceeded,
          },
        });
      }

      if (customerId) {
        let methodId = "";
        let method = "Stripe-Webhook";
        if (
          paymentIntentSucceeded.payment_method &&
          typeof paymentIntentSucceeded.payment_method === "string"
        ) {
          const paymentMethod = await paymentMethodCallback(
            paymentIntentSucceeded.payment_method,
          );
          const displayPaymentMethod =
            getStripeDisplayPaymentMethod(paymentMethod);
          method = displayPaymentMethod.method;
          methodId = displayPaymentMethod.methodId;
        }
        await db.paymentDetails.create({
          data: {
            method: method,
            methodId: methodId,
            processorId: paymentIntentId,
            processor: "stripe",
            amount: stripeAmountPaid.amount,
            tip: tipAmount?.amount || 0,
            orderId: order.id,
            customerId,
          },
        });
      }

      const orderTotalPaid = CurrencyValue.fromPlatform(order.totalPaid);
      const newTotalPaid = orderTotalPaid.add(stripeAmountPaid);
      const updatedOrder = await db.order.update({
        where: {
          id: order.id,
        },
        data: {
          totalPaid: newTotalPaid.amount,
          state: OrderState.ACTIVE,
        },
      });

      try {
        await sendOrderReceiptAPI(
          updatedOrder.id,
          updatedOrder.accountId,
          false,
          order.state === OrderState.QUOTE,
        );
      } catch (e) {
        Sentry.captureException(e);
        console.error(e);
      }
      break;
    // ... handle other event types
    default:
      console.log(`Unhandled event type ${event.type}`);
  }
};

export default handler;
