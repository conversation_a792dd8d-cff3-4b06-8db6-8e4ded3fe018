import { NextApiRequest, NextApiResponse } from "next";
import * as Sentry from "@sentry/nextjs";
import { captureMessage } from "@sentry/nextjs";
import process from "process";
import { buffer } from "~/pages/api/backend/stripe/paymentWebhook";
import { Stripe } from "stripe";
import { db } from "~/server/db";
import { SubscriptionStatus } from ".prisma/client";
import { PlanLevel, planLevelFromString } from "~/server/lib/entitlement/types";
import { platformStripeServer } from "~/server/lib/stripe";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { sendReactEmail } from "~/server/email/sendMail";
import { NEW_ACCOUNT_EMAIL } from "../../../../../emails/NewAccountEmail";

export const config = {
  api: {
    bodyParser: false,
  },
};

const findAccountFromSubscription = async (
  subscription: Stripe.Subscription,
) => {
  return db.account.findFirst({
    where: {
      stripeCustomerId: subscription.customer as string,
    },
  });
};

const handleNewSubscription = async (
  customerSubscriptionCreated: Stripe.Subscription,
): Promise<boolean> => {
  const account = await findAccountFromSubscription(
    customerSubscriptionCreated,
  );
  if (!account) {
    captureMessage(
      `No account found for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
    return false;
  }

  const foundCustomer = await platformStripeServer.customers.retrieve(
    customerSubscriptionCreated.customer as string,
  );

  if (!foundCustomer || foundCustomer.deleted) {
    throw new Error(
      `No customer found for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
  }

  if (
    account.businessEmail &&
    foundCustomer &&
    !foundCustomer.deleted &&
    foundCustomer?.metadata?.customerFlow === "true"
  ) {
    if (foundCustomer.address) {
      const address = await findOrInsertAddress({
        city: foundCustomer.address.city ?? undefined,
        country: foundCustomer.address.country ?? undefined,
        line1: foundCustomer.address.line1 ?? undefined,
        line2: foundCustomer.address.line2 ?? undefined,
        postalCode: foundCustomer.address.postal_code ?? undefined,
        state: foundCustomer.address.state ?? undefined,
      });
      if (address) {
        await db.account.update({
          where: {
            id: account.id,
          },
          data: {
            billingAddressId: address.id,
          },
        });
      }
    }

    const staff = await db.staff.findFirst({
      where: {
        accountId: account.id,
        email: account.businessEmail,
      },
    });

    if (staff) {
      const updatedPassword = staff.password.replace("unpaid-", "");
      if (staff.userId) {
        await db.user.update({
          where: {
            id: staff.userId,
          },
          data: {
            password: updatedPassword,
          },
        });
      }
      await db.staff.update({
        where: {
          id: staff.id,
        },
        data: {
          password: updatedPassword,
        },
      });
    }

    await sendReactEmail(
      false,
      account,
      account.businessEmail,
      NEW_ACCOUNT_EMAIL,
      {
        temporaryPassword: undefined,
      },
    );
  }

  const planItem = customerSubscriptionCreated.items.data[0]?.plan?.product;

  if (!planItem) {
    captureMessage(
      `No plan product found for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
    return false;
  }

  const price = await platformStripeServer.products.retrieve(
    planItem as string,
  );
  if (!price) {
    captureMessage(
      `No product found for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
    return false;
  }

  const planLevel: PlanLevel = planLevelFromString(price.name);
  if (planLevel === PlanLevel.Free) {
    captureMessage(
      `Free plan level found for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
    return false;
  }

  const isTrialing = customerSubscriptionCreated.status === "trialing";

  try {
    await db.subscription.create({
      data: {
        account: {
          connect: {
            id: account.id,
          },
        },
        stripeSubscriptionId: customerSubscriptionCreated.id,
        trialEndsAt: isTrialing
          ? new Date(
              customerSubscriptionCreated.trial_end! * 1000,
            ).toISOString()
          : null,
        status: isTrialing
          ? SubscriptionStatus.TRIALING
          : SubscriptionStatus.ACTIVE,
        planLevel: planLevel,
      },
    });
  } catch (ex) {
    captureMessage(
      `Failed to create subscription for customer.subscription.created ${customerSubscriptionCreated.id}`,
    );
    console.error(ex);
  }

  return true;
};

const handleSubscriptionUpdate = async (
  customerSubscriptionDeleted: Stripe.Subscription,
  newStatus: SubscriptionStatus,
): Promise<boolean> => {
  const subscription = await db.subscription.findFirst({
    where: {
      stripeSubscriptionId: customerSubscriptionDeleted.id,
    },
  });

  if (!subscription) {
    captureMessage(
      `No subscription found for customer.subscription.deleted ${customerSubscriptionDeleted.id}`,
    );
    return false;
  }

  await db.subscription.update({
    where: {
      id: subscription.id,
    },
    data: {
      status: newStatus,
    },
  });
  return true;
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const sig = req.headers["stripe-signature"];

  if (!sig || typeof sig !== "string") {
    res.status(400).json({ error: "Missing stripe signature" });
    return;
  }

  let event;
  let buf;
  try {
    buf = await buffer(req);
  } catch (e) {
    Sentry.captureException(e);
    res.status(400).send(`No Buffer`);
    return;
  }

  try {
    event = platformStripeServer.webhooks.constructEvent(
      buf,
      sig,
      process.env.PRP_STRIPE_WEBHOOK_SECRET || "",
    );
  } catch (err) {
    Sentry.captureException(err);
    console.log(err);
    res.status(400).send(`Webhook Error`);
    return;
  }

  let valid = false;
  switch (event.type) {
    case "customer.subscription.created":
      const customerSubscriptionCreated = event.data.object;
      valid = await handleNewSubscription(customerSubscriptionCreated);
      if (!valid) {
        res.status(400).send(`Invalid subscription`);
        return;
      }
      return res.status(200).json({ received: true });
    case "customer.subscription.deleted":
      const customerSubscriptionDeleted = event.data.object;
      // Then define and call a function to handle the event customer.subscription.deleted
      valid = await handleSubscriptionUpdate(
        customerSubscriptionDeleted,
        SubscriptionStatus.CANCELED,
      );
      if (!valid) {
        res.status(400).send(`Invalid subscription delete`);
        return;
      }
      return res.status(200).json({ received: true });
    case "customer.subscription.paused":
      const customerSubscriptionPaused = event.data.object;
      valid = await handleSubscriptionUpdate(
        customerSubscriptionPaused,
        SubscriptionStatus.PENDING,
      );
      if (!valid) {
        res.status(400).send(`Invalid subscription paused`);
        return;
      }
      return res.status(200).json({ received: true });
    case "customer.subscription.resumed":
      const customerSubscriptionResumed = event.data.object;
      valid = await handleSubscriptionUpdate(
        customerSubscriptionResumed,
        SubscriptionStatus.ACTIVE,
      );
      if (!valid) {
        res.status(400).send(`Invalid subscription paused`);
        return;
      }
      return res.status(200).json({ received: true });
    case "customer.subscription.updated":
      const subscription = event.data.object;
      valid = await handleSubscriptionFromUpdate(subscription);
      if (!valid) {
        res.status(400).send(`Invalid subscription paused`);
        return;
      }
      return res.status(200).json({ received: true });
    // ... handle other event types
    default:
      console.log(`Unhandled event type ${event.type}`);
  }
  res.status(200).send("OK");
};

const handleSubscriptionFromUpdate = async (
  subscription: Stripe.Subscription,
): Promise<boolean> => {
  const existingSubscription = await db.subscription.findUnique({
    where: { stripeSubscriptionId: subscription.id },
  });

  if (!existingSubscription) {
    captureMessage(
      `No subscription found for customer.subscription.deleted ${subscription.id}`,
    );
    return false;
  }

  const planItem = subscription.items.data[0]?.plan?.product;

  if (!planItem) {
    captureMessage(
      `No plan product found for customer.subscription.created ${subscription.id}`,
    );
    return false;
  }

  const price = await platformStripeServer.products.retrieve(
    planItem as string,
  );
  if (!price) {
    captureMessage(
      `No product found for customer.subscription.created ${subscription.id}`,
    );
    return false;
  }

  const planLevel: PlanLevel = planLevelFromString(price.name);
  if (planLevel === PlanLevel.Free) {
    captureMessage(
      `Free plan level found for customer.subscription.created ${subscription.id}`,
    );
    return false;
  }

  // Update subscription
  await db.subscription.update({
    where: { id: existingSubscription.id },
    data: {
      planLevel:
        subscription.items.data[0]?.price.id || existingSubscription.planLevel,
      status: mapStripeStatusToPrisma(subscription.status),
      trialEndsAt: subscription.trial_end
        ? new Date(subscription.trial_end * 1000)
        : null,
    },
  });
  return true;
};

function mapStripeStatusToPrisma(stripeStatus: string) {
  switch (stripeStatus) {
    case "active":
      return SubscriptionStatus.ACTIVE;
    case "trialing":
      return SubscriptionStatus.TRIALING;
    case "past_due":
      return SubscriptionStatus.PAST_DUE;
    case "canceled":
      return SubscriptionStatus.CANCELED;
    case "incomplete":
    case "incomplete_expired":
      return SubscriptionStatus.PENDING;
    default:
      return SubscriptionStatus.PENDING;
  }
}

export default handler;
