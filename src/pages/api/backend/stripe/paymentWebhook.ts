import { NextApiRequest, NextApiResponse } from "next";
import { centralArkansasInflatablesStripe } from "~/server/lib/stripe";
import { Stripe } from "stripe";
import type { Readable } from "stream";
import * as Sentry from "@sentry/nextjs";
import { handleCustomerPayment } from "~/pages/api/backend/stripe/customerPaymentWebhook";

export const config = {
  api: {
    bodyParser: false,
  },
};
export async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const sig = req.headers["stripe-signature"];

  if (!sig || typeof sig !== "string") {
    res.status(400).json({ error: "Missing stripe signature" });
    return;
  }

  let event;
  let buf;
  try {
    buf = await buffer(req);
  } catch (e) {
    Sentry.captureException(e);
    res.status(400).send(`No Buffer`);
    return;
  }

  try {
    event = centralArkansasInflatablesStripe.webhooks.constructEvent(
      buf,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET || "",
    );
  } catch (err) {
    Sentry.captureException(err);
    console.log(err);
    res.status(400).send(`Webhook Error`);
    return;
  }

  const stripeEvent: Stripe.Event = event;

  const handlePaymentMethod = async (paymentMethod: string) => {
    return await centralArkansasInflatablesStripe.paymentMethods.retrieve(
      paymentMethod,
    );
  };

  await handleCustomerPayment(stripeEvent, handlePaymentMethod);

  res.status(200).send("OK");
};

export default handler;
