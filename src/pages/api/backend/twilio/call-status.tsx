import { NextApiRequest, NextApiResponse } from "next";
import twilio from "twilio";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const twiml = new twilio.twiml.VoiceResponse();

  const gather = twiml.gather({
    numDigits: 1,
    timeout: 5,
    action: `${process.env.NEXT_PUBLIC_SELF_URL}/api/backend/twilio/call-check`,
  });

  gather.say(
    {
      voice: "alice",
    },
    "Incoming customer call. Press any key to accept.",
  );

  twiml.hangup();

  res.setHeader("Content-Type", "text/xml");
  res.send(twiml.toString());
};

export default handler;
