import { NextApiRequest, NextApiResponse } from "next";
import { formatStandardPhone } from "~/server/lib/phone";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const phoneNumber = req.query.phoneNumber as string;

  if (!phoneNumber) {
    return res.status(400).json({ error: "Missing phone" });
  }

  // Set the content type of the response
  res.setHeader("Content-Type", "text/xml");
  // Send TwiML as the response
  res
    .status(200)
    .send(
      `<Response><Dial>${formatStandardPhone(phoneNumber)}</Dial></Response>`,
    );
};

export default GET;
