import { NextApiRequest, NextApiResponse } from "next";
import { r2 } from "~/server/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { db } from "~/server/db";
import { captureMessage } from "@sentry/nextjs";
import { PhoneAccount } from "@prisma/client";
import VoiceResponse from "twilio/lib/twiml/VoiceResponse";
import { captureException } from "@sentry/core";

export const VOICEMAIL_RECORDINGS_BUCKET = "voicemail";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const twiml = new VoiceResponse();
  const { RecordingUrl, RecordingSid, CallSid, RecordingDuration, AccountSid } =
    req.body;

  twiml.hangup();

  const accountData = await db.phoneAccount.findFirst({
    where: {
      accountSid: AccountSid,
    },
  });

  if (!accountData) {
    captureMessage("Phone Account not found for recording", {
      extra: {
        CallSid,
        RecordingSid,
        RecordingUrl,
        RecordingDuration,
        AccountSid,
      },
    });
    // Set the content type of the response
    res.setHeader("Content-Type", "text/xml");
    // Send TwiML as the response
    res.status(200).send(twiml.toString());
    return;
  }

  const phoneCallLog = await db.phoneCallLog.findFirst({
    where: {
      accountId: accountData.accountId,
      callSid: CallSid,
    },
  });

  if (!phoneCallLog) {
    console.error("Call log not found");
    captureMessage("Call not found for recording", {
      extra: {
        CallSid,
        RecordingSid,
        RecordingUrl,
        RecordingDuration,
        AccountSid,
      },
    });
    // Set the content type of the response
    res.setHeader("Content-Type", "text/xml");
    // Send TwiML as the response
    res.status(200).send(twiml.toString());
    return;
  }

  let recordingHandled = false;

  for (let i = 0; i < 30; i++) {
    try {
      // Handle the recording
      recordingHandled = await handleRecording(
        RecordingUrl,
        CallSid,
        AccountSid,
        accountData,
        phoneCallLog.id,
      );
      if (recordingHandled) {
        break; // Exit the loop if successful
      }
    } catch (error) {
      console.error("Error handling recording, retrying...", error);
      // Wait for a second before retrying
      await new Promise(
        (resolve) => setTimeout(resolve, i * (100 * (1 - Math.random()))), // Exponential backoff with jitter
      );
    }
  }

  if (!recordingHandled) {
    captureException(new Error("Recording failed to fetch"), {
      extra: {
        RecordingUrl,
        CallSid,
        AccountSid,
        accountData,
      },
    });
  }

  // Set the content type of the response
  res.setHeader("Content-Type", "text/xml");
  // Send TwiML as the response
  res.status(200).send(twiml.toString());
};

export const handleRecording = async (
  RecordingUrl: string,
  callSid: string,
  accountSid: string,
  accountData: PhoneAccount,
  callLogId: number,
): Promise<boolean> => {
  // Append ".mp3" to the URL to get the MP3 version of the recording
  const recordingUrlWithExtension = `${RecordingUrl}.mp3`;

  // Download the recording file using fetch

  const response = await fetch(recordingUrlWithExtension);
  if (response.status !== 200) {
    return false;
  }

  const buffer = await response.arrayBuffer();

  if (buffer) {
    const key = `recordings/${accountData.accountId}/` + callSid + ".mp3";
    const putCommand = await r2.send(
      new PutObjectCommand({
        Bucket: VOICEMAIL_RECORDINGS_BUCKET,
        Key: key,
        // @ts-expect-error - fileBlob is a Blob, not a Buffer and that's fine
        Body: buffer,
        ContentType: "audio/mpeg",
      }),
    );
    if (putCommand) {
      await db.phoneVoicemail.create({
        data: {
          phoneCallLogId: callLogId,
          recordingUrl: key,
          transcription: null,
        },
      });
      return true;
    }
  }

  captureMessage("Recording failed in the R2 upload", {
    extra: {
      RecordingUrl,
    },
  });
  return false;
};

export default POST;
