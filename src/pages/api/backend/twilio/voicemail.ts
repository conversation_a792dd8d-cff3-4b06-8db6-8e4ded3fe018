import { NextApiRequest, NextApiResponse } from "next";
import twilio from "twilio";
import { db } from "~/server/db";
import { displayPrettyPhone } from "~/server/lib/phone";
import { sendReactEmail } from "~/server/email/sendMail";
import {
  MISSED_CALL_EMAIL,
  MissedCallEmailProps,
} from "@/../emails/MissedCallEmail";
import { getOrCreatePhoneContact } from "~/pages/api/backend/twilio/sms";
import { handleRecording } from "./recording";
import { createSlackWebhook } from "~/lib/slack";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const accountData = await db.phoneAccount.findFirst({
    where: {
      accountSid: req.body.AccountSid,
    },
    include: {
      account: true,
    },
  });

  if (!accountData) {
    console.error("Account not found");
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const phoneCallLog = await db.phoneCallLog.findFirst({
    where: {
      accountId: accountData.accountId,
      callSid: req.body.CallSid,
    },
  });

  const phoneContact = await getOrCreatePhoneContact(
    req.body.From,
    accountData.accountId,
  );

  const missed =
    req.body.CallStatus === "ringing" &&
    (req.body.DialCallStatus === "no-answer" ||
      req.body.DialCallStatus === "busy" ||
      req.body.DialCallStatus === "cancelled" ||
      req.body.DialCallStatus === "completed");

  const callLog = await db.phoneCallLog.upsert({
    where: {
      id: phoneCallLog?.id,
    },
    create: {
      phoneContactId: phoneContact.id,
      accountId: accountData.accountId,
      inbound: true,
      callSid: req.body.CallSid,
    },
    update: {
      duration: Number(req.body?.DialCallDuration ?? 0),
      answered: !missed,
    },
  });

  if (req.body?.RecordingUrl && req.body.CallStatus === "completed") {
    await handleRecording(
      req.body.RecordingUrl,
      req.body.CallSid,
      req.body.AccountSid,
      accountData,
      callLog.id,
    );
    twiml.hangup();
    res.setHeader("Content-Type", "text/xml");
    res.status(200).send(twiml.toString());
    return;
  }

  if (missed) {
    twiml.play(
      "https://static.dash.partyrentalplatform.com/recordings/cai-recording.wav",
    );
    twiml.record({
      maxLength: 60,
      finishOnKey: "*",
      action: `${process.env.NEXT_PUBLIC_SELF_URL}/api/backend/twilio/recording`,
    });
    twiml.say("We will get back to you as soon as possible. Goodbye!");
    if (accountData.account.businessEmail) {
      const missedCallEmailProps: MissedCallEmailProps = {
        sender: displayPrettyPhone(req.body.From),
        receivedString: new Date().toLocaleString("en-US", {
          timeZone: accountData.account.businessTimezone,
        }),
        conversationId: phoneContact.id.toString(),
      };
      await sendReactEmail(
        false,
        accountData.account,
        accountData.account.businessEmail,
        MISSED_CALL_EMAIL,
        missedCallEmailProps,
      );
    }

    if (accountData.forwardMessagesToSlackWebhookUrl !== null) {
      await createSlackWebhook(
        accountData.forwardMessagesToSlackWebhookUrl,
      ).sendMissedCallNotification({
        from: displayPrettyPhone(req.body.From),
        timestamp: new Date(),
        callbackUrl: `https://dash.partyrentalplatform.com/phone?conversationId=${phoneContact.id.toString()}`,
      });
    }

    twiml.hangup();

    // const twilio = getTwilioForSubAccount(
    //   accountData.accountSid,
    //   accountData.authToken,
    // );
    //
    // const bodyMessage = `Hi there! Sorry we missed your call, our team is likely away from the phone assisting customers, how can we help you today?`;
    //
    // const messageResponse = await twilio.messages.create({
    //   body: bodyMessage,
    //   from: accountData.phoneNumber ?? undefined,
    //   to: req.body.From,
    // });
    //
    // // create message log
    // await db.phoneMessageLog.create({
    //   data: {
    //     accountId: phoneContact.accountId,
    //     phoneContactId: phoneContact.id,
    //     body: bodyMessage,
    //     inbound: false,
    //     messageSid: messageResponse.sid,
    //   },
    // });
  }

  res.setHeader("Content-Type", "text/xml");
  res.status(200).send(twiml.toString());
};

export default POST;
