import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { PhoneContact } from "@prisma/client";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { sendReactEmail } from "~/server/email/sendMail";
import {
  NEW_TEXT_EMAIL,
  NewTextEmailProps,
} from "../../../../../emails/NewTextEmail";
import { displayPrettyPhone } from "~/server/lib/phone";
import { createSlackWebhook } from "~/lib/slack";

type TwilioSMSInboundRequest = {
  AccountSid: string;
  To: string;
  From: string;
  Body: string;
  NumMedia: string;
  MessageSid: string;
  [key: string]: string;
};

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const body = req.body as TwilioSMSInboundRequest;

  // get the business phone number
  const accountData = await db.phoneAccount.findFirst({
    where: {
      accountSid: body.AccountSid,
    },
    include: {
      account: true,
    },
  });

  if (!accountData) {
    console.error("Account not found");
    res.status(404).json({ error: "Account not found" });
    return;
  }

  // get the phone contact for the incoming message
  const phoneContact = await getOrCreatePhoneContact(
    body.From,
    accountData.accountId,
  );

  const receivedDate = new Date();

  const numMedia = parseInt(body.NumMedia) ?? 0;

  const messages = [];
  // create the base db object.
  messages.push({
    body: body.Body,
    createdAt: receivedDate,
    inbound: true,
    phoneContactId: phoneContact.id,
    mediaUrl: numMedia > 0 ? body.MediaUrl0 : null,
    messageSid: body.MessageSid,
    accountId: accountData.accountId,
  });

  // Log the incoming SMS
  if (numMedia > 1) {
    for (let i = 1; i < numMedia; i++) {
      messages.push({
        body: "",
        inbound: true,
        createdAt: receivedDate,
        phoneContactId: phoneContact.id,
        mediaUrl: body[`MediaUrl${i}`],
        messageSid: body.MessageSid,
        accountId: accountData.accountId,
      });
    }
  }

  const account = accountData.account;
  let conversationName = displayPrettyPhone(phoneContact.phoneNumber);
  if (phoneContact?.customerId) {
    // if the phone contact has a customer, use their name
    const customerObj = await db.customer.findFirst({
      where: {
        id: phoneContact.customerId,
      },
      select: {
        firstName: true,
        lastName: true,
      },
    });
    if (customerObj) {
      conversationName = `${customerObj.firstName} ${customerObj.lastName}`;
    }
  }
  if (account?.businessEmail) {
    const newTextProps: NewTextEmailProps = {
      sender: conversationName,
      message: body.Body,
      receivedString: receivedDate.toLocaleString("en-US", {
        timeZone: account.businessTimezone,
      }),
      conversationId: phoneContact.id.toString(),
    };
    await sendReactEmail(
      false,
      account,
      account.businessEmail,
      NEW_TEXT_EMAIL,
      newTextProps,
    );
  }

  if (accountData?.forwardMessagesToSlackWebhookUrl !== null) {
    await createSlackWebhook(
      accountData.forwardMessagesToSlackWebhookUrl,
    ).sendSMSNotification({
      from: displayPrettyPhone(phoneContact.phoneNumber),
      message: body.Body,
      timestamp: receivedDate,
      customerName: conversationName,
      quickReplyButtonUrl: `https://dash.partyrentalplatform.com/phone?conversationId=${phoneContact.id.toString()}`,
    });
  }

  await db.phoneMessageLog.createMany({
    data: messages,
  });

  res.status(200).json({ success: true });
};

export const getOrCreatePhoneContact = async (
  phoneNumber: string,
  accountId: number,
): Promise<PhoneContact> => {
  return db.$transaction(async (tx) => {
    let contact = await tx.phoneContact.findFirst({
      where: {
        phoneNumber: formatPhoneNumber(phoneNumber),
        accountId,
      },
    });

    if (!contact) {
      // look for a customer with the same phone number
      const customer = await tx.customer.findFirst({
        where: {
          phoneNumber: formatPhoneNumber(phoneNumber),
          accountId,
        },
      });

      // create a new phone contact
      contact = await tx.phoneContact.create({
        data: {
          phoneNumber: formatPhoneNumber(phoneNumber),
          accountId,
          customerId: customer?.id,
          lastMessage: new Date(),
        },
      });
    } else {
      // update the last message date
      contact = await tx.phoneContact.update({
        where: {
          id: contact.id,
        },
        data: {
          lastMessage: new Date(),
        },
      });
    }
    return contact;
  });
};

export default POST;
