import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import VoiceResponse from "twilio/lib/twiml/VoiceResponse";
import { getOrCreatePhoneContact } from "~/pages/api/backend/twilio/sms";
import process from "process";

type TwilioInboundRequest = {
  AccountSid: string;
  To: string;
  From: string;
  CallSid: string;
};

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const body = req.body as TwilioInboundRequest;

  // Log the incoming call
  console.log(`Incoming call from ${body.From}`);

  // get the business phone number
  const accountData = await db.phoneAccount.findFirst({
    where: {
      accountSid: body.AccountSid,
    },
  });

  if (!accountData) {
    console.error("Account not found");
    res.status(404).json({ error: "Account not found" });
    return;
  }

  // Create TwiML response to forward the call
  const twiml = new VoiceResponse();

  // Incoming call!
  if (accountData?.phoneNumber === body.To) {
    const phoneContact = await getOrCreatePhoneContact(
      body.From,
      accountData.accountId,
    );

    await db.phoneCallLog.create({
      data: {
        phoneContactId: phoneContact.id,
        accountId: accountData.accountId,
        inbound: true,
        callSid: body.CallSid,
      },
    });

    // todo check online agents
    // todo check if business is open
    const dial = twiml.dial({
      callerId: accountData.showCallerId ? body.From : accountData.phoneNumber,
      action: `${process.env.NEXT_PUBLIC_SELF_URL}/api/backend/twilio/voicemail`,
      answerOnBridge: true,
      record: "record-from-answer",
      recordingStatusCallback: `${process.env.NEXT_PUBLIC_SELF_URL}/api/backend/twilio/recording`,
      timeout: 15,
    });

    const client = dial.client({}, accountData.accountId.toString());
    client.parameter({
      name: "phoneContactId",
      value: phoneContact.id.toString(),
    });
    client.parameter({
      name: "From",
      value: body.From,
    });
    if (phoneContact.customerId) {
      client.parameter({
        name: "customerId",
        value: phoneContact.customerId.toString(),
      });
    }

    if (
      accountData.forwardIncomingCallsToMobile &&
      accountData.forwardIncomingCallsTo.length > 0
    ) {
      const forwardTo = accountData.forwardIncomingCallsTo[0] ?? "";
      if (isValidPhoneNumber(forwardTo)) {
        dial.number(
          {
            url: `${process.env.NEXT_PUBLIC_SELF_URL}/api/backend/twilio/call-status`,
          },
          forwardTo,
        );
      }
    }
  } else if (body.To) {
    const phoneContact = await getOrCreatePhoneContact(
      body.To,
      accountData.accountId,
    );

    await db.phoneCallLog.create({
      data: {
        phoneContactId: phoneContact.id,
        accountId: accountData.accountId,
        inbound: false,
        callSid: body.CallSid,
      },
    });

    console.log("Outgoing call");
    // Outgoing call!
    const dial = twiml.dial({
      callerId: accountData?.phoneNumber || body.From,
    });
    const attr = isValidPhoneNumber(body.To) ? "number" : "client";
    dial[attr]({}, body.To);
  } else {
    console.error("Unsure?");
    twiml.say("Thank you for calling!");
  }

  // Set the content type of the response
  res.setHeader("Content-Type", "text/xml");
  // Send TwiML as the response
  res.status(200).send(twiml.toString());
};

const isValidPhoneNumber = (phoneNumber: string) => {
  return /^[\d+\-() ]+$/.test(phoneNumber);
};

export default POST;
