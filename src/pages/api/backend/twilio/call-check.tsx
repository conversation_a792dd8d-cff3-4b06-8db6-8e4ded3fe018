import { NextApiRequest, NextApiResponse } from "next";
import twilio from "twilio";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const digits = req.body.Digits;

  if (!digits || digits?.length === 0) {
    twiml.hangup();
  }

  res.setHeader("Content-Type", "text/xml");
  res.send(twiml.toString());
};

export default handler;
