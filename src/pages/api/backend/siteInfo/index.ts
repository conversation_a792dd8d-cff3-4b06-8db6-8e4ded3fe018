import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { DEFAULT_WEBSITE_CONFIG, WebsiteSettings } from "@prp/blocks/dist";
import { ScheduleDay } from "@prisma/client";
import { Account } from ".prisma/client";
import { FooterProps } from "@prp/blocks/dist/components/Footer";
import { getConciseAddress } from "~/server/lib/location/types";
import { displayPrettyPhone } from "~/server/lib/phone";
import { withErrorHandling } from "~/pages/api/apiMiddleware";
import { JsonObject } from "@prisma/client/runtime/library";
import { getAccountLogo } from "~/server/lib/logo";

type DailySchedule = {
  day: ScheduleDay;
  openTime: Date;
  closeTime: Date;
};

export type SiteInfoResponse = {
  name: string;
  description: string;
  stripeAccountId: string | null;
  logo: string;
  email: string;
  phoneNumber: string;
  customDomain: string;
  allSlugs?: string[];
  seoSlugs: string[];
  settings: WebsiteSettings;
  footerProps: Omit<
    FooterProps,
    "showAddress" | "showGoogleBusinessName" | "backgroundColor" | "textColor"
  >;
  googleAnalyticsId?: string;
  postHogId?: string;
  afterHeader?: string;
  afterBody?: string;
  schedule: DailySchedule[];
};

export const getWebsiteData = async (
  account: Account & { address: string },
  hostHeader: string,
) => {
  const [totalPages, websiteSettings, schedules, seoWebPages] =
    await db.$transaction([
      db.websitePage.findMany({
        where: {
          accountId: account.id,
        },
        select: {
          slug: true,
          content: true,
        },
      }),
      db.websiteSettings.findFirst({
        where: {
          accountId: account.id,
        },
      }),
      db.schedule.findMany({
        where: {
          active: true,
          accountId: account.id,
        },
      }),
      db.simpleWebsitePage.findMany({
        where: {
          accountId: account.id,
        },
        select: {
          slug: true,
        },
      }),
    ]);

  const dailySchedule: DailySchedule[] = [];

  for (const scheduleDayKey of Object.values(ScheduleDay)) {
    const scheduleForDay = schedules
      .filter((schedule) => schedule.days.includes(scheduleDayKey))
      .sort((a, b) => {
        return a.priority - b.priority;
      })
      .pop();
    if (scheduleForDay) {
      dailySchedule.push({
        day: scheduleDayKey,

        openTime: scheduleForDay.openTime,
        closeTime: scheduleForDay.closeTime,
      });
    } else {
      dailySchedule.push({
        day: scheduleDayKey,
        openTime: new Date(0),
        closeTime: new Date(0),
      });
    }
  }

  const savedSettings = websiteSettings?.settings as JsonObject;

  const settings =
    (websiteSettings?.settings as WebsiteSettings) ?? DEFAULT_WEBSITE_CONFIG;

  settings.backgroundColor = (savedSettings?.background as string) ?? "#FFF";

  const siteInfo: SiteInfoResponse = {
    name: account.name,
    schedule: dailySchedule,
    description: "",
    stripeAccountId:
      account.name === "CentralArkansasInflatables"
        ? null
        : account.stripeAccountId,
    logo:
      (await getAccountLogo(account.id)) ??
      "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon",
    customDomain: account.customDomain ?? (hostHeader as string),
    allSlugs: totalPages.map((page) => page.slug),
    seoSlugs: seoWebPages.map((page) => page.slug),
    postHogId: websiteSettings?.postHogId ?? undefined,
    googleAnalyticsId: websiteSettings?.googleAnalyticsId ?? undefined,
    afterHeader: websiteSettings?.afterHeader ?? undefined,
    afterBody: websiteSettings?.afterBody ?? undefined,
    phoneNumber: displayPrettyPhone(account?.businessPhone ?? ""),
    email: account?.businessEmail ?? "",
    settings,
    footerProps: {
      address: account.address,
      googleBusinessName: account.name,
      socialMedia: undefined,
    },
  };

  return siteInfo;
};

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  console.log("Fetching site info!");
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== process.env.INFINITY_GRID_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { hostHeader } = req.query;

  if (!hostHeader) {
    return res.status(400).json({ error: "Missing host header" });
  }

  const account = await db.account.findFirst({
    where: {
      customDomain: hostHeader as string,
    },
    include: {
      billingAddress: true,
    },
  });

  if (!account) {
    return res.status(404).json({ error: "Account not found z" });
  }

  const siteInfo = await getWebsiteData(
    {
      ...account,
      address: account.billingAddress
        ? getConciseAddress(account.billingAddress)
        : "",
    },
    hostHeader as string,
  );

  return res.status(200).json(siteInfo);
};

export default withErrorHandling(GET);
