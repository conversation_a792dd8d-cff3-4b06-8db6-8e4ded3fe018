import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { LegalTextPage } from "~/components/Contract/legalText";
import { BottomCopy, MiddleCopy } from "~/components/Contract/copy";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

type ContractInfoResponse = {
  clauses: LegalTextPage[];
  cleaningFee: number;
  state: string;
  signed: boolean;
};

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const { contractId, order } = req.query;
  const { account } = req;
  const orderObject = await db.order.findUnique({
    where: {
      accountId: account.id,
      id: Number(order),
    },
    include: {
      Contract: true,
      eventAddress: true,
    },
  });

  if (!orderObject) {
    return res.status(400).json({
      error: "No order defined!",
    });
  }

  const contract = orderObject.Contract.find((c) => c.id === contractId);

  if (!contract) {
    return res.status(400).json({
      error: "No contract defined!",
    });
  }

  const response: ContractInfoResponse = {
    clauses: [MiddleCopy, BottomCopy],
    cleaningFee: 75,
    state: orderObject.eventAddress?.state || "",
    signed: contract.signed,
  };

  return res.status(200).json(response);
};

export default withInfinityGridAuth(GET);
