import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { TopContractProps } from "~/components/Contract/Top/types";
import {
  orderContractItems,
  orderInfoToSharedItems,
  orderPaidItems,
} from "~/server/lib/orderUtil";
import { renderToStaticMarkup } from "react-dom/server";
import TopContract from "~/components/Contract/Top";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const orderId = req.query.order;
  const { account } = req;
  const order = await db.order.findUnique({
    where: {
      accountId: account.id,
      id: Number(orderId),
    },
    include: {
      eventAddress: true,
      Customer: true,
      setupSurface: {
        select: {
          name: true,
        },
      },
      PaymentDetails: {
        select: {
          method: true,
          methodId: true,
          amount: true,
          tip: true,
        },
      },
      OrderFee: true,
      OrderDiscount: true,
      OrderProduct: {
        select: {
          productId: true,
          pricePaid: true,
          product: {
            select: {
              name: true,
              price: true,
            },
          },
          quantity: true,
        },
      },
    },
  });

  if (!order) {
    return res.status(400).json({ error: "Order not found!ø" });
  }

  const contractItems = orderContractItems(
    order.OrderProduct,
    order.OrderProduct.map((item) => {
      return {
        id: item.productId,
        name: item.product.name,
      };
    }),
  );

  const info = orderInfoToSharedItems(
    order,
    order.OrderFee,
    order.OrderDiscount,
  );

  const paidItems = orderPaidItems(order.PaymentDetails);

  const topData: TopContractProps = {
    header: "PAYMENT",
    invoiceNumber: order.id.toString(),
    customerName: `${order.Customer.firstName} ${order.Customer.lastName}`,
    eventAddress: order.eventAddress,
    phoneNumber: order.Customer.phoneNumber || undefined,
    email: order.Customer.email,
    surface: order?.setupSurface?.name || undefined,
    minimumDeposit: account.minimumOrderPaymentPercentage,
    contractItems: contractItems,
    paidItems: paidItems,
    contractSharedItems: info,
    accountTimezone: account.businessTimezone,
  };

  const data = renderToStaticMarkup(TopContract({ ...topData }));

  return res.status(200).send(data);
};

export default withInfinityGridAuth(GET);
