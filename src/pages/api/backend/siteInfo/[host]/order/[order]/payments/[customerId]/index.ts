import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { findStripeCustomer, getStripeAccount } from "~/server/lib/stripe";
import { CurrencyValue, getMinimumDeposit } from "~/server/lib/currency";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

type PaymentInfoResponse = {
  hasPaymentLeft: boolean;
  formattedAmountLeftToPay: string;
  isCAI: boolean;
  stripeAccountId: string;
  stripeClientSecret: string;
};

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const orderId = req.query.order;
  const customerId = req.query.customerId;
  const { account } = req;

  const order = await db.order.findUnique({
    where: {
      accountId: account.id,
      customerId: customerId as string,
      id: Number(orderId),
    },
    include: {
      Customer: true,
      PaymentDetails: {
        select: {
          method: true,
          methodId: true,
          amount: true,
          tip: true,
        },
      },
      couponApplied: {
        select: {
          name: true,
          discount: true,
          discountType: true,
        },
      },
      OrderFee: true,
      OrderProduct: {
        select: {
          productId: true,
          pricePaid: true,
          product: {
            select: {
              name: true,
              price: true,
            },
          },
          quantity: true,
        },
      },
    },
  });

  if (!order) {
    return res.status(400).json({
      error: "No order defined!",
    });
  }

  const customer = order.Customer;

  const orderFinalTotal = CurrencyValue.fromPlatform(order.finalTotal);
  const orderTotalPaid = CurrencyValue.fromPlatform(order.totalPaid);

  const stripe = getStripeAccount(account);
  const stripeCustomer = await findStripeCustomer(
    account,
    customer.email,
    customer.id,
  );
  const amountLeftToPay = orderFinalTotal.subtract(orderTotalPaid);
  let minimum_payment = CurrencyValue.fromPlatform(
    getMinimumDeposit(
      amountLeftToPay.amount,
      account.minimumOrderPaymentPercentage,
    ),
  );

  if (amountLeftToPay.amount < 30) {
    minimum_payment = amountLeftToPay;
  }

  const adjustable_price = await stripe.prices.create({
    currency: "usd",
    product_data: {
      name: `Order #${order.id}`,
    },
    custom_unit_amount: {
      enabled: true,
      minimum: Math.max(50, minimum_payment.stripeAmount),
      maximum: Math.max(51, amountLeftToPay.stripeAmount + 1),
      preset: Math.max(50, amountLeftToPay.stripeAmount),
    },
  });

  // Create Checkout Sessions from body params.
  const session = await stripe.checkout.sessions.create({
    ui_mode: "embedded",
    line_items: [
      {
        price: adjustable_price.id,
        quantity: 1,
      },
    ],
    metadata: {
      lastAction: "dashboard_payment",
      orderId: order.id.toString(),
      customerId: customer.id,
    },
    payment_intent_data: {
      metadata: {
        lastAction: "dashboard_payment",
        orderId: order.id.toString(),
        customerId: customer.id,
      },
      setup_future_usage: "off_session",
    },
    customer: stripeCustomer?.id,
    mode: "payment",
    return_url: `https://${account.customDomain}/post-order/orders?session_id={CHECKOUT_SESSION_ID}&accountId=${account.id}`,
    automatic_tax: { enabled: false },
  });

  const response: PaymentInfoResponse = {
    isCAI: account.name === "CentralArkansasInflatables",
    stripeAccountId: account.stripeAccountId ?? "Null",
    stripeClientSecret: session.client_secret ?? "",
    hasPaymentLeft: amountLeftToPay.amount > 1,
    formattedAmountLeftToPay: amountLeftToPay.toPlatformString(),
  };

  return res.status(200).json(response);
};

export default withInfinityGridAuth(GET);
