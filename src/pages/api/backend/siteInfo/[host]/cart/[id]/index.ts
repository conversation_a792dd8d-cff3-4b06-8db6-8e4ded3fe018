import { db } from "~/server/db";
import { NextApiResponse } from "next";
import slugify from "slugify";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const { account } = req;
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Missing cart id" });
  }

  const cart = await db.shoppingSession.findUnique({
    where: {
      id: id as string,
      accountId: account.id,
    },
    include: {
      CartItem: {
        include: {
          product: true,
        },
      },
    },
  });

  if (!cart) {
    return res.status(404).json({ error: "Cart not found" });
  }

  return res.status(200).json({
    startTime: cart.eventStartTime,
    endTime: cart.eventEndTime,
    cartItems: cart.CartItem.map((item) => {
      return {
        id: item.productId,
        slug: item.product.slug || slugify(item.product.name, { lower: true }),
        quantity: item.quantity,
      };
    }),
  });
};

export default withInfinityGridAuth(GET);
