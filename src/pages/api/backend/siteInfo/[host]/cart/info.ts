import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { SetupSurfaceValues } from "~/query/surface/types";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

type CartInfoResponse = {
  setupSurface: SetupSurfaceValues[];
  damageWaiver: boolean;
  damageWaiverPercentage: number;
  stripeAccountId: string | null;
};

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const { account } = req;
  const setupSurface = await db.setupSurface.findMany({
    where: {
      accountId: account.id,
      archived: false,
    },
  });

  const surfaceList: SetupSurfaceValues[] = setupSurface.map((surface) => ({
    name: surface.name,
    description: surface.description,
    feeAmount: surface.feeAmount,
    scaleFee: surface.scaleFeeWithQuantity,
  }));

  const cartInfo: CartInfoResponse = {
    setupSurface: surfaceList,
    damageWaiver: account.damageWaiverRate !== null,
    damageWaiverPercentage: account.damageWaiverRate ?? 0,
    stripeAccountId:
      account.name === "CentralArkansasInflatables"
        ? null
        : account.stripeAccountId,
  };

  return res.status(200).json(cartInfo);
};

export default withInfinityGridAuth(GET);
