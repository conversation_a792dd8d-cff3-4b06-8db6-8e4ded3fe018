import { NextApiRequest, NextApiResponse } from "next";
import { captureException } from "@sentry/core";
import { Account } from "@prisma/client";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

export interface InfinityGridRequest extends NextApiRequest {
  host: string;
  account: Account;
}

export type AuthorizedNextApiHandler<T extends NextApiRequest> = (
  req: T,
  res: NextApiResponse,
) => void | Promise<void>;

export const withInfinityGridAuth = <T extends NextApiRequest>(
  handler: AuthorizedNextApiHandler<T>,
) => {
  return async function (req: T, res: NextApiResponse) {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const secret = authHeader.split(" ")[1];

    if (secret !== process.env.INFINITY_GRID_SECRET) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { host } = req.query;

    if (!host) {
      return res.status(400).json({ error: "Missing host header" });
    }

    const account = await getCachedAccountFromNameOrDomain(host as string);

    if (!account) {
      return res.status(404).json({ error: "Account not found" });
    }
    const request: T = {
      ...req,
      host: host as string,
      account: account,
    };

    try {
      return handler(request, res);
    } catch (error) {
      captureException(error);
      return res.status(500).json({ error: "Internal Server Error" });
    }
  };
};
