import { NextApiResponse } from "next";
import { getPageContent } from "~/pages/api/websites/pages/[slug]";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const { page } = req.query;

  const websitePage = await getPageContent(req.account.id, page as string);

  if (!websitePage) {
    return res.status(404).json({ error: "Page not found" });
  }

  return res.status(200).json({ page: websitePage.content });
};

export default withInfinityGridAuth(GET);
