import { NextApiRequest, NextApiResponse } from "next";
import { unstable_cache } from "next/cache";
import { db } from "~/server/db";
import { r2 } from "~/server/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";

async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export const config = {
  api: {
    bodyParser: false,
  },
};

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const { host, title, id } = req.query;

  if (!host) {
    return res.status(400).json({ error: "Missing host header" });
  }
  const account = await unstable_cache(
    async () => {
      return db.account.findFirst({
        where: {
          customDomain: host as string,
        },
      });
    },
    [`account-${host as string}`],
    {
      revalidate: 900,
      tags: [`account-${host as string}`],
    },
  )();

  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const document = await unstable_cache(
    async () => {
      return db.document.findFirst({
        where: {
          title: decodeURIComponent(title as string),
          accountId: account.id,
          public: true,
        },
      });
    },
    [`document-${title as string}-${account.id}`],
    {
      revalidate: 900,
      tags: [`document-${title as string}-${account.id}`],
    },
  )();

  if (!document) {
    return res.status(404).json({ error: "Document not found!" });
  }
  const buf = await buffer(req);
  const formData = new FormData();
  const fileBlob = new Blob([buf], { type: "image/jpeg" });

  if (fileBlob.size > ********) {
    res.status(400).json({ error: "File too large" });
    return;
  }

  formData.append("image", fileBlob, "image.jpeg");

  const file = formData.get("image") as File;

  if (!file) {
    res.status(400).json({ error: "No blob data provided" });
    return;
  }

  const docId = id as string;

  const key = `${account.id}/documents/${document.id}/${docId}`;

  const putCommand = await r2.send(
    new PutObjectCommand({
      Bucket: "contracts",
      Key: `${key}`,
      // @ts-expect-error - fileBlob is a Blob, not a Buffer and that's fine
      Body: await fileBlob.arrayBuffer(),
    }),
  );

  if (putCommand.$metadata.httpStatusCode !== 200) {
    res.status(500).json({ error: "Failed to upload image" });
    return;
  }

  let ipAddress = req.headers["x-real-ip"] as string;

  const forwardedFor = req.headers["x-forwarded-for"] as string;
  if (!ipAddress && forwardedFor) {
    ipAddress = forwardedFor?.split(",").at(0) ?? "Unknown";
  }

  const signedDocument = await db.signedDocument.findUnique({
    where: {
      id: docId,
      ipAddress,
    },
  });

  if (!signedDocument) {
    return res.status(404).json({ error: "Document not found!" });
  }

  await db.signedDocument.update({
    where: {
      id: signedDocument.id,
    },
    data: {
      storageUrl: key,
    },
  });

  return res.status(200).json({ message: "Contract signed" });
};

export default POST;
