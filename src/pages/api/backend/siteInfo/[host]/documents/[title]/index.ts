import { NextApiResponse } from "next";
import { unstable_cache } from "next/cache";
import { db } from "~/server/db";
import { Maily } from "~/lib/email/maily";
import { JSONContent } from "@tiptap/react";
import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";

const GET = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const { title } = req.query;
  const { account } = req;

  const document = await unstable_cache(
    async () => {
      return db.document.findFirst({
        where: {
          title: decodeURIComponent(title as string),
          accountId: account.id,
          public: true,
        },
      });
    },
    [`document-${title as string}-${account.id}`],
    {
      revalidate: 900,
      tags: [`document-${title as string}-${account.id}`],
    },
  )();

  if (!document?.body) {
    return res.status(404).json({ error: "Document not found!" });
  }

  const mail = new Maily(document.body as JSONContent);
  const content = await mail.renderAsync({});
  return res.status(200).json({
    content: content,
    title: document.title,
    documentType: document.type,
    lastUpdated: new Date(document.updatedAt).getTime(),
  });
};

export default withInfinityGridAuth(GET);
