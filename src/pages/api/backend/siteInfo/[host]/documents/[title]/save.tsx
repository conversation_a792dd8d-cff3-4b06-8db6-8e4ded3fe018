import { NextApiRequest, NextApiResponse } from "next";
import { unstable_cache } from "next/cache";
import { db } from "~/server/db";
import { createId } from "@paralleldrive/cuid2";
import { object, string } from "yup";

const SignedDocumentSchema = object().shape({
  firstName: string().required(),
  lastName: string().required(),
  email: string().required(),
  extraData: string().required(),
});

type SignedDocumentType = typeof SignedDocumentSchema.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { host, title } = req.query;

  if (!host) {
    return res.status(400).json({ error: "Missing host header" });
  }
  const account = await unstable_cache(
    async () => {
      return db.account.findFirst({
        where: {
          customDomain: host as string,
        },
      });
    },
    [`account-${host as string}`],
    {
      revalidate: 900,
      tags: [`account-${host as string}`],
    },
  )();

  if (!account) {
    return res.status(400).json({ error: "Account not found" });
  }

  const document = await unstable_cache(
    async () => {
      return db.document.findFirst({
        where: {
          title: decodeURIComponent(title as string),
          accountId: account.id,
          public: true,
        },
      });
    },
    [`document-${title as string}-${account.id}`],
    {
      revalidate: 900,
      tags: [`document-${title as string}-${account.id}`],
    },
  )();

  if (!document) {
    return res.status(404).json({ error: "Document not found!" });
  }
  let body: SignedDocumentType | undefined;
  try {
    body = SignedDocumentSchema.validateSync(req.body);
  } catch (e) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }
  let ipAddress = req.headers["x-real-ip"] as string;

  const forwardedFor = req.headers["x-forwarded-for"] as string;
  if (!ipAddress && forwardedFor) {
    ipAddress = forwardedFor?.split(",").at(0) ?? "Unknown";
  }

  const customer = await db.customer.findUnique({
    where: {
      email_accountId: {
        email: body.email,
        accountId: account.id,
      },
    },
  });

  let customerId: string;
  if (customer) {
    customerId = customer.id;
  } else {
    customerId = createId();
    await db.customer.create({
      data: {
        id: customerId,
        email: body.email,
        accountId: account.id,
        firstName: body.firstName,
        lastName: body.lastName,
      },
    });
  }

  const signedDocument = await db.signedDocument.create({
    data: {
      storageUrl: "",
      ipAddress,
      customerId,
      userAgent: req.headers["user-agent"]!,
      documentId: document.id,
      extraData: JSON.parse(body.extraData || "{}"),
    },
  });

  return res.status(200).json({ signedDocumentId: signedDocument.id });
};

export default handler;
