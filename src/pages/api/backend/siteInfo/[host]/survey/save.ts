import {
  InfinityGridRequest,
  withInfinityGridAuth,
} from "~/pages/api/backend/siteInfo/[host]/middleware";
import { NextApiResponse } from "next";
import { number, object, string } from "yup";
import { withValidation } from "~/pages/api/apiMiddleware";
import { db } from "~/server/db";

const SURVEY_SCHEMA = object().shape({
  customerId: string().required(),
  orderId: number().required(),
  feedback: string().optional().nullable(),
  rating: number()
    .moreThan(0, "Minimum rating is 0")
    .lessThan(1.01, "Maximum rating is 1")
    .required(),
});

const POST = async (req: InfinityGridRequest, res: NextApiResponse) => {
  const order = await db.order.findFirst({
    where: {
      accountId: req.account.id,
      id: req.body.orderId,
      customerId: req.body.customerId,
    },
  });

  if (!order) {
    return res.status(404).json({ error: "Order not found" });
  }

  await db.orderSurvey.create({
    data: {
      orderId: order.id,
      customerId: order.customerId,
      feedback: req.body.feedback ?? null,
      sentimentScore: req.body.rating,
    },
  });

  return res.status(200).json({ success: true });
};

export default withInfinityGridAuth(withValidation(SURVEY_SCHEMA, POST));
