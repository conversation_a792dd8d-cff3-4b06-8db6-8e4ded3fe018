import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { unstable_cache } from "next/cache";
import { getPageContent } from "~/pages/api/websites/pages/[slug]";
import { withErrorHandling } from "~/pages/api/apiMiddleware";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== process.env.INFINITY_GRID_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { hostHeader, page } = req.query;

  if (!hostHeader) {
    return res.status(400).json({ error: "Missing host header" });
  }

  const account = await unstable_cache(
    async () => {
      return db.account.findFirst({
        where: {
          customDomain: host<PERSON>eader as string,
        },
      });
    },
    [`account-${hostHeader as string}`],
    {
      revalidate: 900,
      tags: [`account-${hostHeader as string}`],
    },
  )();

  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const websitePage = await getPageContent(account.id, page as string);

  if (!websitePage) {
    return res.status(404).json({ error: "Page not found" });
  }

  return res.status(200).json({ page: websitePage.content });
};

export default withErrorHandling(GET);
