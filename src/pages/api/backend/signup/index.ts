import { boolean, object, string } from "yup";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { handlePlainTextPassword } from "~/server/lib/password";
import { ScheduleDay } from ".prisma/client";
import {
  AddressValidation,
  getConciseAddress,
} from "~/server/lib/location/types";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { platformStripeServer } from "~/server/lib/stripe";
import { Client } from "@hubspot/api-client";
import { createDefaultWebsite } from "~/lib/signup/website";
import { sendReactEmail } from "~/server/email/sendMail";
import { NEW_ACCOUNT_EMAIL } from "../../../../../emails/NewAccountEmail";
import slugify from "slugify";
import {
  updateWebsite,
  updateWebsiteCollection,
  updateWebsitePage,
} from "~/server/lib/website";

const SHARED_SECRET = "********-62ba-42f3-9b7a-d63c8d44ede0";

enum AccountCreationSteps {
  ACCOUNT = "account",
  USER = "user",
  USER_ROLE = "userRole",
  STRIPE_CUSTOMER = "stripeCustomer",
  HUBSPOT = "hubspot",
  SETUP_SURFACE = "setupSurface",
  SCHEDULE = "schedule",
  EMAIL_TEMPLATE = "emailTemplate",
  WEBSITE = "website",
}

const ACCOUNT_CREATION_SCHEMA = object().shape({
  businessName: string().required(),
  businessEmail: string().email().required(),
  businessDomain: string(),
  businessAddress: AddressValidation.required(),
  organizationId: string(),

  userEmail: string().email().required(),
  userPassword: string().required(),

  createBoilerplate: boolean().default(true),
  hubspotReferenceId: string(),
  notify: boolean().default(true),
});

type AccountCreationBody = typeof ACCOUNT_CREATION_SCHEMA.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const secret = authHeader.split(" ")[1];

  if (secret !== SHARED_SECRET) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { body } = req;

  let accountCreationBody: AccountCreationBody;
  try {
    accountCreationBody = await ACCOUNT_CREATION_SCHEMA.validate(body);
  } catch (error) {
    console.error(error);
    return res.status(400).json({ message: "Invalid Body" });
  }

  const capabilities: Record<AccountCreationSteps, boolean> = {
    [AccountCreationSteps.ACCOUNT]: false,
    [AccountCreationSteps.USER]: false,
    [AccountCreationSteps.USER_ROLE]: false,
    [AccountCreationSteps.STRIPE_CUSTOMER]: false,
    [AccountCreationSteps.HUBSPOT]: false,
    [AccountCreationSteps.SETUP_SURFACE]: false,
    [AccountCreationSteps.SCHEDULE]: false,
    [AccountCreationSteps.EMAIL_TEMPLATE]: false,
    [AccountCreationSteps.WEBSITE]: false,
  };

  const defaultRoles = await db.role.findFirst({
    where: {
      name: "Owner",
    },
  });

  if (!defaultRoles) {
    return res.status(500).json({ message: "No default roles found" });
  }

  const {
    businessName,
    businessEmail,
    businessDomain,
    userEmail,
    userPassword,
    organizationId,
  } = accountCreationBody;

  const address = await findOrInsertAddress(
    accountCreationBody.businessAddress,
  );

  const slugifiedName = slugify(businessName.toLowerCase());

  let orgId = organizationId;
  if (!orgId) {
    const organization = await db.organization.create({
      data: {},
    });

    orgId = organization.id;
  }

  const account = await db.account.create({
    data: {
      name: businessName,
      businessEmail: businessEmail,
      customDomain:
        businessDomain?.toLowerCase() ?? `${slugifiedName}.evntflow.com`,
      billingAddressId: address.id,
      organizationId: orgId,
    },
  });

  if (!account) {
    return res.status(500).json({ message: "Failed to create account" });
  }
  capabilities.account = true;

  const passwordHash = await handlePlainTextPassword(userPassword);

  const user = await db.user.create({
    data: {
      email: userEmail,
      password: passwordHash,
    },
  });

  if (!user) {
    return res.status(500).json({ message: "Failed to create user" });
  }

  const staff = await db.staff.create({
    data: {
      email: userEmail,
      name: userEmail,
      password: passwordHash,
      accountId: account.id,
      userId: user.id,
    },
  });

  if (!staff) {
    return res.status(500).json({ message: "Failed to create staff" });
  }
  capabilities.user = true;

  await db.staffRole.create({
    data: {
      roleId: defaultRoles.id,
      staffId: staff.id,
    },
  });

  capabilities.userRole = true;

  const customer = await platformStripeServer.customers.create({
    name: businessName,
    email: userEmail,
    metadata: {
      accountId: account.id.toString(),
    },
  });

  if (customer) {
    await db.account.update({
      where: {
        id: account.id,
      },
      data: {
        stripeCustomerId: customer.id,
      },
    });
    capabilities.stripeCustomer = true;
  }

  try {
    const hubspot = new Client({
      accessToken: process.env.HUBSPOT_API_KEY,
    });

    const companyCreate = {
      properties: {
        name: businessName,
        domain: businessDomain ?? "",
        reference_id: accountCreationBody?.hubspotReferenceId ?? "",
        closedate: new Date().toISOString(),
      },
    };

    await hubspot.crm.companies.basicApi.create(companyCreate);
    capabilities.hubspot = true;
  } catch (e) {
    console.error("Error creating company in HubSpot", e);
  }

  if (accountCreationBody.createBoilerplate) {
    // create default setup surfaces
    const defaultSetupSurface = ["grass", "indoor", "concrete"];
    const boilerPlatePromises = [];
    const defaultSetupSurfacePromises = defaultSetupSurface.map((surface) => {
      return db.setupSurface.create({
        data: {
          name: surface,
          description: `Default setup surface for ${surface}`,
          accountId: account.id,
        },
      });
    });
    boilerPlatePromises.push(...defaultSetupSurfacePromises);

    const now = new Date();

    // create default schedule
    boilerPlatePromises.push({
      data: {
        name: "Default Schedule",
        description: "Default Schedule",
        startTime: now,
        endTime: new Date(9999, 12, 31),
        openTime: new Date(1970, 1, 1, 8, 0, 0),
        closeTime: new Date(1970, 1, 1, 17, 0, 0),
        accountId: account.id,
        days: [
          ScheduleDay.MONDAY,
          ScheduleDay.TUESDAY,
          ScheduleDay.WEDNESDAY,
          ScheduleDay.THURSDAY,
          ScheduleDay.FRIDAY,
          ScheduleDay.SATURDAY,
          ScheduleDay.SUNDAY,
        ],
      },
    });
    await Promise.all(boilerPlatePromises);

    capabilities.setupSurface = true;
    capabilities.schedule = true;

    try {
      await createDefaultEmailTemplates(account.id, businessName);
      capabilities.emailTemplate = true;
    } catch (e) {
      console.error("Error creating default email templates", e);
    }

    try {
      // create default pages
      await createDefaultWebsite(account.id, businessName);

      try {
        const recentlyUpdatedWebsitePages = await db.websitePage.findMany({
          where: {
            accountId: account.id,
          },
        });

        if (recentlyUpdatedWebsitePages.length !== 0) {
          console.log(
            `Updating website data for ${recentlyUpdatedWebsitePages.length} pages`,
          );
          for (const websitePage of recentlyUpdatedWebsitePages) {
            if (!account?.customDomain) {
              continue;
            }

            await updateWebsitePage(
              account.customDomain,
              websitePage.slug,
              websitePage.content as any,
              account,
            );
          }
        }

        if (account.customDomain) {
          await updateWebsiteCollection(account.id, orgId);
          await updateWebsite(
            {
              ...account,
              address: getConciseAddress(address),
            },
            account.customDomain ?? "",
          );
        }

        capabilities.website = true;
      } catch (ex) {
        console.error("Error updating website", ex);
      }
    } catch (e) {
      console.error("Error creating default website", e);
    }
  }

  if (accountCreationBody.notify) {
    await sendReactEmail(false, account, businessEmail, NEW_ACCOUNT_EMAIL, {
      temporaryPassword: userPassword,
    });
  }

  const successCapabilities = Object.entries(capabilities)
    .filter(([, v]) => v)
    .map(([k]) => k);

  const failedCapabilities = Object.entries(capabilities)
    .filter(([, v]) => !v)
    .map(([k]) => k);

  return res.status(200).json({
    message: "Success",
    success: successCapabilities,
    failed: failedCapabilities,
  });
};

export const createDefaultEmailTemplates = async (
  accountId: number,
  accountName: string,
) => {
  const jsonDocument = {
    type: "doc",
    content: [
      {
        type: "image",
        attrs: {
          src: "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/format=auto",
          alt: null,
          title: null,
          width: 422,
          height: 281.33,
          alignment: "center",
          externalLink: null,
        },
      },
      {
        type: "heading",
        attrs: { textAlign: "center", level: 2 },
        content: [
          { type: "text", text: "Order Receipt #" },
          {
            type: "variable",
            attrs: { id: "ORDER_NUMBER", label: null, fallback: "1015" },
          },
          { type: "text", text: " " },
        ],
      },
      {
        type: "paragraph",
        attrs: { textAlign: "center" },
        content: [{ type: "text", text: "Thank you for your order!" }],
      },
      { type: "horizontalRule" },
      {
        type: "orderProducts",
        attrs: { mailyComponent: "button" },
        content: [
          { type: "paragraph", attrs: { textAlign: "left" } },
          { type: "paragraph", attrs: { textAlign: "left" } },
        ],
      },
      {
        type: "button",
        attrs: {
          mailyComponent: "button",
          text: "Pay Remaining Balance",
          url: "ORDER_PAYMENT_LINK",
          alignment: "center",
          variant: "filled",
          borderRadius: "smooth",
          buttonColor: "rgb(0, 0, 0)",
          textColor: "rgb(255, 255, 255)",
        },
      },
      {
        type: "button",
        attrs: {
          mailyComponent: "button",
          text: "Sign Contract",
          url: "ORDER_CONTRACT_LINK",
          alignment: "center",
          variant: "outline",
          borderRadius: "smooth",
          buttonColor: "#09203f",
          textColor: "#09203f",
        },
      },
      { type: "paragraph", attrs: { textAlign: "left" } },
    ],
  };
  const cancellationDocument = {
    type: "doc",
    content: [
      {
        type: "image",
        attrs: {
          src: "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/format=auto",
          alt: `${accountName} Logo`,
          title: null,
          width: 245,
          height: 160,
          alignment: "center",
          externalLink: null,
        },
      },
      {
        type: "heading",
        attrs: { textAlign: "center", level: 1 },
        content: [{ type: "text", text: "Order Cancelled" }],
      },
      {
        type: "paragraph",
        attrs: { textAlign: "left" },
        content: [
          { type: "text", text: "Dear " },
          {
            type: "variable",
            attrs: {
              id: "FIRST_NAME",
              label: null,
              fallback: "Valued Customer",
            },
          },
          { type: "text", text: ", " },
        ],
      },
      {
        type: "paragraph",
        attrs: { textAlign: "left" },
        content: [
          {
            type: "text",
            text: `We are sorry to inform you that your event with ${accountName} has been cancelled. Below is a summary of your order.`,
          },
        ],
      },
      {
        type: "paragraph",
        attrs: { textAlign: "left" },
        content: [{ type: "text", text: "Sincerely," }],
      },
      {
        type: "paragraph",
        attrs: { textAlign: "left" },
        content: [{ type: "text", text: `${accountName} Team` }],
      },
      { type: "orderProducts", attrs: { mailyComponent: "orderProducts" } },
      {
        type: "paragraph",
        attrs: { textAlign: "left" },
        content: [
          {
            type: "text",
            text: `Order Cancellation is based on the terms and conditions of the contract. If you have any questions, please contact us by replying to this email. If you believe this cancellation was made in error, please contact us `,
          },
          { type: "text", marks: [{ type: "bold" }], text: "immediately" },
          { type: "text", text: "." },
        ],
      },
    ],
  };

  const receiptTemplate = await db.customEmailDelivery.create({
    data: {
      subject: `Order Receipt from ${accountName}`,
      name: "Order Receipt",
      text: JSON.stringify(jsonDocument),
      accountId: accountId,
      previewText: `${accountName} Order Receipt`,
    },
  });

  const cancellationTemplate = await db.customEmailDelivery.create({
    data: {
      subject: `Order Cancellation`,
      name: `Cancellation`,
      text: JSON.stringify(cancellationDocument),
      accountId: accountId,
      previewText: `Your order with ${accountName} has been cancelled`,
    },
  });

  await db.scheduledEmailConfig.create({
    data: {
      enabled: true,
      customEmailDeliveryId: receiptTemplate.id,
      accountId: accountId,
      action: "ORDER_UPDATE",
      minutesAfter: 0,
    },
  });

  await db.scheduledEmailConfig.create({
    data: {
      enabled: true,
      customEmailDeliveryId: receiptTemplate.id,
      accountId: accountId,
      action: "RECEIPT_EMAIL",
      minutesAfter: 0,
    },
  });

  await db.scheduledEmailConfig.create({
    data: {
      enabled: true,
      customEmailDeliveryId: cancellationTemplate.id,
      accountId: accountId,
      action: "ORDER_CANCELLATION",
      minutesAfter: 0,
    },
  });
};

export default handler;
