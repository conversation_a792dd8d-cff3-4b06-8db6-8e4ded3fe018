import { NextApiRequest, NextApiResponse } from "next";
import { platformStripeServer } from "~/server/lib/stripe";
import { checkPermission } from "~/pages/api/permissions";
import process from "process";
import { db } from "~/server/db";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const accountData = await db.account.findFirst({
    where: {
      id: permissions.accountId,
    },
  });

  if (!accountData) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }

  if (accountData.stripeAccountId) {
    const account = await platformStripeServer.accounts.retrieve(
      accountData.stripeAccountId,
    );

    const accountLink = await platformStripeServer.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_SELF_URL}/account?tab=billing`,
      return_url: `${process.env.NEXT_PUBLIC_SELF_URL}/account?tab=billing`,
      type: "account_onboarding",
    });

    res.status(200).json({
      url: accountLink.url,
    });
  }

  const account = await platformStripeServer.accounts.create({
    country: "US",
    type: "express",
    business_profile: {
      mcc: "7394",
      url: accountData.customDomain ?? undefined,
    },
    capabilities: {
      card_payments: {
        requested: true,
      },
      transfers: {
        requested: true,
      },
    },
  });

  if (accountData.billingAddressId) {
    const businessAddress = await db.address.findFirst({
      where: {
        id: accountData.billingAddressId,
      },
    });

    if (businessAddress) {
      await platformStripeServer.tax.settings.update(
        {
          defaults: {
            tax_code: "txcd_20030000",
            tax_behavior: "inclusive",
          },
          head_office: {
            address: {
              city: businessAddress.city ?? "Benton",
              state: businessAddress.state ?? "AR",
              postal_code: businessAddress.postalCode ?? "72015",
              country: "US",
            },
          },
        },
        {
          stripeAccount: account.id,
        },
      );
      await platformStripeServer.tax.registrations.create(
        {
          country: "US",
          country_options: {
            us: {
              state: businessAddress.state ?? "AR",
              type: "state_sales_tax",
            },
          },
          active_from: "now",
        },
        {
          stripeAccount: account.id,
        },
      );
    }
  }

  await db.account.update({
    where: {
      id: accountData.id,
    },
    data: {
      stripeAccountId: account.id,
    },
  });

  const accountLink = await platformStripeServer.accountLinks.create({
    account: account.id,
    refresh_url: `${process.env.NEXT_PUBLIC_SELF_URL}/account?tab=billing`,
    return_url: `${process.env.NEXT_PUBLIC_SELF_URL}/account?tab=billing`,
    type: "account_onboarding",
  });

  res.status(200).json({
    url: accountLink.url,
  });
};

export default GET;
