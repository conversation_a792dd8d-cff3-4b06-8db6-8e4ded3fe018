import { NextApiRequest, NextApiResponse } from "next";
import { getToken } from "next-auth/jwt";
import { db } from "~/server/db";
import { object } from "yup";
import { AddressValidation } from "~/server/lib/location/types";
import { getTravelFee } from "~/server/lib/orderUtil";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { getTaxRate } from "~/server/lib/tax";

const TRAVEL_FEE_SCHEMA = object().shape({
  location: AddressValidation.required(),
});

export type AddressPaymentInfoResponse = {
  travelFee: number;
  taxRate: number;
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const token = await getToken({ req });
  if (!token) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const account = await db.account.findFirst({
    where: {
      id: token.accountId,
    },
  });

  if (!account) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  try {
    const body = await TRAVEL_FEE_SCHEMA.validate(req.body);
    const location = body.location;
    const address = await findOrInsertAddress(location);
    const travelFee = (await getTravelFee(account.id, address)) ?? 0;
    const taxRate = (await getTaxRate(account.id, address)) ?? 0;
    return res
      .status(200)
      .json({ travelFee, taxRate } as AddressPaymentInfoResponse);
  } catch (e: any) {
    return res.status(400).json({ error: e.message });
  }
};

export default handler;
