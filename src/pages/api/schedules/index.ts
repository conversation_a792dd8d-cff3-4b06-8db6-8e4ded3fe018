import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";

export const GET = async (
  req: AuthorizedNextApiRequest,
  res: NextApiResponse,
) => {
  const schedules = await db.schedule.findMany({
    where: {
      accountId: req.accountId,
    },
    orderBy: {
      updatedAt: "desc",
    },
    take: 100,
  });

  res.status(200).json({ success: true, schedules });
};

export default withPermissions(
  {
    policy: "api.schedule",
    action: "read",
  },
  GET,
);
