import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { ScheduleSchema } from "~/server/schedule/types";
import { ScheduleDay } from "@prisma/client";
import { checkPermission, PermissionAction } from "~/pages/api/permissions";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const scheduleBasic = await handleBasicSchedule(
    req,
    res,
    req.method === "GET"
      ? "read"
      : req.method === "DELETE"
        ? "execute"
        : "write",
  );

  if (req.method === "DELETE") {
    return handleDeleteSchedule(req, res, scheduleBasic);
  }

  if (req.method === "PUT") {
    return handleEditSchedule(req, res, scheduleBasic);
  }

  if (req.method !== "GET") {
    res.status(400).json({ error: "Invalid Method" });
    return;
  }

  const schedule = await db.schedule.findFirst({
    where: {
      id: scheduleBasic?.scheduleId,
      accountId: scheduleBasic?.accountId,
    },
  });

  if (!schedule) {
    res.status(404).json({ error: "Schedule not found" });
    return;
  }

  const scheduleOutput = ScheduleSchema.cast(schedule, {
    stripUnknown: true,
  });
  res.json({ success: true, schedule: scheduleOutput });
};

type BasicScheduleRequest = {
  scheduleId: number;
  accountId: number;
};

type BasicScheduleBody = {
  schedule: typeof ScheduleSchema.__outputType;
  startTime: Date;
  endTime: Date;
  days: ScheduleDay[];
};

export const handleBasicSchedule = async (
  req: NextApiRequest,
  res: NextApiResponse,
  action: PermissionAction,
): Promise<BasicScheduleRequest | null> => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.schedule",
      action: action,
    },
  });

  if (!permission) {
    return null;
  }

  const { id } = req.query;

  if (!id) {
    res.status(400).json({ error: "Missing ID" });
    return null;
  }

  const numberId = Number(id);

  if (isNaN(numberId)) {
    res.status(400).json({ error: "Invalid ID" });
    return null;
  }

  return { scheduleId: numberId, accountId: permission.accountId };
};

export const handleDeleteSchedule = async (
  req: NextApiRequest,
  res: NextApiResponse,
  schedule: BasicScheduleRequest | null,
) => {
  if (!schedule) {
    return;
  }

  const { scheduleId, accountId } = schedule;
  const scheduleDelete = await db.schedule.delete({
    where: {
      accountId,
      id: scheduleId,
    },
  });

  if (!scheduleDelete) {
    res.status(404).json({ error: "Schedule not found" });
    return;
  }

  res.json({ success: true });
};

export const handleScheduleBody = async (
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<BasicScheduleBody | null> => {
  const data = req.body;
  let scheduleBody;
  try {
    scheduleBody = await ScheduleSchema.validate(data, { stripUnknown: true });
  } catch (e) {
    res.status(400).json({ error: "failed to parse body" });
    return null;
  }

  if (!scheduleBody) {
    res.status(400).json({ error: "Missing required fields" });
    return null;
  }

  const endTime = new Date();
  if (scheduleBody.endTime) {
    endTime.setTime(new Date(scheduleBody.endTime).getTime());
  } else {
    endTime.setFullYear(9999);
  }

  const startTime = new Date();
  if (scheduleBody.startTime) {
    startTime.setTime(new Date(scheduleBody.startTime).getTime());
  }
  const days = scheduleBody.days
    .flatMap((item) => (!item ? [] : [item]))
    .map((item: string) => {
      const enumDay = Object.entries(ScheduleDay).find(([key]) => key === item);
      if (!enumDay) {
        return null;
      }
      return enumDay[1];
    })
    .flatMap((item) => (!item ? [] : [item]));

  return {
    schedule: scheduleBody,
    startTime,
    endTime,
    days,
  };
};

export const handleEditSchedule = async (
  req: NextApiRequest,
  res: NextApiResponse,
  scheduleBasic: BasicScheduleRequest | null,
) => {
  if (!scheduleBasic) {
    return;
  }

  const { scheduleId, accountId } = scheduleBasic;
  const body = await handleScheduleBody(req, res);

  if (!body) {
    return;
  }

  const { schedule, startTime, endTime, days } = body;

  console.log(endTime);

  const scheduleUpdate = await db.schedule.update({
    where: {
      accountId,
      id: scheduleId,
    },
    data: {
      name: schedule.name,
      description: schedule.description,
      active: schedule.active,
      closed: schedule.closed,
      openTime: schedule.openTime,
      closeTime: schedule.closeTime,
      days: days,
      priority: schedule.priority,
      startTime: startTime,
      endTime: endTime,
    },
  });

  if (!scheduleUpdate) {
    res.status(404).json({ error: "Schedule not found" });
    return;
  }

  res.json({ success: true });
};

export default handler;
