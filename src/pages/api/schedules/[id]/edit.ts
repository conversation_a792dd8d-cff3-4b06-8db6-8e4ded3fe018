import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicSchedule,
  handleEditSchedule,
} from "~/pages/api/schedules/[id]/index";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const items = await handleBasicSchedule(req, res, "write");

  if (!items) {
    return;
  }
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  return handleEditSchedule(req, res, items);
};

export default handler;
