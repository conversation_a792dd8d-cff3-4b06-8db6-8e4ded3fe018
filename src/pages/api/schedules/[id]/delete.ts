import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicSchedule,
  handleDeleteSchedule,
} from "~/pages/api/schedules/[id]/index";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const scheduleBasic = await handleBasicSchedule(req, res, "execute");

  if (!scheduleBasic) {
    return;
  }

  return handleDeleteSchedule(req, res, scheduleBasic);
};

export default handler;
