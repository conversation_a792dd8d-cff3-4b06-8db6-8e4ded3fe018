import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { handleScheduleBody } from "~/pages/api/schedules/[id]";
import { checkPermission } from "~/pages/api/permissions";
import { captureException } from "@sentry/core";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.schedule",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const scheduleBody = await handleScheduleBody(req, res);

  if (!scheduleBody) {
    return;
  }

  const { schedule, startTime, endTime, days } = scheduleBody;

  try {
    await db.schedule.create({
      data: {
        accountId: permission.accountId,
        name: schedule.name,
        description: schedule.description,
        active: schedule.active,
        closed: schedule.closed,
        openTime: schedule.openTime,
        closeTime: schedule.closeTime,
        days: days,
        priority: schedule.priority,
        startTime: startTime,
        endTime: endTime,
      },
    });
  } catch (e) {
    captureException(e);
    console.error(e);
    res.status(500).json({ error: "Internal Server Error" });
    return;
  }

  res.json({ success: true });
};

export default POST;
