import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { captureException } from "@sentry/core";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.schedule",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const body = req.body;

  if (!body) {
    res.status(400).json({ error: "Bad Request" });
    return;
  }

  let schedules = body.schedules;

  try {
    schedules = schedules as { id: number; priority: number }[];
  } catch (e) {
    captureException(e);
    console.error(e);
    res.status(400).json({ error: "Bad Request" });
    return;
  }

  if (!schedules) {
    res.status(400).json({ error: "Bad Request" });
    return;
  }

  const updates: any[] = [];

  schedules.forEach((schedule: { id: number; priority: number }) => {
    updates.push(
      db.schedule.update({
        where: {
          id: schedule.id,
          accountId: permission.accountId,
        },
        data: {
          priority: schedule.priority,
        },
      }),
    );
  });

  const updateSchedules = await db.$transaction(updates);

  if (!updateSchedules) {
    res.status(500).json({ error: "Internal Server Error" });
    return;
  }

  return res.status(200).json({ success: true });
};

export default POST;
