import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { createId } from "@paralleldrive/cuid2";
import type { Readable } from "stream";
import { fullImageUrl } from "~/server/globalTypes";
import { captureMessage } from "@sentry/nextjs";
import { ImageUpload } from ".prisma/client";
import { checkPermission } from "~/pages/api/permissions";

async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export const config = {
  api: {
    bodyParser: false,
  },
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.images",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }
  const { fileName } = req.query;

  if (!fileName) {
    res.status(400).json({ error: "No file name provided" });
    return;
  }

  const buf = await buffer(req);
  const fileBlob = new Blob([buf], { type: req.headers["content-type"]! });

  if (!fileBlob) {
    res.status(400).json({ error: "No file provided" });
    return;
  }

  const image = await uploadImageInternal(
    fileBlob,
    fileName as string,
    permission.accountId,
  );

  if (!image) {
    return res
      .status(500)
      .json({ success: false, error: "Error uploading image!" });
  }

  return res.status(200).json({
    token: image.id,
    image: {
      name: image.name,
      url: fullImageUrl(image.url),
      id: image.id,
    },
  });
};

export default handler;

type ImageUploadMetadata = {
  accountId: number;
  id: string;
  name: string;
};

export const uploadImageInternal = async (
  file: Blob,
  fileName: string,
  accountId: number,
): Promise<ImageUpload | null> => {
  const id = createId();

  const metadata: ImageUploadMetadata = {
    accountId: accountId,
    id: id,
    name: fileName,
  };

  const form = new FormData();
  form.append("file", file, fileName);
  form.append("metadata", JSON.stringify(metadata));

  const request = await fetch(
    `https://api.cloudflare.com/client/v4/accounts/${process.env.R2_ACCOUNT_ID}/images/v1`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CLOUDFLARE_IMAGES_API_KEY}`,
      },
      body: form,
    },
  );

  if (!request.ok) {
    captureMessage("Error uploading image to Cloudflare", {
      extra: {
        accountId: accountId,
        error: request.statusText,
      },
    });
    return null;
  }

  const resp = await request.json();

  if (!resp.success) {
    captureMessage("Error uploading image to Cloudflare", {
      extra: {
        accountId: accountId,
        error: JSON.stringify(resp),
      },
    });
    return null;
  }

  return db.imageUpload.create({
    data: {
      accountId: accountId,
      id: id,
      name: fileName,
      url: resp.result.id,
    },
  });
};
