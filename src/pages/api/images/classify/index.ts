import { NextApiResponse } from "next";
import { mixed, object, string } from "yup";
import { ImageType } from "@prisma/client";
import { db } from "~/server/db";
import { fullImageUrl } from "~/server/globalTypes";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";

export const ImageInputSchema = object().shape({
  imageId: string().required(),
  classification: mixed<ImageType>().oneOf(Object.values(ImageType)).required(),
});

type ImageInput = typeof ImageInputSchema.__outputType;

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { imageId, classification }: ImageInput = req.body;

  const currentImage = await db.imageUpload.findFirst({
    where: {
      accountId: req.accountId,
      id: imageId,
    },
  });

  if (!currentImage) {
    res.status(404).json({ error: "Image not found" });
    return;
  }

  const classificationType = classification as ImageType;

  const currentImageWithClassification = await db.imageUpload.findFirst({
    where: {
      accountId: req.accountId,
      type: classificationType,
    },
  });

  if (currentImageWithClassification) {
    await db.imageUpload.update({
      where: {
        accountId: req.accountId,
        id: currentImageWithClassification.id,
      },
      data: {
        type: null,
      },
    });
  }

  const image = await db.imageUpload.update({
    where: {
      accountId: req.accountId,
      id: imageId,
    },
    data: {
      type: classificationType,
    },
  });

  const response = {
    id: image.id,
    name: image.name,
    url: fullImageUrl(image.url),
    type: image.type,
  };

  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.website",
    action: "write",
  },
  withMethods(["POST"], withValidation(ImageInputSchema, POST)),
);
