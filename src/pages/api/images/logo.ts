import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { ImageType } from "@prisma/client";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.images",
      action: "read",
    },
  });
  if (!permission) {
    return;
  }

  const image = await db.imageUpload.findFirst({
    where: {
      accountId: permission.accountId,
      type: ImageType.LOGO,
    },
  });

  res.status(200).json({ image: image || null });
};

export default GET;
