import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { ImageUploadSimpleType } from "~/components/image/image";
import { fullImageUrl } from "~/server/globalTypes";
import { ImageLookupResult } from "~/query/images";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { cursor } = req.query;

  if (cursor !== undefined && typeof cursor !== "string") {
    res.status(400).json({ error: "Invalid cursor" });
    return;
  }

  const pageLimit = 20;

  const images = await db.imageUpload.findMany({
    where: {
      accountId: req.accountId,
    },

    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      url: true,
      name: true,
    },
    cursor: cursor
      ? {
          id: cursor,
        }
      : undefined,
    skip: cursor ? 1 : 0,
    take: pageLimit,
  });

  const imageSelection: ImageUploadSimpleType[] = images.map((image) => {
    return {
      id: image.id,
      name: image.name,
      url: fullImageUrl(image.url),
    };
  });

  const result: ImageLookupResult = {
    images: imageSelection,
    cursor: images[pageLimit - 1]?.id,
  };

  res.status(200).json(result);
};

export default withPermissions(
  {
    policy: "api.product",
    action: "read",
  },
  handler,
);
