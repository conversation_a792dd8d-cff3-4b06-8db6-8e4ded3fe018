import { NextApiRequest, NextApiResponse } from "next";
import { AnyObject, Maybe, ObjectSchema, ValidationError } from "yup";
import { captureException } from "@sentry/core";
import {
  checkPermission,
  OpenPermissionCheck,
  PermissionCheck,
} from "~/pages/api/permissions";
import { JWT } from "next-auth/jwt";

interface AuthorizationBit {
  token: JWT;
  accountId: number;
  organizationId: string;
  totalPermissions: string[];
  userId: string;
  staffId: string;
}

export interface AuthorizedNextApiRequest
  extends NextApiRequest,
    AuthorizationBit {}

export type AuthorizedNextApiHandler<T extends NextApiRequest> = (
  req: T,
  res: NextApiResponse,
) => void | Promise<void>;

export function withErrorHandling<T extends NextApiRequest>(
  handler: AuthorizedNextApiHandler<T>,
) {
  return async function (req: T, res: NextApiResponse) {
    try {
      return await handler(req, res);
    } catch (error) {
      captureException(error);
      return res.status(500).json({ error: "Internal Server Error" });
    }
  };
}

export function withPermissions<T extends NextApiRequest>(
  permission: PermissionCheck | OpenPermissionCheck,
  handler: AuthorizedNextApiHandler<T>,
) {
  return async function (req: T, res: NextApiResponse) {
    const runCheck = async () => {
      const permissionResult = await checkPermission({ req, res, permission });

      if (!permissionResult) {
        return;
      }

      const authorizedReq: AuthorizationBit = {
        token: permissionResult.token,
        accountId: permissionResult.accountId,
        totalPermissions: permissionResult.totalPermissions,
        organizationId: permissionResult.organizationId,
        userId: permissionResult.userId,
        staffId: permissionResult.staffId,
      };

      if (!permissionResult.accountId || !permissionResult.organizationId) {
        return res
          .status(401)
          .json({ error: "Unauthorized, authorization stature not set." });
      }

      return handler(
        {
          ...req,
          ...authorizedReq,
        },
        res,
      );
    };
    return withErrorHandling(runCheck)(req, res);
  };
}

export function withMethods<T extends NextApiRequest>(
  methods: string[],
  handler: AuthorizedNextApiHandler<T>,
) {
  return async function (request: T, response: NextApiResponse) {
    if (!methods.includes(request?.method || "")) {
      return response.status(405).json("");
    }

    return handler(request, response);
  };
}

/**
 * Middleware to validate request body against a schema
 * @param schema - yup schema to validate request body against
 * @param handler - next api handler
 * @param options - Extra options for validation
 */
export function withValidation<
  T extends Maybe<AnyObject>,
  V extends NextApiRequest,
>(
  schema: ObjectSchema<T>,
  handler: AuthorizedNextApiHandler<V>,
  options?: { stripUnknown: boolean },
) {
  return async (req: V, res: NextApiResponse) => {
    try {
      req.body = await schema.validate(req.body, {
        abortEarly: false,
        stripUnknown: options?.stripUnknown ?? true,
      });

      return handler(req, res);
    } catch (error) {
      if (error instanceof ValidationError) {
        return res
          .status(422)
          .json({ error: "Invalid Request", message: error.message });
      }

      captureException(error);
      return res.status(422).json({ error: "Invalid request" });
    }
  };
}
