import { NextApiRequest, NextApiResponse } from "next";
import { array, number, object } from "yup";
import { db } from "~/server/db";
import { getMilesBetweenLocations } from "~/server/lib/location/maps";
import { AddressValidation, getFullAddress } from "~/server/lib/location/types";
import { checkPermission } from "~/pages/api/permissions";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { Address } from "@prisma/client";

export const DISTANCE_INPUT_SCHEMA = object().shape({
  origins: array().of(AddressValidation.required()).required(),
  locations: array().of(number().required()),
});

export type DeliveryDistanceLocations = {
  id: number;
  latitude: number;
  longitude: number;
  distance_from_depot: {
    line1: string;
    distance: number;
  }[];
  distance_to_other_locations: Record<number, number>;
};

export type DistanceResponse = {
  locations: DeliveryDistanceLocations[];
  origins: {
    id: number;
    line1: string;
    longitude: number;
    latitude: number;
  }[];
};

type DistanceLookupCache = {
  distanceMiles: number;
  travelTimeMinutes: number;
};

export type DistanceInput = typeof DISTANCE_INPUT_SCHEMA.__outputType;

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.driver",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const account = await db.account.findUnique({
    where: {
      id: permissions.accountId,
    },
  });

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  let body: DistanceInput | undefined = undefined;
  try {
    body = await DISTANCE_INPUT_SCHEMA.validate(req.body);
  } catch (error: any) {
    res.status(400).json({ error: error.message });
    return;
  }

  if (!body) {
    res.status(400).json({ error: "Invalid body" });
    return;
  }

  const originAddresses = await db.$transaction((database) => {
    return Promise.all(
      body!.origins.map((origin) => {
        return findOrInsertAddress(origin, database);
      }),
    );
  });

  const locations: Array<number> = [...(body?.locations || [])];

  for (const origin of originAddresses) {
    console.log("Origin", origin);
    locations.push(origin.id);
  }

  if (account.billingAddressId) {
    locations.push(account.billingAddressId);
  }

  const distance_lookup: Record<string, DistanceLookupCache> = {};

  const addresses = await db.address.findMany({
    where: {
      id: {
        in: locations,
      },
    },
  });

  // find all the distance information in the travel_cache table
  const travel_cache = await db.travelCache.findMany({
    where: {
      originId: {
        in: locations,
      },
      destinationId: {
        in: locations,
      },
    },
  });

  // create a lookup table for the travel_cache
  for (const travel of travel_cache) {
    const distanceValue = {
      distanceMiles: travel.distanceMiles,
      travelTimeMinutes: travel.travelTimeMinutes,
    };
    distance_lookup[`${travel.originId}-${travel.destinationId}`] =
      distanceValue;
    distance_lookup[`${travel.destinationId}-${travel.originId}`] =
      distanceValue;
  }

  let businessAddress: DeliveryDistanceLocations | null = null;

  // create the missing travel_cache entries
  for (const origin of addresses) {
    if (origin.id === account.billingAddressId) {
      businessAddress = {
        id: origin.id,
        latitude: origin.latitude || 0,
        longitude: origin.longitude || 0,
        distance_from_depot: [],
        distance_to_other_locations: {},
      };
    }
    for (const destination of addresses) {
      if (origin.id === destination.id) {
        continue;
      }

      if (
        distance_lookup[`${origin.id}-${destination.id}`] ||
        distance_lookup[`${destination.id}-${origin.id}`]
      ) {
        continue;
      }

      const originLookup: [number, number] | string =
        origin.latitude && origin.longitude
          ? [origin.latitude, origin.longitude]
          : getFullAddress(origin);
      const destinationLookup: [number, number] | string =
        destination.latitude && destination.longitude
          ? [destination.latitude, destination.longitude]
          : getFullAddress(destination);

      const distance = await getMilesBetweenLocations(
        originLookup,
        destinationLookup,
      );

      const distanceMiles = distance?.miles;
      const travelTimeMinutes = distance?.travelTimeMinutes;

      if (!distanceMiles || !travelTimeMinutes) {
        continue;
      }

      if (!origin.latitude || !origin.longitude) {
        await db.address.update({
          where: {
            id: origin.id,
          },
          data: {
            latitude: distance?.originLatLang[0],
            longitude: distance?.originLatLang[1],
          },
        });
      }

      if (!destination.latitude || !destination.longitude) {
        await db.address.update({
          where: {
            id: destination.id,
          },
          data: {
            latitude: distance?.destinationLatLang[0],
            longitude: distance?.destinationLatLang[1],
          },
        });
      }

      try {
        await db.travelCache.createMany({
          data: [
            {
              originId: origin.id,
              destinationId: destination.id,
              distanceMiles,
              travelTimeMinutes,
            },
            {
              originId: destination.id,
              destinationId: origin.id,
              distanceMiles,
              travelTimeMinutes,
            },
          ],
        });
      } catch (error) {
        console.error("Error creating travel cache:", error);
      }

      distance_lookup[`${origin.id}-${destination.id}`] = {
        distanceMiles,
        travelTimeMinutes,
      };
      distance_lookup[`${destination.id}-${origin.id}`] = {
        distanceMiles,
        travelTimeMinutes,
      };
    }
  }

  const response: DistanceResponse = {
    origins: originAddresses.map((origin) => {
      const addy = addresses?.find((item) => item.line1 === origin.line1);
      return {
        id: origin.id,
        line1: origin.line1 ?? "",
        latitude: addy?.latitude || 0,
        longitude: addy?.longitude || 0,
      };
    }),
    locations: addresses
      .filter((address) => {
        return body?.locations?.includes(address.id);
      })
      .map((address: Address) => {
        const distance_from_depot = addresses
          .filter((item) => {
            return (
              item.id === account?.billingAddressId ||
              !body?.locations?.includes(item.id)
            );
          })
          .map((item) => {
            return {
              line1: item.line1 ?? "",
              distance:
                getDistanceBetween(distance_lookup, item.id, address.id)
                  ?.distanceMiles || 0,
            };
          });
        const distance_to_other_locations = addresses
          .filter((otherAddress) => {
            return (
              body?.locations?.includes(otherAddress.id) &&
              otherAddress.id !== address.id
            );
          })
          .reduce(
            (acc, otherAddress) => {
              acc[otherAddress.id] =
                getDistanceBetween(distance_lookup, address.id, otherAddress.id)
                  ?.travelTimeMinutes || 0;
              return acc;
            },
            {} as Record<number, number>,
          );
        return {
          id: address.id,
          latitude: address.latitude || 0,
          longitude: address.longitude || 0,
          distance_from_depot,
          distance_to_other_locations,
        };
      }),
  };

  res.status(200).json(response);
};

const getDistanceBetween = (
  distance_lookup: Record<string, DistanceLookupCache>,
  origin: number,
  destination: number,
): DistanceLookupCache | null => {
  return (
    distance_lookup[`${origin}-${destination}`] ||
    distance_lookup[`${destination}-${origin}`] ||
    null
  );
};

export default POST;
