import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { object } from "yup";

export const AUTO_ROUTE_SETTINGS_SCHEMA = object().shape({});

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  // todo add logic to get the settings.

  res.status(200).json({
    settings: {},
  });
};

export default withMethods(
  ["POST"],
  withValidation(
    AUTO_ROUTE_SETTINGS_SCHEMA,
    withPermissions(
      {
        policy: "api.driver",
        action: "write",
      },
      handler,
    ),
  ),
);
