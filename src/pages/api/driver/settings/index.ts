import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  // todo add logic to get the settings.

  res.status(200).json({
    settings: {},
  });
};

export default withMethods(
  ["GET"],
  withPermissions(
    {
      policy: "api.driver",
      action: "read",
    },
    handler,
  ),
);
