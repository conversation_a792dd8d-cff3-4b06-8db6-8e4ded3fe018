import { array, boolean, number, object, string } from "yup";
import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";

const TASK_SCHEMA = object().shape({
  id: string().required(),
  index: number().required(),
  displayAction: string().required(),
  timelineId: string().required(),
  durationMinutes: number().required(),
  durationAffectsTime: boolean().required(),
  actionTimeFlexibilityMinutes: number().required(),
  actionTime: string().required(),
  orderId: number().nullable(),
  notes: string().nullable(),
  address: object().shape({
    id: number().required(),
  }),
});

export const TIMELINE_UPDATE_SCHEMA = object().shape({
  timelines: array()
    .of(
      object()
        .shape({
          id: string().required(),
          startTime: string(),
          staff: array().of(string()),
          tasks: array().of(TASK_SCHEMA).required(),
        })
        .required(),
    )
    .required(),
});

type TimelineUpdateBody = typeof TIMELINE_UPDATE_SCHEMA.__outputType;

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.driver",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  let validatedBody: TimelineUpdateBody;
  try {
    validatedBody = await TIMELINE_UPDATE_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (error: any) {
    return res.status(400).json({
      error: error.message,
    });
  }

  if (!validatedBody) {
    return res.status(400).json({
      error: "Body is required",
    });
  }

  const { timelines } = validatedBody;

  const timelinesInDb = await db.logisticTimeline.findMany({
    where: {
      id: {
        in: timelines.map((timeline) => timeline.id),
      },
    },
    select: {
      id: true,
      tasks: true,
    },
  });

  const toUpdate = timelines.map(async (timeline) => {
    const timelineInDb = timelinesInDb.find((t) => t.id === timeline.id);

    if (!timelineInDb) {
      return;
    }

    const taskMapping = timeline.tasks.map((task) => ({
      ...task,
      address: task.address.id,
    }));

    return db.logisticTimeline.update({
      where: {
        id: timeline.id,
      },
      data: {
        startTime: timeline?.startTime,
        tasks: taskMapping,
      },
    });
  });

  await Promise.all(toUpdate);

  const staffMapping = timelines.flatMap(
    (timeline) =>
      timeline?.staff?.flatMap((staffId) => {
        if (!staffId) {
          return [];
        }
        return {
          staffId,
          timelineId: timeline.id,
        };
      }) || [],
  );

  if (staffMapping && staffMapping.length > 0) {
    await db.logicistcTimelineStaff.deleteMany({
      where: {
        timelineId: {
          in: timelines.map((timeline) => timeline.id),
        },
      },
    });

    await db.logicistcTimelineStaff.createMany({
      data: staffMapping,
    });
  }

  res.status(200).json({
    success: true,
  });
};

export default POST;
