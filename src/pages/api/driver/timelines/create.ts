import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { array, object, string } from "yup";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "../../apiMiddleware";
import { getCachedAccountFromId } from "~/server/account/lookup";

export const CREATE_TIMELINE_SCHEMA = object().shape({
  startTime: string().required(),
  staff: array().of(string().required()).required(),
});

type CreateTimelineRequestBody = {
  startTime: string; // ISO date string
  staff: string[]; // Array of staff IDs
};

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const account = await getCachedAccountFromId(req.accountId);
  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const { startTime, staff } = req.body as CreateTimelineRequestBody;

  const newTimeline = await db.logisticTimeline.create({
    data: {
      organizationId: req.organizationId,
      accountId: req.accountId,
      startTime: new Date(startTime),
      originId: account.billingAddressId ?? undefined,
    },
    select: {
      LogicistcTimelineStaff: true,
      id: true,
      startTime: true,
      origin: true,
    },
  });

  if (staff.length > 0) {
    await db.logicistcTimelineStaff.createMany({
      data: staff.map((staffId) => ({
        timelineId: newTimeline.id,
        staffId,
      })),
    });
  }

  const startDate = new Date(startTime);
  startDate.setSeconds(0);
  startDate.setUTCSeconds(0);

  res.status(200).json({
    newTimeline: newTimeline,
  });
};

export default withPermissions(
  {
    policy: "api.driver",
    action: "write",
  },
  withValidation(CREATE_TIMELINE_SCHEMA, withMethods(["POST"], POST)),
);
