import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { AddressType } from "~/server/lib/location/types";
import { toAddressType } from "~/server/lib/location/util";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

export type LogisticTimeline = {
  id: string;
  index: number;
  startTime: Date;
  origin?: AddressType;
  staff: string[];
};

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startTime } = req.query;
  const date = startTime as string;
  const startDate = new Date(date);
  startDate.setSeconds(0);
  startDate.setUTCMilliseconds(0);
  const endOfStartDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
  const timelines = await getTimelines(
    req.organizationId,
    startDate,
    endOfStartDate,
  );

  res.status(200).json({
    timelines,
  });
};

export const getTimelines = async (
  organizationId: string,
  startDate: Date,
  endDate: Date,
): Promise<LogisticTimeline[]> => {
  const timelines = await db.logisticTimeline.findMany({
    where: {
      organizationId: organizationId,
      startTime: {
        gte: startDate,
        lt: endDate,
      },
    },
    select: {
      LogicistcTimelineStaff: true,
      id: true,
      startTime: true,
      origin: true,
    },
    orderBy: {
      startTime: "asc",
    },
  });

  return timelines.map((timeline, index) => ({
    ...timeline,
    staff: timeline?.LogicistcTimelineStaff?.map((s) => s.staffId) || [],
    origin: timeline.origin ? toAddressType(timeline.origin) : undefined,
    index,
  }));
};

export default withPermissions(
  {
    policy: "api.driver",
    action: "read",
  },
  GET,
);
