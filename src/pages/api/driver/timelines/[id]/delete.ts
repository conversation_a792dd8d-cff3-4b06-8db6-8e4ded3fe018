import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { getTimelines } from "~/pages/api/driver/timelines";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.driver",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const { id } = req.query;

  await db.logisticTimeline.delete({
    where: {
      id: id as string,
    },
  });

  const { startTime } = req.body;

  const startDate = new Date(startTime);
  startDate.setUTCSeconds(0);
  startDate.setUTCMilliseconds(0);
  const endOfStartDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);

  const timelines = await getTimelines(
    permission.organizationId,
    startDate,
    endOfStartDate,
  );

  res.status(200).json({
    timelines,
  });
};

export default handler;
