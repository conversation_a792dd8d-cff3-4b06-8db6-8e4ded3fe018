import { NextApiResponse } from "next";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { AIRouteTask, HermesRouteTask } from "~/components/driver/types";
import { handleRouting } from "~/lib/ai";

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const body = req.body;

  if (!body?.routes) {
    return res.status(400).json({ error: "No routes provided" });
  }

  if (!body?.delivery_locations) {
    return res.status(400).json({ error: "No delivery locations provided" });
  }

  if (body?.settings?.aiRoute === true) {
    try {
      const startOfDayNum = body.start_of_day;
      const startOfDay = new Date(body.date);
      const tasks = (body.delivery_locations as HermesRouteTask[]).map(
        (item) => {
          // These should be timezone aware dates
          const startTimeWindowRelativeToStart = Math.ceil(
            (new Date(item.fvst).getTime() - startOfDay.getTime()) /
              (60 * 1000),
          );
          const endTimeWindowRelativeToStart = Math.ceil(
            (new Date(item.lvst).getTime() - startOfDay.getTime()) /
              (60 * 1000),
          );

          return {
            id: item.id,
            taskId: item.taskId,
            setup_minutes: item.setup_minutes,
            distance_from_depot: item.distance_from_depot,
            last_valid_arrival_time: endTimeWindowRelativeToStart.toString(),
            first_valid_arrival_time: Math.max(
              0,
              startTimeWindowRelativeToStart,
            ).toString(),
            distances_to_other_locations: item.distances_to_other_locations,
          };
        },
      );

      const aiResponse = await handleRouting(
        tasks.filter((task) => task !== null) as AIRouteTask[],
        startOfDay,
      );
      if (!aiResponse?.routes) {
        return res.status(500).json({
          message: "AI response is invalid or missing routes",
        });
      }

      console.log(
        "Ai Response",
        aiResponse,
        "Translated",
        JSON.stringify({
          timelines: aiResponse.routes.map((route: any) => ({
            id: route.id,
            tasks: route.locations.map((location: any) => ({
              id: location,
            })),
            startTime: route.startTime,
          })),
        }),
      );
      return res.status(200).json({
        timelines: aiResponse.routes.map((route: any) => ({
          id: route.id,
          tasks: route.locations.map((location: any) => ({
            id: location,
          })),
          startTime: route.startTime,
        })),
      });
    } catch (error) {
      console.error("AI Route Error", error);
      return res.status(500).json({
        message: "Failed to process AI route",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  // This is mad helpful: https://groups.google.com/g/or-tools-discuss/c/tKKX71ChzDg
  const request = await fetch(
    "https://hermes-lmzcm.ondigitalocean.app/route/",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer 12321321356213n`,
      },
      body: JSON.stringify({
        ...body,
        time_at_depot_minutes: body?.time_at_depot_minutes ?? 0,
        allowed_waiting_time_minutes: body?.allowed_waiting_time_minutes ?? 30,
        maximum_time_per_vehicle: body?.maximum_time_per_vehicle ?? 100000,
      }),
    },
  );

  if (!request.ok) {
    const reason = await request.text();
    console.error("Failed to fetch", reason);
    return res.status(500).json({ error: "Failed to fetch", details: reason });
  }

  const response = await request.json();
  console.log("Auto Route Response", JSON.stringify(response, null, 2));
  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.driver",
    action: "execute",
  },
  POST,
);
