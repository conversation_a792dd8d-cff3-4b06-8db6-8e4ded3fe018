import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { revalidateTag } from "next/cache";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.staff.others",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const id = req.query.id as string;
  if (!id) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  try {
    const staff = await db.staff.delete({
      where: {
        id: id,
        accountId: permission.accountId,
      },
    });

    try {
      revalidateTag(`staff-${permission.accountId}-${staff.id}`);
    } catch (error) {}
  } catch (error) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  res.status(200).json({ success: true });
};

export default handler;
