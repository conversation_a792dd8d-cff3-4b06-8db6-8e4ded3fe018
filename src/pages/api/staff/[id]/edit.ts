import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { stringPermissionCheck } from "~/pages/api/permissionsUtil";
import { STAFF_FORM_SCHEMA, StaffValues } from "~/form/form";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { revalidateTag } from "next/cache";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.staff",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const id = req.query.id as string;
  if (!id) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const isAuthorized =
    id === permission.staffId ||
    (id !== permission.staffId &&
      stringPermissionCheck(
        permission.totalPermissions,
        "com.partyrentalplatform.api.staff.others",
        "write",
      ));

  if (!isAuthorized) {
    res.status(403).json({ error: "Forbidden" });
    return;
  }

  let staffValues: StaffValues | undefined = undefined;
  try {
    staffValues = await STAFF_FORM_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (e) {
    res.status(400).json({ error: "Invalid request body" });
    return;
  }

  if (!staffValues) {
    res.status(400).json({ error: "Invalid request body" });
    return;
  }

  const staff = await db.staff.findFirst({
    where: {
      account: {
        id: permission.accountId,
      },
      id: id,
    },
    select: {
      id: true,
      name: true,
      email: true,
    },
  });

  if (!staff) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  if (staffValues.email !== staff.email) {
    const existingUser = await db.staff.findFirst({
      where: {
        account: {
          id: permission.accountId,
        },
        email: staffValues.email,
      },
    });

    if (existingUser) {
      res.status(400).json({ error: "Email already in use" });
      return;
    }
  }

  const currentRoles = await db.staffRole.findMany({
    where: {
      staffId: staff.id,
    },
    select: {
      role: {
        select: {
          name: true,
        },
      },
    },
  });

  if (currentRoles.some((r) => r.role.name !== staffValues?.role)) {
    const role = await db.role.findFirst({
      where: {
        name: staffValues.role,
      },
    });

    if (!role) {
      res.status(400).json({ error: "Invalid role" });
      return;
    }

    await db.staffRole.deleteMany({
      where: {
        staffId: staff.id,
      },
    });

    await db.staffRole.create({
      data: {
        role: {
          connect: {
            id: role.id,
          },
        },
        staff: {
          connect: {
            id: staff.id,
          },
        },
      },
    });
  }

  await db.staff.update({
    where: {
      accountId: permission.accountId,
      id: staff.id,
    },
    data: {
      phoneNumber: staffValues.phoneNumber
        ? formatPhoneNumber(staffValues.phoneNumber)
        : null,
      name: staffValues.name,
      email: staffValues.email,
    },
  });

  try {
    revalidateTag(`staff-${permission.accountId}-${staff.id}`);
  } catch (error) {}

  res.status(200).json({ success: true });
};

export default POST;
