import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";
import { generatePassword } from "~/server/lib/password";
import { RESET_PASSWORD_EMAIL } from "../../../../../emails/ResetPasswordEmail";
import { sendReactEmail } from "~/server/email/sendMail";
import { Staff } from "@prisma/client";
import { NEW_USER_EMAIL } from "../../../../../emails/NewUserEmail";
import { stringPermissionCheck } from "~/pages/api/permissionsUtil";
import { getAccountLogo } from "~/server/lib/logo";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.staff",
      action: "execute",
    },
  });

  if (!permission) {
    return;
  }

  const id = req.query.id as string;
  if (!id) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const isAuthorized =
    id === permission.staffId ||
    (id !== permission.staffId &&
      stringPermissionCheck(
        permission.totalPermissions,
        "com.partyrentalplatform.api.staff.others",
        "execute",
      ));

  if (!isAuthorized) {
    res.status(403).json({ error: "Forbidden" });
    return;
  }

  const staff = await db.staff.findFirst({
    where: {
      account: {
        id: permission.accountId,
      },
      id: id,
    },
  });

  if (!staff) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const { password, hash } = await generatePassword();

  await sendResetPasswordEmail({
    staff,
    newAccount: false,
    tempPassword: password,
    tempPasswordHash: hash,
  });

  res.status(200).json({ success: true, tempPassword: password });
};

export const sendResetPasswordEmail = async ({
  staff,
  newAccount,
  permissive,
  tempPassword,
  tempPasswordHash,
}: {
  staff: Staff;
  newAccount: boolean;
  permissive?: boolean;
  tempPassword: string;
  tempPasswordHash: string;
}) => {
  await db.staffPasswordReset.create({
    data: {
      staffId: staff.id,
      token: tempPasswordHash,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24),
    },
  });

  const account = await db.account.findUnique({
    where: {
      id: staff.accountId,
    },
  });

  if (!account) {
    return;
  }

  if (!permissive) {
    await db.staff.update({
      where: {
        id: staff.id,
      },
      data: {
        password: tempPasswordHash,
      },
    });
    if (staff.userId) {
      await db.user.update({
        where: {
          id: staff.userId,
        },
        data: {
          password: tempPasswordHash,
        },
      });
    }
  }

  const props = {
    resetToken: tempPassword,
    accountName: account.name,
    accountLogo: await getAccountLogo(account),
    userName: staff?.name || "",
  };

  if (newAccount) {
    await sendReactEmail(false, account, staff.email, NEW_USER_EMAIL, props);
    return;
  }

  await sendReactEmail(
    false,
    account,
    staff.email,
    RESET_PASSWORD_EMAIL,
    props,
  );
};

export default handler;
