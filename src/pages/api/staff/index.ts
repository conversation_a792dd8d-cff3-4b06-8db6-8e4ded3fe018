import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { checkPermission } from "~/pages/api/permissions";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.staff",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const staff = await db.staff.findMany({
    where: {
      account: {
        id: permission.accountId,
      },
    },
    select: {
      id: true,
      name: true,
      email: true,
      phoneNumber: true,
      StaffRole: {
        select: {
          role: {
            select: {
              name: true,
            },
          },
        },
      },
    },
  });

  const staffOutput = staff.map((s) => ({
    id: s.id,
    name: s.name,
    email: s.email,
    phoneNumber: s.phoneNumber,
    role: s.StaffRole.map((role) => role.role.name).at(0) || "No Role",
  }));

  const roles = await db.role.findMany({
    select: {
      id: true,
      name: true,
      description: true,
    },
  });

  res.status(200).json({ staff: staffOutput, roles: roles.map((r) => r.name) });
};

export default handler;
