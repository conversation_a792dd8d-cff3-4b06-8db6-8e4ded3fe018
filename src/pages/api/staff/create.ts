import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { generatePassword } from "~/server/lib/password";
import { sendResetPasswordEmail } from "~/pages/api/staff/[id]/resetPassword";
import { findBestEntitlement } from "~/server/lib/entitlement/entitlement";
import { StaffByPlan } from "~/pages/staff";
import { STAFF_FORM_SCHEMA, StaffValues } from "~/form/form";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { formatPhoneNumber } from "~/server/lib/twilio";

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const entitlement = await findBestEntitlement(req.accountId);
  // CAI override.
  const maxStaff = req.accountId === 1 ? 1000 : StaffByPlan[entitlement.level];
  const currentStaff = await db.staff.count({
    where: {
      account: {
        id: req.accountId,
      },
    },
  });
  if (currentStaff >= maxStaff) {
    res.status(400).json({
      error: `You have reached the maximum number of staff for your plan (${maxStaff})`,
    });
    return;
  }

  const staff: StaffValues = req.body;

  let user = await db.user.findFirst({
    where: {
      email: staff.email,
    },
  });

  if (!user) {
    const { hash } = await generatePassword();

    user = await db.user.create({
      data: {
        email: staff.email,
        password: hash,
      },
    });
  }

  const existingStaff = await db.staff.findFirst({
    where: {
      account: {
        id: req.accountId,
      },
      email: staff.email,
    },
  });

  if (existingStaff) {
    res
      .status(400)
      .json({ error: "Staff member already exists with that email." });
    return;
  }

  const role = await db.role.findFirst({
    where: {
      name: staff.role,
    },
  });

  if (!role) {
    res.status(400).json({ error: "Invalid role" });
    return;
  }

  const newStaff = await db.staff.create({
    data: {
      name: staff.name,
      email: staff.email,
      password: "",
      phoneNumber: staff.phoneNumber
        ? formatPhoneNumber(staff.phoneNumber)
        : null,
      accountId: req.accountId,
      userId: user.id,
    },
  });

  await db.staffRole.create({
    data: {
      staff: {
        connect: {
          id: newStaff.id,
        },
      },
      role: {
        connect: {
          id: role.id,
        },
      },
    },
  });

  await sendResetPasswordEmail({ staff: newStaff, newAccount: true });

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.staff.others",
    action: "write",
  },
  withMethods(["POST"], withValidation(STAFF_FORM_SCHEMA, POST)),
);
