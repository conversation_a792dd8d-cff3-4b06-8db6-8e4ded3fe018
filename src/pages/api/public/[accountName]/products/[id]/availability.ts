import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { getQuantityOfProductsAvailable } from "~/server/lib/orderUtil";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName, id, month, year } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await getCachedAccountFromNameOrDomain(accountName as string);

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (!id) {
    res.status(400).json({ error: "Product Unknown" });
    return;
  }
  const product = await db.product.findUnique({
    where: {
      id: Number(id),
      organizationId: account.organizationId,
    },
  });
  if (!product) {
    res.status(404).json({ error: "Product not found" });
    return;
  }
  const monthNum = month ? Number(month) : undefined;
  const yearNum = year ? Number(year) : undefined;
  let startDate = new Date();
  if (
    monthNum &&
    yearNum &&
    (startDate.getUTCMonth() !== monthNum ||
      startDate.getUTCFullYear() !== yearNum)
  ) {
    startDate = new Date(yearNum, monthNum, 1);
  }
  const endDate = new Date(startDate.getTime() + 31 * 24 * 60 * 60 * 1000);
  const availability = await getQuantityOfProductsAvailable(
    account.organizationId,
    product.id,
    product.quantity,
    startDate,
    endDate,
    30 * 60,
    product.beforeRentalBufferMinutes,
    product.afterRentalBufferMinutes,
  );

  res.json({ availability: availability });
};

export default handler;
