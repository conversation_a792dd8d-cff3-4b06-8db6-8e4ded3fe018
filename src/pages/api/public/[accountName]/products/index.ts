import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { productImageValidator } from "~/server/product/types";
import { fullImageUrl } from "~/server/globalTypes";
import { trace } from "@opentelemetry/api";
import slugify from "slugify";
import { unstable_cache } from "next/cache";

export type PublicProduct = {
  id: number;
  display: boolean;
  name: string;
  description: string;
  price: number;
  quantity: number;
  categories: number[];
  altImages: string[];
  heroImage: string;
  slug: string;
  rules: number[];
  metaTitle?: string;
  metaDescription?: string;
  ProductImageUpload: {
    priority: number;
    imageUpload: {
      url: string;
    };
  }[];
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const tracer = trace.getTracer("public_products");
  const { accountName } = req.query;

  const accountLookupSpan = tracer.startSpan("account_lookup");
  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await db.account.findFirst({
    where: {
      OR: [
        {
          name: accountName as string,
        },
        {
          customDomain: accountName as string,
        },
      ],
    },
  });

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }
  accountLookupSpan.end();

  const publicProducts = await getPublicProducts(account.organizationId);

  return res.status(200).json({ products: publicProducts });
};

export const getBestSellers = async (orgId: string) => {
  return unstable_cache(
    async () => {
      return db.product.findMany({
        where: {
          organizationId: orgId,
          archived: false,
          display: true,
        },
        select: {
          id: true,
        },
        take: 10,
        orderBy: {
          OrderProduct: {
            _count: "desc",
          },
        },
      });
    },
    [`bestSellers-${orgId}`],
    {
      revalidate: 12 * 60 * 60,
      tags: [`bestSellers-${orgId}`],
    },
  )();
};

export const getPublicProducts = async (
  organizationId: string,
): Promise<PublicProduct[]> => {
  return unstable_cache(
    async () => {
      const products = await db.product.findMany({
        where: {
          organizationId: organizationId,
          archived: false,
        },
        select: {
          id: true,
          display: true,
          name: true,
          description: true,
          price: true,
          quantity: true,
          slug: true,
          ProductCategory: {
            select: {
              categoryId: true,
            },
          },
          ProductRule: {
            select: {
              ruleId: true,
            },
          },
          categoryId: true,
          subCategoryId: true,
          ProductImageUpload: {
            select: productImageValidator,
          },
          metaTitle: true,
          metaDescription: true,
        },
      });

      const publicProducts: PublicProduct[] = [];

      products.forEach((product) => {
        const images = product.ProductImageUpload.sort(
          (a, b) => a.priority - b.priority,
        );

        const heroImage = fullImageUrl(images.at(0)?.imageUpload);

        const otherImages = images
          .slice(1)
          .map((image) => fullImageUrl(image.imageUpload));

        const categoryIds = product.ProductCategory.map(
          (category) => category.categoryId,
        );

        publicProducts.push({
          id: product.id,
          rules: product.ProductRule.map((rule) => rule.ruleId),
          display: product.display,
          name: product.name,
          slug: product.slug ?? slugify(product.name, { lower: true }),
          description: product.description,
          price: product.price,
          quantity: product.quantity,
          categories: categoryIds,
          altImages: otherImages,
          heroImage: heroImage,
          ProductImageUpload: [],
        });
      });
      return publicProducts;
    },
    [`products-${organizationId}`],
    {
      revalidate: 12 * 60 * 60,
      tags: [`products-${organizationId}`],
    },
  )();
};

export default handler;
