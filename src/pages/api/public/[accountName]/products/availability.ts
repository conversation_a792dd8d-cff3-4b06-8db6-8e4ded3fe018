import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { getQuantityOfProductsAvailableBulk } from "~/server/lib/orderUtil";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName, startDateISO, endDateISO } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await db.account.findFirst({
    where: {
      OR: [
        {
          name: accountName as string,
        },
        {
          customDomain: accountName as string,
        },
      ],
    },
  });

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const startDate = new Date(startDateISO as string);
  const endDate = new Date(endDateISO as string);

  if (!startDate || !endDate) {
    res.status(400).json({ error: "Invalid Date" });
    return;
  }

  if (startDate > endDate) {
    res.status(400).json({ error: "Start Date must be before End Date" });
    return;
  }

  const products = await db.product.findMany({
    where: {
      organizationId: account.organizationId,
      display: true,
      archived: false,
    },
    select: {
      id: true,
      quantity: true,
      beforeRentalBufferMinutes: true,
      afterRentalBufferMinutes: true,
    },
  });
  const availabilityMap: Record<number, number> = {};

  const productQuantity = await getQuantityOfProductsAvailableBulk(
    account.organizationId,
    products.map((product) => {
      availabilityMap[product.id] = product.quantity;
      return {
        productId: product.id,
        quantity: product.quantity,
        beforeRentalBufferMinutes: product.beforeRentalBufferMinutes,
        afterRentalBufferMinutes: product.afterRentalBufferMinutes,
      };
    }),
    startDate,
    endDate,
    30 * 60,
  );
  productQuantity.forEach((dateInfo) => {
    dateInfo.productInfo.forEach((productInfo) => {
      availabilityMap[productInfo.productId] = Math.min(
        availabilityMap[productInfo.productId] ?? 0,
        productInfo.quantity,
      );
    });
  });

  res.json({
    availability: products.map((product) => {
      return {
        productId: product.id,
        quantity: availabilityMap[product.id] ?? product.quantity,
      };
    }),
  });
};

export default handler;
