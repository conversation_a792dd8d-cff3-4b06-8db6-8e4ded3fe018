import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { object, string } from "yup";
import { sendReactEmail } from "~/server/email/sendMail";
import { CONTACT_EMAIL } from "../../../../../../emails/ContactEmail";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

export const ContactRequestSchema = object().shape({
  firstName: string().required(),
  lastName: string().required(),
  email: string().required(),
  phone: string().optional(),
  subject: string().required(),
  message: string().required(),
  shoppingSessionId: string(),
  company: string(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // todo turnstile

  const { accountName } = req.query;
  if (!accountName) {
    res.status(400).json({ error: "No account name provided" });
    return;
  }

  const account = await getCachedAccountFromNameOrDomain(accountName as string);

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const businessEmail = account.businessEmail;
  if (!businessEmail) {
    res.status(400).json({ error: "Account does not have a business email" });
    return;
  }

  const contactRequest = req.body;
  contactRequest.email = contactRequest.email.toLowerCase();

  const customer = await db.customer.findFirst({
    where: {
      accountId: account.id,
      email: contactRequest.email,
    },
  });

  if (!customer) {
    const phoneNumber = formatPhoneNumber(contactRequest.phone);
    const newCustomer = await db.customer.create({
      data: {
        accountId: account.id,
        firstName: contactRequest.firstName,
        lastName: contactRequest.lastName,
        email: contactRequest.email,
        phoneNumber: phoneNumber,
        company: contactRequest.company,
      },
    });
    if (contactRequest.shoppingSessionId) {
      const shoppingSession = await db.shoppingSession.findFirst({
        where: {
          accountId: account.id,
          id: contactRequest.shoppingSessionId,
        },
      });
      if (shoppingSession) {
        await db.shoppingSession.update({
          where: {
            id: contactRequest.shoppingSessionId,
          },
          data: {
            customerId: newCustomer.id,
          },
        });
      }
    }
  }

  await sendReactEmail(
    false,
    {
      id: account.id,
      name: account.name,
      businessEmail: contactRequest.email,
    },
    businessEmail,
    CONTACT_EMAIL,
    {
      accountName: account.name,
      emailFrom: contactRequest.email,
      message: contactRequest.message,
      name: `${contactRequest.firstName} ${contactRequest.lastName}${
        contactRequest.company ? ` (${contactRequest.company})` : ""
      }`,
      subject: contactRequest.subject,
      content: contactRequest.message,
    },
  );

  return res.status(200).json({ success: true });
};

export default withMethods(
  ["POST"],
  withValidation(ContactRequestSchema, handler),
);
