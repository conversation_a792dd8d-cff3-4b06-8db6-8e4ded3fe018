import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { Account } from ".prisma/client";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account: Account | null = await db.account.findFirst({
    where: {
      OR: [
        {
          name: accountName,
        },
        {
          customDomain: accountName,
        },
      ],
    },
  });

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const currentTime = new Date();
  const [sale, setupSurface] = await db.$transaction([
    db.sale.findFirst({
      where: {
        account: account,
        OR: [
          {
            startDate: {
              lte: currentTime,
            },
            endDate: {
              gte: currentTime,
            },
          },
          {
            startDate: null,
            endDate: null,
          },
        ],
      },
    }),
    db.setupSurface.findMany({
      where: {
        account: {
          id: account.id,
        },
        archived: false,
      },
    }),
  ]);

  const accountData: {
    sale?: { displayName: string; discount: number; endDate?: string };
    damageWaiver: boolean;
    damageWaiverPercentage: number;
    setupSurface: string[];
  } = {
    damageWaiver: account.damageWaiverRate !== null,
    damageWaiverPercentage: account.damageWaiverRate || 0,
    setupSurface: [],
  };
  if (sale) {
    accountData.sale = {
      displayName: sale.displayName,
      discount: sale.discount,
      endDate: sale?.endDate?.toISOString(),
    };
  }

  accountData.setupSurface = setupSurface.map((surface) => surface.name);

  return res.status(200).json(accountData);
};

export default handler;
