import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { Prisma } from ".prisma/client";
import {
  areProductsAvailable,
  calculateOrderTotal,
  createOrder,
  getSetupSurfaceFee,
  getTravelFee,
  handleOrderDiscounts,
  NamedProductChargeInput,
  OrderFeeInput,
} from "~/server/lib/orderUtil";
import { boolean, object, string } from "yup";
import { AddressValidation } from "~/server/lib/location/types";
import {
  CurrencyValue,
  getMinimumDeposit,
  PaymentInfoLineItemName,
} from "~/server/lib/currency";
import { getTaxRate } from "~/server/lib/tax";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { formatPhoneNumber } from "~/server/lib/twilio";
import { getStripeAccount } from "~/server/lib/stripe";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";
import { captureMessage } from "@sentry/nextjs";
import { handleQuoteCreation } from "~/server/services/orderEvents";
import slugify from "slugify";
import { handleRules, RuleWithJson, SimpleProductWithRules } from "~/lib/rules";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

const shoppingWithCustomerAndCart =
  Prisma.validator<Prisma.ShoppingSessionInclude>()({
    customer: true,
    CartItem: {
      include: {
        product: true,
      },
    },
  });

const BODY_SCHEMA = object().shape({
  shoppingId: string().required(),
  customerData: object().shape({
    firstName: string().required(),
    lastName: string().required(),
    phoneNumber: string().optional(),
    reference: string().optional(),
    customerNotes: string().optional(),
  }),
  setupSurface: string().required(),
  damageWaiverApplied: boolean().required(),
  couponCode: string().optional(),
  location: AddressValidation.required(),
});

type CartFinalizeRequest = typeof BODY_SCHEMA.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await getCachedAccountFromNameOrDomain(accountName as string);

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const stripe = getStripeAccount(account);

  const bodyData: CartFinalizeRequest = req.body;
  const { shoppingId } = bodyData;

  const shoppingSession = await db.shoppingSession.findFirst({
    where: {
      id: shoppingId,
      account: {
        id: account.id,
      },
    },
    include: shoppingWithCustomerAndCart,
  });

  if (!shoppingSession) {
    res.status(400).json({ error: "Invalid Shopping Session" });
    return;
  }

  let couponId: number | undefined;

  if (bodyData.couponCode) {
    const coupon = await db.coupon.findFirst({
      where: {
        accountId: account.id,
        name: bodyData.couponCode,
        deleted: false,
        AND: [
          {
            OR: [{ startDate: null }, { startDate: { lte: new Date() } }],
          },
          {
            OR: [{ endDate: null }, { endDate: { gte: new Date() } }],
          },
        ],
      },
    });

    if (!coupon) {
      res.status(400).json({ error: "Invalid Coupon Code" });
      return;
    }
    couponId = coupon.id;
  }

  const cart = shoppingSession.CartItem;

  if (
    cart.length === 0 ||
    shoppingSession.eventStartTime === null ||
    shoppingSession.eventEndTime === null
  ) {
    res.status(400).json({ error: "Cart is empty" });
    return;
  }

  const productId: number[] = cart.map((item) => item.productId);
  const productRules = await db.productRule.findMany({
    where: {
      productId: { in: productId },
    },
    include: {
      rule: true,
    },
  });

  const customer = shoppingSession.customer;

  if (!customer?.email) {
    res
      .status(400)
      .json({ error: "Invalid Customer, must link before finalizing." });
    return;
  }

  if (customer.banned) {
    res.status(400).json({
      error: "Some products are not available",
      taken: cart.map((item) => item.productId),
    });
    return;
  }

  const startDateTime = new Date(shoppingSession.eventStartTime);
  const endDateTime = new Date(shoppingSession.eventEndTime);
  startDateTime.setUTCSeconds(0);
  endDateTime.setUTCSeconds(0);
  startDateTime.setUTCMilliseconds(0);
  endDateTime.setUTCMilliseconds(0);

  const simpleProducts: SimpleProductWithRules[] = cart.map((item) => {
    return {
      id: item.productId,
      basePrice: item.product.price,
      price: item.product.price,
      rules: productRules
        .filter((rule) => rule.productId === item.productId)
        .map((rule) => rule.ruleId),
    };
  });

  const rulesWithJson: RuleWithJson[] = productRules.map((item) => {
    return {
      ...item.rule,
      ruleJson: JSON.parse(item.rule.ruleJson),
    };
  });

  const afterRulesProducts = await handleRules(rulesWithJson, simpleProducts, {
    startDate: startDateTime,
    endDate: endDateTime,
  });

  const items: NamedProductChargeInput[] = cart.map((item) => {
    const price = afterRulesProducts.find(
      (product) => product.id === item.productId,
    )?.price;
    return {
      productId: item.productId,
      quantity: item.quantity,
      pricePaid: price ?? item.product.price,
      productName: item.product.name,
    };
  });

  const { available, taken } = await areProductsAvailable(
    account.organizationId,
    cart.map((item) => {
      return {
        productId: item.productId,
        quantity: item.quantity,
        beforeRentalBufferMinutes: item.product.beforeRentalBufferMinutes,
        afterRentalBufferMinutes: item.product.afterRentalBufferMinutes,
      };
    }),
    shoppingSession.eventStartTime,
    shoppingSession.eventEndTime,
  );

  if (available.length !== cart.length) {
    res
      .status(400)
      .json({ status: false, error: "Some products are not available", taken });
    console.warn("Some products are not available");
    captureMessage("Some products are not available", {
      extra: {
        available,
        taken,
        shoppingId,
      },
    });
    return;
  }

  const customerData = bodyData.customerData;
  if (!customerData) {
    res.status(400).json({ error: "Invalid Customer Data" });
    return;
  }
  let phoneNumber = customerData.phoneNumber;
  if (phoneNumber) {
    phoneNumber = formatPhoneNumber(phoneNumber);
  }
  await db.customer.update({
    where: {
      account: {
        id: account.id,
      },
      id: customer.id,
    },
    data: {
      firstName: customerData.firstName,
      lastName: customerData.lastName,
      phoneNumber: phoneNumber,
      reference: customerData.reference,
    },
  });

  const addressFromDb = await findOrInsertAddress(bodyData.location);

  const fees: OrderFeeInput[] = [];

  if (cart.some((item) => item.product.deliverable === true)) {
    const travelFee = await getTravelFee(account.id, addressFromDb);

    if (travelFee) {
      fees.push({
        type: "TRAVEL_FEE",
        name: null,
        amount: travelFee,
        taxable: true,
      });
    }
  }

  let setupSurface = await db.setupSurface.findFirst({
    where: {
      account: {
        id: account.id,
      },
      name: bodyData.setupSurface,
    },
  });

  if (!setupSurface) {
    setupSurface = await db.setupSurface.create({
      data: {
        accountId: account.id,
        description: "Customer Input",
        name: bodyData.setupSurface,
      },
    });
  }

  const surfaceFee = await getSetupSurfaceFee(
    account.id,
    setupSurface,
    items.length,
  );

  if (surfaceFee) {
    fees.push({
      type: "SURFACE_FEE",
      name: bodyData.setupSurface,
      amount: surfaceFee,
      taxable: true,
    });
  }

  const discounts = await handleOrderDiscounts(account.id, couponId ?? null);

  const paymentInfo = await calculateOrderTotal(
    account.id,
    items,
    discounts,
    fees,
    {
      enabled: bodyData.damageWaiverApplied,
      percentage: account.damageWaiverRate,
    },
    await getTaxRate(account, addressFromDb),
    0,
  );

  const finalTotal = CurrencyValue.fromPlatform(paymentInfo.finalTotal);
  const depositAmount = CurrencyValue.fromPlatform(
    getMinimumDeposit(finalTotal.amount, account.minimumOrderPaymentPercentage),
  );

  let paymentIntent;
  if (shoppingSession.paymentIntent) {
    paymentIntent = await stripe.paymentIntents.retrieve(
      shoppingSession.paymentIntent,
    );
    if (paymentIntent.status === "succeeded") {
      res.status(400).json({ error: "Payment already processed" });
      return;
    }
    paymentIntent = await stripe.paymentIntents.update(paymentIntent.id, {
      amount: finalTotal.stripeAmount,
      metadata: {
        lastAction: "finalize-checkout",
        ...paymentIntent.metadata,
        depositAmount: depositAmount.amount,
        totalAmount: finalTotal.amount,
      },
    });
  }

  let customerStripeId: string | undefined;
  const stripeCustomer = await stripe.customers.search({
    query: `metadata['customerId']:'${customer.id}' or email:"${customer.email}"`,
    limit: 1,
  });
  if (stripeCustomer.data.length > 0) {
    customerStripeId = stripeCustomer.data[0]?.id;
  } else {
    const newStripeCustomer = await stripe.customers.create({
      email: customer.email,
      metadata: {
        customerId: customer.id,
        shoppingSessionId: shoppingSession.id,
      },
    });
    customerStripeId = newStripeCustomer.id;
  }

  const orderResult = await createOrder(
    account.id,
    account.organizationId,
    addressFromDb.id,
    setupSurface.id,
    items,
    discounts,
    paymentInfo.fees,
    {
      enabled: bodyData.damageWaiverApplied,
      percentage: paymentInfo.damageWaiver?.percentage || 0,
    },
    paymentInfo.tax?.percentage || 0,
    startDateTime,
    endDateTime,
    customer.id,
    bodyData.customerData?.customerNotes,
    undefined,
    true,
    "QUOTE",
  );

  if (!orderResult) {
    console.error(
      "Failed to create order with result",
      JSON.stringify(orderResult),
    );
    res.status(400).json({ error: "Failed to create order" });
    return;
  }

  const order = orderResult.success?.order;

  if (!order) {
    console.error("Failed to create order", JSON.stringify(orderResult));
    res.status(400).json({ error: "Failed to create order" });
    return;
  }

  const baseWebsite = account.customDomain
    ? `https://${account.customDomain}`
    : `https://${slugify(account.name.toLowerCase())}.evntflow.com`;

  await handleQuoteCreation(order.id, account.id, {
    ...orderResult!.success!.emailProps!,
    shoppingId: `${baseWebsite}/cart`,
  });

  paymentIntent = await stripe.paymentIntents.create({
    amount: finalTotal.stripeAmount,
    metadata: {
      shoppingId: shoppingId,
      depositAmount: depositAmount.amount,
      totalAmount: finalTotal.amount,
      orderId: order.id,
    },
    customer: customerStripeId,
    setup_future_usage: "off_session",
    currency: "usd",
    automatic_payment_methods: {
      enabled: true,
      allow_redirects: "always",
    },
  });

  await db.shoppingSession.update({
    where: {
      id: shoppingId,
    },
    data: {
      paymentIntent: paymentIntent.id,
    },
  });

  return res.status(200).json({
    status: true,
    paymentIntent: paymentIntent.client_secret,
    totalAmount: finalTotal.amount,
    depositAmount: depositAmount.amount,
    lineItems: orderResult.success?.displayItems.filter(
      (item) => item.name !== PaymentInfoLineItemName.Product,
    ),
  });
};

export default withMethods(["POST"], withValidation(BODY_SCHEMA, handler));
