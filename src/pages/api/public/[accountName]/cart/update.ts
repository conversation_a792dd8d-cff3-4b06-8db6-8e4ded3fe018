import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { array, date, number, object, string } from "yup";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";
import { captureMessage } from "@sentry/nextjs";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

type BodyType = {
  shoppingId: string;
  cart: CartItem[];
  startDateTime?: Date;
  endDateTime?: Date;
};

type CartItem = {
  productId: number;
  amount: number;
};

const BODY_SCHEMA = object().shape({
  shoppingId: string().required(),
  cart: array()
    .of(
      object().shape({
        productId: number().required(),
        amount: number().required(),
      }),
    )
    .required(),
  startDateTime: date(),
  endDateTime: date(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await getCachedAccountFromNameOrDomain(accountName);

  if (!account) {
    captureMessage("Account Unknown", {
      extra: {
        accountName,
      },
    });
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const body: BodyType = req.body;
  const { shoppingId, cart, startDateTime, endDateTime } = body;
  const promise = [];
  const shoppingSession = await db.shoppingSession.findFirst({
    where: {
      id: shoppingId,
      account: {
        id: account.id,
      },
    },
  });
  if (!shoppingSession) {
    captureMessage("Invalid Shopping Session", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Shopping Session" });
    return;
  }

  const dataToUpdate: { eventStartTime?: Date; eventEndTime?: Date } = {};

  if (startDateTime) {
    dataToUpdate.eventStartTime = startDateTime;
  }

  if (endDateTime) {
    dataToUpdate.eventEndTime = endDateTime;
  }

  if (cart && cart.length > 0) {
    // create cart items that are in the cart array but not in the db

    // validate the cart items
    const cartItemIds = cart.map((cartItem: CartItem) => cartItem.productId);
    const products = await db.product.findMany({
      where: {
        organizationId: account.organizationId,
        id: {
          in: cartItemIds,
        },
        archived: false,
      },
    });

    if (products.length !== cartItemIds.length) {
      captureMessage("Invalid Cart Items", {
        extra: {
          accountName,
          cartItemIds,
          products,
        },
      });
      res.status(400).json({ error: "Invalid Cart Items" });
      return;
    }

    const cartItemsToAdd = cart.map((cartItem: CartItem) => {
      return {
        productId: cartItem.productId,
        quantity: cartItem.amount,
        shoppingSessionId: shoppingId,
      };
    });
    // delete cart items that are not in the cart
    const deleteRequest = db.cartItem.deleteMany({
      where: {
        shoppingSessionId: shoppingId,
      },
    });
    const addRequest = db.cartItem.createMany({
      data: cartItemsToAdd,
    });
    promise.push(deleteRequest);
    promise.push(addRequest);
  } else {
    // delete all cart items
    const deleteRequest = db.cartItem.deleteMany({
      where: {
        shoppingSessionId: shoppingId,
      },
    });
    promise.push(deleteRequest);
  }

  if (dataToUpdate.eventStartTime || dataToUpdate.eventEndTime) {
    const updatedShoppingSession = db.shoppingSession.update({
      where: {
        id: shoppingId,
        account: {
          id: account.id,
        },
      },
      data: dataToUpdate,
    });
    promise.push(updatedShoppingSession);
  }

  await db.$transaction(promise);

  return res.status(200).json({ success: true });
};

export default withMethods(["POST"], withValidation(BODY_SCHEMA, handler));
