import { NextApiRequest, NextApiResponse } from "next";
import { createId } from "@paralleldrive/cuid2";
import { db } from "~/server/db";
import { withMethods } from "~/pages/api/apiMiddleware";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

//todo add some sort of validation to prevent bots
const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await getCachedAccountFromNameOrDomain(accountName);

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const shoppingId = createId();
  await db.shoppingSession.create({
    data: {
      id: shoppingId,
      account: {
        connect: {
          id: account.id,
        },
      },
    },
  });

  return res.status(200).json({ shoppingId });
};

export default withMethods(["GET"], handler);
