import { NextApiRequest, NextApiResponse } from "next";
import { object, string } from "yup";
import { db } from "~/server/db";
import { findStripeCustomer, getStripeAccount } from "~/server/lib/stripe";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

const BODY_SCHEMA = object().shape({
  shoppingCartId: string().required(),
  email: string().email().required(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    return res.status(400).json({ error: "Account name is required" });
  }

  if (typeof accountName !== "string") {
    return res.status(404).json({ error: "Account not found" });
  }

  const account = await getCachedAccountFromNameOrDomain(accountName);

  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const { shoppingCartId, email }: typeof BODY_SCHEMA.__outputType = req.body;

  const customerEmail = email.toLowerCase();

  const shoppingCart = await db.shoppingSession.findFirst({
    where: {
      account,
      id: shoppingCartId,
    },
    select: {
      paymentIntent: true,
      id: true,
      customer: {
        select: {
          id: true,
          email: true,
        },
      },
    },
  });

  if (!shoppingCart) {
    return res.status(404).json({ error: "Shopping cart not found" });
  }

  if (shoppingCart.customer?.email?.toLowerCase() === customerEmail) {
    return res.status(200).json({ success: true });
  }

  const stripe = getStripeAccount(account);

  let customer = await db.customer.findFirst({
    where: {
      account,
      email: customerEmail,
    },
    select: {
      id: true,
    },
  });

  if (!customer) {
    customer = await db.customer.create({
      data: {
        account: {
          connect: {
            id: account.id,
          },
        },
        email: customerEmail,
      },
    });
    await stripe.customers.create({
      email: customerEmail,
      metadata: {
        customerId: customer.id,
        shoppingSessionId: shoppingCart.id,
      },
    });
  } else if (shoppingCart.customer && shoppingCart.paymentIntent) {
    const stripeCustomer = await findStripeCustomer(
      account,
      customerEmail,
      customer.id,
    );
    let customerId: string | undefined;
    if (!stripeCustomer) {
      const stripeCustomer = await stripe.customers.create({
        email: customerEmail,
        metadata: {
          customerId: customer.id,
          shoppingSessionId: shoppingCart.id,
        },
      });
      customerId = stripeCustomer.id;
    } else {
      customerId = stripeCustomer.id;
    }
    if (customerId) {
      await stripe.paymentIntents.update(shoppingCart.paymentIntent, {
        customer: customerId,
      });
    }
  }

  await db.shoppingSession.update({
    where: {
      id: shoppingCart.id,
    },
    data: {
      customer: {
        connect: {
          id: customer.id,
        },
      },
    },
  });

  return res.status(200).json({ success: true });
};

export default withMethods(["POST"], withValidation(BODY_SCHEMA, handler));
