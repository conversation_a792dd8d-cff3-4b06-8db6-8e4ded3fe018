import { NextApiRequest, NextApiResponse } from "next";
import { number, object, string } from "yup";
import { Account } from ".prisma/client";
import { db } from "~/server/db";
import { AddressValidation } from "~/server/lib/location/types";
import { findOrInsertAddress } from "~/server/lib/location/util";
import { CurrencyValue, getCurrencyValue } from "~/server/lib/currency";
import * as Sentry from "@sentry/nextjs";
import { captureMessage } from "@sentry/nextjs";
import { getStripeAccount } from "~/server/lib/stripe";

const BODY_SCHEMA = object().shape({
  shoppingId: string().required(),
  paymentAmount: number().required(),
  tipAmount: number().required(),
  billingAddress: AddressValidation.required(),
});

type BodyType = typeof BODY_SCHEMA.__outputType;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    res.status(400).json({ error: "Invalid Method" });
    return;
  }
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account: Account | null = await db.account.findFirst({
    where: {
      OR: [
        {
          name: accountName as string,
        },
        {
          customDomain: accountName as string,
        },
      ],
    },
  });

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const body = req.body;
  let bodyData: BodyType;
  try {
    bodyData = await BODY_SCHEMA.validate(body);
  } catch (e) {
    console.error(e);
    Sentry.captureException(e);
    res.status(400).json({ error: "Invalid Body" });
    return;
  }
  const { shoppingId } = bodyData;
  const shoppingSession = await db.shoppingSession.findFirst({
    where: {
      id: shoppingId,
      account: {
        id: account.id,
      },
    },
    include: {
      customer: true,
      CartItem: {
        include: {
          product: true,
        },
      },
    },
  });
  if (!shoppingSession) {
    captureMessage("Invalid Shopping Session", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Shopping Session" });
    return;
  }

  if (!shoppingSession.paymentIntent) {
    captureMessage("No Payment found!", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "No Payment found!" });
    return;
  }

  const paymentIntent = await getStripeAccount(account).paymentIntents.retrieve(
    shoppingSession.paymentIntent,
  );
  if (!paymentIntent) {
    captureMessage("No Payment found during stripe checkout!", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "No Payment found!" });
    return;
  }
  if (paymentIntent.status !== "requires_payment_method") {
    captureMessage("Payment already processed!", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Payment already processed!" });
    return;
  }
  const order = paymentIntent.metadata.orderId;
  if (!order) {
    captureMessage("Invalid Order in stripe checkout", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Order" });
    return;
  }
  const orderId = parseInt(order);
  if (isNaN(orderId)) {
    captureMessage("Invalid Order Id in stripe checkout", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Order" });
    return;
  }

  const orderData = await db.order.findFirst({
    where: {
      id: orderId,
      account: {
        id: account.id,
      },
    },
  });

  if (!orderData) {
    captureMessage("Order Not Found During Checkout", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Order" });
    return;
  }

  const amountDue = orderData.finalTotal - orderData.totalPaid;
  const minimumAmount = Math.max(
    getCurrencyValue(amountDue * (account.minimumOrderPaymentPercentage / 100)),
    1,
  );

  if (bodyData.paymentAmount < minimumAmount) {
    res.status(400).json({
      error: "The payment must be more than the deposit amount",
    });
    return;
  }

  if (bodyData.paymentAmount > amountDue) {
    res
      .status(400)
      .json({ error: "The payment must be less than the amount due" });
    return;
  }

  const amount = bodyData.paymentAmount;
  if (!amount) {
    res.status(400).json({ error: "Invalid Amount" });
    return;
  }

  if (!shoppingSession.customer) {
    captureMessage("Invalid Customer", {
      extra: {
        accountName,
        shoppingId,
      },
    });
    res.status(400).json({ error: "Invalid Customer" });
    return;
  }

  const address = await findOrInsertAddress(bodyData.billingAddress);

  await db.customer.update({
    where: {
      id: shoppingSession.customer.id,
    },
    data: {
      addressId: address.id,
    },
  });

  const updatedPaymentIntent = await getStripeAccount(
    account,
  ).paymentIntents.update(paymentIntent.id, {
    amount: CurrencyValue.fromPlatform(amount + (bodyData.tipAmount || 0))
      .stripeAmount,
    metadata: {
      lastAction: "checkout-update",
      ...paymentIntent.metadata,
      tipAmount: bodyData.tipAmount,
    },
  });

  const updatePaymentIntentAmount = CurrencyValue.fromStripe(
    updatedPaymentIntent.amount,
  );

  res.json({
    success: true,
    amount: updatePaymentIntentAmount.amount,
  });
};

export default handler;
