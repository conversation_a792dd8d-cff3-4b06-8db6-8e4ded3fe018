import { object, string } from "yup";
import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";
import { getCachedAccountFromNameOrDomain } from "~/server/account/lookup";

const BODY_SCHEMA = object().shape({
  shoppingCartId: string().required(),
  couponCode: string().required(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    return res.status(400).json({ error: "Account name is required" });
  }

  if (typeof accountName !== "string") {
    return res.status(404).json({ error: "Account not found" });
  }

  const account = await getCachedAccountFromNameOrDomain(accountName as string);

  if (!account) {
    return res.status(404).json({ error: "Account not found" });
  }

  const { couponCode }: typeof BODY_SCHEMA.__outputType = req.body;
  const coupon = await db.coupon.findFirst({
    where: {
      accountId: account.id,
      name: couponCode,
      deleted: false,
      AND: [
        {
          OR: [{ startDate: null }, { startDate: { lte: new Date() } }],
        },
        {
          OR: [{ endDate: null }, { endDate: { gte: new Date() } }],
        },
      ],
    },
  });

  if (!coupon) {
    res.status(400).json({ error: "No Coupon Code Found" });
    return;
  }

  return res.status(200).json({
    success: true,
    discountAmount: coupon.discount,
    discountType: coupon.discountType,
  });
};

export default withMethods(["POST"], withValidation(BODY_SCHEMA, handler));
