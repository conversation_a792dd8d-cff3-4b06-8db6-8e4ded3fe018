import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { fullImageUrl } from "~/server/globalTypes";
import slugify from "slugify";
import { unstable_cache } from "next/cache";

export type PublicCategory = {
  id: number;
  display: boolean;
  name: string;
  slug: string;
  description: string;
  heroImage: string;
  altImages: string[];
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { accountName } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  if (typeof accountName !== "string") {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const account = await db.account.findFirst({
    where: {
      OR: [
        {
          name: accountName as string,
        },
        {
          customDomain: accountName as string,
        },
      ],
    },
  });

  if (!account) {
    res.status(400).json({ error: "Account Unknown" });
    return;
  }

  const publicCategories = await getPublicCategories(account.organizationId);

  res.json({ categories: publicCategories });
};

export const getPublicCategories = async (
  organizationId: string,
): Promise<PublicCategory[]> => {
  return unstable_cache(
    async () => {
      const categories = await db.category.findMany({
        where: {
          organizationId: organizationId,
        },
        select: {
          id: true,
          display: true,
          slug: true,
          name: true,
          description: true,
          CategoryImageUpload: {
            select: {
              imageUpload: {
                select: {
                  name: true,
                  url: true,
                },
              },
            },
          },
        },
      });
      const publicCategories: PublicCategory[] = [];

      categories.forEach((category) => {
        const heroImage = fullImageUrl(
          category.CategoryImageUpload?.at(0)?.imageUpload,
        );
        const altImages =
          category.CategoryImageUpload?.slice(1).map((imageUpload) =>
            fullImageUrl(imageUpload.imageUpload),
          ) || [];
        publicCategories.push({
          id: category.id,
          display: category.display,
          slug: category.slug || slugify(category.name, { lower: true }),
          name: category.name,
          description: category.description,
          heroImage,
          altImages,
        });
      });
      return publicCategories;
    },
    [`categories-${organizationId}`],
    {
      revalidate: 12 * 60 * 60,
      tags: [`categories-${organizationId}`],
    },
  )();
};

export default handler;
