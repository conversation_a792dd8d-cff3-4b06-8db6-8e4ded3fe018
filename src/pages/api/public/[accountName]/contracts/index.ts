import { NextApiRequest, NextApiResponse } from "next";
import { getToken } from "next-auth/jwt";
import { db } from "~/server/db";
import { r2 } from "~/server/lib/r2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import * as Sentry from "@sentry/nextjs";

async function buffer(readable: Readable) {
  const chunks = [];
  for await (const chunk of readable) {
    chunks.push(typeof chunk === "string" ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export const config = {
  api: {
    bodyParser: false,
  },
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    res.status(405).json({ message: "Method not allowed" });
    return;
  }

  const token = await getToken({ req });

  // if we have the token, we can assume it's staff. If not, we can assume it's a customer.

  const { accountName, contractId, orderId } = req.query;

  if (!accountName) {
    res.status(400).json({ error: "No account name provided" });
    return;
  }

  let account;

  try {
    if (token) {
      account = await db.account.findUnique({
        where: {
          id: token.accountId,
        },
      });
    } else {
      account = await db.account.findUnique({
        where: {
          name: accountName as string,
        },
      });
    }
  } catch (e) {
    Sentry.captureException(e);
    console.log(e);
    res.status(500).json({ error: "Failed to lookup account" });
    return;
  }

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  if (!contractId) {
    res.status(400).json({ error: "No contract id provided" });
    return;
  }

  if (!orderId) {
    res.status(400).json({ error: "No order id provided" });
    return;
  }

  const contract = await db.contract.findUnique({
    where: {
      orderId: Number(orderId),
      id: contractId as string,
    },
    include: {
      order: {
        select: {
          accountId: true,
        },
      },
    },
  });

  const buf = await buffer(req);
  const formData = new FormData();
  const fileBlob = new Blob([buf], { type: "image/jpeg" });

  if (fileBlob.size > ********) {
    res.status(400).json({ error: "File too large" });
    return;
  }

  formData.append("image", fileBlob, "image.jpeg");

  const file = formData.get("image") as File;

  if (!file) {
    res.status(400).json({ error: "No blob data provided" });
    return;
  }

  if (!contract?.order) {
    res.status(404).json({ error: "Contract not found" });
    return;
  }

  if (contract?.order?.accountId !== account.id) {
    res.status(401).json({ error: "Unauthorized" });
    return;
  }
  const key = `${account.id}/${contract.orderId}/${contract.id}`;

  const putCommand = await r2.send(
    new PutObjectCommand({
      Bucket: "contracts",
      Key: `${key}`,
      // @ts-expect-error - fileBlob is a Blob, not a Buffer and that's fine
      Body: await fileBlob.arrayBuffer(),
    }),
  );

  if (putCommand.$metadata.httpStatusCode !== 200) {
    res.status(500).json({ error: "Failed to upload image" });
    return;
  }

  let ipAddress = req.headers["x-real-ip"] as string;

  const forwardedFor = req.headers["x-forwarded-for"] as string;
  if (!ipAddress && forwardedFor) {
    ipAddress = forwardedFor?.split(",").at(0) ?? "Unknown";
  }

  await db.contract.update({
    where: {
      id: contract.id,
    },
    data: {
      signed: true,
      signedDate: new Date(),
      signedIpAddress: ipAddress,
      signedStorageUrl: key,
      signedUserAgent: req.headers["user-agent"]!,
      inPerson: !!token,
    },
  });

  return res.json({ message: "Contract signed" });
};

export default handler;
