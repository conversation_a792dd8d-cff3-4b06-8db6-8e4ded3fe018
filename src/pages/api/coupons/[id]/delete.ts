import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicCoupon } from "~/pages/api/coupons/[id]/index";
import { db } from "~/server/db";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicCoupon = await handleBasicCoupon(req, res, "write");

  if (!basicCoupon) {
    return;
  }

  // we don't actually delete the coupon, we just mark it as deleted
  const { couponId, accountId } = basicCoupon;

  const coupon = await db.coupon.findFirst({
    where: {
      id: couponId,
      accountId: accountId,
    },
  });

  if (!coupon) {
    res.status(404).json({ error: "Coupon not found" });
    return;
  }

  await db.coupon.update({
    where: {
      id: couponId,
      accountId: accountId,
    },
    data: {
      deleted: true,
    },
  });

  return res.status(200).json({ success: true, id: couponId });
};

export default handler;
