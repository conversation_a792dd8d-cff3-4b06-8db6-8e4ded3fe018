import { NextApiRequest, NextApiResponse } from "next";
import { handleBasicCoupon } from "~/pages/api/coupons/[id]/index";
import { db } from "~/server/db";
import { COUPON_SCHEMA, CouponValues } from "~/form/CouponForm";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }
  const basicCouponRequest = await handleBasicCoupon(req, res, "write");

  if (!basicCouponRequest) {
    return;
  }

  const { couponId, accountId } = basicCouponRequest;

  const coupon = await db.coupon.findFirst({
    where: {
      id: couponId,
      accountId: accountId,
    },
  });

  if (!coupon) {
    res.status(404).json({ error: "Coupon not found" });
    return;
  }

  let body: CouponValues | undefined = undefined;
  try {
    body = await COUPON_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (e) {
    res.status(400).json({ error: "Invalid JSON" });
    return;
  }

  const updatedCoupon = await db.coupon.update({
    where: {
      id: couponId,
      accountId: accountId,
    },
    data: {
      ...body,
    },
  });

  return res.status(200).json({ coupon: updatedCoupon });
};

export default handler;
