import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission, PermissionAction } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { COUPON_SCHEMA, CouponValues } from "~/form/CouponForm";

type BasicCouponRequest = {
  couponId: number;
  accountId: number;
};

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const basicCouponRequest = await handleBasicCoupon(req, res, "read");

  if (!basicCouponRequest) {
    return;
  }

  const { couponId, accountId } = basicCouponRequest;

  const coupon = await db.coupon.findFirst({
    where: {
      id: couponId,
      accountId: accountId,
    },
  });

  if (!coupon) {
    res.status(404).json({ error: "Coupon not found" });
    return;
  }

  const couponOutput: CouponValues = await COUPON_SCHEMA.validate(coupon, {
    stripUnknown: true,
  });

  return res.status(200).json({ coupon: couponOutput });
};

export const handleBasicCoupon = async (
  req: NextApiRequest,
  res: NextApiResponse,
  action: PermissionAction,
): Promise<BasicCouponRequest | null> => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.coupons",
      action: action,
    },
  });

  if (!permission) {
    return null;
  }

  const { id } = req.query;
  if (!id) {
    res.status(400).json({ error: "No coupon id provided" });
    return null;
  }

  const couponId = parseInt(id as string);
  if (isNaN(couponId)) {
    res.status(400).json({ error: "Invalid coupon id" });
    return null;
  }

  return { couponId, accountId: permission.accountId };
};

export default GET;
