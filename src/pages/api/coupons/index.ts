import { NextApiResponse } from "next";
import { db } from "~/server/db";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { CouponListValues } from "~/query/coupon/query";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const data = await db.coupon.findMany({
    where: {
      accountId: req.accountId,
    },
    include: {
      _count: {
        select: {
          Order: true,
        },
      },
    },
  });

  const coupons: CouponListValues[] = data.map((coupon) => {
    let status = "Active";
    if (coupon?.endDate && coupon?.endDate < new Date()) {
      status = "Expired";
    }
    if (coupon?.startDate && coupon?.startDate > new Date()) {
      status = "Pending";
    }
    if (coupon?.deleted) {
      status = "Deleted";
    }
    let formattedDisplayName: string;

    if (coupon.discountType === "PERCENTAGE") {
      formattedDisplayName = `${coupon.displayName} (${coupon.discount}%)`;
    } else {
      formattedDisplayName = `${coupon.displayName} ($${coupon.discount})`;
    }

    const usageCount = coupon._count.Order;
    return {
      id: coupon.id,
      name: coupon.name,
      displayName: coupon.displayName,
      formattedDisplayName: formattedDisplayName,
      discount: coupon.discount,
      discountType: coupon.discountType,
      usageCount: usageCount,
      status: status,
    };
  });

  return res.status(200).json({ coupons: coupons });
};

export default withPermissions(
  {
    policy: "api.coupons",
    action: "read",
  },
  handler,
);
