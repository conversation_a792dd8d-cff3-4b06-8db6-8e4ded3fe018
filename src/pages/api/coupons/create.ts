import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { COUPON_SCHEMA, CouponValues } from "~/form/CouponForm";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  let coupon: CouponValues | undefined = undefined;
  try {
    coupon = await COUPON_SCHEMA.validate(req.body, {
      stripUnknown: true,
    });
  } catch (e: any) {
    res.status(400).json({ error: e.message });
    return;
  }
  if (!coupon) {
    res.status(400).json({ error: "Coupon data is required" });
    return;
  }

  const newCoupon = await db.coupon.create({
    data: {
      ...coupon,
      description: coupon.description || "",
      accountId: req.accountId,
    },
  });
  return res.status(200).json({
    coupon: newCoupon,
  });
};

export default withPermissions(
  {
    policy: "api.coupons",
    action: "write",
  },
  withMeth<PERSON>(["POST"], handler),
);
