import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { comparePlainTextPassword } from "~/server/lib/password";
import { CommonValues, generateUser } from "~/server/auth";
import { encode } from "next-auth/jwt";
import { withValidation } from "~/pages/api/apiMiddleware";
import { object, string } from "yup";

const verifySchema = object().shape({
  email: string().email(),
  password: string().min(1),
});

export async function POST(request: NextApiRequest, res: NextApiResponse) {
  try {
    const { email, password } = request.body;

    const user = await db.user.findFirst({
      where: {
        email,
      },
    });

    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" });
    }

    // Verify password
    const isValidPassword = await comparePlainTextPassword(
      password,
      user.password,
    );

    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" });
    }

    const userItem = await generateUser(user.id, undefined);

    // Create a fake token/user object to run through Auth.js callbacks
    const token: CommonValues = {
      ...userItem.commonValue,
    };
    const jwtToken = await encode({
      secret: process.env.NEXTAUTH_SECRET ?? "",
      token,
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });
    return res.status(200).json({
      token: jwtToken,
      user: {
        accountId: token.accountId,
        organizationId: token.organizationId,
        planLevel: token.planLevel,
        trialEndDate: token.trialEndDate,
        roles: token.roles,
        name: token.name,
        id: user.id,
        email: user.email,
      },
    });
  } catch (error) {
    console.error("Auth verify error:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

export default withValidation(verifySchema, POST);
