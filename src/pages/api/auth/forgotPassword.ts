import { NextApiRequest, NextApiResponse } from "next";
import { object, string } from "yup";
import { db } from "~/server/db";
import { sendResetPasswordEmail } from "~/pages/api/staff/[id]/resetPassword";
import { captureMessage } from "@sentry/nextjs";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";

export const FORGOT_PASSWORD_SCHEMA = object().shape({
  email: string().required(),
});

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const { body } = req;

  const staff = await db.staff.findMany({
    where: {
      email: body.email,
    },
    include: {
      account: true,
    },
  });

  if (staff.length === 0) {
    captureMessage("No user found.");
    return res
      .status(400)
      .json({ error: "We could not find a staff member with that email." });
  }

  if (staff?.length != 1) {
    captureMessage("Multiple users with the same email found.");
    res.status(400).json({ error: "Invalid user" });
    return;
  }

  const user = staff[0];

  if (!user) {
    res.status(400).json({ error: "Invalid user" });
    return;
  }

  await sendResetPasswordEmail({
    staff: user,
    newAccount: false,
    permissive: true,
  });

  res.status(200).json({ success: true });
};

export default withMethods(
  ["POST"],
  withValidation(FORGOT_PASSWORD_SCHEMA, POST),
);
