import { NextApiRequest, NextApiResponse } from "next";
import { number, object, string } from "yup";
import { db } from "~/server/db";
import {
  comparePlainTextPassword,
  handlePlainTextPassword,
} from "~/server/lib/password";
import { withMethods, withValidation } from "~/pages/api/apiMiddleware";

export const RESET_PASSWORD_SCHEMA = object().shape({
  staffId: string().required(),
  accountId: number().required(),
  password: string().required(),
  token: string().required(),
  csrfToken: string().required(),
});

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const body = req.body;

  const user = await db.staff.findUnique({
    where: {
      id: body.staffId,
      accountId: body.accountId,
    },
  });

  if (!user) {
    res.status(400).json({ error: "Invalid user" });
    return;
  }

  const allTokens = await db.staffPasswordReset.findMany({
    where: {
      staffId: body.staffId,
    },
  });

  const token = allTokens.find((t) =>
    comparePlainTextPassword(body.token, t.token),
  );

  if (!token) {
    // Create intentional delay to prevent timing attacks and make it harder to guess if a token is valid
    const delay = 3000 * Math.random();
    const promise = new Promise((resolve) => setTimeout(resolve, delay));
    await promise;
    res.status(400).json({ error: "Invalid token" });
    return;
  }

  if (token.expires < new Date()) {
    res.status(400).json({ error: "Token expired" });
    return;
  }

  const password = await handlePlainTextPassword(body.password);
  await db.staff.update({
    where: {
      id: body.staffId,
    },
    data: {
      password,
    },
  });

  if (user.userId) {
    await db.user.update({
      where: {
        id: user.userId,
      },
      data: {
        password,
      },
    });
  }

  await db.staffPasswordReset.deleteMany({
    where: {
      staffId: user.id,
    },
  });
  res.status(200).json({ success: true });
};

export default withMethods(
  ["POST"],
  withValidation(RESET_PASSWORD_SCHEMA, handler),
);
