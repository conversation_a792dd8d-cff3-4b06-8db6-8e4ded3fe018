import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { SetupSurfaceSchema } from "~/query/surface/types";
import { NextApiResponse } from "next";
import { db } from "~/server/db";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const surfaceId = req.query.id as string;

  const surfaceNumber = parseInt(surfaceId);

  if (isNaN(surfaceNumber)) {
    return res.status(400).json({ error: "Invalid surface id" });
  }

  const surface = await db.setupSurface.findUnique({
    where: {
      accountId: req.accountId,
      id: surfaceNumber,
      archived: false,
    },
  });

  if (!surface) {
    return res.status(404).json({ error: "Surface not found" });
  }

  await db.setupSurface.update({
    where: {
      id: surfaceNumber,
    },
    data: {
      name: req.body.name,
      description: req.body.description,
      feeAmount: req.body.feeAmount ?? undefined,
      scaleFeeWithQuantity: req.body.scaleFee ?? undefined,
    },
  });

  return res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.surface",
    action: "write",
  },
  withMethods(["POST"], withValidation(SetupSurfaceSchema, handler)),
);
