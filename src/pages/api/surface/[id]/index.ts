import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { SetupSurfaceValues } from "~/query/surface/types";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const surfaceId = req.query.id as string;

  const surfaceNumber = parseInt(surfaceId);

  if (isNaN(surfaceNumber)) {
    return res.status(400).json({ error: "Invalid surface id" });
  }

  const surface = await db.setupSurface.findUnique({
    where: {
      accountId: req.accountId,
      id: surfaceNumber,
      archived: false,
    },
  });

  if (!surface) {
    return res.status(404).json({ error: "Surface not found" });
  }

  const responseData: SetupSurfaceValues = {
    name: surface.name,
    description: surface.description,
    feeAmount: surface.feeAmount,
    scaleFee: surface.scaleFeeWithQuantity,
  };

  return res.status(200).json({ setupSurface: responseData });
};

export default withPermissions(
  {
    policy: "api.surface",
    action: "read",
  },
  handler,
);
