import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const surfaceId = req.query.id as string;

  const surfaceNumber = parseInt(surfaceId);

  if (isNaN(surfaceNumber)) {
    return res.status(400).json({ error: "Invalid surface id" });
  }

  const surface = await db.setupSurface.findUnique({
    where: {
      accountId: req.accountId,
      id: surfaceNumber,
      archived: false,
    },
    include: {
      Order: {
        select: {
          id: true,
        },
      },
    },
  });

  if (!surface) {
    return res.status(404).json({ error: "Surface not found" });
  }

  if (surface.Order.length > 0) {
    await db.setupSurface.update({
      where: {
        id: surfaceNumber,
      },
      data: {
        archived: true,
      },
    });
  } else {
    await db.setupSurface.delete({
      where: {
        accountId: req.accountId,
        id: surfaceNumber,
      },
    });
  }

  return res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.surface",
    action: "write",
  },
  handler,
);
