import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { SurfaceList } from "~/query/surface/types";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const includeArchived = req.query?.includeArchived === "true";

  const setupSurface = await db.setupSurface.findMany({
    where: {
      accountId: req.accountId,
      archived: includeArchived ? undefined : false,
    },
  });

  const surfaceList: SurfaceList[] = setupSurface.map((surface) => ({
    id: surface.id,
    name: surface.name,
    archived: surface.archived,
    feeAmount: surface.feeAmount,
    scaleFee: surface.scaleFeeWithQuantity,
  }));

  return res.status(200).json({ setupSurface: surfaceList });
};

export default withPermissions(
  {
    policy: "api.surface",
    action: "read",
  },
  handler,
);
