import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "../apiMiddleware";
import { NextApiResponse } from "next";
import { SetupSurfaceSchema, SetupSurfaceValues } from "~/query/surface/types";
import { db } from "~/server/db";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const surface: SetupSurfaceValues = req.body;

  await db.setupSurface.create({
    data: {
      name: surface.name,
      description: surface.description,
      accountId: req.accountId,
      feeAmount: surface.feeAmount ?? null,
      scaleFeeWithQuantity: surface.scaleFee ?? false,
      archived: false,
    },
  });

  return res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.surface",
    action: "read",
  },
  withMethods(["POST"], withValidation(SetupSurfaceSchema, handler)),
);
