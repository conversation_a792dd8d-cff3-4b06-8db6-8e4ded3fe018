import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState } from ".prisma/client";
import { OrdersPlacedResponse } from "~/query/reports/ordersPlaced";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    res.status(400).json({
      error: "Missing required query parameters",
    });
    return;
  }

  if (typeof startDate !== "string" || typeof endDate !== "string") {
    res.status(400).json({
      error: "Invalid query parameters",
    });
    return;
  }

  const offset = new Date(startDate).getTimezoneOffset();

  const orders = await db.order.findMany({
    where: {
      accountId: req.accountId,
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate),
      },
      OR: [
        { state: OrderState.COMPLETED },
        { state: OrderState.ACTIVE },
        { state: OrderState.CANCELLED },
      ],
    },
  });

  if (!orders) {
    res.status(404).json({
      error: "No orders found",
    });
    return;
  }

  if (orders.length === 0) {
    res.status(200).json({
      sales: [],
      collectionReason: [],
      paymentType: [],
    });
    return;
  }

  const sales: Record<string, number> = {};

  // prefill the sales object with 0 values for each date or hour if start time and end time are in the same day
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  const isSameDay =
    endDateObj.getTime() - startDateObj.getTime() <= 24 * 60 * 60 * 1000;
  if (isSameDay) {
    for (
      let date = new Date(startDate);
      date <= endDateObj;
      date.setHours(date.getHours() + 1)
    ) {
      sales[date.toISOString()] = 0;
    }
  } else {
    for (
      let date = new Date(startDate);
      date <= endDateObj;
      date.setDate(date.getDate() + 1)
    ) {
      sales[date.toLocaleDateString()] = 0;
    }
  }

  for (const order of orders) {
    const date = new Date(order.createdAt);
    const offsetDiff = date.getTimezoneOffset() - offset;
    date.setTime(date.getTime() + offsetDiff * 60 * 1000);
    const sliceKey = isSameDay ? date.toISOString() : date.toLocaleDateString();

    sales[sliceKey] += 1;
  }

  const response: OrdersPlacedResponse = {
    sales: Object.entries(sales).map(([date, total]) => ({
      date,
      total,
    })),
  };

  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.reports.sales",
    action: "read",
  },
  GET,
);
