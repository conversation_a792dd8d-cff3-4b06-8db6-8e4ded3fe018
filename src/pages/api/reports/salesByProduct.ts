import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { SalesReportByProduct } from "~/query/reports/sales-by-product";
import { eachDayOfInterval, eachHourOfInterval, format } from "date-fns";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    res.status(400).json({
      error: "Missing required query parameters",
    });
    return;
  }

  if (typeof startDate !== "string" || typeof endDate !== "string") {
    res.status(400).json({
      error: "Invalid query parameters",
    });
    return;
  }
  const offset = new Date(startDate).getTimezoneOffset();

  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  const isSameDay =
    endDateObj.getTime() - startDateObj.getTime() <= 24 * 60 * 60 * 1000;
  const orders: {
    total_charge: number;
    productId: string;
    name: string;
    order_date: string;
  }[] = await db.$queryRaw`
    SELECT
      date_trunc(${
        isSameDay ? "hour" : "day"
      }, "Order"."startTime") AS order_date,
      OP."productId",
      P."name",
      SUM(OP."pricePaid" * OP."quantity") AS total_charge
    FROM "Order"
           LEFT JOIN public."OrderProduct" OP ON "Order".id = OP."orderId"
           LEFT JOIN public."Product" P ON OP."productId" = P.id
    WHERE "Order"."startTime" > ${new Date(startDate)}
      AND "Order"."state" IN ('COMPLETED', 'ACTIVE')
      AND "Order"."endTime" < ${new Date(endDate)}
      AND "Order"."accountId" = ${req.accountId}
      AND OP."pricePaid" IS NOT NULL
    GROUP BY order_date, OP."productId", P."name"
    ORDER BY order_date, total_charge DESC;
  `;

  if (!orders) {
    res.status(404).json({
      error: "No orders found",
    });
    return;
  }

  const bookingCount: Record<string, number> = {};

  type DateEntry = {
    date: string;
    // Using an index signature to allow additional properties of type number.
    [productName: string]: number | string;
  };

  const totalByProduct = orders.reduce(
    (acc, row) => {
      acc[row.name] = (acc[row.name] || 0) + row.total_charge;
      return acc;
    },
    {} as Record<string, number>,
  );

  const topProducts = Object.entries(totalByProduct).sort(
    ([, a], [, b]) => b - a,
  );

  const topProductsName = topProducts.map(([productName]) => productName);

  const dataByDate = orders.reduce(
    (acc, row) => {
      const date = new Date(row.order_date);
      const offsetDiff = date.getTimezoneOffset() - offset;
      date.setTime(date.getTime() + offsetDiff * 60 * 1000);
      const dateKey = isSameDay
        ? date.toISOString().slice(0, 13)
        : date.toLocaleDateString();
      let value = acc[dateKey];

      if (!value) {
        value = { date: dateKey };
      }

      if (topProductsName.includes(row.name)) {
        value[row.name] = ((value[row.name] as number) || 0) + row.total_charge;
        const currentBookingCount = bookingCount[row.name] || 0;
        bookingCount[row.name] = currentBookingCount + 1;
      }
      acc[dateKey] = value;

      return acc;
    },
    {} as Record<string, DateEntry>,
  );

  const sortedDates = Object.values(dataByDate).sort((a, b) =>
    b.date.localeCompare(a.date),
  );

  const firstDate = sortedDates.at(0)?.date;
  const lastDate = sortedDates.at(-1)?.date;

  let allDates: DateEntry[];
  // Fill in missing dates
  if (firstDate && lastDate) {
    const dates = isSameDay
      ? eachHourOfInterval({ start: startDateObj, end: endDateObj })
      : eachDayOfInterval({ start: startDateObj, end: endDateObj });

    allDates = dates.map((date) => {
      const dateKey = isSameDay
        ? format(date, "yyyy-MM-dd'T'HH")
        : format(date, "M/d/yyyy");

      return dataByDate[dateKey] || { date: dateKey };
    });
  } else {
    allDates = sortedDates;
  }

  const formattedData = allDates.map((row) => {
    topProductsName.forEach((productName) => {
      if (!row[productName]) {
        row[productName] = 0;
      }
    });
    return row;
  });

  console.log(formattedData);

  const response: SalesReportByProduct = {
    sales: formattedData,
    totalByProduct: topProducts,
    bookings: Object.entries(bookingCount).map(([label, total]) => ({
      label,
      total,
    })),
  };

  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.reports.sales",
    action: "read",
  },
  GET,
);
