import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { SalesReportResponse } from "~/query/reports/sales";
import { db } from "~/server/db";
import { ChargeType, OrderState } from ".prisma/client";
import { Order } from "@prisma/client";
import { getCurrencyValue } from "~/server/lib/currency";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    res.status(400).json({
      error: "Missing required query parameters",
    });
    return;
  }

  if (typeof startDate !== "string" || typeof endDate !== "string") {
    res.status(400).json({
      error: "Invalid query parameters",
    });
    return;
  }

  const offset = new Date(startDate).getTimezoneOffset();

  const orders = await db.order.findMany({
    where: {
      accountId: req.accountId,
      startTime: {
        gte: new Date(startDate),
        lte: new Date(endDate),
      },
      OR: [{ state: OrderState.COMPLETED }, { state: OrderState.ACTIVE }],
    },
    include: {
      OrderFee: {
        select: {
          amount: true,
        },
      },
      OrderDiscount: {
        select: {
          amount: true,
          chargeType: true,
        },
      },
      PaymentDetails: {
        select: {
          method: true,
          amount: true,
        },
      },
    },
  });

  if (!orders) {
    res.status(404).json({
      error: "No orders found",
    });
    return;
  }

  if (orders.length === 0) {
    res.status(200).json({
      sales: [],
      collectionReason: [],
      paymentType: [],
    });
    return;
  }

  const sales: Record<string, number> = {};

  // prefill the sales object with 0 values for each date or hour if start time and end time are in the same day
  const startDateObj = new Date(startDate);
  const endDateObj = new Date(endDate);
  const isSameDay =
    endDateObj.getTime() - startDateObj.getTime() <= 24 * 60 * 60 * 1000;
  if (isSameDay) {
    for (
      let date = new Date(startDate);
      date <= endDateObj;
      date.setHours(date.getHours() + 1)
    ) {
      sales[date.toISOString()] = 0;
    }
  } else {
    for (
      let date = new Date(startDate);
      date <= endDateObj;
      date.setDate(date.getDate() + 1)
    ) {
      sales[date.toLocaleDateString()] = 0;
    }
  }

  const collectionReason: Record<string, number> = {};
  const paymentType: Record<string, number> = {};

  const collectionReasons = [
    {
      label: "Item Sales",
      value: (order: Order) => order.baseTotal,
    },
    {
      label: "Fees Collected",
      value: (order: { OrderFee: { amount: number }[] } & Order) =>
        order.OrderFee.reduce((acc, fee) => acc + fee.amount, 0),
    },
    {
      label: "Rental Protection",
      value: (order: Order) => order.damageWaiverAmount,
    },
    {
      label: "Discounts",
      value: (
        order: Order & {
          OrderDiscount: { amount: number; chargeType: ChargeType }[];
        },
      ) =>
        order.OrderDiscount.reduce((acc, discount) => {
          if (discount.chargeType === ChargeType.PERCENTAGE) {
            return acc + order.baseTotal * (discount.amount / 100);
          }
          return acc + discount.amount;
        }, 0),
    },
    {
      label: "Sales Tax",
      value: (order: Order) => order.taxAmount,
    },
    {
      label: "Outstanding Balance",
      value: (order: Order) => (order.totalPaid || 0) - (order.finalTotal || 0),
    },
    {
      label: "Sales Collected",
      value: (order: Order) => order.totalPaid,
    },
    {
      label: "Net Sales",
      value: (order: Order) => order.finalTotal,
    },
  ];

  for (const order of orders) {
    const date = new Date(order.startTime);
    const offsetDiff = date.getTimezoneOffset() - offset;
    date.setTime(date.getTime() + offsetDiff * 60 * 1000);
    const sliceKey = isSameDay ? date.toISOString() : date.toLocaleDateString();

    sales[sliceKey] = getCurrencyValue(
      (sales[sliceKey] ?? 0) + order.totalPaid,
    );

    for (const reason of collectionReasons) {
      collectionReason[reason.label] = getCurrencyValue(
        (collectionReason[reason.label] || 0) + (reason.value(order) || 0),
      );
    }

    if (order.PaymentDetails) {
      order.PaymentDetails.forEach((payment) => {
        const formattedMethod =
          payment.method.toUpperCase().slice(0, 1) +
          payment.method.replace(/_/g, " ").slice(1);

        paymentType[formattedMethod] = getCurrencyValue(
          (paymentType[formattedMethod] || 0) + payment.amount,
        );
      });
    }
  }

  const response: SalesReportResponse = {
    sales: Object.entries(sales).map(([date, total]) => ({
      date,
      total,
    })),
    collectionReason: Object.entries(collectionReason).map(
      ([label, total]) => ({
        label,
        total,
      }),
    ),
    paymentType: Object.entries(paymentType).map(([label, total]) => ({
      label,
      total,
    })),
  };

  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.reports.sales",
    action: "read",
  },
  GET,
);
