import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { CustomerSchema } from "~/query/customer";
import { checkPermission } from "~/pages/api/permissions";
import { getStripeAccount } from "~/server/lib/stripe";
import { formatPhoneNumber } from "~/server/lib/twilio";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.customer",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const account = await db.account.findUnique({
    where: {
      id: permission.accountId,
    },
  });

  if (!account) {
    res.status(404).json({ error: "Account not found" });
    return;
  }

  const jsonData = req.body;

  let customer;
  try {
    customer = await CustomerSchema.validate(jsonData, { stripUnknown: true });
  } catch (e) {
    res.status(400).json({ error: "Invalid customer" });
    return;
  }
  if (customer.phoneNumber) {
    customer.phoneNumber = formatPhoneNumber(customer.phoneNumber);
  }
  customer.email = customer.email.toLowerCase();
  const customerFromDatabase = await db.customer.findFirst({
    where: {
      email: customer.email,
      accountId: account.id,
    },
  });
  if (customerFromDatabase) {
    // update null values with new data
    const updatedCustomer = {
      ...customerFromDatabase,
      ...customer,
    };
    await db.customer.update({
      where: {
        id: customerFromDatabase.id,
      },
      data: updatedCustomer,
    });

    return res.json({ success: true, customer: customerFromDatabase });
  }
  const customerCreated = await db.customer.create({
    data: {
      accountId: account.id,
      ...customer,
    },
  });
  if (customer.phoneNumber) {
    const existingPhoneContact = await db.phoneContact.findFirst({
      where: {
        phoneNumber: formatPhoneNumber(customer.phoneNumber),
        accountId: account.id,
      },
    });
    if (!existingPhoneContact) {
      await db.phoneContact.create({
        data: {
          phoneNumber: formatPhoneNumber(customer.phoneNumber),
          accountId: account.id,
          customerId: customerCreated.id,
        },
      });
    }
  }
  if (account.stripeAccountId) {
    await getStripeAccount(account).customers.create({
      email: customer.email,
      metadata: {
        customerId: customerCreated.id,
      },
    });
  }

  return res.json({ success: true, customer: customerCreated });
};

export default POST;
