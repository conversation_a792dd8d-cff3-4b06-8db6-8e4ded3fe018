import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { Prisma } from ".prisma/client";
import { checkPermission } from "~/pages/api/permissions";
import { CustomerResultProps } from "~/query/customer";
import CustomerWhereInput = Prisma.CustomerWhereInput;

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.customer",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const customerSearch = req.query.search;

  let orSearch: CustomerWhereInput[] | undefined = undefined;
  if (
    customerSearch &&
    typeof customerSearch === "string" &&
    customerSearch !== ""
  ) {
    if (customerSearch.startsWith("id:")) {
      orSearch = [
        {
          id: {
            equals: customerSearch.replace("id:", ""),
          },
        },
      ];
    } else {
      orSearch = [
        {
          firstName: {
            contains: customerSearch,
            mode: "insensitive",
          },
        },
        {
          lastName: {
            contains: customerSearch,
            mode: "insensitive",
          },
        },
        {
          email: {
            contains: customerSearch,
            mode: "insensitive",
          },
        },
        {
          phoneNumber: {
            contains: customerSearch,
            mode: "insensitive",
          },
        },
      ];
    }
  }

  const customers = await db.customer.findMany({
    orderBy: [
      {
        updatedAt: "desc",
      },
    ],
    where: {
      account: {
        id: permission.accountId,
      },
      OR: orSearch,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      company: true,
      email: true,
      phoneNumber: true,
    },
  });

  const customerResults: CustomerResultProps[] = [];

  customers.forEach((customer) => {
    customerResults.push({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      company: customer.company,
      email: customer.email,
      phoneNumber: customer.phoneNumber,
    });
  });
  return res.status(200).json({ customers: customerResults });
};

export default GET;
