import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicCustomer,
  handleEditCustomer,
} from "~/pages/api/customers/[id]/index";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const items = await handleBasicCustomer(req, res, "write");

  if (!items) {
    return;
  }
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  return handleEditCustomer(req, res, items.customerID, items.accountId);
};

export default handler;
