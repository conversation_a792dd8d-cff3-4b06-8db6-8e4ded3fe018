import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { MessageHistory } from "~/pages/phone";

export type DocumentsByCustomer = {
  title: string;
  date: Date;
  id: string;
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const customerId = req.query.id;
  if (!customerId) {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  if (typeof customerId !== "string") {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  const phoneContact = await db.phoneContact.findFirst({
    where: {
      customerId: customerId,
      accountId: req.accountId,
    },
    select: {
      id: true,
      PhoneMessageLog: {
        select: {
          id: true,
          body: true,
          mediaUrl: true,
          inbound: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      },
      PhoneCallLog: {
        select: {
          id: true,
          inbound: true,
          answered: true,
          duration: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      },
    },
  });

  if (!phoneContact) {
    return res.status(200).json({
      success: true,
      recentInteractions: [],
    });
  }

  const messageHistory: MessageHistory[] = phoneContact.PhoneMessageLog.map(
    (message) => {
      return {
        id: message.id,
        call: false,
        message: message.body,
        time: message.createdAt,
        from: message.inbound ? "customer" : "agent",
      };
    },
  );

  const callHistory: MessageHistory[] = phoneContact.PhoneCallLog.map(
    (call) => {
      return {
        id: call.id,
        call: true,
        message: call.inbound
          ? `${call.answered ? "Answered" : "Missed"}${
              call.duration ? ` (${call.duration} seconds)` : ""
            }`
          : call.duration
            ? ` (${call.duration} seconds)`
            : "",
        time: call.createdAt,
        from: call.inbound ? "customer" : "agent",
      };
    },
  );

  const messageHistoryWithCalls = [
    ...messageHistory.filter((item) => item.from === "agent"),
    ...callHistory,
  ].sort((a, b) => {
    return a.time.getTime() - b.time.getTime();
  });
  return res.status(200).json({
    success: true,
    recentInteractions: messageHistoryWithCalls
      .map((item) => {
        return {
          ...item,
          time: item.time.toISOString(),
        };
      })
      .slice(0, 5),
    conversationId: phoneContact.id,
  });
};

export default withPermissions(
  {
    policy: "api.customer",
    action: "read",
  },
  handler,
);
