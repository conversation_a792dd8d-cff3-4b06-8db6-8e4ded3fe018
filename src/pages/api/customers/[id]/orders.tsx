import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { OrderState } from ".prisma/client";

export type OrdersByCustomer = {
  id: number;
  startTime: Date;
  endTime: Date;
  state: OrderState;
  finalTotal: number;
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const customerId = req.query.id;
  if (!customerId) {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  if (typeof customerId !== "string") {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  const ordersByCustomer = await db.order.findMany({
    where: {
      accountId: req.accountId,
      customerId: customerId,
    },
    select: {
      id: true,
      startTime: true,
      endTime: true,
      state: true,
      finalTotal: true,
    },
    orderBy: {
      startTime: "desc",
    },
  });

  return res.json({ success: true, orders: ordersByCustomer });
};

export default withPermissions(
  {
    policy: "api.customer",
    action: "read",
  },
  handler,
);
