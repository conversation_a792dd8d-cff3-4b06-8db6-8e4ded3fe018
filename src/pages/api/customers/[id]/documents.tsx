import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";

export type DocumentsByCustomer = {
  title: string;
  date: string;
  id: string;
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const customerId = req.query.id;
  if (!customerId) {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  if (typeof customerId !== "string") {
    res.status(400).json({ error: "Invalid customer id" });
    return;
  }

  const signedDocumentsByCustomer = await db.signedDocument.findMany({
    where: {
      customerId: customerId,
    },
    select: {
      id: true,
      document: {
        select: {
          title: true,
        },
      },
      createdAt: true,
    },
  });

  return res.status(200).json({
    success: true,
    documents: signedDocumentsByCustomer.map((item) => {
      return {
        title: item.document.title,
        date: new Date(item.createdAt).toISOString(),
        id: item.id,
      };
    }),
  });
};

export default withPermissions(
  {
    policy: "api.customer",
    action: "read",
  },
  handler,
);
