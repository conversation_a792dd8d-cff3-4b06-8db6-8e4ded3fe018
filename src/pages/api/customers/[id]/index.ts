import { NextApiRequest, NextApiResponse } from "next";
import { db } from "~/server/db";
import { CustomerSchema } from "~/query/customer";
import { checkPermission } from "~/pages/api/permissions";
import { formatPhoneNumber } from "~/server/lib/twilio";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const items = await handleBasicCustomer(
    req,
    res,
    req.method !== "GET" ? "write" : "read",
  );
  if (!items) {
    return;
  }
  if (req.method === "DELETE") {
    return handleDeleteCustomer(req, res, items.customerID, items.accountId);
  }

  if (req.method === "POST") {
    return handleEditCustomer(req, res, items.customerID, items.accountId);
  }

  if (req.method !== "GET") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }
  const customer = await db.customer.findFirst({
    where: {
      id: items.customerID,
      accountId: items.accountId,
    },
  });
  if (!customer) {
    res.status(404).json({ error: "Customer not found" });
    return;
  }

  const customerOutput = CustomerSchema.cast(customer, {
    stripUnknown: true,
  });

  return res.json({
    success: true,
    customer: customerOutput,
    resultPop: {
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      company: customer.company,
      email: customer.email,
      phoneNumber: customer.phoneNumber,
      orders: -1,
      totalSpent: -1,
    },
  });
};

export const handleBasicCustomer = async (
  req: NextApiRequest,
  res: NextApiResponse,
  action: "read" | "write" = "read",
): Promise<{ customerID: string; accountId: number } | null> => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.customer",
      action: action,
    },
  });

  if (!permission) {
    return null;
  }

  const customerID = req.query.id;
  if (!customerID) {
    res.status(400).json({ error: "Invalid customer id" });
    return null;
  }

  if (typeof customerID !== "string") {
    res.status(400).json({ error: "Invalid customer id" });
    return null;
  }

  return { customerID, accountId: permission.accountId };
};

export const handleDeleteCustomer = async (
  req: NextApiRequest,
  res: NextApiResponse,
  customerId: string,
  accountId: number,
) => {
  const deletion = await db.customer.delete({
    where: {
      id: customerId,
      accountId: accountId,
    },
  });
  if (!deletion) {
    res.status(404).json({ error: "Customer not found" });
    return;
  }
  return res.json({ success: true });
};

export const handleEditCustomer = async (
  req: NextApiRequest,
  res: NextApiResponse,
  customerId: string,
  accountId: number,
) => {
  const data = req.body;
  let customer;
  try {
    customer = await CustomerSchema.validate(data, { stripUnknown: true });
  } catch (e) {
    res.status(400).json({ error: "Invalid customer" });
    return;
  }
  if (customer.phoneNumber) {
    customer.phoneNumber = formatPhoneNumber(customer.phoneNumber);
  }
  customer.email = customer.email.toLowerCase();
  const customerFromDatabase = await db.customer.findFirst({
    where: {
      id: customerId,
      accountId: accountId,
    },
  });
  let shouldThrow = false;
  if (customer.email !== customerFromDatabase?.email?.toLowerCase()) {
    const conflictingCustomer = await db.customer.findFirst({
      where: {
        email: customer.email,
        accountId: accountId,
      },
    });
    if (conflictingCustomer) {
      customer.email = customerFromDatabase?.email?.toLowerCase() || "";
      shouldThrow = true;
    }
  }
  await db.customer.update({
    where: {
      id: customerId,
      accountId: accountId,
    },
    data: {
      ...customer,
    },
  });
  if (customer.phoneNumber) {
    const existingPhoneContact = await db.phoneContact.findFirst({
      where: {
        phoneNumber: formatPhoneNumber(customer.phoneNumber),
        accountId: accountId,
      },
    });
    if (!existingPhoneContact) {
      await db.phoneContact.create({
        data: {
          phoneNumber: formatPhoneNumber(customer.phoneNumber),
          accountId: accountId,
          customerId: customerId,
        },
      });
    } else {
      // Update the phone contact if it exists
      await db.phoneContact.update({
        where: {
          id: existingPhoneContact.id,
        },
        data: {
          phoneNumber: formatPhoneNumber(customer.phoneNumber),
          customerId: customerId,
        },
      });
    }
  }
  if (shouldThrow) {
    res.status(409).json({ error: "Customer with that email already exists." });
    return;
  }
  return res.json({ success: true });
};

export default handler;
