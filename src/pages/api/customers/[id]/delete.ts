import { NextApiRequest, NextApiResponse } from "next";
import {
  handleBasicCustomer,
  handleDeleteCustomer,
} from "~/pages/api/customers/[id]/index";

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const items = await handleBasicCustomer(req, res, "write");
  if (!items) {
    return;
  }
  return handleDeleteCustomer(req, res, items.customerID, items.accountId);
};

export default handler;
