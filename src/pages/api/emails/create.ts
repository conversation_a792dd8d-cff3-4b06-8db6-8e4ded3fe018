import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { EMAIL_SCHEMA, EmailDetail } from "~/query/email";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const createdEmail = await db.customEmailDelivery.create({
    data: {
      ...req.body,
      previewText: req.body.previewText ?? null,
      accountId: req.accountId,
    },
  });

  const email: EmailDetail = {
    name: createdEmail.name,
    subject: createdEmail.subject,
    text: createdEmail.text,
  };

  return res.status(200).json({
    id: createdEmail.id,
    email,
  });
};

export default withPermissions(
  {
    policy: "api.email",
    action: "write",
  },
  withMethods(
    ["POST"],
    withValidation(
      EMAIL_SCHEMA,

      handler,
    ),
  ),
);
