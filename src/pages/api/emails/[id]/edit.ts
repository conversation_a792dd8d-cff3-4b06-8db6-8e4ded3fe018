import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { EMAIL_SCHEMA, EmailDetail } from "~/query/email";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const emailId = req.query.id as string;

  const numberId = Number(emailId);

  if (isNaN(numberId)) {
    return res.status(400).json({
      message: "Invalid email ID",
    });
  }

  const customEmailDelivery = await db.customEmailDelivery.findFirst({
    where: {
      id: numberId,
      accountId: req.accountId,
    },
  });

  if (!customEmailDelivery) {
    return res.status(404).json({
      message: "Email not found",
    });
  }

  const updatedEmail = await db.customEmailDelivery.update({
    where: {
      accountId: req.accountId,
      id: numberId,
    },
    data: {
      ...req.body,
    },
  });

  const email: EmailDetail = {
    name: updatedEmail.name,
    subject: updatedEmail.subject,
    text: updatedEmail.text,
  };

  res.status(200).json({
    id: updatedEmail.id,
    email,
  });
};

export default withPermissions(
  {
    policy: "api.email",
    action: "write",
  },
  withMethods(
    ["POST"],
    withValidation(
      EMAIL_SCHEMA,

      handler,
    ),
  ),
);
