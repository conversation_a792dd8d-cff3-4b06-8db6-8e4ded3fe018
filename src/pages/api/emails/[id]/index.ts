import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { EmailDetail } from "~/query/email";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const id = req.query.id as string;

  const numberId = Number(id);

  if (isNaN(numberId)) {
    res.status(400).json({
      message: "Invalid email ID",
    });
    return;
  }

  const customEmailDelivery = await db.customEmailDelivery.findFirst({
    where: {
      id: numberId,
      accountId: req.accountId,
    },
  });

  if (!customEmailDelivery) {
    return res.status(404).json({
      message: "Email not found",
    });
  }
  const email: EmailDetail = {
    name: customEmailDelivery.name,
    subject: customEmailDelivery.subject,
    previewText: customEmailDelivery.previewText ?? undefined,
    text: customEmailDelivery.text,
  };

  res.status(200).json({
    email: email,
  });
};

export default withPermissions(
  {
    policy: "api.email",
    action: "read",
  },
  withMethods(["GET"], handler),
);
