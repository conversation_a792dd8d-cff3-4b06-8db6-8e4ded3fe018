import { NextApiResponse } from "next";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { db } from "~/server/db";
import { AutomationBody } from "~/query/emailAutomations";
import { EmailAction } from "@prisma/client";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const automations: Record<string, AutomationBody> = {};

  Object.values(EmailAction).forEach((action) => {
    automations[action] = {
      action,
      enabled: false,
      minutesAfter: 0,
      emailTemplateId: -1,
    };
  });

  const scheduledEmailConfigs = await db.scheduledEmailConfig.findMany({
    where: {
      accountId: req.accountId,
    },
  });

  for (const config of scheduledEmailConfigs) {
    automations[config.action] = {
      action: config.action,
      enabled: config.enabled,
      minutesAfter: config.minutesAfter,
      emailTemplateId: config.customEmailDeliveryId,
    };
  }

  res.status(200).json({ automations: Object.values(automations) });
};

export default withPermissions(
  {
    policy: "api.emails-automations",
    action: "read",
  },
  handler,
);
