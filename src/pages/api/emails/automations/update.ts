import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "../../apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { AUTOMATIONS_SCHEMA, AutomationsBody } from "~/query/emailAutomations";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const requestBody: AutomationsBody = req.body;
  for (const body of requestBody.automations) {
    const emailTemplate = await db.customEmailDelivery.findUnique({
      where: {
        accountId: req.accountId,
        id: body.emailTemplateId,
      },
    });

    if (!emailTemplate) {
      continue;
    }

    await db.scheduledEmailConfig.upsert({
      where: {
        accountId_action: {
          accountId: req.accountId,
          action: body.action,
        },
      },
      update: {
        enabled: body.enabled,
        minutesAfter: body.minutesAfter,
        customEmailDeliveryId: body.emailTemplateId,
      },
      create: {
        accountId: req.accountId,
        action: body.action,
        enabled: body.enabled,
        minutesAfter: body.minutesAfter,
        customEmailDeliveryId: body.emailTemplateId,
      },
    });
  }

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.emails-automations",
    action: "write",
  },
  withMethods(["POST"], withValidation(AUTOMATIONS_SCHEMA, handler)),
);
