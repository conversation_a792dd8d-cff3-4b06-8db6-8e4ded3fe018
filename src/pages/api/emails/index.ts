import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { EmailOverview } from "~/query/email";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const customEmailDelivery = await db.customEmailDelivery.findMany({
    where: {
      accountId: req.accountId,
    },
  });

  const emails: EmailOverview[] = customEmailDelivery.map((email) => ({
    createdAt: email.createdAt,
    id: email.id,
    name: email.name,
    subject: email.subject,
  }));

  res.status(200).json({
    emails,
  });
};

export default withPermissions(
  {
    policy: "api.email",
    action: "read",
  },
  withMethods(
    ["GET"],

    handler,
  ),
);
