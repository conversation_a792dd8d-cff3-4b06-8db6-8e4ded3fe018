import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { formatPhoneNumber, getTwilioForSubAccount } from "~/server/lib/twilio";
import { getOrCreatePhoneContact } from "~/pages/api/backend/twilio/sms";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({
      error: "Phone account not found",
    });
    return;
  }

  if (!phoneAccount.phoneNumber) {
    res.status(400).json({
      error: "Phone number not found",
    });
    return;
  }

  // get the conversation sid from the path
  const { conversationId } = req.query;

  if (!conversationId) {
    res.status(400).json({
      error: "Conversation ID not found",
    });
    return;
  }

  if (!Number(conversationId)) {
    res.status(400).json({
      error: "Invalid conversation ID",
    });
    return;
  }

  const phoneContact = await db.phoneContact.findFirst({
    where: {
      id: Number(conversationId),
    },
  });

  if (!phoneContact) {
    res.status(400).json({
      error: "Phone contact not found",
    });
    return;
  }

  const { body } = req.body;
  if (!body) {
    res.status(400).json({
      error: "Message body not found",
    });
    return;
  }

  // send the message
  const twilio = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );

  const message = await twilio.messages.create({
    body,
    from: phoneAccount.phoneNumber,
    to: phoneContact.phoneNumber,
  });

  // create the message log
  const messageLog = await db.phoneMessageLog.create({
    data: {
      body,
      phoneContactId: phoneContact.id,
      inbound: false,
      messageSid: message.sid,
      accountId: permissions.accountId,
      errorMessage: message.errorCode !== null ? message.errorMessage : null,
    },
  });

  await db.phoneContact.update({
    where: {
      id: phoneContact.id,
    },
    data: {
      lastMessage: new Date(),
    },
  });

  res.json({
    success: true,
    message: messageLog,
  });
};

export const sendMessage = async (
  to: string,
  accountId: number,
  content: string,
): Promise<boolean> => {
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: accountId,
    },
  });

  if (!phoneAccount?.phoneNumber) {
    return false;
  }

  const phoneContact = await getOrCreatePhoneContact(
    formatPhoneNumber(to),
    accountId,
  );
  // send the message
  const twilio = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );

  const message = await twilio.messages.create({
    body: content,
    from: phoneAccount.phoneNumber,
    to: phoneContact.phoneNumber,
  });

  // create the message log
  await db.phoneMessageLog.create({
    data: {
      body: content,
      phoneContactId: phoneContact.id,
      inbound: false,
      messageSid: message.sid,
      accountId: accountId,
      errorMessage: message.errorCode !== null ? message.errorMessage : null,
    },
  });

  await db.phoneContact.update({
    where: {
      id: phoneContact.id,
    },
    data: {
      lastMessage: new Date(),
    },
  });

  return true;
};

export default POST;
