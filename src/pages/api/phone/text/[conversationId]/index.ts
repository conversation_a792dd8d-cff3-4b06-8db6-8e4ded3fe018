import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { MessageHistory } from "~/pages/phone";
import { displayPrettyPhone } from "~/server/lib/phone";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { formatDuration } from "date-fns";

export type ConversationDetails = {
  hasMore: boolean;
  messages: MessageHistory[];
  customerDetails: CustomerDetails;
};

export type CustomerDetails = {
  displayName: string;
  phoneNumber: string;
  customerId?: string;
};

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  // get the conversation sid from the path
  const { conversationId, cursor } = req.query;

  if (!conversationId) {
    res.status(400).json({
      error: "Conversation ID not found",
    });
    return;
  }

  const conversionNumber = Number(conversationId);

  if (!conversionNumber) {
    res.status(400).json({
      error: "Invalid conversation ID",
    });
    return;
  }

  const limit = 30;

  const cursorDb = cursor
    ? {
        id: conversionNumber,
      }
    : undefined;

  const [phoneContact, messages, calls] = await db.$transaction([
    db.phoneContact.findFirst({
      where: {
        id: conversionNumber,
        accountId: req.accountId,
      },
      include: {
        customer: true,
      },
    }),
    db.phoneMessageLog.findMany({
      cursor: cursorDb,
      take: limit,
      skip: cursor ? 1 : 0,
      where: {
        phoneContactId: conversionNumber,
      },
      orderBy: {
        createdAt: "desc",
      },
    }),
    db.phoneCallLog.findMany({
      where: {
        phoneContactId: conversionNumber,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        phoneVoicemail: true,
      },
    }),
  ]);

  if (!phoneContact) {
    res.status(400).json({
      error: "Phone contact not found",
    });
    return;
  }
  const messageHistory: MessageHistory[] = messages.map((message) => {
    return {
      id: message.id,
      call: false,
      message: message.body,
      mediaUrl: message.mediaUrl ?? undefined,
      time: message.createdAt,
      from: message.inbound ? "customer" : "agent",
    };
  });

  const callHistory: MessageHistory[] = calls.map((call) => {
    return {
      id: call.id,
      call: true,
      message: call.inbound
        ? `${call.answered ? "Answered" : "Missed"}${
            call.duration
              ? ` (${formatDuration({ seconds: call.duration })})`
              : ""
          }`
        : call.duration
          ? ` (${formatDuration({ seconds: call.duration })})`
          : "",
      voicemail: call?.phoneVoicemail
        ? {
            transcription: call.phoneVoicemail.transcription ?? undefined,
            url: call.id,
          }
        : undefined,
      time: call.createdAt,
      from: call.inbound ? "customer" : "agent",
    };
  });

  const messageHistoryWithCalls = [...messageHistory, ...callHistory].sort(
    (a, b) => {
      return a.time.getTime() - b.time.getTime();
    },
  );

  const customerDetails: CustomerDetails = {
    displayName: phoneContact.customer
      ? `${phoneContact.customer.firstName} ${phoneContact.customer.lastName}`
      : `${displayPrettyPhone(phoneContact.phoneNumber)}`,
    phoneNumber: displayPrettyPhone(phoneContact.phoneNumber),
    customerId: phoneContact.customerId ?? undefined,
  };

  await db.phoneContact.update({
    where: {
      id: conversionNumber,
    },
    data: {
      lastRead: new Date(),
    },
  });

  return res.status(200).json({
    hasMore: messages.length === limit,
    messages: messageHistoryWithCalls,
    customerDetails: customerDetails,
  });
};

export default withPermissions(
  {
    policy: "api.phone",
    action: "read",
  },
  GET,
);
