import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { displayPrettyPhone } from "~/server/lib/phone";
import { isAfter } from "date-fns";
import { RecentConversation } from "~/query/phone";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { cursor } = req.query;
  const phoneContacts = await db.phoneContact.findMany({
    where: {
      accountId: req.accountId,
    },
    orderBy: {
      lastMessage: "desc",
    },
    skip: cursor ? 1 : 0,
    cursor: cursor ? { id: Number(cursor) } : undefined,
    take: 60,
    include: {
      customer: true,
      PhoneCallLog: {
        take: 1,
        orderBy: {
          createdAt: "desc",
        },
      },
      PhoneMessageLog: {
        take: 1,
        orderBy: {
          createdAt: "desc",
        },
      },
    },
  });

  const responseList: RecentConversation[] = [];
  for (const conversation of phoneContacts) {
    const conversationSid = conversation.id;
    const lastMessage = conversation.PhoneMessageLog[0];
    const lastMessageText = lastMessage?.body;
    const lastMessageDate = lastMessage?.createdAt;
    let conversationName = displayPrettyPhone(conversation.phoneNumber);
    if (conversation.customerId && conversation.customer) {
      const customer = conversation.customer;
      conversationName = `${customer.firstName} ${customer.lastName}`;
    }
    const lastActionTime = Math.max(
      lastMessageDate?.getTime() ?? 0,
      conversation.PhoneCallLog[0]?.createdAt?.getTime() || 0,
    );

    const lastAction =
      lastActionTime === lastMessageDate?.getTime() ? lastMessageText : "Call";

    responseList.push({
      phoneNumber: displayPrettyPhone(conversation.phoneNumber),
      id: conversationSid,
      name: conversationName,
      lastMessage: lastAction || "",
      lastMessageTime: new Date(lastActionTime)?.toISOString() || "",
      read: isAfter(
        conversation.lastRead || new Date(),
        new Date(lastActionTime || new Date(0)),
      ),
    });
  }

  res.status(200).json({
    conversations: responseList,
  });
};

export default withPermissions(
  {
    policy: "api.phone",
    action: "read",
  },
  GET,
);
