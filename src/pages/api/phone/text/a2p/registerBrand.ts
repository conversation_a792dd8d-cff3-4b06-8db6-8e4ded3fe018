import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { getTwilioForSubAccount } from "~/server/lib/twilio";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone.a2p",
      action: "write",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({ error: "Phone account not found" });
    return;
  }

  const twilioClient = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );
  const prefix = process.env.NODE_ENV === "production" ? "" : "staging-";
  // Start the A2P messaging process
  const customerProfileList =
    await twilioClient.trusthub.v1.customerProfiles.list({
      friendlyName: `${prefix}${permissions.accountId.toString()}`,
      limit: 1,
    });

  if (!customerProfileList) {
    res.status(500).json({ error: "Failed to find customer profile" });
    return;
  }

  const customerProfile = customerProfileList[0];

  if (!customerProfile) {
    res.status(500).json({ error: "Failed to get customer profile" });
    return;
  }

  const trustProducts = await twilioClient.trusthub.v1.trustProducts.list({
    friendlyName: `${prefix}${permissions.accountId.toString()}`,
    limit: 1,
  });

  if (!trustProducts) {
    res.status(500).json({ error: "Failed to find trust products" });
    return;
  }

  const trustProduct = trustProducts[0];

  if (!trustProduct) {
    res.status(500).json({ error: "Failed to get trust product" });
    return;
  }

  const mock = process.env.NODE_ENV !== "production";

  const brand = await twilioClient.messaging.v1.brandRegistrations.create({
    skipAutomaticSecVet: true,
    customerProfileBundleSid: customerProfile.sid,
    a2PProfileBundleSid: trustProduct.sid,
    mock,
  });

  if (!brand) {
    res.status(500).json({ error: "Failed to create brand" });
    return;
  }

  await twilioClient.messaging.v1.services.create({
    friendlyName: `${prefix}${permissions.accountId.toString()}`,
    inboundRequestUrl: `https://dash.partyrentalplatform.com/api/backend/twilio/sms`,
  });

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      messagingStatus: "brand-approval",
    },
  });

  res.status(200).json({ success: true });
};

export default POST;
