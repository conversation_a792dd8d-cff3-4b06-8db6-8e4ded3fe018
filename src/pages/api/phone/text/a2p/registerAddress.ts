import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { getTwilioForSubAccount } from "~/server/lib/twilio";
import { db } from "~/server/db";
import { captureMessage } from "@sentry/nextjs";
import {
  A2P_ADDRESS_REQUEST_SCHEMA,
  A2PAddressRegistrationRequest,
} from "~/components/phone/register/PhoneA2PAddress";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone.a2p",
      action: "write",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({ error: "Phone account not found" });
    return;
  }

  let body: A2PAddressRegistrationRequest | undefined;
  try {
    body = A2P_ADDRESS_REQUEST_SCHEMA.validateSync(req.body);
  } catch (e) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const twilioClient = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );
  const prefix = process.env.NODE_ENV === "production" ? "" : "staging-";
  // Start the A2P messaging process
  const customerProfileList =
    await twilioClient.trusthub.v1.customerProfiles.list({
      friendlyName: `${prefix}${permissions.accountId.toString()}`,
      limit: 1,
    });

  if (!customerProfileList) {
    res.status(500).json({ error: "Failed to find customer profile" });
    return;
  }

  const customerProfile = customerProfileList[0];

  if (!customerProfile) {
    res.status(500).json({ error: "Failed to get customer profile" });
    return;
  }

  const address = await twilioClient.addresses.create({
    customerName: body.customer_name,
    street: body.street,
    city: body.city,
    region: body.region,
    postalCode: body.postal_code,
    isoCountry: body.iso_country,
  });

  if (!address) {
    res.status(500).json({ error: "Failed to create address" });
    return;
  }

  const addressAttachment =
    await twilioClient.trusthub.v1.supportingDocuments.create({
      attributes: {
        address_sids: address.sid,
      },
      friendlyName: `${prefix}${permissions.accountId.toString()}`,
      type: "customer_profile_address",
    });

  await twilioClient.trusthub.v1
    .customerProfiles(customerProfile.sid)
    .customerProfilesEntityAssignments.create({
      objectSid: addressAttachment.sid,
    });

  const evaluation = await twilioClient.trusthub.v1
    .customerProfiles(customerProfile.sid)
    .customerProfilesEvaluations.create({
      policySid: "RNdfbf3fae0e1107f8aded0e7cead80bf5",
    });

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      messagingStatus: "pending-review",
    },
  });

  if (evaluation.status !== "compliant") {
    captureMessage(`A2P Evaluation failed: ${evaluation.status}`, {
      extra: {
        evaluation,
      },
    });

    return res.status(200).json({ success: true, auto_approval: false });
  }

  await twilioClient.trusthub.v1
    .customerProfiles(customerProfile.sid)
    .update({ status: "pending-review" });

  const trustProduct = await twilioClient.trusthub.v1.trustProducts.create({
    friendlyName: `${prefix}${permissions.accountId.toString()}`,
    email: "<EMAIL>",
    policySid: "RNb0d4771c2c98518d916a3d4cd70a8f8b",
  });

  const trustEndUser = await twilioClient.trusthub.v1.endUsers.create({
    attributes: {
      company_type: "private",
    },
    friendlyName: `${prefix}${permissions.accountId.toString()}-trust-end-user`,
    type: "us_a2p_messaging_profile_information",
  });

  await twilioClient.trusthub.v1
    .trustProducts(trustProduct.sid)
    .trustProductsEntityAssignments.create({
      objectSid: trustEndUser.sid,
    });

  await twilioClient.trusthub.v1
    .trustProducts(trustProduct.sid)
    .trustProductsEntityAssignments.create({
      objectSid: customerProfile.sid,
    });

  const trustCompliance = await twilioClient.trusthub.v1
    .trustProducts(trustProduct.sid)
    .trustProductsEvaluations.create({
      policySid: "RNb0d4771c2c98518d916a3d4cd70a8f8b",
    });

  if (trustCompliance.status !== "compliant") {
    captureMessage(`Trust Evaluation failed: ${trustCompliance.status}`, {
      extra: {
        trustCompliance,
      },
    });
    res.status(200).json({ success: true, auto_approval: false });
    return;
  }

  await twilioClient.trusthub.v1.trustProducts(trustProduct.sid).update({
    status: "pending-review",
  });

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      messagingStatus: "pending-trust-review",
    },
  });

  res.status(200).json({ success: true, auto_approval: true });
};

export default POST;
