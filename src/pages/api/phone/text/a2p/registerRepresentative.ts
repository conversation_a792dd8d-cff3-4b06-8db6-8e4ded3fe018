import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { formatPhoneNumber, getTwilioForSubAccount } from "~/server/lib/twilio";
import { db } from "~/server/db";
import {
  A2P_REP_REQUEST_SCHEMA,
  A2PRepRegistrationRequest,
} from "~/components/phone/register/PhoneA2PRepresentative";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone.a2p",
      action: "write",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({ error: "Phone account not found" });
    return;
  }

  let body: A2PRepRegistrationRequest | undefined;
  try {
    body = A2P_REP_REQUEST_SCHEMA.validateSync(req.body);
  } catch (e) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const twilioClient = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );
  const prefix = process.env.NODE_ENV === "production" ? "" : "staging-";
  // Start the A2P messaging process
  const customerProfileList =
    await twilioClient.trusthub.v1.customerProfiles.list({
      friendlyName: `${prefix}${permissions.accountId.toString()}`,
      limit: 1,
    });

  if (!customerProfileList) {
    res.status(500).json({ error: "Failed to find customer profile" });
    return;
  }

  const customerProfile = customerProfileList[0];

  if (!customerProfile) {
    res.status(500).json({ error: "Failed to get customer profile" });
    return;
  }

  const endUser = await twilioClient.trusthub.v1.endUsers.create({
    attributes: {
      job_position: body.job_position,
      last_name: body.last_name,
      phone_number: formatPhoneNumber(body.phone_number),
      first_name: body.first_name,
      email: body.email,
      business_title: body.business_title,
    },
    friendlyName: `${prefix}${permissions.accountId.toString()}-authorized-representative-1`,
    type: "authorized_representative_1",
  });

  if (!endUser) {
    res.status(500).json({ error: "Failed to create end user" });
    return;
  }

  await twilioClient.trusthub.v1
    .customerProfiles(customerProfile.sid)
    .customerProfilesEntityAssignments.create({
      objectSid: endUser.sid,
    });

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      messagingStatus: "representative_registered",
    },
  });

  res.status(200).json({ success: true });
};

export default POST;
