import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { getTwilioForSubAccount } from "~/server/lib/twilio";
import { db } from "~/server/db";
import {
  A2P_REQUEST_SCHEMA,
  A2PRegistrationRequest,
} from "~/components/phone/register/PhoneA2PFirst";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone.a2p",
      action: "write",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({ error: "Phone account not found" });
    return;
  }

  let body: A2PRegistrationRequest | undefined;
  try {
    body = A2P_REQUEST_SCHEMA.validateSync(req.body);
  } catch (e) {
    res.status(400).json({ error: "Invalid request" });
    return;
  }

  const twilioClient = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );

  const prefix = process.env.NODE_ENV === "production" ? "" : "staging-";
  // Start the A2P messaging process
  const customerProfile =
    await twilioClient.trusthub.v1.customerProfiles.create({
      friendlyName: `${prefix}${permissions.accountId.toString()}`,
      email: "<EMAIL>",
      policySid: "RNdfbf3fae0e1107f8aded0e7cead80bf5",
    });

  const endUser = await twilioClient.trusthub.v1.endUsers.create({
    attributes: {
      business_identity: "direct_customer",
      business_industry: "CONSUMER",
      business_name: body.business_name,
      business_registration_identifier: "EIN",
      business_registration_number: body.business_registration_number,
      business_regions_of_operation: body.business_regions_of_operation,
      business_type: body.business_type,
      social_media_profile_urls: body.social_media_profile_urls,
      website_url: body.website_url,
    },
    friendlyName: `${prefix}${permissions.accountId.toString()}`,
    type: "customer_profile_business_information",
  });

  if (!endUser) {
    res.status(500).json({ error: "Failed to create end user" });
    return;
  }

  await twilioClient.trusthub.v1
    .customerProfiles(customerProfile.sid)
    .customerProfilesEntityAssignments.create({
      objectSid: endUser.sid,
    });

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      messagingStatus: "draft",
    },
  });

  res.status(200).json({ success: true });
};

export default POST;
