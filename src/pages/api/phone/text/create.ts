import { checkPermission } from "~/pages/api/permissions";
import { NextApiRequest, NextApiResponse } from "next";
import { formatPhoneNumber, getTwilioForSubAccount } from "~/server/lib/twilio";
import { db } from "~/server/db";
import { getOrCreatePhoneContact } from "~/pages/api/backend/twilio/sms";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "write",
    },
  });

  if (!permissions) {
    return;
  }

  // check for phoneAccount
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({
      error: "Phone account not found",
    });
    return;
  }

  if (!phoneAccount.phoneNumber) {
    res.status(400).json({
      error: "Phone number not found",
    });
    return;
  }

  if (!phoneAccount.messagingApproved) {
    res.status(400).json({
      error: "Messaging not approved",
    });
  }

  const { phoneNumber, body } = req.body;

  if (!phoneNumber) {
    res.status(400).json({
      error: "Phone number is required",
    });
    return;
  }

  if (!body) {
    res.status(400).json({
      error: "Message is required",
    });
    return;
  }

  if (phoneNumber.length < 10 || phoneNumber.length > 15) {
    res.status(400).json({
      error: "Invalid phone number",
    });
    return;
  }

  if (!/^\+?[1-9]\d{1,14}$/.test(phoneNumber)) {
    res.status(400).json({
      error: "Invalid phone number",
    });
    return;
  }

  const formattedPhoneNumber = formatPhoneNumber(phoneNumber);

  // find or get phone contact
  const phoneContact = await getOrCreatePhoneContact(
    phoneNumber,
    permissions.accountId,
  );

  const twilio = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );

  const messageResponse = await twilio.messages.create({
    body: body,
    from: phoneAccount.phoneNumber,
    to: formattedPhoneNumber,
  });

  // create message log
  await db.phoneMessageLog.create({
    data: {
      accountId: permissions.accountId,
      phoneContactId: phoneContact.id,
      body: body,
      inbound: false,
      messageSid: messageResponse.sid,
    },
  });

  res.status(200).json({
    id: phoneContact.id,
  });
};

export default POST;
