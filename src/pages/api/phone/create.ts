import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { captureException } from "@sentry/core";
import { getTwilio, getTwilioForSubAccount } from "~/server/lib/twilio";

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "write",
    },
  });

  if (!permission) {
    return;
  }

  const account = await db.phoneAccount.findFirst({
    where: {
      accountId: permission.accountId,
    },
  });

  if (account) {
    res.status(400).json({
      error: "Phone account already exists",
    });
    return;
  }

  const twilioClient = await getTwilio();
  const prefix = process.env.NODE_ENV === "production" ? "" : "staging-";
  const accountResponse = await twilioClient.api.v2010.accounts.create({
    friendlyName: `${prefix}${permission.accountId.toString()}`,
  });

  if (!accountResponse) {
    captureException(new Error(`Failed to create phone account`));
    res.status(500).json({
      error: "Failed to create phone account",
    });
    return;
  }
  const twilio = getTwilioForSubAccount(
    accountResponse.sid,
    accountResponse.authToken,
  );
  const apiKeyForSubAccount = await twilio.newKeys.create({
    friendlyName: permission.accountId.toString(),
  });

  await db.phoneAccount.create({
    data: {
      accountId: permission.accountId,
      accountSid: accountResponse.sid,
      authToken: accountResponse.authToken,
      apiKeySid: apiKeyForSubAccount.sid,
      apiKeySecret: apiKeyForSubAccount.secret,
    },
  });

  res.status(200).json({ success: true });
};

export default POST;
