import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { r2 } from "~/server/lib/r2";
import { GetObjectCommand, NoSuchKey } from "@aws-sdk/client-s3";
import stream, { Readable } from "stream";
import { promisify } from "util";
import { captureException } from "@sentry/core";
import { VOICEMAIL_RECORDINGS_BUCKET } from "~/pages/api/backend/twilio/recording";

const pipeline = promisify(stream.pipeline);

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const { id } = req.query;

  const phoneCall = await db.phoneCallLog.findFirst({
    where: {
      id: Number(id as string),
    },
    include: {
      phoneVoicemail: true,
    },
  });

  if (!phoneCall) {
    res.status(404).json({ error: "Call not found" });
    return;
  }

  if (phoneCall.phoneVoicemail === null) {
    res.status(404).json({ error: "Voicemail not found" });
    return;
  }

  try {
    const item = await r2.send(
      new GetObjectCommand({
        Bucket: VOICEMAIL_RECORDINGS_BUCKET,
        Key: `recordings/${permission.accountId}/${phoneCall.callSid}.mp3`,
      }),
    );

    if (!item?.Body) {
      res.status(404).json({ error: "Voicemail not found" });
      return;
    }
    if (item.Body instanceof Readable) {
      res.setHeader("Content-Type", "audio/mpeg");
      if (item.ContentLength) {
        res.setHeader("Content-Length", item.ContentLength);
      }
      await pipeline(item.Body, res);
    } else {
      res.status(500).json({ message: "Unexpected response type" });
    }
  } catch (error) {
    if (error instanceof NoSuchKey) {
      res.status(404).json({ error: "Voicemail not found" });
      return;
    }

    console.log(error);
    captureException(error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export default GET;
