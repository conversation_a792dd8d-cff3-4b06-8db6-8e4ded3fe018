import { NextApiRequest, NextApiResponse } from "next";
import TwilioSDK from "twilio";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { getTwilioForSubAccount } from "~/server/lib/twilio";
import AccessToken = TwilioSDK.jwt.AccessToken;

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permissions = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.phone",
      action: "read",
    },
  });

  if (!permissions) {
    return;
  }

  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: permissions.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(404).json({ error: "Phone account not found" });
    return;
  }

  const twilio = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );
  let apiKeySid = phoneAccount.apiKeySid;
  let apiKeySecret = phoneAccount.apiKeySecret;
  if (!apiKeySid || !apiKeySecret) {
    const apiKeyForSubAccount = await twilio.newKeys.create({
      friendlyName: permissions.accountId.toString(),
    });
    await db.phoneAccount.update({
      where: {
        id: phoneAccount.id,
      },
      data: {
        apiKeySid: apiKeyForSubAccount.sid,
        apiKeySecret: apiKeyForSubAccount.secret,
      },
    });
    apiKeySid = apiKeyForSubAccount.sid;
    apiKeySecret = apiKeyForSubAccount.secret;
  }

  const accessToken = new AccessToken(
    phoneAccount.accountSid,
    apiKeySid,
    apiKeySecret,
    {
      identity: permissions.accountId.toString(),
    },
  );

  const appSid = await twilio.applications.list({
    friendlyName: "Twilio App",
  });

  if (appSid.length === 0) {
    console.error("Twilio App not found");
    res.status(404).json({ error: "Twilio App not found" });
    return;
  }

  const voiceGrant = new AccessToken.VoiceGrant({
    outgoingApplicationSid: appSid[0]?.sid,
    incomingAllow: true,
  });

  accessToken.addGrant(voiceGrant);

  res.status(200).json({
    identity: accessToken.identity,
    token: accessToken.toJwt(),
  });
};

export default GET;
