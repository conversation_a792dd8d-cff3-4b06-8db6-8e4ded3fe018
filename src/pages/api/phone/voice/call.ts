import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { getTwilioForSubAccount } from "~/server/lib/twilio";
import { db } from "~/server/db";
import { object, string } from "yup";
import { formatStandardPhone } from "~/server/lib/phone";

export const OutboundCallSchema = object().shape({
  phoneNumber: string().required("Phone number is required"),
});

export type OutboundCall = typeof OutboundCallSchema.__outputType;

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: req.accountId,
    },
  });

  if (!phoneAccount?.phoneNumber) {
    res.status(404).json({ error: "Phone account not found" });
    return;
  }

  const twilio = getTwilioForSubAccount(
    phoneAccount.accountSid,
    phoneAccount.authToken,
  );

  if (!twilio) {
    res.status(500).json({ error: "Twilio not enabled!" });
    return;
  }

  const staff = await db.staff.findFirst({
    where: {
      id: req.staffId,
    },
  });

  if (!staff?.phoneNumber) {
    res.status(400).json({ error: "User does not have a phone number" });
    return;
  }

  const twiml = `
  <Response>
  <Gather timeout="10" action="${
    process.env.NEXT_PUBLIC_SELF_URL
  }/api/backend/twilio/human-detection?phoneNumber=${formatStandardPhone(
    req.body.phoneNumber,
  )}" numDigits="1" method="GET"> 
  <Pause length="2" />
  <Say>Press any key to be connected to the customer.</Say>
   </Gather>
  <Say>No key detected. Goodbye.</Say>
</Response>`;

  await twilio.calls.create({
    to: staff?.phoneNumber,
    from: phoneAccount.phoneNumber,
    twiml: twiml,
  });

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    bypass: true,
  },
  withMethods(["POST"], withValidation(OutboundCallSchema, handler)),
);
