import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { PhoneSettings } from "~/query/phone";
import { db } from "~/server/db";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: req.accountId,
    },
  });

  if (!phoneAccount) {
    return res.status(404).json({ error: "Account not found" });
  }

  const settings: PhoneSettings = {
    mobileClickToCall: phoneAccount.mobileClickToCall,
    voicemailText: phoneAccount.voicemailText ?? "",
    showCallerId: phoneAccount.showCallerId,
    forwardIncomingCallsToMobile: phoneAccount.forwardIncomingCallsToMobile,
    forwardIncomingCallsTo: phoneAccount.forwardIncomingCallsTo,
    forwardIncomingMessagesToMobile: phoneAccount.forwardMessagesToMobile,
    forwardIncomingMessagesTo: phoneAccount.forwardMessagesTo,
    forwardIncomingMessagesToSlack:
      phoneAccount.forwardMessagesToSlackWebhookUrl !== null,
    slackWebhookUrl: phoneAccount.forwardMessagesToSlackWebhookUrl ?? "",
    slackChannelName: phoneAccount.slackChannel ?? "",
  };

  return res.status(200).json({ settings });
};

export default withPermissions(
  { policy: "api.phone.settings", action: "read" },
  handler,
);
