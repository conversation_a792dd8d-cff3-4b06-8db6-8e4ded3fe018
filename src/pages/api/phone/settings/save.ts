import {
  AuthorizedNextApiRequest,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { PhoneSettings, PhoneSettingsSchema } from "~/query/phone";
import { formatPhoneNumber } from "~/server/lib/twilio";

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: req.accountId,
    },
  });

  if (!phoneAccount) {
    return res.status(400).json({ error: "Account not found" });
  }

  const settings: PhoneSettings = req.body;

  await db.phoneAccount.update({
    where: {
      id: phoneAccount.id,
    },
    data: {
      voicemailText: settings.voicemailText,
      showCallerId: settings.showCallerId,
      forwardIncomingCallsToMobile: settings.forwardIncomingCallsToMobile,
      mobileClickToCall: settings.mobileClickToCall,
      forwardMessagesToSlackWebhookUrl:
        settings.forwardIncomingMessagesToSlack && settings.slackWebhookUrl
          ? settings.slackWebhookUrl
          : null,
      slackChannel: settings.slackChannelName,
      forwardIncomingCallsTo:
        settings.forwardIncomingCallsTo?.map((item) => {
          return formatPhoneNumber(item);
        }) ?? [],
      forwardMessagesToMobile: settings.forwardIncomingMessagesToMobile,
      forwardMessagesTo:
        settings.forwardIncomingMessagesTo?.map((item) => {
          return formatPhoneNumber(item);
        }) ?? [],
    },
  });

  return res.status(200).json({ status: "success" });
};

export default withPermissions(
  { policy: "api.phone.settings", action: "read" },
  withValidation(PhoneSettingsSchema, handler),
);
