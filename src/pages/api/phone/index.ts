import { NextApiResponse } from "next";
import { db } from "~/server/db";
import twilio from "twilio";
import { getTwilioForSubAccount } from "~/server/lib/twilio";
import { AuthorizedNextApiRequest, withPermissions } from "../apiMiddleware";

export type PhoneResponse = {
  enabled: boolean;
  number: string | null;
  messagingStatus: string | null;
  mobileClickToCall: boolean;
  currentUserPhoneNumber: string | null;
};

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const phoneAccount = await db.phoneAccount.findFirst({
    where: {
      accountId: req.accountId,
    },
  });

  if (!phoneAccount) {
    res.status(400).json({ error: "Phone account not found" });
    return;
  }

  const staff = await db.staff.findFirst({
    where: {
      id: req.staffId,
    },
  });

  if (!staff?.phoneNumber) {
    res.status(400).json({ error: "User does not have a phone number" });
    return;
  }

  const response: PhoneResponse = {
    enabled: !!phoneAccount,
    number: phoneAccount?.phoneNumber || null,
    messagingStatus: phoneAccount?.messagingStatus || null,
    mobileClickToCall: phoneAccount?.mobileClickToCall || false,
    currentUserPhoneNumber: staff?.phoneNumber || null,
  };

  if (phoneAccount?.messagingStatus) {
    const twilioClient = getTwilioForSubAccount(
      phoneAccount.accountSid,
      phoneAccount.authToken,
    );
    const numberList =
      await twilioClient.messaging.v1.brandRegistrations.list();
    if (numberList.length > 0) {
      const brand = numberList[0];
      const mappedStatus = {
        APPROVED: "approved",
        FAILED: "rejected",
        DELETED: "rejected",
        PENDING: null,
        IN_REVIEW: null,
      };
      if (brand?.status !== undefined && mappedStatus[brand?.status] !== null) {
        await db.phoneAccount.update({
          where: {
            id: phoneAccount.id,
          },
          data: {
            messagingApproved: mappedStatus[brand?.status] === "approved",
            messagingStatus: mappedStatus[brand?.status],
          },
        });
        response.messagingStatus = mappedStatus[brand?.status];
      }
    }
  }

  if (phoneAccount && !phoneAccount?.phoneNumber) {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;

    const twilioClient = twilio(accountSid, process.env.TWILIO_AUTH_TOKEN);
    const numberList = await twilioClient.api.v2010.accounts
      .get(phoneAccount.accountSid)
      .incomingPhoneNumbers.list();
    if (numberList.length > 0 && numberList[0]?.phoneNumber) {
      await db.phoneAccount.update({
        where: {
          id: phoneAccount.id,
        },
        data: {
          phoneNumber: numberList[0].phoneNumber,
        },
      });

      const twiml_app = await twilioClient.applications.create({
        friendlyName: "Twilio App",
        voiceUrl:
          "https://dash.partyrentalplatform.com/api/backend/twilio/voice",
        voiceMethod: "POST",
        smsUrl: "https://dash.partyrentalplatform.com/api/backend/twilio/sms",
        smsMethod: "POST",
      });
      await twilioClient.api.v2010
        .accounts(phoneAccount.accountSid)
        .incomingPhoneNumbers(numberList[0].sid)
        .update({
          voiceApplicationSid: twiml_app.sid,
          smsApplicationSid: twiml_app.sid,
        });
      response.number = numberList[0].phoneNumber;
    }
  }

  res.status(200).json(response);
};

export default withPermissions(
  {
    policy: "api.phone",
    action: "read",
  },
  GET,
);
