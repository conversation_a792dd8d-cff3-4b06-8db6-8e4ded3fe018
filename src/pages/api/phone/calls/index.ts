import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { displayPrettyPhone } from "~/server/lib/phone";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

export type RecentCall = {
  id: number;
  answered: boolean;
  incoming: boolean;
  duration: number;
  phoneNumber: string;
  name: string;
  createdAt: string;
  conversationSid: number;
};

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const phoneContacts = await db.phoneCallLog.findMany({
    where: {
      accountId: req.accountId,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 60,
    include: {
      phoneContact: {
        include: {
          customer: true,
        },
      },
    },
  });

  const responseList: RecentCall[] = [];
  for (const call of phoneContacts) {
    const phoneNumber = displayPrettyPhone(call.phoneContact.phoneNumber);
    let conversationName = phoneNumber;
    if (call?.phoneContact?.customerId && call?.phoneContact.customer) {
      const customer = call?.phoneContact.customer;
      conversationName = `${customer.firstName} ${customer.lastName}`;
    }

    responseList.push({
      phoneNumber: phoneNumber,
      name: conversationName,
      incoming: call.inbound,
      answered: call.answered,
      duration: call.duration ?? 0,
      createdAt: new Date(call.createdAt.getTime())?.toISOString() || "",
      id: call.id,
      conversationSid: call.phoneContact.id,
    });
  }

  res.status(200).json({
    calls: responseList,
  });
};

export default withPermissions(
  {
    policy: "api.phone",
    action: "read",
  },
  GET,
);
