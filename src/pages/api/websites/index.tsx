import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";
import { JsonValue } from "@prisma/client/runtime/library";
import { SystemPages } from "~/pages/website";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.website",
      action: "read",
    },
  });

  if (!permission) {
    return null;
  }

  // check if we have system pages generated, if not generate them then return the home page content

  const homePage = await db.websitePage.findFirst({
    where: {
      accountId: permission.accountId,
      slug: "/",
    },
  });
  let homePageContent: JsonValue | undefined;

  if (!homePage) {
    await createSystemPages(permission.accountId);
    homePageContent = {};
  } else {
    homePageContent = homePage.content;
  }

  const totalPages = await db.websitePage.findMany({
    where: {
      accountId: permission.accountId,
    },
    select: {
      name: true,
      slug: true,
      systemPage: true,
      published: true,
    },
  });

  return res.status(200).json({ page: homePageContent, totalPages });
};

export const createSystemPages = async (accountId: number) => {
  await db.websitePage.createMany({
    data: SystemPages.map((page) => ({
      accountId: accountId,
      name: page.name,
      slug: page.slug,
      content: {},
      systemPage: true,
      published: true,
    })),
  });
};

export default GET;
