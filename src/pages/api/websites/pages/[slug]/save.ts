import { NextApiResponse } from "next";
import { boolean, object } from "yup";
import { db } from "~/server/db";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { updateWebsitePage } from "~/server/lib/website";
import { revalidateTag } from "next/cache";

export const WEBSITE_SAVE_SCHEMA = object().shape({
  data: object().required(),
  published: boolean().required(),
});

type WebsiteSaveBody = typeof WEBSITE_SAVE_SCHEMA.__outputType;

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const body: WebsiteSaveBody = req.body;

  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({ error: "Missing page slug" });
  }

  const page = await db.websitePage.findFirst({
    where: {
      accountId: req.accountId,
      slug: slug as string,
    },
  });

  if (!page) {
    return res.status(404).json({ error: "Page not found" });
  }

  const websitePage = await db.websitePage.update({
    where: {
      accountId: req.accountId,
      id: page.id,
    },
    data: {
      content: body.data,
      published: body.published,
    },
    include: {
      account: true,
    },
  });

  if (websitePage.account?.customDomain) {
    await updateWebsitePage(
      websitePage.account.customDomain,
      slug as string,
      body.data as any,
      websitePage.account,
    );
  }

  try {
    // invalidate the cache
    await fetch("https://simple-cron.ib4est123.workers.dev/", {
      method: "GET",
    });

    revalidateTag(`page-${req.accountId}`);
  } catch (e) {}

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "write",
  },
  withMethods(
    ["POST"],
    withValidation(WEBSITE_SAVE_SCHEMA, handler, { stripUnknown: false }),
  ),
);
