import { db } from "~/server/db";
import { NextApiResponse } from "next";
import { unstable_cache } from "next/cache";
import { JsonValue } from "@prisma/client/runtime/library";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

export const getPageContent = async (
  accountId: number,
  slug: string,
): Promise<{ content: JsonValue } | null> => {
  const websitePage = await unstable_cache(
    async () => {
      return db.websitePage.findFirst({
        where: {
          accountId: accountId,
          slug: slug,
          published: true,
        },
        select: {
          content: true,
        },
      });
    },
    [`website-${slug}-${accountId}`],
    {
      revalidate: 60 * 60,
      tags: [`page-${slug}`, `page-${accountId}`],
    },
  )();

  if (!websitePage) {
    return null;
  }

  return websitePage;
};

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({ error: "Missing page slug" });
  }

  const websitePage = await db.websitePage.findFirst({
    where: {
      accountId: req.accountId,
      slug: slug as string,
    },
    select: {
      content: true,
    },
  });

  if (!websitePage) {
    return res.status(404).json({ error: "Page not found" });
  }

  return res.status(200).json({ page: websitePage.content });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "read",
  },
  GET,
);
