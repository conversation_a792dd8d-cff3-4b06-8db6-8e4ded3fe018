import { NextApiResponse } from "next";
import { db } from "~/server/db";
import { object, string } from "yup";
import slugify from "slugify";
import { updateWebsite, updateWebsitePage } from "~/server/lib/website";
import { getConciseAddress } from "~/server/lib/location/types";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";

export const WEBSITE_CREATE_SCHEMA = object().shape({
  name: string().required(),
});

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const slug = slugify(req.body.name, { lower: true });

  try {
    const account = await db.$transaction(async (tx) => {
      const page = await tx.websitePage.findFirst({
        where: {
          accountId: req.accountId,
          slug: slug as string,
        },
      });

      if (page) {
        throw new Error("Page with that name already exists.");
      }

      await tx.websitePage.create({
        data: {
          accountId: req.accountId,
          name: req.body.name,
          slug: slug,
          content: {},
          systemPage: false,
          published: true,
        },
      });

      return tx.account.findFirst({
        where: {
          id: req.accountId,
        },
        include: {
          billingAddress: true,
        },
      });
    });

    if (account) {
      await Promise.all([
        updateWebsite(
          {
            ...account,
            address: account.billingAddress
              ? getConciseAddress(account.billingAddress)
              : "",
          },
          account.customDomain ?? "",
        ),
        updateWebsitePage(account.customDomain ?? "", slug, {} as any, account),
      ]);
    }

    return res.status(200).json({
      success: true,
      page: {
        name: req.body.name,
        slug: slug,
        systemPage: false,
        published: true,
      },
    });
  } catch (ex) {
    if (ex instanceof Error) {
      return res.status(400).json({ error: ex.message });
    }
    throw ex;
  }
};

export default withPermissions(
  {
    policy: "api.website",
    action: "write",
  },
  withValidation(WEBSITE_CREATE_SCHEMA, withMethods(["POST"], POST)),
);
