import { NextApiRequest, NextApiResponse } from "next";
import { checkPermission } from "~/pages/api/permissions";
import { db } from "~/server/db";

const GET = async (req: NextApiRequest, res: NextApiResponse) => {
  const permission = await checkPermission({
    req,
    res,
    permission: {
      policy: "api.staff.others",
      action: "read",
    },
  });

  if (!permission) {
    return;
  }

  const settings = await db.websiteSettings.findFirst({
    where: {
      accountId: permission.accountId,
    },
  });

  res.status(200).json({
    postHogId: settings?.postHogId ?? null,
    googleAnalytics: settings?.googleAnalyticsId ?? null,
    afterHead: settings?.afterHeader ?? null,
    afterBody: settings?.afterBody ?? null,
    settings: settings?.settings ?? null,
  });
};

export default GET;
