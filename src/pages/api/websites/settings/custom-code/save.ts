import { NextApiResponse } from "next";
import { WebsiteCustomCodeSchema } from "~/form/WebsiteCustomCodeForm";
import { db } from "~/server/db";
import { updateWebsite } from "~/server/lib/website";
import { getConciseAddress } from "~/server/lib/location/types";
import {
  AuthorizedNextApiRequest,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";

const POST = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const data = req.body;

  const settings = await db.websiteSettings.upsert({
    where: {
      accountId: req.accountId,
    },
    create: {
      accountId: req.accountId,
      afterBody: data.afterBody,
      afterHeader: data.afterHead,
      postHogId: data.postHogId,
      googleAnalyticsId: data.googleAnalytics,
      settings: data.settings,
    },
    update: {
      afterBody: data.afterBody,
      afterHeader: data.afterHead,
      postHogId: data.postHogId,
      googleAnalyticsId: data.googleAnalytics,
      settings: data.settings,
    },
    include: {
      account: {
        include: {
          billingAddress: true,
        },
      },
    },
  });

  const account = settings.account;

  if (account) {
    await updateWebsite(
      {
        ...account,
        address: account.billingAddress
          ? getConciseAddress(account.billingAddress)
          : "",
      },
      account.customDomain ?? "",
    );
  }

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "write",
  },
  withValidation(WebsiteCustomCodeSchema, POST),
);
