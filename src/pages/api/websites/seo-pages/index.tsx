import { NextApiResponse } from "next";
import { db } from "~/server/db";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const totalPages = await db.simpleWebsitePage.findMany({
    where: {
      accountId: req.accountId,
    },
    select: {
      name: true,
      slug: true,
    },
  });

  return res.status(200).json({ pages: totalPages });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "read",
  },
  GET,
);
