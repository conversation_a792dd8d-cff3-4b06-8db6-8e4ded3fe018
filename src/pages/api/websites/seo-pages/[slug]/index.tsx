import { db } from "~/server/db";
import { NextApiResponse } from "next";
import {
  AuthorizedNextApiRequest,
  withPermissions,
} from "~/pages/api/apiMiddleware";

const GET = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({ error: "Missing page slug" });
  }

  const websitePage = await db.simpleWebsitePage.findFirst({
    where: {
      accountId: req.accountId,
      slug: slug as string,
    },
  });

  if (!websitePage) {
    return res.status(404).json({ error: "Page not found" });
  }

  return res.status(200).json({
    name: websitePage.name,
    slug: websitePage.slug,
    beforeContent: websitePage.beforeContent,
    afterContent: websitePage.afterContent,
    metaTitle: websitePage.metaTitle,
    metaDescription: websitePage.metaDescription,
  });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "read",
  },
  GET,
);
