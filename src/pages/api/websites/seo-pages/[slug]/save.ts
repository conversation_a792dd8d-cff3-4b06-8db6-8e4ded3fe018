import { NextApiResponse } from "next";
import { object, string } from "yup";
import { db } from "~/server/db";
import {
  AuthorizedNextApiRequest,
  withMethods,
  withPermissions,
  withValidation,
} from "~/pages/api/apiMiddleware";
import { updateSimplePage } from "~/server/lib/website";

export const SEO_PAGE_SAVE_SCHEMA = object().shape({
  name: string().required(),
  beforeContent: string().required(),
  afterContent: string().required(),
  metaTitle: string().required(),
  metaDescription: string().required(),
});

type WebsiteSaveBody = typeof SEO_PAGE_SAVE_SCHEMA.__outputType;

export const handleSeoPage = async (
  accountId: number,
  body: WebsiteSaveBody,
  slug: string,
) => {
  const page = await db.simpleWebsitePage.findFirst({
    where: {
      accountId: accountId,
      slug: slug as string,
    },
    include: {
      account: true,
    },
  });

  let simpleWebPage;
  if (!page) {
    simpleWebPage = await db.simpleWebsitePage.create({
      data: {
        metaTitle: body.metaTitle,
        metaDescription: body.metaDescription,
        accountId: accountId,
        slug: slug as string,
        name: body.name,
        beforeContent: body.beforeContent,
        afterContent: body.afterContent,
      },
    });
  } else {
    simpleWebPage = await db.simpleWebsitePage.update({
      where: {
        id: page.id,
      },
      data: {
        metaTitle: body.metaTitle,
        metaDescription: body.metaDescription,
        name: body.name,
        beforeContent: body.beforeContent,
        afterContent: body.afterContent,
      },
    });
  }

  if (page?.account?.customDomain) {
    await updateSimplePage({
      domain: page.account.customDomain,
      slug: slug as string,
      beforeContent: simpleWebPage.beforeContent,
      description: simpleWebPage.metaDescription,
      title: simpleWebPage.metaTitle,
      afterContent: simpleWebPage?.afterContent ?? undefined,
      account: page.account,
    });
  }
};

const handler = async (req: AuthorizedNextApiRequest, res: NextApiResponse) => {
  const body: WebsiteSaveBody = req.body;

  const { slug } = req.query;

  if (!slug) {
    return res.status(400).json({ error: "Missing page slug" });
  }
  await handleSeoPage(req.accountId, body, slug as string);

  res.status(200).json({ success: true });
};

export default withPermissions(
  {
    policy: "api.website",
    action: "write",
  },
  withMethods(
    ["POST"],
    withValidation(SEO_PAGE_SAVE_SCHEMA, handler, { stripUnknown: false }),
  ),
);
