import React, { useContext, useEffect } from "react";
import { PhoneContext } from "~/providers/phone/PhoneProvider";
import PhoneUpsellPage from "~/components/phone/PhoneUpsellPage";
import Page from "~/components/Page";
import PhoneA2PRepresentative from "~/components/phone/register/PhoneA2PRepresentative";
import PhoneA2pAddress from "~/components/phone/register/PhoneA2PAddress";
import PhoneA2PFirst from "~/components/phone/register/PhoneA2PFirst";
import PhoneA2PStatus from "~/components/phone/register/PhoneA2PStatus";
import { redirect } from "next/navigation";

const A2pRegistrationPage = () => {
  const { phoneInfo, isMessagingEnabled } = useContext(PhoneContext);
  const [messagingServiceStatus, setMessagingServiceStatus] = React.useState<
    string | null
  >(phoneInfo?.messagingStatus ?? null);

  useEffect(() => {
    if (messagingServiceStatus === null && phoneInfo) {
      setMessagingServiceStatus(phoneInfo.messagingStatus);
    }
  }, [phoneInfo]);

  if (!phoneInfo?.enabled) {
    return <PhoneUpsellPage />;
  }

  if (isMessagingEnabled()) {
    return redirect("/phone");
  }

  if (messagingServiceStatus === "draft") {
    return (
      <Page title={"Customer Communications"}>
        <PhoneA2PRepresentative
          enabled={phoneInfo?.enabled}
          messagingStatus={phoneInfo?.messagingStatus}
          next={() => {
            setMessagingServiceStatus("representative_registered");
          }}
        />
      </Page>
    );
  }

  if (messagingServiceStatus === "representative_registered") {
    return (
      <Page title={"Customer Communications"}>
        <PhoneA2pAddress
          enabled={phoneInfo?.enabled}
          messagingStatus={phoneInfo?.messagingStatus}
          next={() => {
            setMessagingServiceStatus("pending-review");
            fetch("/api/phone/text/a2p/registerBrand", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }).then((res) => {
              if (res.ok) {
                setMessagingServiceStatus("brand-approval");
              }
            });
          }}
        />
      </Page>
    );
  }

  if (messagingServiceStatus === null) {
    return (
      <Page title={"Customer Communications"}>
        <PhoneA2PFirst
          enabled={phoneInfo?.enabled}
          messagingStatus={phoneInfo?.messagingStatus}
          next={() => {
            setMessagingServiceStatus("draft");
          }}
        />
      </Page>
    );
  } else if (
    messagingServiceStatus !== "success" &&
    messagingServiceStatus !== "approved"
  ) {
    return (
      <Page title={"Customer Communications"}>
        <PhoneA2PStatus
          enabled={phoneInfo?.enabled}
          messagingStatus={phoneInfo?.messagingStatus}
        />
      </Page>
    );
  }
};

export default A2pRegistrationPage;
