import Page from "~/components/Page";
import { But<PERSON> } from "~/components/ui/button";
import React, { useContext, useEffect, useRef, useState } from "react";
import ConversationMessage from "~/components/phone/ConversationMessage";
import PhoneUpsellPage from "~/components/phone/PhoneUpsellPage";
import { toast } from "sonner";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "~/components/ui/resizable";
import { PhoneContext } from "~/providers/phone/PhoneProvider";
import NewConversationBox from "~/components/phone/NewConversationBox";
import { useRouter } from "next/router";
import CustomerInfo from "~/components/phone/customer/CustomerInfo";
import AddCustomerCard from "~/components/phone/customer/AddCustomerCard";
import ConversationHeader from "~/components/phone/ConversationHeader";
import {
  usePhoneConversation,
  useRecentPhoneConversations,
  useSendTextMessage,
} from "~/query/phone";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
} from "~/components/ui/sidebar";
import { cn } from "~/lib/utils";
import TextMessageInputForm from "~/components/phone/TextMessageInputForm";
import { Phone, Plus } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import PhoneListTile from "~/components/phone/sidebar/PhoneListTile";
import { useMediaQuery } from "~/lib/use-media-query";
import {
  LocalTabs,
  LocalTabsContent,
  LocalTabsList,
  LocalTabsTrigger,
} from "~/components/ui/local-tabs";
import CallView from "~/components/phone/CallView";

export type MessageHistory = {
  id: number;
  message: string;
  mediaUrl?: string;
  voicemail?: Voicemail;
  time: Date;
  from: "customer" | "agent";
  call?: boolean;
};

export type Voicemail = {
  transcription?: string;
  url: number;
};

const PhonePage = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { phoneInfo, openOutboundCall, isMessagingEnabled } =
    useContext(PhoneContext);
  const [startNewConversation, setStartNewConversation] =
    useState<boolean>(false);
  const [selectedConversation, setSelectedConversation] = React.useState<
    number | undefined
  >(undefined);
  const [selectedTab, setSelectedTab] = useState<"messages" | "calls">(
    "messages",
  );
  const { data } = usePhoneConversation(selectedConversation);
  const sendMessageMutation = useSendTextMessage();
  const recentConversations = useRecentPhoneConversations();
  const endMessageRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (router.query.phoneNumber) {
    }
    if (router.query.conversationId) {
      const conversationId = Number(router.query.conversationId);
      setSelectedConversation(conversationId);
    }
  }, [router.query]);

  const onClickConversation = (conversationId: number) => {
    setStartNewConversation(false);
    setSelectedConversation(conversationId);
  };

  const sendMessage = async (
    conversationId: number,
    message: string,
  ): Promise<boolean> => {
    if (!isMessagingEnabled()) {
      return false;
    }
    if (message === "") {
      toast.error("Message cannot be empty");
      return false;
    }
    const resp = await sendMessageMutation.mutateAsync({
      conversationId: conversationId.toString(),
      message: message,
    });

    return !resp?.error;
  };

  if (phoneInfo === undefined) {
    return null;
  }

  if (!phoneInfo?.enabled) {
    return <PhoneUpsellPage />;
  }

  const getPhoneStatus = () => {
    if (!isMessagingEnabled()) {
      return "Call Only";
    }
    if (phoneInfo?.number) {
      return "Active";
    }
    return "Pending";
  };

  return (
    <Page
      title={"Reach Phone"}
      actionButton={
        isMessagingEnabled()
          ? undefined
          : {
              label: "Enroll In Messages",
              onClick: () => {
                router.push("/phone/a2p-registration");
              },
            }
      }
    >
      <LocalTabs
        onValueChange={(value) => {
          setSelectedTab(value as "messages" | "calls");
        }}
        value={selectedTab}
      >
        <LocalTabsContent value={"messages"}>
          <div
            className={
              "flex flex-col lg:flex-row md:h-[80vh] bg-white rounded-md shadow-sm overflow-hidden border"
            }
          >
            <Sidebar
              variant={"sidebar"}
              collapsible={"none"}
              className={cn("lg:w-1/3 bg-white shadow-sm p-4 border-r w-full", {
                "hidden md:block": selectedConversation !== undefined,
              })}
            >
              <SidebarHeader className="gap-3.5 border-b p-4 coll">
                <div className="flex w-full items-center justify-between">
                  <div>
                    <LocalTabsList>
                      <LocalTabsTrigger value={"messages"}>
                        Messages
                      </LocalTabsTrigger>
                      <LocalTabsTrigger value={"calls"}>Calls</LocalTabsTrigger>
                    </LocalTabsList>

                    <p className={"text-muted-foreground text-xs"}>
                      {`Status: ${getPhoneStatus()}`}
                    </p>
                  </div>
                  <TooltipProvider delayDuration={0}>
                    <div className={"flex flex-row gap-2"}>
                      <Tooltip>
                        <TooltipTrigger asChild={isMessagingEnabled()}>
                          <Button
                            size={"icon"}
                            variant={"outline"}
                            className={"ml-auto rounded-full p-4"}
                            disabled={!isMessagingEnabled()}
                            onClick={() => {
                              // if (!isMessagingEnabled()) {
                              //   openOutboundCall();
                              //   return;
                              // }
                              setSelectedConversation(undefined);
                              setStartNewConversation(true);
                            }}
                          >
                            <Plus />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={10}>
                          {isMessagingEnabled()
                            ? "New message"
                            : "Outbound Messaging is not enabled."}
                        </TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size={"icon"}
                            variant={"outline"}
                            className={"ml-auto rounded-full p-4"}
                            onClick={openOutboundCall}
                          >
                            <Phone />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent sideOffset={10}>
                          New Call
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </TooltipProvider>
                </div>
              </SidebarHeader>
              <SidebarContent>
                <SidebarGroup className="px-0">
                  <SidebarGroupContent
                    className={cn({ hidden: startNewConversation && isMobile })}
                  >
                    {recentConversations?.data?.map((convo) => (
                      <PhoneListTile
                        outbound={false}
                        conversation={convo}
                        onSelectConversation={onClickConversation}
                        isSelected={selectedConversation === convo.id}
                        key={convo.id}
                      />
                    ))}
                  </SidebarGroupContent>
                </SidebarGroup>
              </SidebarContent>
            </Sidebar>
            {startNewConversation && (
              <NewConversationBox
                close={() => {
                  setStartNewConversation(false);
                }}
                selectConversation={(id) => {
                  onClickConversation(id);
                }}
                displayClose={true}
                displayTitle={true}
              />
            )}
            {selectedConversation !== undefined && (
              <>
                <ResizablePanelGroup
                  direction="horizontal"
                  className={"lg:w-2/4 flex flex-col gap-2 h-full"}
                >
                  <ResizablePanel
                    defaultSize={75}
                    minSize={20}
                    className={"p-4"}
                  >
                    <ConversationHeader
                      customerDetails={data?.customerDetails}
                      setSelectedConversation={setSelectedConversation}
                    />
                    <div
                      className={
                        "flex flex-col gap-2 p-4 bg-white shadow-sm h-[48svh] md:h-[75%] overflow-y-auto"
                      }
                    >
                      {data?.messages?.map((conversation, index) => {
                        if (index === data?.messages?.length - 1) {
                          return (
                            <ConversationMessage
                              conversation={conversation}
                              key={`item-${index}`}
                              ref={(item) => {
                                endMessageRef.current = item;
                              }}
                            />
                          );
                        }
                        return (
                          <ConversationMessage
                            conversation={conversation}
                            key={`item-${index}`}
                            ref={(item) => {
                              endMessageRef.current = item;
                            }}
                          />
                        );
                      })}
                    </div>
                    <TextMessageInputForm
                      onSubmit={(message) => {
                        return sendMessage(selectedConversation, message);
                      }}
                      isDisabled={
                        !isMessagingEnabled() || sendMessageMutation.isPending
                      }
                    />
                  </ResizablePanel>
                  <ResizableHandle withHandle className={"hidden md:flex"} />
                  <ResizablePanel
                    defaultSize={25}
                    maxSize={80}
                    minSize={15}
                    collapsible={true}
                    collapsedSize={0}
                    className={"hidden md:block"}
                  >
                    {data?.customerDetails?.customerId ? (
                      <CustomerInfo
                        customerId={data?.customerDetails?.customerId}
                      />
                    ) : (
                      <AddCustomerCard
                        conversationId={selectedConversation}
                        phoneNumber={data?.customerDetails?.phoneNumber}
                      />
                    )}
                  </ResizablePanel>
                </ResizablePanelGroup>
              </>
            )}
          </div>
        </LocalTabsContent>
        <CallView />
      </LocalTabs>
    </Page>
  );
};

export default PhonePage;
