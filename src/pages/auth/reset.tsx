import React from "react";
import Image from "next/image";

const ResetPage = () => {
  return (
    <div className={"content-center justify-center text-center mt-5"}>
      <div className={"flex justify-center"}>
        <Image src={"/wordmark.svg"} alt="Logo" width={400} height={100} />
      </div>
      <p className={"text-2xl"}>Password Reset!</p>
      <p>
        Check your email (including your spam folder) for an email to reset your
        password.
      </p>
    </div>
  );
};

ResetPage.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

ResetPage.auth = false;
export default ResetPage;
