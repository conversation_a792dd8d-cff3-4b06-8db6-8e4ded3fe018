import type {
  GetServerSidePropsContext,
  InferGetServerSidePropsType,
} from "next";
import { getCsrfToken } from "next-auth/react";
import React, { useState } from "react";
import { db } from "~/server/db";
import { captureException } from "@sentry/core";
import { useRouter } from "next/router";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { RESET_PASSWORD_SCHEMA } from "~/pages/api/auth/passwordReset";

function ResetPassword({
  csrfToken,
  accountId,
  staffId,
  token,
  ...props
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);

  if (props.error !== undefined) {
    return <div>Invalid token</div>;
  }

  const handleShowClick = () => setShowPassword(!showPassword);
  return (
    <div
      className={
        "flex flex-col min-w-100wh min-h-100vh w-screen h-screen bg-gray-200 justify-center items-center"
      }
    >
      <div
        className={"flex flex-col mb-2 justify-center items-center space-y-2"}
      >
        <h2 className={"text-2xl font-bold my-3"}>Password Reset</h2>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);
            if (formData.get("password") !== formData.get("confirmPassword")) {
              alert("Passwords do not match");
              return;
            }

            const values: typeof RESET_PASSWORD_SCHEMA.__outputType = {
              password: formData.get("password") as string,
              token: token ?? "",
              accountId: accountId ?? -1,
              staffId: staffId ?? "",
              csrfToken: csrfToken ?? "",
            };

            fetch("/api/auth/passwordReset", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(values),
            })
              .then((response) => {
                console.log(response);
                if (response.ok) {
                  return response.json();
                }
                throw new Error("Network response was not ok.");
              })
              .then((data) => {
                if (data.error) {
                  captureException(data.error);
                  return;
                }
                router.push("/auth/login");
              });
          }}
        >
          <div
            className={
              "flex flex-col min-w-full rounded-md shadow-md p-4 bg-white bg-opacity-90 gap-4"
            }
          >
            <div>
              <Label htmlFor={"password"}>Password</Label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                name={"password"}
              />
            </div>

            <div>
              <Label htmlFor={"confirmPassword"}>Confirm Password</Label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                name={"confirmPassword"}
              />
            </div>

            <Button type="button" className={"mt-2"} onClick={handleShowClick}>
              {showPassword ? "Hide" : "Show"} Password
            </Button>

            <Button type="submit" variant="primary" className={"mt-2"}>
              Reset Password
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

ResetPassword.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

ResetPassword.auth = false;

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { account, token, user } = context.query;

  if (!account || !token || !user) {
    return {
      props: {
        error: "Invalid Token Param",
      },
    };
  }

  if (Array.isArray(account) || Array.isArray(token) || Array.isArray(user)) {
    return {
      props: {
        error: "Invalid token format",
      },
    };
  }

  const accountObj = await db.account.findFirst({
    where: {
      name: account,
    },
  });

  if (!accountObj) {
    return {
      props: {
        error: "Invalid token account",
      },
    };
  }

  const userObj = await db.staff.findFirst({
    where: {
      accountId: accountObj.id,
      name: user,
    },
  });

  if (!userObj) {
    return {
      props: {
        error: "Invalid token user",
      },
    };
  }

  return {
    props: {
      csrfToken: await getCsrfToken(context),
      accountId: accountObj.id,
      staffId: userObj.id,
      token: token,
    },
  };
}

export default ResetPassword;
