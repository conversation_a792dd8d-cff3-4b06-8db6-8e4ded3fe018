import type {
  GetServerSidePropsContext,
  InferGetServerSidePropsType,
} from "next";
import { getCsrfToken } from "next-auth/react";
import { Label } from "~/components/ui/label";
import { Input } from "~/components/ui/input";
import NextLink from "next/link";
import { Button } from "~/components/ui/button";
import React from "react";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/server/auth";
import { cn } from "~/lib/utils";

function Login({
  csrfToken,
  error,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  return (
    <div className="w-screen h-screen grid lg:grid lg:min-h-[600px]">
      <div className="flex items-center justify-center py-12 px-4 md:px-0">
        <div className="mx-auto grid w-[350px] gap-6">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold">Login</h1>
            <p className="text-balance text-muted-foreground">
              Enter your email below to login to your account
            </p>
          </div>
          <form
            className="grid gap-4"
            method="post"
            action="/api/auth/callback/credentials"
          >
            <input name="csrfToken" type="hidden" defaultValue={csrfToken} />
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name={"email"}
                type="email"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <NextLink
                  href="/auth/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                  prefetch={false}
                >
                  Forgot your password?
                </NextLink>
              </div>
              <Input
                id="password"
                type="password"
                name={"password"}
                className={cn({
                  "border-red-500": error,
                })}
                required
              />
              {error && <p className="text-red-500 text-sm">{error}</p>}
            </div>
            <Button
              type="submit"
              variant={"primary"}
              size={"full"}
              className={"mt-2"}
            >
              Login
            </Button>
          </form>
          <div className="mt-4 text-center text-sm">
            Don't have an account?{" "}
            <NextLink
              href="https://dash.partyrentalplatform.com/public/signup"
              className="underline"
              prefetch={false}
            >
              Get Started
            </NextLink>
          </div>
        </div>
      </div>
      {/*<div className="hidden bg-muted lg:block">*/}
      {/*  <Image*/}
      {/*    src="/wordmark.svg"*/}
      {/*    alt="Image"*/}
      {/*    width="1920"*/}
      {/*    height="1080"*/}
      {/*    className="h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"*/}
      {/*  />*/}
      {/*</div>*/}
    </div>
  );
}

Login.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

Login.auth = false;

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (session) {
    let callbackUrl = "/";
    if (context?.query?.callbackUrl) {
      callbackUrl = decodeURIComponent(context.query.callbackUrl as string);
    }
    return {
      redirect: {
        destination: callbackUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      csrfToken: await getCsrfToken(context),
      error: context.query.error ?? null,
    },
  };
}

export default Login;
