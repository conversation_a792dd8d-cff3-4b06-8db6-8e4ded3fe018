import type {
  GetServerSidePropsContext,
  InferGetServerSidePropsType,
} from "next";
import { getCsrfToken } from "next-auth/react";
import React from "react";
import { captureException } from "@sentry/core";
import { useRouter } from "next/router";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { FORGOT_PASSWORD_SCHEMA } from "~/pages/api/auth/forgotPassword";

function ForgotPassword({
  csrfToken,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
  const router = useRouter();

  return (
    <div
      className={
        "flex flex-col min-w-100wh min-h-100vh w-screen h-screen bg-gray-200 justify-center items-center"
      }
    >
      <div
        className={"flex flex-col mb-2 justify-center items-center space-y-2"}
      >
        <h2 className={"text-2xl font-bold my-3"}>Password Reset</h2>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.currentTarget);

            const values: typeof FORGOT_PASSWORD_SCHEMA.__outputType = {
              email: formData.get("email") as string,
            };

            fetch("/api/auth/forgotPassword", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(values),
            })
              .then((response) => {
                if (response.ok) {
                  return response.json();
                }
                throw new Error("Network response was not ok.");
              })
              .then((data) => {
                if (data.error) {
                  captureException(data.error);
                  return;
                }
                router.push("/auth/reset");
              });
          }}
        >
          <div
            className={
              "flex flex-col min-w-full rounded-md shadow-md p-4 bg-white bg-opacity-90 gap-4"
            }
          >
            <div>
              <Label htmlFor={"email"}>Email</Label>
              <Input type={"email"} placeholder="Email" name={"email"} />
            </div>

            <Button type="submit" variant="primary" className={"mt-2"}>
              Submit
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

ForgotPassword.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

ForgotPassword.auth = false;

export async function getServerSideProps(context: GetServerSidePropsContext) {
  return {
    props: {
      csrfToken: await getCsrfToken(context),
    },
  };
}

export default ForgotPassword;
