import { Button } from "~/components/ui/button";
import React from "react";
import { signOut } from "next-auth/react";

const Logout = () => {
  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <Button
        onClick={() => {
          signOut();
          window.location.href = "/";
        }}
      >
        Sign out
      </Button>
    </div>
  );
};

Logout.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};
Logout.auth = false;
export default Logout;
