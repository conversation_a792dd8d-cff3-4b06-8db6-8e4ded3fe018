import React from "react";
import Page from "~/components/Page";
import { DataTable } from "~/components/ui/datatable/data-table";
import { ProductColumns } from "~/table/ProductTable/columns";
import { useAllProducts } from "~/query/product";
import { useAccountSwitcher } from "~/providers/TeamProvider";

const ProductListPage = () => {
  const { data, isPending } = useAllProducts();
  const team = useAccountSwitcher();

  return (
    <Page
      title={"Products"}
      actionButton={{
        label: "Create Product",
        newItem: true,
        permission: "api.product.write",
        href: "/products/create",
      }}
    >
      <DataTable
        columns={ProductColumns()}
        data={
          data?.filter((prod) => prod.accountId === team.currentTeam?.id) || []
        }
        isLoading={isPending}
        linkTo={(row) => {
          return `/products/${row.id}/edit`;
        }}
      />
    </Page>
  );
};

export default ProductListPage;
