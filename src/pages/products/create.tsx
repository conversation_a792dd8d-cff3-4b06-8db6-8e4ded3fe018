import Page from "~/components/Page";
import { ProductSchemaType } from "~/server/product/types";
import ProductForm from "~/form/ProductForm";
import { toast } from "sonner";
import { useRouter } from "next/router";
import { useCategories } from "~/query/category";

const ProductCreate = () => {
  const router = useRouter();
  const categories = useCategories();
  const attachImage = async (productId: string, image: string) => {
    const request = await fetch(`/api/products/${productId}/images/attach`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ imageId: image, priority: 0 }),
    });
    if (request.ok) {
      return request.json();
    }
    return null;
  };

  const onSubmit = async (
    values: ProductSchemaType,
    imagesToAttach: string[],
  ) => {
    const request = fetch("/api/products/create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(request, {
      loading: "Creating product...",
      success: (data) => {
        data.json().then((data) => {
          const promises = imagesToAttach.map((image) =>
            attachImage(data.id, image),
          );
          Promise.all(promises).then(() => {
            router.push("/products");
          });
        });

        return "Successfully created product";
      },
      error: "Error creating product",
    });
  };

  return (
    <Page title={"Create Product"}>
      <ProductForm
        onSubmit={onSubmit}
        categoryOptions={
          categories.data?.map((category) => ({
            name: category.name,
            id: category.id,
          })) || []
        }
        productId={null}
      />
    </Page>
  );
};

export default ProductCreate;
