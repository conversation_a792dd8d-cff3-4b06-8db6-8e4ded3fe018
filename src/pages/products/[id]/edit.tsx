import Page from "~/components/Page";
import { ProductSchemaType } from "~/server/product/types";
import ProductForm from "~/form/ProductForm";
import { toast } from "sonner";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useCategories } from "~/query/category";

const ProductEditPage = () => {
  const router = useRouter();
  const [product, setProduct] = useState<ProductSchemaType | null>(null);
  const { id } = router.query as { id: string };
  const { data } = useCategories();

  useEffect(() => {
    const fetchProduct = async () => {
      const response = await fetch(`/api/products/${id}`);
      const data = await response.json();
      setProduct(data.product);
    };
    void fetchProduct();
  }, [id]);

  if (!product) {
    return <Page title="Loading..." />;
  }

  const onSubmit = async (values: ProductSchemaType) => {
    const request = fetch(`/api/products/${id}/edit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(request, {
      loading: "Editing product...",
      success: () => {
        return "Edit successful!";
      },
      error: (err) => `Error editing product:
${err}`,
    });
  };

  return (
    <Page title={product.name}>
      <ProductForm
        onSubmit={onSubmit}
        categoryOptions={
          data?.map((category) => ({
            name: category.name,
            id: category.id,
          })) || []
        }
        productId={Number(id as string)}
        initialValues={product}
      />
    </Page>
  );
};

export default ProductEditPage;
