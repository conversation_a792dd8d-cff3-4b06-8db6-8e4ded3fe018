import Page from "~/components/Page";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { ChargeType } from ".prisma/client";
import { Badge } from "~/components/ui/badge";
import { DataTable } from "~/components/ui/datatable/data-table";
import React, { useState } from "react";
import { DataTableRowActions } from "~/components/ui/datatable/data-table-row-actions";
import {
  DropdownMenuItem,
  DropdownMenuLabel,
} from "~/components/ui/dropdown-menu";
import CouponForm from "~/form/CouponForm";
import FormDialog from "~/components/Actions/FormDialog";
import { CouponListValues, useCoupons } from "~/query/coupon/query";
import {
  useCreateCouponMutation,
  useDeleteCouponMutation,
  useUpdateCouponMutation,
} from "~/query/coupon/mutation";

const CouponsListPage = () => {
  const coupons = useCoupons();

  const createCoupon = useCreateCouponMutation();
  const updateCoupon = useUpdateCouponMutation();
  const [editCoupon, setEditCoupon] = useState<CouponListValues | null>(null);
  const [newCoupon, setNewCoupon] = useState<boolean>(false);

  return (
    <Page
      title={"Coupons"}
      actionButton={{
        onClick: () => {
          setEditCoupon(null);
          setNewCoupon(true);
        },
        label: "New Coupon",
        newItem: true,
      }}
    >
      <FormDialog
        open={editCoupon != null || newCoupon}
        setOpen={(value) => {
          if (!value) {
            setNewCoupon(false);
            setEditCoupon(null);
          }
        }}
        title={editCoupon ? "Edit Coupon" : "Add Coupon"}
        form={
          <CouponForm
            initialValues={editCoupon ?? undefined}
            onSubmit={(values) => {
              if (editCoupon) {
                updateCoupon.mutate({ id: editCoupon.id, values });
              } else {
                createCoupon.mutate(values);
              }
              setEditCoupon(null);
              setNewCoupon(false);
            }}
          />
        }
      />
      <DataTable
        columns={[
          {
            accessorKey: "name",
            header: ({ column }) => {
              return <DataTableColumnHeader column={column} title={"Code"} />;
            },
            cell: ({ row }) => {
              return <div>{row.getValue("name")}</div>;
            },
          },
          {
            accessorKey: "status",
            header: ({ column }) => {
              return <DataTableColumnHeader column={column} title={"Status"} />;
            },
            cell: ({ row }) => {
              const status = row.original.status;
              if (status.toLowerCase() === "active") {
                return <Badge variant={"default"}>Active</Badge>;
              }
              if (status.toLowerCase() === "pending") {
                return <Badge variant={"secondary"}>Pending</Badge>;
              }
              return <Badge variant={"destructive"}>{status}</Badge>;
            },
          },
          {
            accessorKey: "usageCount",
            header: ({ column }) => {
              return (
                <DataTableColumnHeader column={column} title={"Usage Count"} />
              );
            },
            cell: ({ row }) => {
              return (
                <span>{`Used ${row.original.usageCount.toString()} times`}</span>
              );
            },
          },
          {
            accessorKey: "displayName",
            header: ({ column }) => {
              return (
                <DataTableColumnHeader column={column} title={"Display Name"} />
              );
            },
            cell: ({ row }) => {
              return <div>{row.getValue("displayName")}</div>;
            },
          },
          {
            accessorKey: "discount",
            header: ({ column }) => {
              return (
                <DataTableColumnHeader column={column} title={"Discount"} />
              );
            },
            cell: ({ row }) => {
              const discount = row.original.discount.toString();

              return (
                <div>{`${
                  row.original.discountType === ChargeType.DOLLAR.toString()
                    ? "-$"
                    : ""
                }${discount}${
                  row.original.discountType === ChargeType.DOLLAR.toString()
                    ? ""
                    : "%"
                }`}</div>
              );
            },
          },
          {
            accessorKey: "actions",
            header: () => <div className={"w-0"}></div>,
            cell: ({ row }) => {
              const deleteCoupon = useDeleteCouponMutation();
              if (row.original.status.toLowerCase() === "deleted") {
                return (
                  <DataTableRowActions>
                    <DropdownMenuLabel>No Actions</DropdownMenuLabel>
                  </DataTableRowActions>
                );
              }
              return (
                <DataTableRowActions
                  remove={{
                    id: row.original.id.toString(),
                    name: row.original.name,
                    onDelete: (id) => {
                      deleteCoupon.mutate(Number(id));
                    },
                  }}
                >
                  <DropdownMenuItem
                    onClick={() => {
                      setTimeout(() => {
                        setEditCoupon(row.original);
                      }, 50);
                    }}
                  >
                    Edit
                  </DropdownMenuItem>
                </DataTableRowActions>
              );
            },
          },
        ]}
        data={coupons?.data || []}
        isLoading={coupons.isPending}
      />
    </Page>
  );
};

export default CouponsListPage;
