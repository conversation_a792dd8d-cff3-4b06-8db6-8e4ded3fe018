import { useEmails } from "~/query/email";
import Page from "~/components/Page";
import { EmailColumns } from "~/table/EmailTable/columns";
import { DataTable } from "~/components/ui/datatable/data-table";
import React from "react";
import { useRouter } from "next/router";

import EmailAutomations from "~/components/email/EmailAutomations";

const EmailListPage = () => {
  const { data, isPending } = useEmails();
  const router = useRouter();

  // Check if we're on the automations tab
  const isAutomationsTab = router.query.tab === "automations";

  return (
    <Page
      title={"Emails"}
      actionButton={{
        label: "New Email",
        href: "/email/create",
        newItem: true,
      }}
    >
      {isAutomationsTab ? (
        <EmailAutomations />
      ) : (
        <DataTable
          data={data || []}
          isLoading={isPending}
          linkTo={(row) => `/email/${row.id}/`}
          responsive={{
            gridAreas: "'id actions' 'name createdAt' 'subject subject'",
          }}
          columns={EmailColumns()}
        />
      )}
    </Page>
  );
};

export default EmailListPage;
