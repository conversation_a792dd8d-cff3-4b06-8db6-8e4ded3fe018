import { useEmails } from "~/query/email";
import Page from "~/components/Page";
import { EmailColumns } from "~/table/EmailTable/columns";
import { DataTable } from "~/components/ui/datatable/data-table";
import React from "react";

import EmailAutomations from "~/components/email/EmailAutomations";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";

const EmailListPage = () => {
  const { data, isPending } = useEmails();

  return (
    <Page
      title={"Emails"}
      actionButton={{
        label: "New Email",
        href: "/email/create",
        newItem: true,
      }}
    >
      <Tabs defaultValue={"templates"}>
        <TabsList>
          <TabsTrigger value={"templates"}>Templates</TabsTrigger>
          <TabsTrigger value={"automations"}>Automations</TabsTrigger>
        </TabsList>
        <TabsContent value={"templates"}>
          <DataTable
            data={data || []}
            isLoading={isPending}
            linkTo={(row) => `/email/${row.id}/`}
            responsive={{
              gridAreas: "'id actions' 'name createdAt' 'subject subject'",
            }}
            columns={EmailColumns()}
          />
        </TabsContent>
        <EmailAutomations />
      </Tabs>
    </Page>
  );
};

export default EmailListPage;
