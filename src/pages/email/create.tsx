import { useCreateEmail } from "~/query/email";
import Page from "~/components/Page";
import EmailForm from "~/form/EmailForm";
import { useRouter } from "next/router";
import { Button } from "~/components/ui/button";
import React from "react";

const CreateEmailPage = () => {
  const createEmail = useCreateEmail();
  const router = useRouter();

  return (
    <Page title={"Create Email"}>
      <EmailForm
        onSubmit={(values) => {
          createEmail.mutate(values, {
            onSuccess: () => {
              router.push("/email");
            },
          });
        }}
      />
      <Button
        type={"submit"}
        variant={"primary"}
        form={"email-form"}
        className={"mt-2"}
      >
        Create Email
      </Button>
    </Page>
  );
};

export default CreateEmailPage;
