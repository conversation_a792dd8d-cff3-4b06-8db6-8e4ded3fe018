import { useRouter } from "next/router";
import { useEmail, useUpdateEmail } from "~/query/email";
import EmailForm from "~/form/EmailForm";
import { Button } from "~/components/ui/button";
import Page from "~/components/Page";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import FormCard from "~/components/FormLayout/FormCard";
import FormLayout from "~/components/FormLayout";
import React from "react";
import Skeleton from "react-loading-skeleton";

const EditEmailPage = () => {
  const router = useRouter();
  const { data, isLoading } = useEmail(Number(router.query.id));
  const updateEmail = useUpdateEmail();

  return (
    <Page title={`Edit ${data?.name || ""} Email`}>
      {!isLoading && (
        <EmailForm
          initialValues={data}
          onSubmit={(values) => {
            updateEmail.mutate({
              email: values,
              emailId: Number(router.query.id),
            });
          }}
        />
      )}
      {isLoading && (
        <FormLayout>
          <FormLayoutSection style={"full"}>
            <FormCard>
              <Skeleton count={4} />
            </FormCard>
            <FormCard>
              <Skeleton count={3} />
            </FormCard>
          </FormLayoutSection>
        </FormLayout>
      )}
      <Button
        variant={"primary"}
        type={"submit"}
        form={"email-form"}
        className={"mt-2"}
      >
        Save Email
      </Button>
    </Page>
  );
};

export default EditEmailPage;
