import { ColumnDef } from "@tanstack/react-table";
import React, { useMemo, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import { Checkbox } from "~/components/ui/checkbox";
import DeleteDialog from "~/components/Actions/DeleteDialog";
import * as Sentry from "@sentry/react";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { toast } from "sonner";
import { DataTable } from "~/components/ui/datatable/data-table";
import Page from "~/components/Page";
import FormDialog from "~/components/Actions/FormDialog";
import { getNextPlanLevel, PlanLevel } from "~/server/lib/entitlement/types";
import { useSession } from "next-auth/react";
import TemporaryPasswordDialog from "~/components/Staff/TemporaryPasswordDialog";
import StaffForm, { StaffValues } from "~/form/form";
import { captureEvent } from "@sentry/core";
import { useFeatureFlagEnabled } from "posthog-js/react";
import { STAFF_QUERY, useStaff } from "~/query/staff/query";
import { useQueryClient } from "@tanstack/react-query";

type StaffUser = StaffValues & {
  id: string;
};

export const StaffByPlan: Record<PlanLevel, number> = {
  [PlanLevel.Free]: 1,
  [PlanLevel.Lite]: 1,
  [PlanLevel.Core]: 5,
  [PlanLevel.Pro]: 15,
  [PlanLevel.Unlimited]: 100,
};

const StaffListPage = () => {
  const bypassStaffLimit = useFeatureFlagEnabled("bypass_staff_limit");
  const { isLoading, data } = useStaff();
  const queryClient = useQueryClient();
  const [tempPasswordOpen, setTempPasswordOpen] = useState(false);
  const [temporaryPassword, setTemporaryPassword] = useState<string | null>(
    null,
  );
  const [removeUser, setRemoveUser] = useState<StaffUser | null>(null);
  const [editedUser, setEditedUser] = useState<StaffUser | null>(null);
  const [open, setOpen] = useState(false);
  const session = useSession();

  const columns: ColumnDef<StaffUser>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Email
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className={"flex-col"}>
            <div className={"flex-row font-bold"}>{row.original.name}</div>
            <div>{row.getValue("email")}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "role",
      header: ({ column }) => {
        return <DataTableColumnHeader column={column} title={"Role"} />;
      },
      cell: ({ row }) => {
        return (
          <div className={"flex-row"}>
            <div className={"font-bold"}>{row.original.role || "No Role"}</div>
          </div>
        );
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  setOpen(true);
                  setEditedUser(row.original);
                }}
              >
                Edit User
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  resetPassword(row.original);
                }}
              >
                Reset Password
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  createTemporaryPassword(row.original);
                }}
              >
                Create Temporary Password
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setRemoveUser(row.original);
                }}
              >
                Delete User
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const submitForm = async (values: StaffValues) => {
    let request;
    if (editedUser) {
      request = fetch(`/api/staff/${editedUser?.id}/edit`, {
        method: "POST",
        body: JSON.stringify(values),
        headers: new Headers({
          "Content-Type": "application/json",
        }),
      });
    } else {
      request = fetch(`/api/staff/create`, {
        method: "POST",
        body: JSON.stringify(values),
        headers: new Headers({
          "Content-Type": "application/json",
        }),
      });
    }
    setEditedUser(null);
    setOpen(false);
    toast.promise(request, {
      loading: "Saving...",
      success: () => {
        queryClient.invalidateQueries({ queryKey: [STAFF_QUERY] });
        return "Staff saved.";
      },
      error: "Failed to save staff.",
    });
  };

  const deleteStaff = async () => {
    toast.promise(
      new Promise((resolve) => {
        resolve(
          fetch(`/api/staff/${removeUser?.id}/delete`, {
            method: "DELETE",
          }),
        );
      }),
      {
        loading: "Deleting Staff...",
        success: () => {
          queryClient.invalidateQueries({ queryKey: [STAFF_QUERY] });
          return "Staff deleted.";
        },
        error: "Failed to delete staff.",
      },
    );
  };

  const createTemporaryPassword = async (values: StaffValues) => {
    const find = data?.staff?.find((user) => user.email === values.email);
    if (!find) {
      captureEvent({
        message: "User not found",
        extra: {
          values,
        },
      });
      console.error("User not found");
      return;
    }
    const request = fetch(`/api/staff/${find.id}/createTemporaryPassword`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    toast.promise(request, {
      loading: "Creating temporary password...",
      success: (response) => {
        response.json().then((data) => {
          setTemporaryPassword(data.tempPassword);
          setTempPasswordOpen(true);
        });
        return "Opening temporary password...";
      },
      error: "Failed to create temporary password, try again.",
    });
  };

  const resetPassword = async (values: StaffValues) => {
    const find = data?.staff?.find((user) => user.email === values.email);
    if (!find) {
      Sentry.captureEvent({
        message: "User not found",
        extra: {
          values,
        },
      });
      console.error("User not found");
      return;
    }
    const request = await fetch(`/api/staff/${find.id}/resetPassword`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    const response = await request.json();
    if (response.success) {
      toast.success("Password reset successfully.");
    } else {
      toast.error("Failed to reset password, try again.");
    }
  };

  const minimumPlanLevel = useMemo(() => {
    const currentStaff = data?.staff?.length || 0;
    const maxStaff = bypassStaffLimit
      ? 1000
      : StaffByPlan[session.data?.user?.planLevel || PlanLevel.Free];
    // if we're at max return the next plan
    if (currentStaff >= maxStaff) {
      return getNextPlanLevel(session.data?.user?.planLevel ?? PlanLevel.Free);
    }
    return PlanLevel.Core;
  }, [session.data?.user?.planLevel, data]);

  return (
    <Page
      title={"Staff"}
      actionButton={{
        minimumPlanLevel: minimumPlanLevel,
        label: "New Staff",
        newItem: true,
        onClick: () => {
          setOpen(true);
          setEditedUser(null);
        },
      }}
    >
      <TemporaryPasswordDialog
        open={tempPasswordOpen}
        onClose={() => {
          setTempPasswordOpen(false);
          setTemporaryPassword(null);
        }}
        temporaryPassword={temporaryPassword ?? undefined}
      />
      <DeleteDialog
        open={removeUser !== null}
        onOpenChange={(open) => {
          if (!open) {
            setRemoveUser(null);
          }
        }}
        onDelete={() => {
          deleteStaff();
        }}
        item={removeUser?.name ?? ""}
      />
      <FormDialog
        open={open}
        setOpen={setOpen}
        title={editedUser ? `Edit ${editedUser?.name}` : "Create New User"}
        form={
          <StaffForm
            roles={data?.roles || []}
            onResetPassword={async (values) => {
              resetPassword(values);
            }}
            onSubmit={submitForm}
            self={false}
            initialValues={editedUser ?? undefined}
          />
        }
      />
      <DataTable
        columns={columns}
        data={data?.staff || []}
        isLoading={isLoading}
      />
    </Page>
  );
};

export default StaffListPage;
