import React, { useState } from "react";
import { useRouter } from "next/router";
import Page from "~/components/Page";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import OrderForm, { OrderFormSchema } from "~/form/OrderForm";
import OrderSideBar from "~/components/Order/OrderSideBar";
import { toast } from "sonner";
import { useAccountData } from "~/query/account/query";

const OrderStartPage = () => {
  const router = useRouter();
  const accountData = useAccountData();
  const [internalNotes, setInternalNotes] = useState<string | undefined>(
    undefined,
  );

  const onSubmit = async (
    values: OrderFormSchema,
    sendNotification: boolean,
  ) => {
    const {
      startDate,
      endDate,
      eventLocation,
      products,
      damageWaiver,
      taxRate,
    } = values;

    startDate.setSeconds(0);
    endDate.setSeconds(0);
    startDate.setMilliseconds(0);
    endDate.setMilliseconds(0);

    const submitPayload = {
      customerId: values.customerId,
      eventStartTime: startDate.toISOString(),
      eventEndTime: endDate.toISOString(),
      location: eventLocation,
      products: products.map((product) => {
        return {
          id: product.id,
          quantity: product.quantity,
          pricePaid: product.price,
        };
      }),
      fees: values.fees,
      taxRate,
      damageWaiverApplied: damageWaiver ?? false,
      setupSurface: values.setupSurface,
      couponCode: null,
      internalNotes: internalNotes,
      customerNotes: null,
      sendNotification: sendNotification,
      taxExempt: values.taxExempt,
    };
    const request = fetch(`/api/orders/create`, {
      method: "POST",
      body: JSON.stringify(submitPayload),
      headers: {
        "Content-Type": "application/json",
      },
    });
    toast.promise(request, {
      loading: "Creating New Order...",
      success: (response) => {
        response.json().then(() => {
          router.push(`/orders/`);
        });
        return `Order Created${
          submitPayload.sendNotification ? "" : " Silently"
        }!`;
      },
      error: "Failed to create order. Please try again.",
    });
  };

  return (
    <Page title={`Create Order`}>
      <FormLayout>
        <FormLayoutSection>
          <OrderForm
            onSubmit={onSubmit}
            damageWaiverPercentage={
              accountData?.data?.settings?.damageWaiverRate || 0
            }
          />
        </FormLayoutSection>
        <OrderSideBar
          onInternalNoteChange={(value) => {
            setInternalNotes(value);
          }}
        />
      </FormLayout>
    </Page>
  );
};

export default OrderStartPage;
