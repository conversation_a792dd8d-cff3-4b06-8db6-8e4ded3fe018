import { useRouter } from "next/router";
import React, { useC<PERSON>back, useMemo, useState } from "react";
import { useOrdersByDay } from "~/query/order";
import { useAllProducts } from "~/query/product";
import { useMediaQuery } from "~/lib/use-media-query";
import { OrderWithCustomerAndPaymentsAndAddress } from "~/pages/api/orders/day";
import PacklistMobileOrderCard from "~/components/Order/packlist/PacklistMobileOrderCard";
import { PacklistDeliverySummary } from "~/components/Order/packlist/PacklistDeliverySummary";
import SearchFilterBar from "~/components/Order/packlist/SearchFilterBar";
import { getConciseAddress } from "~/server/lib/location/types";
import Page from "~/components/Page";
import PacklistOrderView from "~/components/Order/packlist/PacklistOrderView";
import { Separator } from "~/components/ui/separator";
import { OrderState } from "@prisma/client";
import OrderCalendarPop from "~/components/Order/OrderCalendarPop";
import { AlertCircle, CheckCircle, XCircle } from "lucide-react";

const OrderPackList = () => {
  const router = useRouter();
  const specificDate = router.query.date;
  const [currentDate, setCurrentDate] = useState<Date>(() => {
    if (!specificDate) {
      return new Date();
    }
    return new Date(specificDate as string);
  });
  const { data, isPending } = useOrdersByDay(currentDate);
  const products = useAllProducts();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  // const [activeFilter, setActiveFilter] = useState(null); // Commented out category filter
  const [packedItems, setPackedItems] = useState<Record<string, boolean>>({});
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Item packing functionality
  const toggleItemPacked = (orderId: number, itemId: number) => {
    const key = `${orderId}-${itemId}`;
    setPackedItems((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const isItemPacked = useCallback(
    (orderId: number, itemId: number) => {
      const key = `${orderId}-${itemId}`;
      return packedItems[key] || false;
    },
    [packedItems],
  );

  // Filter orders based on search term
  const filteredOrders = useMemo(
    (): OrderWithCustomerAndPaymentsAndAddress[] =>
      data
        ?.filter((order: OrderWithCustomerAndPaymentsAndAddress) => {
          if (!searchTerm) return true;

          const searchLower = searchTerm.toLowerCase();

          // Match by order ID
          const matchesOrderId = order.id.toString().includes(searchLower);

          // Match by customer name, email or company
          const matchesCustomer =
            `${order.Customer.firstName} ${order.Customer.lastName}`
              .toLowerCase()
              .includes(searchLower) ||
            order.Customer.email.toLowerCase().includes(searchLower) ||
            (order.Customer.company &&
              order.Customer.company.toLowerCase().includes(searchLower));

          // Match by address
          const matchesAddress = getConciseAddress(order.eventAddress)
            .toLowerCase()
            .includes(searchLower);

          return matchesOrderId || matchesCustomer || matchesAddress;
        })
        ?.sort((order, orderB) => {
          if (order.state !== OrderState.ACTIVE) {
            return 1;
          }
          if (orderB.state !== OrderState.ACTIVE) {
            return -1;
          }
          return (
            new Date(order.startTime).getTime() -
            new Date(orderB.startTime).getTime()
          );
        }) || [],
    [data, searchTerm],
  );

  // Mobile view render
  if (isMobile) {
    return (
      <Page
        title={"Packing List"}
        actionButton={{
          label: "Print",
          onClick: () => window.print(),
        }}
      >
        <div className={"mb-2"}>
          <OrderCalendarPop
            loading={isPending}
            currentDate={currentDate}
            setCurrentDate={(date) => {
              router.replace(
                `/orders/packlist/${encodeURIComponent(
                  date.toLocaleDateString(),
                )}`,
              );
              setCurrentDate(date);
            }}
          />
        </div>
        <SearchFilterBar onSearch={setSearchTerm} />

        {/* Main content */}
        {isPending ? (
          <div className="bg-white border border-gray-100 rounded-lg shadow-sm mb-3 overflow-hidden">
            <div className="p-3">
              <div className="flex justify-between items-center">
                <div className="w-full">
                  <div className="h-4 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                </div>
                <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse ml-2"></div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="space-y-3 mt-3 pb-16">
              {filteredOrders.length > 0 ? (
                filteredOrders.map(
                  (order: OrderWithCustomerAndPaymentsAndAddress) => (
                    <PacklistMobileOrderCard
                      setupSurface={order.setupSurface.name}
                      customer={`${order.Customer.firstName} ${order.Customer.lastName}`}
                      address={getConciseAddress(order.eventAddress)}
                      items={order.OrderProduct.map((item) => {
                        const product = products?.data?.find(
                          (p) => p.id === item.productId,
                        );
                        return {
                          id: item.productId,
                          quantity: item.quantity,
                          name: product?.name || "Unknown Product",
                        };
                      })}
                      key={order.id}
                      startTime={new Date(order.startTime).toISOString()}
                      endTime={new Date(order.endTime).toISOString()}
                      isItemPacked={isItemPacked}
                      toggleItemPacked={toggleItemPacked}
                      id={order.id}
                    />
                  ),
                )
              ) : (
                <div className="p-4 text-center text-gray-500 bg-white rounded-lg shadow-sm border border-gray-100">
                  No orders found matching your search.
                </div>
              )}
            </div>
          </>
        )}

        {/* Print footer */}
        <div className="hidden print:block text-center text-xs text-gray-500 p-4 border-t mt-8">
          Printed on {new Date().toLocaleDateString()} • Party Rental Platform
          Packing List
        </div>
      </Page>
    );
  }

  // Desktop view render
  return (
    <Page
      title={"Packing List"}
      actionButton={{
        label: "Print",
        onClick: () => window.print(),
      }}
    >
      <div className={"mb-2"}>
        <OrderCalendarPop
          loading={isPending}
          currentDate={currentDate}
          setCurrentDate={(date) => {
            router.replace(
              `/orders/packlist/${encodeURIComponent(
                date.toLocaleDateString(),
              )}`,
            );
            setCurrentDate(date);
          }}
        />
      </div>
      <PacklistDeliverySummary
        loading={isPending}
        orders={filteredOrders?.map(
          (item: OrderWithCustomerAndPaymentsAndAddress) => {
            return {
              items: item.OrderProduct.map((item) => {
                return {
                  quantity: item.quantity,
                };
              }),
              id: item.id,
              startTime: new Date(item.startTime).toISOString(),
              endTime: new Date(item.endTime).toISOString(),
            };
          },
        )}
      />
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 print:shadow-none print:border print:border-gray-200">
        <div className="p-4 border-b border-gray-100 print:p-4 print:hidden flex justify-between items-center">
          <p className="text-sm text-gray-500">Delivery Schedule</p>
        </div>
        {/* Main content based on view mode */}
        {isPending && (
          <div className="bg-white border border-gray-100 rounded-lg shadow-sm mb-3 overflow-hidden">
            <div className="p-3">
              <div className="flex justify-between items-center">
                <div className="w-full">
                  <div className="h-4 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                </div>
                <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse ml-2"></div>
              </div>
            </div>
          </div>
        )}
        {filteredOrders?.map(
          (item: OrderWithCustomerAndPaymentsAndAddress, index) => {
            return (
              <div className={"border-b border-gray-100"} key={item.id}>
                <PacklistOrderView
                  order={item}
                  products={products?.data || []}
                  key={item.id}
                  isItemPacked={isItemPacked}
                  toggleItemPacked={toggleItemPacked}
                />
              </div>
            );
          },
        )}
        <div className="p-4 border-t border-gray-100 hidden print:block">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-xs font-medium mb-2">Payment Status:</h3>
              <div className="flex flex-wrap gap-2">
                <div className="flex items-center text-xs border rounded px-2 py-1 bg-green-50 text-green-700 border-green-100">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span>Paid</span>
                </div>
                <div className="flex items-center text-xs border rounded px-2 py-1 bg-yellow-50 text-yellow-700 border-yellow-100">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  <span>Partial</span>
                </div>
                <div className="flex items-center text-xs border rounded px-2 py-1 bg-red-50 text-red-700 border-red-100">
                  <XCircle className="h-4 w-4 mr-1" />
                  <span>Unpaid</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Page>
  );
};
export default OrderPackList;
