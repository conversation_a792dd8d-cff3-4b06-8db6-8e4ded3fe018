import React from "react";
import { useRouter } from "next/router";
import Page from "~/components/Page";
import OrderCalendar from "~/components/Calendar/OrderCalendar";
import { startOfMonth } from "date-fns";

const SchedulePage = () => {
  const router = useRouter();

  return (
    <Page title={"Order Calendar"}>
      <OrderCalendar
        onClick={(date) => {
          router.push(
            `/orders/calendar/${encodeURIComponent(date.toLocaleDateString())}`,
          );
        }}
        date={
          router.query?.date
            ? new Date(router.query?.date as string)
            : startOfMonth(new Date())
        }
        onDateChange={(date) => {
          void router.replace(
            `/orders/calendar?date=${encodeURIComponent(
              date.toLocaleDateString(),
            )}`,
            undefined,
            {
              shallow: true,
            },
          );
        }}
      />
    </Page>
  );
};

export default SchedulePage;
