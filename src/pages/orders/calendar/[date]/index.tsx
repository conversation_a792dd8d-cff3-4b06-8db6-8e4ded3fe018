import React, { use<PERSON>emo, useState } from "react";
import Page from "~/components/Page";
import NextLink from "next/link";
import { useRouter } from "next/router";
import { getCurrencyString } from "~/server/lib/currency";
import { cn } from "~/lib/utils";
import { OrderWithCustomerAndPaymentsAndAddress } from "~/pages/api/orders/day";
import { ProductOrderItem } from "~/server/product/types";
import { fullImageUrl } from "~/server/globalTypes";
import Image from "next/image";
import { OrderState } from ".prisma/client";
import { useAllProducts } from "~/query/product";
import { useOrdersByDay } from "~/query/order";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  ClockIcon,
  FileTextIcon,
  GavelIcon,
  HandCoinsIcon,
  MapPinIcon,
  UserIcon,
} from "lucide-react";

import { COMPACT_FORMATTER } from "~/server/lib/time";
import { getConciseAddress } from "~/server/lib/location/types";
import OrderActionsDropdown from "~/table/OrderTable/OrderActionsDropdown";
import OrderCalendarPop from "~/components/Order/OrderCalendarPop";
import AccountLink from "~/components/AccountLink";

const OrderByDay = () => {
  const router = useRouter();
  const specificDate = router.query.date;
  const [currentDate, setCurrentDate] = useState<Date>(() => {
    if (!specificDate) {
      return new Date();
    }
    return new Date(specificDate as string);
  });
  const { data, isPending } = useOrdersByDay(currentDate);
  const products = useAllProducts();
  return (
    <Page
      title={`Orders on ${currentDate.toLocaleDateString()}`}
      subtitle={currentDate.toLocaleDateString()}
      secondaryActions={[
        {
          label: "Route",
          onClick: () => {
            router.push(
              `/orders/driver/calendar?specificDate=${encodeURIComponent(
                currentDate.toLocaleDateString(),
              )}`,
            );
          },
        },
        {
          label: "Print",
          onClick: () => {
            window.print();
          },
        },
      ]}
      actionButton={{
        label: "New order",
        onClick: () => {
          router.push(
            `/orders/start?specificDate=${currentDate.toLocaleDateString()}`,
          );
        },
        newItem: true,
      }}
    >
      <div className={"mb-2"}>
        <OrderCalendarPop
          loading={isPending}
          currentDate={currentDate}
          setCurrentDate={(date) => {
            setCurrentDate(date);
            router.replace(
              `/orders/calendar/${encodeURIComponent(
                date.toLocaleDateString(),
              )}`,
            );
          }}
        />
      </div>
      {data?.length === 0 && !isPending && (
        <div className={"text-center"}>
          <p>No orders found for this day.</p>
        </div>
      )}
      <div className={"grid grid-cols-1 md:grid-cols-2 gap-4"}>
        {data
          ?.sort((order, orderB) => {
            if (order.state !== OrderState.ACTIVE) {
              return 1;
            }
            if (orderB.state !== OrderState.ACTIVE) {
              return -1;
            }
            return (
              new Date(order.startTime).getTime() -
              new Date(orderB.startTime).getTime()
            );
          })
          .map((order) => {
            return (
              <OrderItem
                key={order.id}
                products={products?.data || []}
                order={order}
                date={currentDate}
              />
            );
          })}
      </div>
    </Page>
  );
};

const OrderItem = ({
  order,
  products,
  date,
}: {
  order: OrderWithCustomerAndPaymentsAndAddress;
  products: ProductOrderItem[];
  date: Date;
}) => {
  const router = useRouter();

  const customerName = useMemo(() => {
    let name = order.Customer.firstName;
    if (order.Customer.company) {
      name = `${name} (${order.Customer.company})`;
    } else {
      name = `${name} ${order.Customer.lastName}`;
    }
    return name;
  }, [order.Customer]);
  const paymentSnippetShown = useMemo(() => {
    let snippet = `Owes ${getCurrencyString(order.finalTotal)}`;
    let color = "text-red-500";
    if (order.totalPaid >= order.finalTotal) {
      snippet = `Paid`;
      color = "text-green-500";
    } else if (order.totalPaid > 0) {
      snippet = `Owes ${getCurrencyString(order.finalTotal - order.totalPaid)}`;
      color = "text-yellow-500";
    }
    return [color, snippet];
  }, [order.finalTotal, order.totalPaid]);

  const contractSnippet = useMemo(() => {
    const anySignedContract = order.Contract?.find(
      (contract) => contract.signed,
    );
    if (anySignedContract) {
      return ["text-green-500", "Contract Signed"];
    }
    return ["text-red-500", "No Contract Signed"];
  }, [order.Contract]);

  const { dropOff, pickUp } = useMemo(() => {
    const dropOff = new Date(order.startTime).getDate() === date.getDate();
    const pickUp = new Date(order.endTime).getDate() === date.getDate();
    return { dropOff, pickUp };
  }, [order.startTime, order.endTime, date]);

  return (
    <Card
      className={cn(
        "w-full max-w-4xl",
        order.state === OrderState.COMPLETED ? "bg-green-50" : "",
      )}
    >
      <CardHeader className="flex flex-row items-start sm:items-center justify-between pt-1 px-2 pb-2">
        <CardTitle className="text-xl font-bold hover:underline flex flex-row gap-1 items-center">
          <AccountLink
            accountId={order.accountId}
            href={`/orders/${order.id}`}
            className={"text-primary"}
          >
            Order #{order.id}
          </AccountLink>
          <OrderActionsDropdown order={order} />
        </CardTitle>
        <div className="flex center space-x-1">
          {dropOff && (
            <span className="w-3 h-3 rounded-full bg-green-200"></span>
          )}
          {pickUp && <span className="w-3 h-3 rounded-full bg-red-200"></span>}
          {!pickUp && !dropOff && (
            <span className="w-3 h-3 rounded-full bg-blue-200"></span>
          )}
        </div>
      </CardHeader>
      <CardContent className="px-3 pb-3">
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 justify-between mb-2 ">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-2">
              <UserIcon className="w-4 h-4 text-gray-600" />
              <NextLink
                href={"/customers/[id]/"}
                as={`/customers/${order.Customer.id}/`}
                className={
                  " text-l text-prpDarkback underline sm:no-underline sm:hover:underline"
                }
              >
                {customerName}
              </NextLink>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <MapPinIcon className="w-4 h-4 text-gray-600" />
              <a
                href={`https://maps.google.com/?q=${order.eventAddress?.line1}${
                  order.eventAddress?.line2
                    ? ` ${order.eventAddress?.line2}, `
                    : ""
                }, ${order.eventAddress?.city} ${order.eventAddress
                  ?.state} ${order.eventAddress?.postalCode}`}
                target="_blank"
                rel="noopener noreferrer"
                className=" text-l text-prpDarkBack underline sm:no-underline sm:hover:underline"
              >
                <p>{getConciseAddress(order.eventAddress)}</p>
              </a>
            </div>
          </div>

          <div className="flex flex-col space-y-1">
            <div className="flex items-center space-x-1">
              <HandCoinsIcon className="w-5 h-5 text-gray-600" />
              <span
                className={cn(
                  "text-l text-prpDarkBack cursor-pointer text-right underline sm:no-underline sm:hover:underline",
                  paymentSnippetShown[0],
                )}
                onClick={() => {
                  router.push(
                    `https://dash.partyrentalplatform.com/public/orders/${order.accountId}/${order.id}/${order.customerId}`,
                  );
                }}
              >
                {paymentSnippetShown[1]}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <FileTextIcon className="w-5 h-5 text-gray-600" />
              <span
                className={cn(
                  "text-l text-prpDarkBack cursor-pointer text-right underline sm:no-underline sm:hover:underline",
                  contractSnippet[0],
                )}
                onClick={() => {
                  router.push(
                    `https://dash.partyrentalplatform.com/public/contract/${
                      order.accountId
                    }/${order.id}/${order.Contract?.at(0)?.id}`,
                  );
                }}
              >
                {contractSnippet[1]}
              </span>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between mb-2">
          <div className="flex flex-col space-y-1">
            {dropOff && (
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 text-gray-600 mr-2" />
                <span className="text-gray-800 w-20">Delivery:</span>
                <span className="text-gray-800">
                  {COMPACT_FORMATTER.format(new Date(order.startTime))}
                </span>
              </div>
            )}
            {pickUp && (
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 text-gray-600 mr-2" />
                <span className="text-gray-800 w-20">Pickup:</span>
                <span className="text-gray-800">
                  {COMPACT_FORMATTER.format(new Date(order.endTime))}
                </span>
              </div>
            )}
            {!pickUp && !dropOff && (
              <div className="flex items-center">
                <ClockIcon className="w-5 h-5 text-gray-600 mr-2" />
                <span className="text-gray-800 w-20">Out Until:</span>
                <span className="text-gray-800">
                  {COMPACT_FORMATTER.format(new Date(order.endTime))}
                </span>
              </div>
            )}
            <div className="flex items-center">
              <GavelIcon className="w-5 h-5 text-gray-600 mr-2" />
              <span className="text-gray-800 w-20">Surface:</span>
              <span>{order.setupSurface.name}</span>
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold mb-1">Products</h3>
          <div className="flex space-x-4 max-h-12">
            {products
              .filter((product) => {
                return order.OrderProduct.find((orderProduct) => {
                  return orderProduct.productId === product.id;
                });
              })
              .map((product, index) => {
                const orderProduct = order.OrderProduct.find((orderProduct) => {
                  return orderProduct.productId === product.id;
                });
                return (
                  <div
                    key={`product-pic-${product.id}-${index}`}
                    className={"flex flex-col items-center relative"}
                  >
                    {product.productThumbnail && (
                      <Image
                        src={fullImageUrl(product.productThumbnail)}
                        alt={product.name}
                        height={32}
                        width={48}
                        className="object-center object-cover h-full aspect-ratio-square rounded"
                      />
                    )}
                    {orderProduct?.quantity && orderProduct?.quantity > 1 && (
                      <div
                        className={
                          "rounded-full bg-blue-200 -right-2 -top-2 absolute h-4 w-4 text-xs text-blue-800 flex items-center justify-center"
                        }
                      >
                        {orderProduct?.quantity}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrderByDay;
