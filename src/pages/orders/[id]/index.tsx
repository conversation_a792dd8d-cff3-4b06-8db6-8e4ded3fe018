import React, { useMemo } from "react";
import Page from "~/components/Page";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import OrderSideBar from "~/components/Order/OrderSideBar";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import NextLink from "next/link";
import { Skeleton } from "~/components/ui/skeleton";
import DisplayTimeRange from "~/components/DatePicker/display";
import ProductsDisplayCard from "~/components/Product/ProductsDisplayCard";
import PaymentDetailsCard from "~/components/Order/PaymentDetailsCard";
import { toast } from "sonner";
import { useRouter } from "next/router";
import { Badge } from "~/components/ui/badge";
import { useCustomer } from "~/query/customer";
import { ORDER_QUERY_KEY, useOrder, useOrderSendEmail } from "~/query/order";
import { useOrderPaymentInfo } from "~/query/payments";
import { useQueryClient } from "@tanstack/react-query";
import EmailSelectionModal from "~/components/email/EmailSelectionModal";
import { useAllSurfaces } from "~/query/surface/query";
import { cn } from "~/lib/utils";
import { useAccountData } from "~/query/account/query";
import PhoneNumberDisplay from "~/components/phone/PhoneNumberDisplay";
import EmailDisplay from "~/components/email/EmailDisplay";

export type OrderExtraInfo = {
  internalNote: string | undefined;
  customerNote: string | undefined;
  state: string;
};

const OrderViewPage = () => {
  const accountData = useAccountData();
  const router = useRouter();
  const orderId = Number(router.query.id);
  const surfaces = useAllSurfaces(true);
  const { data: order, isPending: isOrderLoading } = useOrder(orderId);

  const queryClient = useQueryClient();
  const { data: customerInfo, isPending: isCustomerLoading } = useCustomer(
    order?.order.customerId,
  );
  const { data: orderPaymentInfo } = useOrderPaymentInfo(orderId, true);
  const sendEmailMutation = useOrderSendEmail();
  const [isEmailModalOpen, setIsEmailModalOpen] = React.useState(false);

  const setupSurfaceLabel = useMemo(() => {
    if (!surfaces.data || !order?.order?.setupSurface) {
      return <Skeleton className={"w-12"} />;
    }

    const surface = surfaces.data.find(
      (option) => option.id === order.order.setupSurface,
    );

    return (
      <p
        className={cn("text-sm", {
          "text-muted-foreground": surface?.archived === true,
        })}
      >{`${surface?.name || "Unknown"}${
        surface?.archived === true ? " (Deleted)" : ""
      }`}</p>
    );
  }, [surfaces.data, order?.order?.setupSurface]);

  return (
    <Page
      title={`Order #${orderId}`}
      actionButton={{
        label: "Edit",
        href: `/orders/${orderId}/edit`,
      }}
      secondaryActions={[
        {
          label: "Payments",
          href: `/orders/${orderId}/payments`,
        },
      ]}
    >
      <EmailSelectionModal
        open={isEmailModalOpen}
        setOpen={setIsEmailModalOpen}
        onEmailSelected={(templateId) => {
          sendEmailMutation.mutate({
            orderId: orderId,
            templateId,
          });
        }}
      />
      <FormLayout>
        <FormLayoutSection>
          <Card className={"mb-3"}>
            <CardHeader>
              <CardTitle>Event Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={"flex flex-col gap-4 text-left items-start"}>
                <div className={"flex flex-col gap-2"}>
                  <Label>Customer</Label>
                  {customerInfo ? (
                    <Button variant={"link"} size={"link"}>
                      <NextLink
                        href={`/customers/${order?.order?.customerId}/`}
                      >
                        {customerInfo.firstName} {customerInfo.lastName}
                      </NextLink>
                    </Button>
                  ) : (
                    <>
                      <Skeleton className={"h-4 w-20"} />
                      <Skeleton className={"h-4 w-[30px]"} />
                    </>
                  )}
                </div>
                <div className={"flex flex-col gap-2"}>
                  <Label>Contact Info</Label>
                  {!isCustomerLoading && customerInfo ? (
                    <div className={"text-xs flex flex-col"}>
                      <EmailDisplay
                        email={customerInfo.email}
                        onClickSendEmail={() => setIsEmailModalOpen(true)}
                      />

                      {customerInfo?.phoneNumber ? (
                        <PhoneNumberDisplay
                          phoneNumber={customerInfo.phoneNumber}
                        />
                      ) : (
                        <p className={"text-xs text-muted-foreground"}>
                          No Phone Number
                        </p>
                      )}
                    </div>
                  ) : (
                    <>
                      <Skeleton className={"h-4 w-20"} />
                      <Skeleton className={"h-4 w-[30px]"} />
                    </>
                  )}
                </div>

                <div className={"flex flex-col gap-2"}>
                  <Label className={""}>Event Location</Label>
                  {!isOrderLoading ? (
                    <p className={"text-sm"}>
                      {order?.order?.eventLocation.line1}
                      {order?.order?.eventLocation.line2}
                      <br />
                      {order?.order?.eventLocation.city},{" "}
                      {order?.order?.eventLocation.state}{" "}
                      {order?.order?.eventLocation.postalCode}
                    </p>
                  ) : (
                    <Skeleton />
                  )}
                </div>

                <div className={"flex flex-col md:flex-row gap-4"}>
                  <div className={"flex flex-col gap-2"}>
                    <Label>Event Time</Label>
                    {!isOrderLoading && order?.order?.startDate ? (
                      <DisplayTimeRange
                        startTime={order.order.startDate}
                        endTime={order.order.endDate}
                      />
                    ) : (
                      <Skeleton />
                    )}
                  </div>

                  <div className={"flex flex-col gap-2"}>
                    <Label>Setup Surface</Label>
                    {setupSurfaceLabel}
                  </div>

                  <div className={"flex flex-col gap-2"}>
                    <Label>Rental Protection</Label>
                    {!isOrderLoading && order && !accountData.isLoading ? (
                      <p className={"text-sm"}>
                        {order.order.damageWaiver
                          ? `Included ${accountData?.data?.settings?.damageWaiverRate}%`
                          : "Not Included"}
                      </p>
                    ) : (
                      <Skeleton />
                    )}
                  </div>

                  <div className={"flex flex-col gap-2"}>
                    <Label>State</Label>
                    {!isOrderLoading && order ? (
                      <div className={"max-w-fit text-sm"}>
                        <Badge className="text-xs" variant="secondary">
                          {order?.extraInfo?.state || "Pending"}
                        </Badge>
                      </div>
                    ) : (
                      <Skeleton />
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            {!isOrderLoading && order?.order?.products ? (
              <ProductsDisplayCard
                editMode={false}
                selectedProducts={order?.order?.products}
              />
            ) : (
              <Skeleton className={"h-9 w-[100%]"} />
            )}
          </Card>
          {orderPaymentInfo?.paymentInfo ? (
            <PaymentDetailsCard
              due={orderPaymentInfo.due}
              paymentInfo={orderPaymentInfo?.paymentInfo}
              paidItems={orderPaymentInfo.paidItems}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>
                  <Skeleton className={"h-4 w-[40%]"} />
                </CardTitle>
              </CardHeader>
              <CardContent className={"flex flex-col gap-3 px-4 py-2"}>
                <Skeleton className={"h-6 w-[80%]"} />
                <Skeleton className={"h-6 w-[50%]"} />
              </CardContent>
            </Card>
          )}
        </FormLayoutSection>
        <OrderSideBar
          onInternalNoteChange={(value) => {
            toast.promise(
              fetch(`/api/orders/${orderId}/updateNote`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ internalNote: value }),
              }),
              {
                loading: "Updating Internal Note",
                success: () => {
                  void queryClient.invalidateQueries({
                    queryKey: [ORDER_QUERY_KEY, Number(orderId)],
                  });
                  return "Internal Note Updated";
                },
                error: "Failed to update Internal Note",
              },
            );
          }}
          initialValues={
            order?.extraInfo?.internalNote
              ? {
                  internalNote: order?.extraInfo?.internalNote,
                  customerNote: order?.extraInfo?.customerNote ?? undefined,
                }
              : undefined
          }
        />
      </FormLayout>
    </Page>
  );
};

export default OrderViewPage;
