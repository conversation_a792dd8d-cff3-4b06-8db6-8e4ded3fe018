import Page from "~/components/Page";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import OrderForm, { OrderFormSchema } from "~/form/OrderForm";
import React, { useEffect, useState } from "react";
import OrderSideBar from "~/components/Order/OrderSideBar";
import { useRouter } from "next/router";
import { toast } from "sonner";
import { useAccountData } from "~/query/account/query";
import { useOrder } from "~/query/order";
import { useOrderPaymentInfo } from "~/query/payments";

const OrderEditPage = () => {
  const router = useRouter();
  const orderId = router.query.id as string;
  const accountData = useAccountData();

  const { data: order } = useOrder(Number(orderId));
  const orderPaymentInfo = useOrderPaymentInfo(Number(orderId), true);
  const [internalNotes, setInternalNotes] = useState<string | undefined>(
    undefined,
  );

  useEffect(() => {
    setInternalNotes(order?.extraInfo?.internalNote);
  }, [order?.extraInfo?.internalNote]);

  const onSubmit = async (
    values: OrderFormSchema,
    sendNotification: boolean,
  ) => {
    const {
      startDate,
      endDate,
      eventLocation,
      products,
      damageWaiver,
      taxRate,
    } = values;

    startDate.setSeconds(0);
    endDate.setSeconds(0);
    startDate.setMilliseconds(0);
    endDate.setMilliseconds(0);

    const submitPayload = {
      eventStartTime: startDate.toISOString(),
      eventEndTime: endDate.toISOString(),
      location: eventLocation,
      products: products.map((product) => {
        return {
          id: product.id,
          quantity: product.quantity,
          pricePaid: product.price,
        };
      }),
      fees: values.fees,
      taxRate,
      damageWaiverApplied: damageWaiver ?? false,
      setupSurface: values.setupSurface,
      discount: values.discount,
      internalNotes: internalNotes,
      customerNotes: null,
      sendNotification: sendNotification,
      taxExempt: values.taxExempt,
    };
    const request = fetch(`/api/orders/${orderId}/edit`, {
      method: "POST",
      body: JSON.stringify(submitPayload),
      headers: {
        "Content-Type": "application/json",
      },
    });
    toast.promise(request, {
      loading: "Editing Order...",
      success: (response) => {
        response.json().then(() => {
          router.push(`/orders/`);
        });
        return `Order Updated${
          submitPayload.sendNotification ? "" : " Silently"
        }!`;
      },
      error: "Failed to update order. Please try again.",
    });
  };

  return (
    <Page title={`Edit`}>
      <FormLayout>
        <FormLayoutSection>
          <OrderForm
            initialValues={order?.order}
            onSubmit={onSubmit}
            damageWaiverPercentage={
              order?.raw?.damageWaiverRate ||
              accountData?.data?.settings?.damageWaiverRate ||
              0
            }
            paidItems={orderPaymentInfo?.data?.paidItems}
          />
        </FormLayoutSection>
        <OrderSideBar
          onInternalNoteChange={(value) => {
            setInternalNotes(value);
          }}
          initialValues={{
            internalNote: internalNotes,
            customerNote: order?.extraInfo.customerNote ?? undefined,
          }}
        />
      </FormLayout>
    </Page>
  );
};

export default OrderEditPage;
