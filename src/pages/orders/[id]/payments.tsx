import React, { useState } from "react";
import Page from "~/components/Page";
import { DataTable } from "~/components/ui/datatable/data-table";
import { toast } from "sonner";
import PaymentDetailsCard from "~/components/Order/PaymentDetailsCard";
import PayActionModal from "~/components/Order/PayActionModal";
import {
  useOrderPaymentInfo,
  useOrderPayments,
  usePaymentCollection,
} from "~/query/payments";
import { PaymentColumns } from "~/table/PaymentTable/columns";
import { useRouter } from "next/router";
import { useOrderSendReceipt } from "~/query/order";

const OrderPaymentsPage = () => {
  const paymentMutation = usePaymentCollection();
  const orderReceiptMutation = useOrderSendReceipt();
  const router = useRouter();
  const orderId = router.query.id as string;
  const paymentInfo = useOrderPayments(Number(router.query.id as string));
  const basicInfo = useOrderPaymentInfo(Number(orderId), true);
  const [addPaymentPopup, setAddPaymentPopup] = useState(false);

  return (
    <Page
      title={"Payments"}
      secondaryActions={[
        {
          label: "Send Silent Receipt",
          onClick: () => {
            const orderIdNumber = Number(orderId);
            if (!orderIdNumber) {
              toast.error("Invalid Order Id, try again.");
              return;
            }
            orderReceiptMutation.mutate({
              orderId: orderIdNumber,
              silent: true,
              newOrder: false,
            });
          },
        },
        {
          label: "Send Receipt",
          onClick: () => {
            const orderIdNumber = Number(orderId);
            if (!orderIdNumber) {
              toast.error("Invalid Order Id, try again.");
              return;
            }
            orderReceiptMutation.mutate({
              orderId: orderIdNumber,
              silent: false,
              newOrder: false,
            });
          },
        },
      ]}
      actionButton={{
        label: "Add Payment",
        onClick: () => {
          setAddPaymentPopup(true);
        },
      }}
    >
      <PayActionModal
        open={addPaymentPopup}
        onOpenChange={(open) => {
          setAddPaymentPopup(open);
        }}
        amountDue={
          (basicInfo?.data?.paymentInfo?.finalTotal || 0) -
          (basicInfo?.data?.paymentInfo?.totalPaid || 0)
        }
        totalPrice={basicInfo?.data?.paymentInfo?.finalTotal || 0}
        onSubmit={(values) => {
          paymentMutation.mutate({
            orderId: Number(orderId),
            values,
          });
        }}
      />
      <div className={"flex flex-col gap-3"}>
        {basicInfo?.data && (
          <PaymentDetailsCard
            subHeader={
              "Original Order Items. See customer payment details below."
            }
            paymentInfo={basicInfo?.data?.paymentInfo}
            paidItems={basicInfo?.data?.paidItems}
            due={basicInfo?.data?.due}
          />
        )}
        <DataTable columns={PaymentColumns()} data={paymentInfo.data || []} />
      </div>
    </Page>
  );
};

export default OrderPaymentsPage;
