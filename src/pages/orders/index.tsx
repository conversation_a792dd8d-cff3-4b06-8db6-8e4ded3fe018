import React from "react";
import { DataTable } from "~/components/ui/datatable/data-table";
import Page from "~/components/Page";
import { OrderColumns } from "~/table/OrderTable/columns";
import { useAbandonedOrders, useOrders } from "~/query/order";
import { Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useRouter } from "next/router";
import { OrderTableActionBar } from "~/table/OrderTable/OrderTableActionBar";

const OrderListPage = () => {
  const tab = useRouter();
  const { data, isPending } = useOrders();
  const abandoned = useAbandonedOrders(tab.query.tab === "abandoned");

  return (
    <Page
      title={"Orders"}
      actionButton={{
        label: "New Order",
        href: `/orders/start`,
        newItem: true,
      }}
    >
      <Tabs defaultValue={"all"}>
        <TabsList>
          <TabsTrigger value={"all"}>All</TabsTrigger>
          <TabsTrigger value={"abandoned"}>Abandoned</TabsTrigger>
        </TabsList>
        <TabsContent value={"all"}>
          <DataTable
            data={data || []}
            isLoading={isPending}
            linkTo={(row) => `/orders/${row.id}/`}
            responsive={{
              gridAreas:
                "'id actions' 'Customer state' 'starttime starttime' 'paymentStatus contractStatus'",
            }}
            columns={OrderColumns()}
            tableFacets={(table) => [
              {
                column: table.getColumn("state"),
                title: "State",
                options: [
                  { value: "ABANDONED", label: "Abandoned" },
                  { value: "ACTIVE", label: "Active" },
                  { value: "COMPLETED", label: "Completed" },
                  { value: "CANCELLED", label: "Cancelled" },
                ],
              },
            ]}
            defaultFilters={[
              {
                id: "state",
                value: ["ACTIVE"],
              },
            ]}
            defaultSorting={[
              {
                id: "starttime",
                desc: false,
              },
            ]}
            actionBar={(table) => <OrderTableActionBar table={table} />}
          />
        </TabsContent>
        <TabsContent value={"abandoned"}>
          <DataTable
            actionBar={(table) => <OrderTableActionBar table={table} />}
            data={abandoned?.data || []}
            isLoading={abandoned.isPending}
            linkTo={(row) => `/orders/${row.id}/`}
            responsive={{
              gridAreas:
                "'id actions' 'Customer state' 'starttime starttime' 'paymentStatus contractStatus'",
            }}
            columns={OrderColumns()}
            defaultSorting={[
              {
                id: "id",
                desc: true,
              },
            ]}
          />
        </TabsContent>
      </Tabs>
    </Page>
  );
};
export default OrderListPage;
