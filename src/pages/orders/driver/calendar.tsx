import React, { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/router";
import Page from "~/components/Page";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { DriverOrderType, OrderWithAddress } from "~/pages/api/orders/driver";
import { DeliveryDistanceLocations } from "~/pages/api/driver/distance";
import { LogisticTimeline } from "~/pages/api/driver/timelines";
import { captureException } from "@sentry/core";
import Timeline, { getTimeWindowForTask } from "~/components/driver/Timeline";
import {
  HermesRouteTimeline,
  LogisticTaskItem,
} from "~/components/driver/types";
import { toast } from "sonner";
import { MapPin, PlusCircle, Zap } from "lucide-react";
import DriverGoogleMap from "~/components/driver/DriverGoogleMap";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "~/components/ui/sheet";
import { Header5 } from "~/components/ui/typography";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Label } from "~/components/ui/label";
import ConfirmationDialog from "~/components/Actions/ConfirmationDialog";
import AutoRouteSettingsModal from "~/components/driver/AutoRouteSettingsModal";
import { HOUR_FORMAT } from "~/server/lib/time";
import EditStartTimeForm from "~/components/driver/EditStartTimeForm";
import FormDialog from "~/components/Actions/FormDialog";
import TimelineTask from "~/components/driver/TimelineTask";
import { AddressType } from "~/server/lib/location/types";
import OrderCalendarPop from "~/components/Order/OrderCalendarPop";
import { startOfDay } from "date-fns";

export const ITEM_LIST_TYPE = "itemsList";

const DriverByDay = () => {
  const router = useRouter();
  const specificDate = router.query.specificDate;
  const [loading, setLoading] = useState(true);
  const [autoRouteLoading, setAutoRouteLoading] = useState(false);
  const [autoRouteAi, setAutoRouteAi] = useState<boolean>(true);
  const [data, setData] = useState<DriverOrderType[]>([]);
  const [currentDate, setCurrentDate] = useState<Date>(() => {
    if (!specificDate) {
      return new Date();
    }
    return new Date(specificDate as string);
  });

  // Function to generate a unique color based on orderId
  const getColorForOrderId = (orderId: number | null) => {
    if (!orderId) return undefined;

    // List of distinct colors that are visually different from each other
    const colors = [
      "#FF5733", // Red-Orange
      "#33FF57", // Green
      "#3357FF", // Blue
      "#FF33A8", // Pink
      "#33FFF5", // Cyan
      "#F5FF33", // Yellow
      "#A833FF", // Purple
      "#FF8C33", // Orange
      "#33FFAA", // Mint
      "#8CFF33", // Lime
      "#FF3333", // Red
      "#33AAFF", // Sky Blue
      "#FF33F5", // Magenta
      "#FFAA33", // Amber
      "#AA33FF", // Violet
    ];

    // Use the orderId to select a color from the array
    return colors[orderId % colors.length];
  };
  const [timelines, setTimelines] = useState<LogisticTimeline[]>([]);
  const [tasks, setTasks] = useState<LogisticTaskItem[]>([]);
  const [newTaskPopup, setNewTaskPopup] = useState<boolean>(false);
  const [origins, setOrigins] = useState<
    {
      id: number;
      latitude: number;
      longitude: number;
      line1: string;
    }[]
  >([]);
  const [dirty, setDirty] = useState(false);
  const [savingTimeout, setSavingTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );
  const [routeDistance, setRouteDistance] = useState<
    Record<string, DeliveryDistanceLocations>
  >({});
  const [renderTimelineRoute, setRenderTimelineRoute] = useState<string | null>(
    null,
  );

  const [rearrangeConfirmation, setRearrangeConfirmation] = useState(false);

  useEffect(() => {
    if (savingTimeout) {
      clearTimeout(savingTimeout);
    }
    const timeout = setTimeout(() => {
      if (!dirty) {
        return;
      }
      console.log("Saving...");
      updateTimeline(timelines, tasks).then(() => {
        setDirty(false);
        console.log("Saved");
      });
    }, 1000);
    setSavingTimeout(timeout);
  }, [tasks]);

  const updateTimeline = async (
    timelines: LogisticTimeline[],
    itemsCopy: LogisticTaskItem[],
  ) => {
    // save the updated timelineData to the server
    const timelineToTaskMap: Record<string, LogisticTaskItem[]> = {};

    timelines.forEach((timeline) => {
      timelineToTaskMap[timeline.id] = [];
    });

    itemsCopy.forEach((item) => {
      const timelineId = item.timelineId.split("-")[1];
      if (!timelineId) return;
      const taskList = timelineToTaskMap[timelineId] || [];
      if (item.id) {
        taskList.push(item);
      }
      timelineToTaskMap[timelineId] = taskList;
    });

    const timelineDataToSave = Object.keys(timelineToTaskMap).map(
      (timelineId) => {
        return {
          id: timelineId,
          startTime: timelines.find((item) => item.id === timelineId)
            ?.startTime,
          staff: timelines.find((item) => item.id === timelineId)?.staff,
          tasks: timelineToTaskMap[timelineId] || [],
        };
      },
    );

    const request = await fetch(`/api/driver/timelines/update`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        timelines: timelineDataToSave,
      }),
    });
    if (!request.ok) {
      captureException(new Error("Failed to update timelines"), {
        extra: {
          timelineDataToSave,
        },
      });
      return;
    }
  };

  const timelinesToRender = useMemo(() => {
    return timelines.sort(
      (a, b) => a.startTime.getTime() - b.startTime.getTime(),
    );
  }, [timelines]);

  const drawnRoute = useMemo(() => {
    if (!renderTimelineRoute) {
      return null;
    }
    return tasks
      .filter(
        (task) =>
          task.timelineId === renderTimelineRoute &&
          routeDistance[task.address.id.toString()],
      )
      .sort((a, b) => a.index - b.index)
      .map((task) => {
        const location = routeDistance[task.address.id.toString()];
        if (!location) {
          return {
            lat: 0,
            lng: 0,
          };
        }
        return {
          lat: location.latitude,
          lng: location.longitude,
        };
      });
  }, [tasks, renderTimelineRoute]);

  const removeTimeline = async (deletedTimeline: LogisticTimeline) => {
    setTasks((task) => [
      ...task.map((item) => {
        if (item.timelineId === `timeline-${deletedTimeline.id}`) {
          item.timelineId = ITEM_LIST_TYPE;
        }
        return item;
      }),
    ]);
    setTimelines((timeline) => [
      ...timeline.filter((item) => item.id !== deletedTimeline.id),
    ]);
  };

  const addTimeline = async (
    time: Date,
    staff: string[],
  ): Promise<string | null> => {
    time.setSeconds(0);
    time.setMilliseconds(0);
    const request = await fetch(`/api/driver/timelines/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        startTime: time.toISOString(),
        staff: staff,
      }),
    });
    if (!request.ok) {
      toast.error(`Failed to create timeline ${await request.text()}`);
      return null;
    }

    const response = await request.json();
    if (!origins || origins.length === 0) {
      setOrigins([response.newTimeline.origin]);
    }
    setTimelines((prev) =>
      [
        ...prev,
        {
          ...response.newTimeline,
          startTime: new Date(response.newTimeline.startTime),
          staff: response.newTimeline.staff || [],
          tasks: [],
        },
      ].sort((a, b) => a.startTime.getTime() - b.startTime.getTime()),
    );

    return response.newTimeline.id;
  };

  const handleDragDrop = (results: DropResult) => {
    const { source, destination } = results;

    if (results.reason === "CANCEL") {
      return;
    }

    const taskId = results.draggableId.split("-")[1];

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }
    const task = tasks.find((item) => item.id === taskId);

    if (!task) {
      return;
    }

    handleReorderingItem(
      tasks,
      task,
      source.index,
      source.droppableId,
      destination.index,
      destination.droppableId,
    );
  };

  const handleReorderingItem = (
    tasks: LogisticTaskItem[],
    task: LogisticTaskItem,
    sourceIndex: number,
    sourceList: string,
    destinationIndex: number,
    destinationList: string,
  ) => {
    const itemsCopy = Array.from(tasks); // Make a copy of the items array
    task.timelineId = destinationList;
    const destinationTasks = itemsCopy.filter((item) => {
      return item.timelineId === destinationList;
    });

    // Fun little hack I found, to avoid the reshuffle, nothing said that the index has to be an integer.
    // so we set it to .5, so that when we sort it, it will be in the middle of the two indexes.
    // then we just recalibrate the indexes based on the list index which will give us an integer.
    const vector =
      destinationList === sourceList
        ? destinationIndex > sourceIndex
          ? 0.5
          : -0.5
        : destinationTasks.find((item) => item.index === destinationIndex) ===
            undefined
          ? 0
          : -0.5;
    task.index = destinationIndex + vector;

    destinationTasks
      .sort((a, b) => a.index - b.index)
      .forEach((item, index) => {
        item.index = index;
      });

    if (sourceList !== destinationList) {
      const sourceTasks = itemsCopy.filter((item) => {
        return item.timelineId === destinationList;
      });

      // we should also update the source list indexes
      sourceTasks
        .sort((a, b) => a.index - b.index)
        .forEach((item, index) => {
          item.index = index;
        });
    }
    setDirty(true);
    setTasks(itemsCopy);
  };

  const fetchDistance = async (
    orderLocations: number[],
    origins: AddressType[],
  ) => {
    const request = await fetch(`/api/driver/distance`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        origins: Array.from(new Set(origins)),
        locations: orderLocations,
      }),
    });
    if (!request.ok) {
      return;
    }
    const response = await request.json();
    setOrigins(response.origins);
    const locations = response.locations;
    const routeDistance: Record<string, DeliveryDistanceLocations> = {};
    locations.forEach((loc: { id: number } & DeliveryDistanceLocations) => {
      routeDistance[loc.id.toString()] = loc;
    });

    setRouteDistance(routeDistance);
  };

  const fetchOrders = async (requestedDate: Date) => {
    setData([]);
    const request = await fetch(
      `/api/orders/driver?startTime=${requestedDate?.toISOString()}`,
    );
    setLoading(false);
    if (!request.ok) {
      return;
    }
    const response = await request.json();
    setData(response.orders);
    setTasks(
      response.tasks.map((task: LogisticTaskItem) => {
        return {
          ...task,
          actionTime: new Date(task.actionTime),
        };
      }),
    );
    setTimelines(
      response.timelines.map((timeline: LogisticTimeline) => {
        return {
          ...timeline,
          startTime: new Date(timeline.startTime),
        };
      }),
    );

    const allItemsMap = [
      ...response.orders.map((order: OrderWithAddress) => order.eventAddressId),
    ];

    void fetchDistance(
      allItemsMap,
      response.timelines.map((item: LogisticTimeline) => item.origin),
    );
  };

  useEffect(() => {
    void fetchOrders(currentDate);
  }, [currentDate, specificDate]);

  const requestAutoRoute = async (confirmation = true) => {
    if (
      confirmation &&
      tasks.some((task) => task.timelineId !== ITEM_LIST_TYPE)
    ) {
      setRearrangeConfirmation(true);
      return;
    }
    // first timeline start time
    const firstTimeline = timelines
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
      ?.at(0);
    const startOfDate = new Date(firstTimeline?.startTime || currentDate);

    const orderIdLocationLookup: {
      eventAddress: number;
      taskId: string;
      indexNumber: number;
    }[] = [];
    const orderIdDistanceMatrix: Record<
      string,
      {
        indexNumber: number;
        distanceMatrix: Record<number, number>;
      }
    > = {};

    tasks.forEach((task, index) => {
      orderIdLocationLookup.push({
        eventAddress: task.address.id,
        taskId: task.id,
        indexNumber: index + 1,
      });
    });

    tasks.forEach((task, index) => {
      const distanceMatrix: Record<number, number> = {};
      orderIdLocationLookup.forEach((item) => {
        if (item.taskId === task.id) {
          return;
        }
        const distance =
          routeDistance[task.address.id]?.distance_to_other_locations[
            item.eventAddress
          ];
        if (distance) {
          distanceMatrix[item.indexNumber] = Math.round(distance);
        } else {
          distanceMatrix[item.indexNumber] = 0;
        }
      });
      orderIdDistanceMatrix[task.id] = {
        indexNumber:
          orderIdLocationLookup.find((lookup) => lookup.taskId === task.id)
            ?.indexNumber || index,
        distanceMatrix,
      };
    });

    const tasksToMap = tasks.map((task) => {
      const matrix = orderIdDistanceMatrix[task.id];

      if (!matrix) {
        return;
      }

      const timeWindow = getTimeWindowForTask(task);
      const startTimeWindowRelativeToStart = Math.ceil(
        (timeWindow.firstValidArrivalTime - startOfDate.getTime()) /
          (60 * 1000),
      );
      const endTimeWindowRelativeToStart = Math.ceil(
        (timeWindow.lastValidArrivalTime - startOfDate.getTime()) / (60 * 1000),
      );
      const timelineOrigin = timelines.find(
        (timeline) => timeline.id === task.timelineId?.split("-")[1],
      )?.origin?.line1;
      return {
        id: matrix.indexNumber,
        taskId: task.id,
        setup_minutes: Math.round(task.durationMinutes),
        distance_from_depot: Math.round(
          routeDistance[task.address.id.toString()]?.distance_from_depot?.find(
            (item) => item.line1 === timelineOrigin,
          )?.distance || 20,
        ),
        start_time_window: Math.max(0, startTimeWindowRelativeToStart),
        raw_time_window: startTimeWindowRelativeToStart,
        end_time_window: endTimeWindowRelativeToStart,
        fvst: timeWindow.firstValidArrivalTime,
        lvst: timeWindow.lastValidArrivalTime,
        distance_to_other_locations: matrix.distanceMatrix,
      };
    });

    const routes: HermesRouteTimeline[] = timelines.map((timeline) => {
      return {
        id: timeline.id,
        start_time: Math.ceil(
          (timeline.startTime.getTime() - startOfDate.getTime()) / (60 * 1000),
        ),
        time_at_depot: 45, // They can start anytime between start of their shift and 3 horus later.
        max_time: 60 * 8,
      };
    });

    setAutoRouteLoading(true);
    const toastMessage = toast.loading("Routing...", {
      duration: 10_000,
    });

    const request = await fetch("/api/driver/auto-route", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        date: startOfDay(currentDate).toISOString(),
        start_of_day: startOfDate.getTime(),
        settings: {
          aiRoute: autoRouteAi,
        },
        routes: routes,
        delivery_locations: tasksToMap,
      }),
    });

    toast.dismiss(toastMessage);
    setAutoRouteLoading(false);

    if (!request.ok) {
      console.error("Failed to fetch", await request.text());
      toast.error("Routing could not be completed, please try again.");
      return;
    }

    const response:
      | { message: string }
      | {
          timelines: never;
          [key: string]: {
            route: number[];
            distance: number;
          };
        }
      | {
          timelines: {
            id: number;
            startTime: string;
            tasks: { id: number }[];
          }[];
        } = await request.json();

    if ("message" in response) {
      console.error(response.message);
      toast.error(
        "Routing could not be completed, include more timelines or change the start times.",
      );
      return;
    }

    const tasksCopy = Array.from(tasks);

    if (response.timelines) {
      const timelineMap: Record<number, string> = {};
      await Promise.all(
        response.timelines.map(
          async (timeline: {
            id: number;
            startTime: string;
            tasks: { id: number }[];
          }) => {
            const splitStartTime = timeline.startTime.split(":");
            const startOfDate = new Date(currentDate);
            startOfDate.setHours(
              parseInt(splitStartTime[0] ?? "0", 10),
              parseInt(splitStartTime[1] ?? "0", 10),
              0,
              0,
            );

            let timelineId = timelines.find((item) => {
              return (
                item.startTime.getHours() === startOfDate.getHours() &&
                item.startTime.getMinutes() === startOfDate.getMinutes()
              );
            })?.id;

            if (!timelineId) {
              timelineId = (await addTimeline(startOfDate, [])) ?? undefined;
            }

            if (!timelineId) {
              return;
            }

            timelineMap[timeline.id] = timelineId;

            console.log("orderIdLocationLookup", orderIdLocationLookup);
            timeline.tasks.forEach((value: { id: number }, index: number) => {
              const orderIdLookup = orderIdLocationLookup.find(
                (item) => item.indexNumber === value.id,
              );
              console.log(
                orderIdLookup,
                "orderIdLookup",
                value.id,
                "= id",
                index,
                "= index",
              );
              if (!orderIdLookup) {
                return;
              }
              const task = tasksCopy.find(
                (task) => task.id === orderIdLookup.taskId,
              );
              console.log(task, "task");
              if (!task) {
                return;
              }

              task.index = index;
              task.timelineId = `timeline-${timelineId}`;
            });
          },
        ),
      );

      toast.success("Routing completed successfully.");
      setDirty(true);
      setTasks(tasksCopy);
      return;
    }

    Object.entries(response).forEach((entry) => {
      const [key, value] = entry;
      if (!value?.route) {
        return;
      }
      if (value.distance === 0) {
        // we don't need this timeline
        return;
      }

      const timeline = timelines.find((timeline) => timeline.id === key);
      if (!timeline) {
        toast.error(
          "Routing could not be completed, add another timeline and try again.",
        );
        return;
      }
      console.log(value, "value", "timeline", timeline.id);
      value.route.forEach((indexNumber: any, index: number) => {
        const orderIdLookup = orderIdLocationLookup.find(
          (item) => item.indexNumber === indexNumber,
        );
        console.log(orderIdLookup, "orderIdLookup");
        if (!orderIdLookup) {
          return;
        }
        const task = tasksCopy.find((task) => task.id === orderIdLookup.taskId);
        console.log(task, "task");
        if (!task) {
          return;
        }

        task.index = index;
        task.timelineId = `timeline-${timeline.id}`;
      });
    });

    console.log(JSON.stringify(tasksCopy, null, 2), "tasksCopy");

    toast.success("Routing completed successfully.");
    setDirty(true);
    setTasks(tasksCopy);
  };

  // const handleNewTask = (task: LogisticTaskItem) => {
  //   setTasks((tasks) => {
  //     return [
  //       ...tasks,
  //       {
  //         ...task,
  //         actionTime: new Date(task.actionTime),
  //       },
  //     ];
  //   });
  // };

  return (
    <Page title={"Delivery By Day"} subtitle={currentDate.toLocaleDateString()}>
      <OrderCalendarPop
        loading={loading}
        currentDate={currentDate}
        setCurrentDate={(date) => {
          setCurrentDate(date);
          router.replace(
            `/orders/driver/calendar?specificDate=${encodeURIComponent(
              date.toLocaleDateString(),
            )}`,
          );
        }}
      />
      <FormDialog
        open={newTaskPopup}
        setOpen={setNewTaskPopup}
        title={"New Timeline Start Time"}
        form={
          <EditStartTimeForm
            startTime={
              new Date(startOfDay(currentDate).getTime() + 7 * 60 * 60 * 1000)
            }
            onSubmit={(values) => {
              setNewTaskPopup(false);
              addTimeline(values.startTime, values.staff);
            }}
            staff={[]}
          />
        }
      />
      <ConfirmationDialog
        open={rearrangeConfirmation}
        onOpenChange={setRearrangeConfirmation}
        onPrimaryAction={() => {
          requestAutoRoute(false);
          setRearrangeConfirmation(false);
        }}
        title={"Rearrange All Tasks?"}
        description={
          "Auto Routing with your current settings may rearrange tasks that you already have in timelines, are you sure?"
        }
      />
      <DragDropContext onDragEnd={handleDragDrop}>
        <div className={"flex flex-row gap-1 sm:gap-4 mt-2 max-w-[80vw]"}>
          <div className={"md:1/4 bg-white shadows-sm p-4 border rounded-md"}>
            <div className={"flex justify-between mb-2"}>
              <h2 className={"font-semibold"}>Tasks</h2>
              {/*<NewTaskModal date={currentDate} onSubmit={handleNewTask}>*/}
              {/*  <Button variant={"primary"} size={"sm"}>*/}
              {/*    <PlusCircle className={"mr-2 w-4 h-4"} />*/}
              {/*    New Task*/}
              {/*  </Button>*/}
              {/*</NewTaskModal>*/}
            </div>
            <div className={"flex flex-row items-center gap-2"}>
              <Button
                variant={"upgrade"}
                disabled={autoRouteLoading}
                className={"my-2"}
                size={"full"}
                onClick={() => {
                  void requestAutoRoute();
                }}
              >
                <Zap className="w-5 h-5 mr-2" />
                Auto Route
              </Button>

              <AutoRouteSettingsModal
                initialValues={{
                  aiAutoRoute: { enabled: autoRouteAi },
                }}
                onSubmit={(values) => {
                  setAutoRouteAi(values.aiAutoRoute?.enabled ?? false);
                }}
              />
            </div>
            <Droppable droppableId={ITEM_LIST_TYPE}>
              {(provided) => (
                <div
                  ref={provided.innerRef}
                  className={
                    "bg-gray-200 rounded-lg mb-4 flex flex-col overflow-y-auto gap-4 p-3 max-h-svh"
                  }
                  {...provided.droppableProps}
                >
                  {tasks
                    .filter((item) => {
                      return item.timelineId === ITEM_LIST_TYPE;
                    })
                    .map((item) => {
                      // Find matching tasks (tasks with the same orderId)
                      const matchingTasks = item.orderId
                        ? tasks.filter(
                            (task) =>
                              task.orderId === item.orderId &&
                              task.id !== item.id,
                          )
                        : [];

                      // Get color for this task pair based on orderId
                      const pairColor = item.orderId
                        ? getColorForOrderId(item.orderId)
                        : undefined;

                      return (
                        <TimelineTask
                          key={item.id}
                          task={item}
                          index={item.index}
                          timeData={undefined}
                          matchingTasks={matchingTasks}
                          pairColor={pairColor}
                        />
                      );
                    })}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
          <div className={"flex md:w-3/4 flex-row sm:flex-col gap-2"}>
            <Card className={"hidden md:block"}>
              <CardContent className={"pt-2"}>
                <DriverGoogleMap
                  businessLat={
                    origins?.find(
                      (item) =>
                        item.line1 ===
                        timelines.find(
                          (item) => item.id === renderTimelineRoute,
                        )?.origin?.line1,
                    )?.latitude ??
                    origins[0]?.latitude ??
                    0
                  }
                  businessLng={
                    origins?.find(
                      (item) =>
                        item.line1 ===
                        timelines.find(
                          (item) => item.id === renderTimelineRoute,
                        )?.origin?.line1,
                    )?.longitude ??
                    origins[0]?.longitude ??
                    0
                  }
                  markerLocations={Object.values(routeDistance).map(
                    (location) => {
                      const orderNumber = data.find(
                        (order) => order.eventAddressId === location.id,
                      )?.id;
                      return {
                        title: orderNumber?.toString() ?? "",
                        hover: orderNumber ? `Order #${orderNumber}` : "Depot",
                        lat: location.latitude,
                        lng: location.longitude,
                      };
                    },
                  )}
                  renderRoute={drawnRoute}
                  renderChildren={
                    <div className={"flex flex-row sm:flex-col w-fit mt-2"}>
                      <Label>Draw Route</Label>
                      <Select
                        onValueChange={(value) => {
                          setRenderTimelineRoute(`timeline-${value}`);
                        }}
                        value={
                          !renderTimelineRoute
                            ? undefined
                            : renderTimelineRoute.replace("timeline-", "")
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={"Select Timeline"} />
                        </SelectTrigger>
                        <SelectContent>
                          {timelinesToRender.map((timeline) => (
                            <SelectItem key={timeline.id} value={timeline.id}>
                              {`Timeline: ${HOUR_FORMAT.format(
                                timeline.startTime,
                              )}`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  }
                />
              </CardContent>
            </Card>

            <>
              {timelinesToRender.map((timeline, index) => {
                // Create a map of orderIds to colors for all tasks in this timeline
                const orderColorMap = new Map();

                // Get all tasks for this timeline
                const timelineTasks =
                  tasks
                    .filter((item) => {
                      return item.timelineId === `timeline-${timeline.id}`;
                    })
                    .sort((a, b) => a.index - b.index) || [];

                // Collect all orderIds from tasks in this timeline
                timelineTasks.forEach((task) => {
                  if (task.orderId && !orderColorMap.has(task.orderId)) {
                    orderColorMap.set(
                      task.orderId,
                      getColorForOrderId(task.orderId),
                    );
                  }
                });

                // Also collect orderIds from tasks not in this timeline (to match with tasks in this timeline)
                tasks
                  .filter(
                    (task) =>
                      task.timelineId !== `timeline-${timeline.id}` &&
                      task.orderId,
                  )
                  .forEach((task) => {
                    if (task.orderId && !orderColorMap.has(task.orderId)) {
                      // Check if any task in this timeline has the same orderId
                      const hasMatchingTask = timelineTasks.some(
                        (t) => t.orderId === task.orderId,
                      );
                      if (hasMatchingTask) {
                        orderColorMap.set(
                          task.orderId,
                          getColorForOrderId(task.orderId),
                        );
                      }
                    }
                  });

                return (
                  <Timeline
                    tasks={timelineTasks}
                    key={timeline.id}
                    {...timeline}
                    id={timeline.id}
                    removeTimeline={async () => {
                      await removeTimeline(timeline);
                    }}
                    updateTimeline={(startTime, staff) => {
                      const updatedTimeline = [
                        ...timelines.filter((item) => item.id !== timeline.id),
                        {
                          ...timeline,
                          startTime: startTime,
                          staff: staff,
                        },
                      ];
                      setTimelines(updatedTimeline);
                      updateTimeline(updatedTimeline, tasks);
                      setDirty(false);
                    }}
                    routeDistance={routeDistance}
                    startTime={timeline.startTime}
                    handleNewTimeline={
                      index === timelines.length - 1
                        ? () => setNewTaskPopup(true)
                        : undefined
                    }
                    // Pass the entire tasks array to allow finding matching tasks across timelines
                    allTasks={tasks}
                    getColorForOrderId={getColorForOrderId}
                  />
                );
              })}
              {loading && (
                <div className={"flex flex-col gap-2"}>
                  <div className={"h-10 w-full bg-gray-200 animate-pulse"} />
                  <div className={"h-10 w-full bg-gray-200 animate-pulse"} />
                </div>
              )}
              {!loading && timelinesToRender.length === 0 && (
                <div
                  className={
                    "flex flex-col justify-center items-center text-center gap-2"
                  }
                >
                  <h5 className={Header5}>No timelines found for this day.</h5>
                  <Button
                    onClick={() => {
                      setNewTaskPopup(true);
                    }}
                  >
                    <PlusCircle className={"mr-2 w-4 h-4"} />
                    Add Timeline
                  </Button>
                </div>
              )}
            </>
          </div>
        </div>
      </DragDropContext>
      <Sheet>
        <SheetTrigger>
          <Button
            size={"md"}
            variant={"primary"}
            className="fixed bottom-4 right-4 rounded-full md:hidden"
          >
            <MapPin className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side={"bottom"}>
          <SheetHeader>
            <SheetTitle>Route Map</SheetTitle>
          </SheetHeader>
          <div className={"p-3 border rounded-md"}>
            <DriverGoogleMap
              businessLat={
                origins?.find(
                  (item) =>
                    item.line1 ===
                    timelines.find((item) => item.id === renderTimelineRoute)
                      ?.origin?.line1,
                )?.latitude ??
                origins[0]?.latitude ??
                0
              }
              businessLng={
                origins?.find(
                  (item) =>
                    item.line1 ===
                    timelines.find((item) => item.id === renderTimelineRoute)
                      ?.origin?.line1,
                )?.longitude ??
                origins[0]?.longitude ??
                0
              }
              markerLocations={Object.values(routeDistance).map((location) => {
                const orderNumber = data.find(
                  (order) => order.eventAddressId === location.id,
                )?.id;
                return {
                  title: orderNumber?.toString() ?? "",
                  hover: orderNumber ? `Order #${orderNumber}` : "Depot",
                  lat: location.latitude,
                  lng: location.longitude,
                };
              })}
              renderRoute={drawnRoute}
              renderChildren={
                <div className={"flex flex-col w-fit mt-2"}>
                  <Label>Draw Route</Label>
                  <Select
                    onValueChange={(value) => {
                      setRenderTimelineRoute(`timeline-${value}`);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={"Select Timeline"} />
                    </SelectTrigger>
                    <SelectContent>
                      {timelinesToRender.map((timeline) => (
                        <SelectItem key={timeline.id} value={timeline.id}>
                          {`Timeline: ${timeline.index + 1}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              }
            />
          </div>
        </SheetContent>
      </Sheet>
    </Page>
  );
};

export default DriverByDay;
