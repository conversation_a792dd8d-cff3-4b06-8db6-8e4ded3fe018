import React, { useEffect } from "react";
import { Data, Render } from "@measured/puck";
import { puckConfig } from "~/pages/website/index";
import { GetServerSidePropsContext } from "next";
import { getToken } from "next-auth/jwt";
import { db } from "~/server/db";
import ProductLookupComponent from "~/components/WebsiteBuilder/ProductLookupComponent";
import { SiteInfoResponse } from "~/pages/api/backend/siteInfo";
import { ProductProvider, SiteConfigProvider } from "@prp/blocks";
import { getWebsiteCollection, getWebsiteInfo } from "~/server/lib/website";
import { PublicProduct } from "~/pages/api/public/[accountName]/products";
import { PublicCategory } from "~/pages/api/public/[accountName]/categories";
import { PublicRule } from "~/lib/rules";

type InternalCollectionData = {
  products: (PublicProduct & {
    rules: number[];
  })[];
  categories: PublicCategory[];
  bestSellers: { id: number }[];
  rules?: PublicRule[];
};

export const getServerSideProps = async ({
  req,
}: GetServerSidePropsContext) => {
  const token = await getToken({ req });
  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  const account = await db.account.findFirst({
    where: {
      id: token.accountId,
    },
  });

  if (!account) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }
  const [siteInfo, products] = await Promise.all([
    getWebsiteInfo(account?.customDomain || ""),
    getWebsiteCollection(account?.customDomain || ""),
  ]);
  return {
    props: {
      siteInfo,
      products,
    },
  };
};

const PreviewPuck = ({
  siteInfo,
  products,
}: {
  siteInfo: SiteInfoResponse;
  products: InternalCollectionData;
}) => {
  const [data, setData] = React.useState<Data | undefined>(undefined);

  useEffect(() => {
    const savedData = localStorage.getItem("websiteData");
    if (savedData) {
      const parsedData: Data = JSON.parse(savedData);

      setData(parsedData);
      return;
    }
  }, []);

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <SiteConfigProvider
      initialConfig={{
        ...siteInfo,
      }}
    >
      <ProductProvider
        initialConfig={{
          bestSellers: products.products.filter((item) =>
            products.bestSellers.some((id) => item.id === id.id),
          ),
          categories: products.categories,
          products: products.products,
        }}
      >
        <ProductLookupComponent />
        <Render data={data} config={puckConfig} />
      </ProductProvider>
    </SiteConfigProvider>
  );
};

PreviewPuck.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

export default PreviewPuck;
