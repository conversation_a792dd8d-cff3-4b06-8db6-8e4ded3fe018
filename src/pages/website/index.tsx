import { Action<PERSON>ar, Config, Data, Drawer, Puck, usePuck } from "@measured/puck";
import "@measured/puck/puck.css";
import React, { useEffect } from "react";
import {
  ContainerBlock,
  ContainerProps,
  EmbeddedProps,
  HeadingTextProps,
  ImageProps,
} from "~/components/WebsiteBuilder/blocks";
import PageColumnsBlock, {
  PageColumnsBlockProps,
} from "~/components/WebsiteBuilder/blocks/PageColumns";
import EmbeddedBlock from "~/components/WebsiteBuilder/blocks/EmbeddedBlock";
import HeadingTextBlock from "~/components/WebsiteBuilder/blocks/HeadingTextBlock";
import ImageBlock from "~/components/WebsiteBuilder/blocks/ImageBlock";
import HeaderCarouselBlock from "~/components/WebsiteBuilder/blocks/HeaderCarouselBlock";
import ProductCarouselBlock, {
  ProductCarouselBlockProps,
} from "~/components/WebsiteBuilder/blocks/ProductCarouselBlock";
import ReviewCarouselBlock, {
  ReviewCarouselBlockProps,
} from "~/components/WebsiteBuilder/blocks/ReviewCarouselBlock";
import FAQBlock, {
  FAQBlockProps,
} from "~/components/WebsiteBuilder/blocks/FAQBlock";
import {
  CategoryGridBlock,
  CategoryGridBlockProps,
} from "~/components/WebsiteBuilder/blocks/CategoryGridBlock";
import {
  GridBlock,
  GridBlockProps,
} from "~/components/WebsiteBuilder/blocks/GridBlock";
import { TextProps } from "~/components/WebsiteBuilder/blocks/TextBlock";
import { Toaster } from "sonner";
import SpacingBlock, {
  SpacingProps,
} from "~/components/WebsiteBuilder/blocks/SpacingBlock";
import { GetServerSidePropsContext } from "next";
import { getToken } from "next-auth/jwt";
import { db } from "~/server/db";
import {
  HeroBlock,
  HeroProps,
} from "~/components/WebsiteBuilder/blocks/HeroBlock";
import Footer from "@prp/blocks/dist/components/Footer";
import Navbar from "@prp/blocks/dist/components/nav/nav";
import {
  BannerBlock,
  BannerBlockSettingsProps,
} from "~/components/WebsiteBuilder/blocks/BannerBlock";
import CategoryCarouselBlock, {
  CategoryCarouselProps,
} from "~/components/WebsiteBuilder/blocks/CategoryCarouselBlock";
import TextBlock from "~/components/WebsiteBuilder/blocks/TextBlock/field";
import ListLinkBlock from "~/components/WebsiteBuilder/blocks/ListLinkBlock";
import CartBannerBlock, {
  CartBannerBlockProps,
} from "~/components/WebsiteBuilder/blocks/CartBannerBlock";
import { useSaveWebsite } from "~/query/website";
import {
  BackgroundHeroProps,
  FunProductCarouselBlockProps,
  HeaderCarouselBlockProps,
  ProductProvider,
  RenderAreasWeServeProps,
  RenderButtonProps,
  SiteConfigProvider,
  TextCarouselBlockProps,
  useSiteConfig,
} from "@prp/blocks";
import { getConciseAddress } from "~/server/lib/location/types";
import { getWebsiteData, SiteInfoResponse } from "~/pages/api/backend/siteInfo";
import ProductLookupComponent from "~/components/WebsiteBuilder/ProductLookupComponent";
import { setLinkProvider } from "@prp/blocks/dist/lib/Link";
import { BackgroundHeroBlock } from "~/components/WebsiteBuilder/blocks/BackgroundHeroBlock";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import ButtonBlock from "~/components/WebsiteBuilder/blocks/ButtonBlock";
import ContactUsBlock from "~/components/WebsiteBuilder/blocks/ContactUsBlock";
import { AreasWeServeBlock } from "~/components/WebsiteBuilder/blocks/AreasWeServeBlock";
import FunProductCarouselBlock from "~/components/WebsiteBuilder/blocks/FunProductCarouselBlock";
import TextCarouselBlock from "~/components/WebsiteBuilder/blocks/TextCarouselBlock";
import WebsiteEditorHeader from "~/components/WebsiteBuilder/editor/WebsiteEditorHeader";
import WebsiteEditorSidebar from "~/components/WebsiteBuilder/editor/WebsiteEditorSidebar";
import WebsiteEditorPreview from "~/components/WebsiteBuilder/editor/WebsiteEditorPreview";
import { Copy, Edit, Trash2 } from "lucide-react";
import { LoadingSpinner } from "~/pages/_app";
import {
  componentIconMap,
  WebsiteEditorComponentItem,
} from "~/components/WebsiteBuilder/editor/WebsiteEditorComponentItem";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { cn } from "~/lib/utils";

export type PuckRootProps = {
  title: string;
  description: string;
  backgroundColor: string;
};

export const SystemPages = [
  { slug: "/", name: "Home" },
  { slug: "cart", name: "Cart", disabled: true },
  { slug: "finalized", name: "Checkout Success", disabled: true },
  { slug: "order-by-date", name: "Order By Date", disabled: true },
  { slug: "product/[slug]", name: "Product Page", disabled: true },
  { slug: "category/[slug]", name: "Category Page" },
  { slug: "rentals", name: "Inventory List" },
  { slug: "category", name: "Category List" },
];

type Components = {
  ContainerBlock: ContainerProps;
  SpacingBlock: SpacingProps;
  TextCarouselBlock: TextCarouselBlockProps;
  HeaderCarouselBlock: HeaderCarouselBlockProps;
  BackgroundHeroBlock: BackgroundHeroProps;
  AreasWeServeBlock: RenderAreasWeServeProps;
  ProductCarouselBlock: ProductCarouselBlockProps;
  ReviewCarouselBlock: ReviewCarouselBlockProps;
  GridBlock: GridBlockProps;
  FunProductCarouselBlock: FunProductCarouselBlockProps & { config?: any };
  PageColumnsBlock: PageColumnsBlockProps;
  EmbeddedBlock: EmbeddedProps;
  ButtonBlock: RenderButtonProps;
  ContactUsBlock: any;
  HeadingTextBlock: HeadingTextProps;
  ImageBlock: ImageProps;
  TextBlock: TextProps;
  FAQBlock: FAQBlockProps;
  CategoryGridBlock: CategoryGridBlockProps;
  HeroBlock: HeroProps;
  BannerBlock: BannerBlockSettingsProps;
  CategoryCarouselBlock: CategoryCarouselProps;
  ListLinkBlock: any;
  CartBannerBlock: CartBannerBlockProps;
  RawHTML: any;
};

export const puckConfig: Config<Components, PuckRootProps> = {
  root: {
    fields: {
      title: {
        type: "text",
      },
      description: {
        type: "text",
      },
      backgroundColor: {
        type: "custom",
        render: ColorPickerPuckField,
      },
    },
    render: ({ children, backgroundColor }) => {
      const { config } = useSiteConfig();
      const hasBackground = config.settings?.backgroundImage !== undefined;
      const backgroundImageStyle = hasBackground
        ? {
            backgroundAttachment: "fixed",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundSize: "cover",
            backgroundImage: `url(${config.settings?.backgroundImage})`,
          }
        : {
            backgroundColor:
              backgroundColor ?? config.settings?.backgroundColor,
          };

      return (
        <div
          suppressHydrationWarning
          style={backgroundImageStyle}
          className={"min-h-screen"}
        >
          <Navbar path={"/"} cartItems={0} buttonStyle={"link"} />
          <div className={"mt-32"}>{children}</div>
          <Footer />
        </div>
      );
    },
  },
  categories: {
    layout: {
      title: "Layout",
      components: [
        "ContainerBlock",
        "SpacingBlock",
        "PageColumnsBlock",
        "GridBlock",
      ],
    },
    content: {
      components: [
        "TextBlock",
        "HeadingTextBlock",
        "ImageBlock",
        "FAQBlock",
        "EmbeddedBlock",
        "RawHTML",
      ],
      title: "Content",
    },
    callToAction: {
      title: "Call to Action",
      components: [
        "ButtonBlock",
        "BannerBlock",
        "CartBannerBlock",
        "ContactUsBlock",
        "ListLinkBlock",
      ],
    },
    hero: {
      title: "Hero (Header)",
      components: ["HeroBlock", "BackgroundHeroBlock", "HeaderCarouselBlock"],
    },
    product: {
      title: "Product Display",
      components: [
        "ProductCarouselBlock",
        "FunProductCarouselBlock",
        "CategoryGridBlock",
        "CategoryCarouselBlock",
      ],
    },
    social: {
      title: "Social Proof",
      components: ["ReviewCarouselBlock", "TextCarouselBlock"],
    },
    utility: {
      title: "Utility (SEO)",
      components: ["AreasWeServeBlock"],
    },
  },
  components: {
    FunProductCarouselBlock,
    SpacingBlock,
    AreasWeServeBlock,
    ContainerBlock,
    HeaderCarouselBlock,
    TextCarouselBlock,
    ProductCarouselBlock,
    ReviewCarouselBlock,
    GridBlock,
    PageColumnsBlock,
    EmbeddedBlock,
    ContactUsBlock,
    ButtonBlock,
    BackgroundHeroBlock,
    HeadingTextBlock,
    ImageBlock,
    HeroBlock,
    TextBlock,
    ListLinkBlock,
    FAQBlock,
    CategoryGridBlock,
    BannerBlock,
    CategoryCarouselBlock,
    CartBannerBlock,
    RawHTML: {
      fields: {
        text: {
          type: "textarea",
        },
      },
      defaultProps: {
        text: "",
      },
      render: (props: { text: string }) => {
        return (
          <div
            dangerouslySetInnerHTML={{ __html: props.text }}
            id={"raw-html"}
            className={"w-full h-full"}
          />
        );
      },
    },
  },
};

// Describe the initial data
const initialData = {
  content: [],
  root: {},
};

// Save the data to your database

export type WebsitePage = {
  name: string;
  slug: string;
  systemPage: boolean;
  published: boolean;
};

export const getServerSideProps = async ({
  req,
}: GetServerSidePropsContext) => {
  const token = await getToken({ req });
  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }

  const account = await db.account.findFirst({
    where: {
      id: token.accountId,
    },
    include: {
      billingAddress: true,
    },
  });

  if (!account) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }
  const siteInfo = await getWebsiteData(
    {
      ...account,
      address: account.billingAddress
        ? getConciseAddress(account.billingAddress)
        : "",
    },
    "",
  );

  return {
    props: {
      siteInfo: JSON.parse(
        JSON.stringify(siteInfo, (key, value) => {
          if (value instanceof Date) {
            return value.toISOString();
          }

          // Handle undefined values explicitly
          if (value === undefined) {
            return null; // Convert undefined to null for JSON.stringify
          }

          // Return the value as is for all other cases
          return value;
        }),
      ),
    },
  };
};

const WebsiteEditorPage = ({ siteInfo }: { siteInfo: SiteInfoResponse }) => {
  const [data, setData] = React.useState<Data | undefined>(undefined);
  const [pages, setPages] = React.useState<WebsitePage[]>([]);
  const [currentPageSlug, setCurrentPageSlug] = React.useState("/");
  const saveWebsite = useSaveWebsite();

  setLinkProvider({
    renderLink: (props: any) => {
      return <div className={"cursor-pointer"} {...props} href={"#"} />;
    },
    route: function (href: string): void {
      console.log("routing... ", href);
    },
  });

  useEffect(() => {
    fetch(`/api/websites`, {
      method: "GET",
    })
      .then((response) => response.json())
      .then((data: { page: Data; totalPages: WebsitePage[] }) => {
        setData(data.page);
        setPages(
          data.totalPages.sort((a, b) => {
            if (a.slug === "") {
              return -1;
            }
            if (b.slug === "") {
              return 1;
            }
            return a.name.localeCompare(b.name);
          }),
        );
      });
  }, []);

  if (!data) {
    return (
      <div className="fixed inset-0 z-50 bg-white bg-opacity-50 flex items-center justify-center">
        <LoadingSpinner className="text-primary" />
      </div>
    );
  }

  return (
    <>
      <SiteConfigProvider
        initialConfig={{
          ...siteInfo,
        }}
      >
        <ProductProvider
          initialConfig={{ bestSellers: [], categories: [], products: [] }}
        >
          <ProductLookupComponent />
          <Puck
            config={puckConfig}
            data={data}
            onChange={(newData) => {
              setData(newData);
              const currentSessionData =
                localStorage.getItem("websiteDataTemp");
              let sessionData: Record<string, any> = {};
              if (currentSessionData) {
                sessionData = JSON.parse(currentSessionData);
              }
              sessionData[currentPageSlug] = newData;
              localStorage.setItem(
                "websiteDataTemp",
                JSON.stringify(sessionData),
              );
            }}
            onPublish={(data) => {
              saveWebsite.mutate({
                data,
                page: currentPageSlug,
              });
            }}
            overrides={{
              components: () => {
                return (
                  <TooltipProvider>
                    {Object.values(puckConfig.categories ?? {}).map(
                      (category) => (
                        <div key={category.title} className="mb-6">
                          <h4 className="text-xs font-medium text-sidebar-accent-foreground mb-2">
                            {category.title}
                          </h4>

                          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                            {category?.components?.map(
                              (componentName: string) => {
                                return (
                                  <Tooltip>
                                    <Drawer key={componentName}>
                                      <TooltipTrigger>
                                        <Drawer.Item name={componentName}>
                                          {() => (
                                            <WebsiteEditorComponentItem
                                              name={componentName}
                                            />
                                          )}
                                        </Drawer.Item>
                                      </TooltipTrigger>
                                    </Drawer>
                                    <TooltipContent>
                                      {componentIconMap[componentName]
                                        ?.description ?? ""}
                                    </TooltipContent>
                                  </Tooltip>
                                );
                              },
                            )}
                          </div>
                        </div>
                      ),
                    )}
                  </TooltipProvider>
                );
              },
              componentItem: WebsiteEditorComponentItem,

              actionBar: ({ label, parentAction }) => {
                const { dispatch, appState } = usePuck();
                return (
                  <div
                    className="relative"
                    style={{ transform: "translateY(3rem)" }}
                  >
                    <ActionBar label={label}>
                      <ActionBar.Group>
                        <ActionBar.Action
                          onClick={() => {
                            if (!appState.ui.leftSideBarVisible) {
                              dispatch({
                                type: "setUi",
                                ui: {
                                  leftSideBarVisible: true,
                                },
                              });
                            }
                          }}
                          label="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </ActionBar.Action>
                        <ActionBar.Action
                          onClick={() => {
                            if (!appState?.ui?.itemSelector?.zone) {
                              return;
                            }
                            dispatch({
                              type: "duplicate",
                              sourceZone: appState?.ui?.itemSelector?.zone,
                              sourceIndex:
                                appState?.ui?.itemSelector?.index ?? 0,
                            });
                          }}
                          label="Duplicate"
                        >
                          <Copy className="w-4 h-4" />
                        </ActionBar.Action>
                        <ActionBar.Action
                          onClick={() => {
                            if (!appState?.ui?.itemSelector?.zone) {
                              return;
                            }

                            dispatch({
                              type: "remove",
                              zone: appState?.ui?.itemSelector?.zone,
                              index: appState?.ui?.itemSelector?.index ?? 0,
                            });
                          }}
                          label="Delete"
                        >
                          <Trash2 className="w-4 h-4 text-red-500" />
                        </ActionBar.Action>
                      </ActionBar.Group>
                      <ActionBar.Group>{parentAction}</ActionBar.Group>
                    </ActionBar>
                  </div>
                );
              },
            }}
          >
            <div className="flex flex-col h-screen">
              {/* Custom Header */}
              <WebsiteEditorHeader
                pages={pages}
                onPageChange={(slug, data) => {
                  setCurrentPageSlug(slug);
                  setData(data);
                }}
                onReset={() => {
                  setData(initialData);
                  setCurrentPageSlug("/");
                }}
                onPreview={() => {
                  localStorage.setItem("websiteData", JSON.stringify(data));
                  window.open(`/website/preview?page=${currentPageSlug}`);
                }}
                onPublish={() => {
                  saveWebsite.mutate({
                    data,
                    page: currentPageSlug,
                  });
                }}
                currentSlug={currentPageSlug}
                onNewPage={(newPage) => {
                  setPages((page) => [...page, newPage]);
                }}
              />

              <div className="flex overflow-hidden bg-gray-50 h-full">
                <WebsiteEditorSidebar />

                {/* Stage / Preview Area */}
                <div className="flex-1 overflow-auto p-4 ">
                  <WebsiteEditorPreview />
                </div>
              </div>
            </div>
          </Puck>
        </ProductProvider>
      </SiteConfigProvider>
    </>
  );
};

WebsiteEditorPage.getLayout = function getLayout(page: React.ReactNode) {
  return (
    <>
      {page}
      <Toaster />
    </>
  );
};

export default WebsiteEditorPage;
