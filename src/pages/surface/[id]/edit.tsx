import { useRouter } from "next/router";
import Page from "~/components/Page";
import { useSurface } from "~/query/surface/query";
import SetupSurfaceForm from "~/form/SetupSurfaceForm";
import { useEditSurface } from "~/query/surface/mutation";

const SetupSurfaceEditPage = () => {
  const query = useRouter().query;
  const surfaceId = query.id as string;
  const surface = useSurface(surfaceId);
  const editSurface = useEditSurface();

  return (
    <Page title={"Edit Surface"}>
      <SetupSurfaceForm
        onSubmit={(values) =>
          editSurface.mutate({ id: Number(surfaceId), data: values })
        }
        initialValues={surface.data}
        loading={surface.isLoading}
      />
    </Page>
  );
};

export default SetupSurfaceEditPage;
