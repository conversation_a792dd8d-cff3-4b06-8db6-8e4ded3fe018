import { useCreateSurface } from "~/query/surface/mutation";
import Page from "~/components/Page";
import SetupSurfaceForm from "~/form/SetupSurfaceForm";
import { useRouter } from "next/router";

const CreateSetupSurfacePage = () => {
  const router = useRouter();
  const createSurface = useCreateSurface();

  return (
    <Page title={"New Setup Surface"}>
      <SetupSurfaceForm
        onSubmit={(values) => {
          createSurface.mutate(values);
          router.push("/surface");
        }}
      />
    </Page>
  );
};

export default CreateSetupSurfacePage;
