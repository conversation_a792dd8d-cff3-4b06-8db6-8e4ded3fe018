import { useAllSurfaces } from "~/query/surface/query";
import Page from "~/components/Page";
import { DataTable } from "~/components/ui/datatable/data-table";
import { SetupSurfaceColumns } from "~/table/SetupSurfaceTable/columns";

const SetupSurfaceList = () => {
  const setupSurface = useAllSurfaces(false);

  return (
    <Page
      title={"Setup Surfaces"}
      actionButton={{
        label: "New Setup Surface",
        href: "/surface/create",
        newItem: true,
      }}
    >
      <DataTable
        columns={SetupSurfaceColumns()}
        data={setupSurface?.data || []}
        isLoading={setupSurface.isLoading}
      />
    </Page>
  );
};

export default SetupSurfaceList;
