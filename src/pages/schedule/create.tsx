import React from "react";
import { useRouter } from "next/router";
import { ScheduleValues } from "~/server/schedule/types";
import { toast } from "sonner";
import ScheduleForm from "~/form/ScheduleForm";
import Page from "~/components/Page";

const CreateSchedulePage = () => {
  const router = useRouter();

  const handleSubmit = async (values: ScheduleValues) => {
    const request = fetch("/api/schedules/create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(request, {
      loading: "Creating schedule...",
      success: () => {
        router.push("/schedule");
        return "Schedule created";
      },
      error: "Error creating schedule",
    });
  };

  return (
    <Page title={"New Schedule"}>
      <ScheduleForm onSubmit={handleSubmit} />
    </Page>
  );
};

export default CreateSchedulePage;
