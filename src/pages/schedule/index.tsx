import React from "react";
import Page from "~/components/Page";
import { ScheduleColumns } from "~/table/SchedulesTable/columns";
import { DataTable } from "~/components/ui/datatable/data-table";
import { useSchedules } from "~/query/schedule/query";

const ScheduleListPage = () => {
  const schedules = useSchedules();
  return (
    <Page
      title={"Schedules"}
      actionButton={{
        label: "Create Schedule",
        newItem: true,
        href: "/schedule/create",
      }}
    >
      <DataTable columns={ScheduleColumns()} data={schedules?.data || []} />
    </Page>
  );
};

export default ScheduleListPage;
