import React from "react";
import { GetServerSidePropsContext } from "next";
import { getToken } from "next-auth/jwt";
import { db } from "~/server/db";
import { Schedule } from "@prisma/client";
import { useRouter } from "next/router";
import { ScheduleValues } from "~/server/schedule/types";
import { toast } from "sonner";
import Page from "~/components/Page";
import ScheduleForm from "~/form/ScheduleForm";

export const getServerSideProps = async ({
  req,
  query,
}: GetServerSidePropsContext) => {
  const token = await getToken({ req });
  if (!token) {
    return {
      redirect: {
        destination: "/auth/login",
        permanent: false,
      },
    };
  }
  const { id } = query;

  const numId = parseInt(id as string);
  if (isNaN(numId)) {
    return {
      redirect: {
        destination: "/schedule",
        permanent: false,
      },
    };
  }

  const data = await db.schedule.findFirst({
    where: {
      account: {
        id: token.accountId,
      },
      id: numId,
    },
  });

  if (!data) {
    return {
      redirect: {
        destination: "/schedule",
        permanent: false,
      },
    };
  }

  return { props: { data } };
};

type EditScheduleProps = {
  data: Schedule;
};

const EditSchedule = ({ data }: EditScheduleProps) => {
  const router = useRouter();

  const onSubmit = async (values: ScheduleValues) => {
    const request = fetch(`/api/schedules/${data.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });

    toast.promise(request, {
      loading: "Updating Schedule...",
      success: () => {
        router.push("/schedule");
        return "Schedule Updated";
      },
      error: "Error Updating Schedule",
    });
  };

  return (
    <Page title={"Edit Schedule"} subtitle={data.name}>
      <ScheduleForm onSubmit={onSubmit} initialValues={data} />
    </Page>
  );
};

export default EditSchedule;
