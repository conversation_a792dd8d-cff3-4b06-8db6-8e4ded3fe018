import React from "react";
import Page from "~/components/Page";
import StatCard from "~/components/StatCard";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import BarChart from "~/components/Graph/BarChart";
import { Skeleton } from "~/components/ui/skeleton";
import RecentSales from "~/components/Order/RecentSales";
import { getCurrencyString, getCurrencyValue } from "~/server/lib/currency";
import {
  useCartSessions,
  useOrderStats,
  useRecentSales,
  useSalesOverview,
} from "~/query/account/query";

export type StatCardProps = {
  title: string;
  value: number;
  percentage: number;
};

export default function Home() {
  const salesOverview = useSalesOverview();
  const orders = useRecentSales();
  const ordersThisWeek = useOrderStats();
  const cartSessionsData = useCartSessions();

  return (
    <Page title={"Dashboard"}>
      <div className={"flex-1 space-y-4 p-8 pt-6"}>
        <div className="grid gap-4 sm:grid-cols-2 xl:grid-cols-2">
          {[ordersThisWeek, cartSessionsData]
            .filter((item) => item.data !== undefined)
            .map((cardItem) => {
              const card = cardItem.data;
              return (
                <StatCard
                  positive={(card?.percentage || 0) > 0}
                  value={card?.value}
                  percentage={card?.percentage}
                  loading={card === null}
                  label={card?.title || ""}
                />
              );
            })}
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          <Card className="col-span-4">
            <CardHeader>
              <CardTitle>Sales Overview</CardTitle>
              <CardDescription>
                {!salesOverview.isPending && salesOverview.data ? (
                  `All time sales: ${getCurrencyString(
                    salesOverview.data.totalAmount,
                  )}`
                ) : (
                  <Skeleton className="h-4 w-24" />
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="px-4 py-2">
              <BarChart
                items={salesOverview?.data?.payments || []}
                loading={salesOverview.isPending}
                money={true}
                tooltipLabelFormatter={(value) => {
                  if (typeof value === "number") {
                    return `$${getCurrencyValue(value)}`;
                  }

                  const monthName = Intl.DateTimeFormat("en", {
                    month: "short",
                  }).format(new Date(value.split("/")?.at(0) ?? 0)); // January

                  return `${monthName} ${value.split("/")[1]}`;
                }}
              />
            </CardContent>
          </Card>
          <Card className="col-span-3">
            <CardHeader>
              <CardTitle>Recent Sales</CardTitle>
              {orders?.data ? (
                <CardDescription>{`You made ${
                  orders?.data?.orderCount || 0
                } sales this month.`}</CardDescription>
              ) : (
                <CardDescription>
                  <Skeleton className="h-4 w-24" />
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <RecentSales
                loading={orders.isPending}
                orders={orders?.data?.orders || []}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </Page>
  );
}
