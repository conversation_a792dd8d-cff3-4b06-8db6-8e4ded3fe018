import { GetServerSidePropsContext } from "next";
import { db } from "~/server/db";

export const getServerSideProps = async ({
  query,
}: GetServerSidePropsContext) => {
  const { accountId, orderId, contractId } = query;

  if (!accountId || !orderId || !contractId) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  const numberAccountId = parseInt(accountId as string);

  if (!numberAccountId || isNaN(numberAccountId)) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  const account = await db.account.findUnique({
    where: {
      id: numberAccountId,
    },
  });

  if (!account) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  return {
    redirect: {
      destination: `https://${account.customDomain}/post-order/contract/${
        orderId as string
      }/${contractId as string}`,
      permanent: false,
    },
  };
};

const Notfound = () => {
  return (
    <div className={"text-center"}>
      If you're seeing this, something went wrong. If you have a rental please
      contact our office as soon as possible, otherwise please try again later.
    </div>
  );
};

Notfound.auth = false;
Notfound.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

export default Notfound;

// export const getServerSideProps = async ({
//   query,
// }: GetServerSidePropsContext) => {
//   const { accountId, orderId, contractId } = query;
//
//   if (!accountId || !orderId || !contractId) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const numberAccountId = parseInt(accountId as string);
//
//   if (!numberAccountId || isNaN(numberAccountId)) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const account = await db.account.findUnique({
//     where: {
//       id: numberAccountId,
//     },
//   });
//
//   if (!account) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const order = await db.order.findUnique({
//     where: {
//       accountId: account.id,
//       id: Number(orderId),
//     },
//     include: {
//       eventAddress: true,
//       Customer: true,
//       setupSurface: {
//         select: {
//           name: true,
//         },
//       },
//       PaymentDetails: {
//         select: {
//           method: true,
//           methodId: true,
//           amount: true,
//           tip: true,
//         },
//       },
//       couponApplied: {
//         select: {
//           name: true,
//           discount: true,
//           discountType: true,
//         },
//       },
//       OrderFee: true,
//       OrderProduct: {
//         select: {
//           productId: true,
//           pricePaid: true,
//           product: {
//             select: {
//               name: true,
//               price: true,
//             },
//           },
//           quantity: true,
//         },
//       },
//     },
//   });
//
//   if (!order) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const contract = await db.contract.findUnique({
//     where: {
//       orderId: order.id,
//       id: contractId as string,
//     },
//   });
//
//   if (!contract) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const contractItems = orderContractItems(
//     order.OrderProduct,
//     order.OrderProduct.map((item) => {
//       return {
//         id: item.productId,
//         name: item.product.name,
//       };
//     }),
//   );
//
//   const info = orderInfoToSharedItems(order, order.OrderFee);
//
//   const paidItems = orderPaidItems(order.PaymentDetails);
//
//   const topData: TopContractProps = {
//     minimumDeposit: account.minimumOrderPaymentPercentage,
//     invoiceNumber: order.id.toString(),
//     customerName: `${order.Customer.firstName} ${order.Customer.lastName}`,
//     eventAddress: order.eventAddress,
//     phoneNumber: order.Customer.phoneNumber ?? undefined,
//     email: order.Customer.email,
//     surface: order?.setupSurface?.name ?? undefined,
//     contractItems: contractItems,
//     paidItems: paidItems,
//     contractSharedItems: info,
//   };
//   return {
//     props: {
//       topData,
//       orderId: order.id,
//       contractId: contract.id,
//       companyName: account.name,
//       cleaningFee: "$75.00",
//       companyState: order.eventAddress.state || "",
//     },
//   };
// };
//
// type ContractPageProps = {
//   orderId: number;
//   contractId: number;
//   topData: TopContractProps;
//   companyName: string;
//   cleaningFee: string;
//   companyState: string;
// };
//
// const ContractPage = ({
//   topData,
//   companyName,
//   companyState,
//   cleaningFee,
//   ...props
// }: ContractPageProps) => {
//   const pageRef = useRef<HTMLDivElement>(null);
//
//   const onSave = () => {
//     if (pageRef.current === null) {
//       return;
//     }
//
//     const filter = (node: HTMLElement) => {
//       const exclusionClasses = ["no-print"];
//       return !exclusionClasses.some(
//         (classname) => node.classList?.contains(classname),
//       );
//     };
//
//     function b64toBlob(dataURI: any) {
//       const byteString = atob(dataURI.split(",")[1]);
//       const ab = new ArrayBuffer(byteString.length);
//       const ia = new Uint8Array(ab);
//
//       for (let i = 0; i < byteString.length; i++) {
//         ia[i] = byteString.charCodeAt(i);
//       }
//       return new Blob([ab], { type: "image/jpeg" });
//     }
//     const sendImage = async (imageData: string) => {
//       const blob = b64toBlob(imageData);
//       const file = new File([blob], "image.jpeg", { type: "image/jpeg" });
//
//       await fetch(
//         `/api/public/${companyName}/contracts?contractId=${props.contractId}&orderId=${props.orderId}`,
//         {
//           method: "POST",
//           headers: {
//             "Content-Type": "image/jpeg",
//           },
//           body: file,
//         },
//       );
//     };
//
//     toJpeg(pageRef.current, {
//       filter: filter,
//       backgroundColor: "#a0a0a0",
//       pixelRatio: 0.8,
//       quality: 0.9,
//     })
//       .then(async (blobData) => {
//         if (blobData === null) {
//           throw new Error("Blob data is null");
//         }
//         try {
//           const blob = new Blob([blobData], { type: "image/jpeg" });
//
//           if (!blob) {
//             return;
//           }
//           await sendImage(blobData);
//         } catch (e) {
//           captureException(e);
//           console.log(e);
//           return;
//         }
//       })
//       .catch((err) => {
//         captureException(err);
//         console.log(err);
//       });
//   };
//
//   return (
//     <div className={"pt-4 container px-10 mx-auto bg-background"}>
//       <article ref={pageRef}>
//         <TopContract {...topData} />
//         <br />
//         <br />
//         <div className={"flex text-center"}>
//           <h1 className={"font-bold text-2xl"}>Thank you for your Business!</h1>
//         </div>
//         <div className={"mt-4 break-before-page"}>
//           <ContractCopyPage
//             companyName={companyName}
//             cleaningFee={cleaningFee}
//             companyState={companyState}
//             bigBox={false}
//             copy={MiddleCopy}
//             onSave={onSave}
//           />
//         </div>
//         <div>
//           <ContractCopyPage
//             companyName={companyName}
//             cleaningFee={cleaningFee}
//             companyState={companyState}
//             bigBox={true}
//             copy={BottomCopy}
//             onSave={onSave}
//           />
//         </div>
//       </article>
//     </div>
//   );
// };
//
// ContractPage.auth = false;
// ContractPage.getLayout = function getLayout(page: React.ReactNode) {
//   return (
//     <div className={"bg-border min-h-screen"}>
//       {page}
//       <Toaster />
//     </div>
//   );
// };
// export default ContractPage;
