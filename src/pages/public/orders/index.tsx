import { getStripeAccount } from "~/server/lib/stripe";
import React from "react";
import { GetServerSidePropsContext } from "next";
import { db } from "~/server/db";
import { getAccountLogo } from "~/server/lib/logo";
import Image from "next/image";

export const getServerSideProps = async ({
  req,
  query,
}: GetServerSidePropsContext) => {
  const { session_id, accountId } = query;

  if (!accountId) {
    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  const account = await db.account.findUnique({
    where: {
      id: parseInt(accountId as string),
    },
  });

  if (!account) {
    return {
      redirect: {
        destination: "/",
        permanent: false,
      },
    };
  }

  const destination = account.customDomain || "/";
  if (!session_id) {
    return {
      redirect: {
        destination: destination,
        permanent: false,
      },
    };
  }

  const session = await getStripeAccount(account).checkout.sessions.retrieve(
    session_id as string,
  );

  if (!session) {
    return {
      redirect: {
        destination: destination,
        permanent: false,
      },
    };
  }

  return {
    props: {
      logo:
        (await getAccountLogo(account)) ??
        `https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/381ff54a-dcbe-4fea-c5c4-f6210e66fd00/w=490,h=330`,
      businessEmail: account.businessEmail,
      sessionId: session.id,
      customerEmail: session.customer_details?.email || "your email",
    },
  };
};

type StripeSuccessPageProps = {
  sessionId: string;
  customerEmail: string;
  businessEmail: string;
  logo: string;
};

const StripeSuccessPage = (props: StripeSuccessPageProps) => {
  return (
    <section
      id="success"
      className={"flex flex-col items-center text-center justify-center"}
    >
      <Image
        src={props.logo}
        width="245"
        height="160"
        alt={`Logo`}
        className={"my-0 mx-auto"}
      />
      <p>
        We appreciate your business! A confirmation email will be sent to{" "}
        {props.customerEmail}. If you have any questions, please email{" "}
        <a href={`mailto:${props.businessEmail}`}>{props.businessEmail}</a>.
      </p>
    </section>
  );
};

StripeSuccessPage.auth = false;
StripeSuccessPage.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};
export default StripeSuccessPage;
