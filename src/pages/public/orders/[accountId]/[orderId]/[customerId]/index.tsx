import { GetServerSidePropsContext } from "next";
import { db } from "~/server/db";

export const getServerSideProps = async ({
  query,
}: GetServerSidePropsContext) => {
  const { accountId, orderId, customerId } = query;

  if (!accountId || !orderId || !customerId) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  const numberAccountId = parseInt(accountId as string);

  if (!numberAccountId || isNaN(numberAccountId)) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  const account = await db.account.findUnique({
    where: {
      id: numberAccountId,
    },
  });

  if (!account) {
    return {
      redirect: {
        destination: "/public/notfound",
        permanent: false,
      },
    };
  }

  return {
    redirect: {
      destination: `https://${account.customDomain}/post-order/orders/${
        orderId as string
      }/${customerId as string}`,
      permanent: false,
    },
  };
};

const Notfound = () => {
  return (
    <div className={"text-center"}>
      If you're seeing this, something went wrong. If you have a rental please
      contact our office as soon as possible, otherwise please try again later.
    </div>
  );
};

Notfound.auth = false;
Notfound.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};

export default Notfound;

//   }
//
// export const getServerSideProps = async ({
//   query,
// }: GetServerSidePropsContext) => {
//   const { accountId, orderId, customerId } = query;
//
//   if (!accountId || !orderId || !customerId) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const numberAccountId = parseInt(accountId as string);
//
//   if (!numberAccountId || isNaN(numberAccountId)) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const account = await db.account.findUnique({
//     where: {
//       id: numberAccountId,
//     },
//   });
//
//   if (!account) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const customer = await db.customer.findUnique({
//     where: {
//       id: customerId as string,
//     },
//     include: {
//       address: true,
//     },
//   });
//
//   if (!customer) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const order = await db.order.findUnique({
//     where: {
//       accountId: account.id,
//       customerId: customer.id,
//       id: Number(orderId),
//     },
//     include: {
//       eventAddress: true,
//       Customer: true,
//       setupSurface: {
//         select: {
//           name: true,
//         },
//       },
//       PaymentDetails: {
//         select: {
//           method: true,
//           methodId: true,
//           amount: true,
//           tip: true,
//         },
//       },
//       couponApplied: {
//         select: {
//           name: true,
//           discount: true,
//           discountType: true,
//         },
//       },
//       OrderFee: true,
//       OrderProduct: {
//         select: {
//           productId: true,
//           pricePaid: true,
//           product: {
//             select: {
//               name: true,
//               price: true,
//             },
//           },
//           quantity: true,
//         },
//       },
//     },
//   });
//
//   if (!order) {
//     return {
//       redirect: {
//         destination: "/public/notfound",
//         permanent: false,
//       },
//     };
//   }
//
//   const orderFinalTotal = CurrencyValue.fromPlatform(order.finalTotal);
//   const orderTotalPaid = CurrencyValue.fromPlatform(order.totalPaid);
//
//   const contractItems = orderContractItems(
//     order.OrderProduct,
//     order.OrderProduct.map((item) => {
//       return {
//         id: item.productId,
//         name: item.product.name,
//       };
//     }),
//   );
//
//   const info = orderInfoToSharedItems(order, order.OrderFee);
//
//   const paidItems = orderPaidItems(order.PaymentDetails);
//
//   const topData: TopContractProps = {
//     header: "PAYMENT",
//     invoiceNumber: order.id.toString(),
//     customerName: `${order.Customer.firstName} ${order.Customer.lastName}`,
//     eventAddress: order.eventAddress,
//     phoneNumber: order.Customer.phoneNumber || undefined,
//     email: order.Customer.email,
//     surface: order?.setupSurface?.name || undefined,
//     minimumDeposit: account.minimumOrderPaymentPercentage,
//     contractItems: contractItems,
//     paidItems: paidItems,
//     contractSharedItems: info,
//   };
//   const stripe = getStripeAccount(account);
//   const stripeCustomer = await findStripeCustomer(
//     account,
//     customer.email,
//     customer.id,
//   );
//   const amountLeftToPay = orderFinalTotal.subtract(orderTotalPaid);
//   let minimum_payment = CurrencyValue.fromPlatform(
//     getMinimumDeposit(
//       amountLeftToPay.amount,
//       account.minimumOrderPaymentPercentage,
//     ),
//   );
//
//   if (amountLeftToPay.amount < 30) {
//     minimum_payment = amountLeftToPay;
//   }
//
//   const adjustable_price = await stripe.prices.create({
//     currency: "usd",
//     product_data: {
//       name: `Order #${order.id}`,
//     },
//     custom_unit_amount: {
//       enabled: true,
//       minimum: Math.max(50, minimum_payment.stripeAmount),
//       maximum: Math.max(51, amountLeftToPay.stripeAmount + 1),
//       preset: Math.max(50, amountLeftToPay.stripeAmount),
//     },
//   });
//
//   // Create Checkout Sessions from body params.
//   const session = await stripe.checkout.sessions.create({
//     ui_mode: "embedded",
//     line_items: [
//       {
//         price: adjustable_price.id,
//         quantity: 1,
//       },
//     ],
//     metadata: {
//       lastAction: "dashboard_payment",
//       orderId: order.id.toString(),
//       customerId: customer.id,
//     },
//     payment_intent_data: {
//       metadata: {
//         lastAction: "dashboard_payment",
//         orderId: order.id.toString(),
//         customerId: customer.id,
//       },
//       setup_future_usage: "off_session",
//     },
//     customer: stripeCustomer?.id,
//     mode: "payment",
//     return_url: `https://dash.partyrentalplatform.com/public/orders?session_id={CHECKOUT_SESSION_ID}&accountId=${account.id}`,
//     automatic_tax: { enabled: false },
//   });
//
//   let publicKey: string | undefined;
//   if (account.name === "CentralArkansasInflatables") {
//     publicKey =
//       process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY ||
//       process.env.STRIPE_PUBLIC_KEY;
//   } else {
//     publicKey = process.env.NEXT_PUBLIC_PRP_STRIPE_KEY;
//   }
//
//   return {
//     props: {
//       topData,
//       hasPaymentLeft: amountLeftToPay.amount > 1,
//       amountToPay: amountLeftToPay.toPlatformString(),
//       orderId: order.id,
//       companyName: account.name,
//       stripeAccountId: account.stripeAccountId,
//       stripePublicKey: publicKey,
//       defaultValues: {
//         name: `${customer.firstName} ${customer.lastName}`,
//         address: customer.address,
//       },
//       clientSecret: session.client_secret,
//       companyState: order.eventAddress.state || "",
//     },
//   };
// };
//
// type OrderPaymentPageProps = {
//   orderId: number;
//   topData: TopContractProps;
//   companyName: string;
//   stripeAccountId: string;
//   stripePublicKey: string;
//   hasPaymentLeft: boolean;
//   amountToPay: string;
//   companyState: string;
//   defaultValues: {
//     name: string;
//     address: AddressType | undefined;
//   };
//   clientSecret: string;
// };
//
// const OrderPaymentPage = ({
//   topData,
//   companyName,
//   companyState,
//   defaultValues,
//   ...props
// }: OrderPaymentPageProps) => {
//   const [stripeObject, setStripeObject] = useState<Stripe | null>(null);
//
//   useEffect(() => {
//     const fetchStripeObject = async () => {
//       if (companyName === "CentralArkansasInflatables") {
//         const stripeKey =
//           props.stripePublicKey ||
//           "pk_live_51OEdrxH0Jv34YGoncYriwGBGKK3yR38TRDcYsjPSC56ZmMFmdjPy5L1yMTsw4u6GVyvRnEbgpAFzHO3H1jBYynR200MSRFMboR";
//         const res = await loadStripe(stripeKey);
//         setStripeObject(res);
//         return;
//       }
//
//       if (props.stripeAccountId) {
//         const res = await loadStripe(props.stripePublicKey || "", {
//           stripeAccount: props.stripeAccountId,
//         });
//         setStripeObject(res);
//       }
//     };
//     fetchStripeObject();
//   }, [props.stripeAccountId, companyName]);
//
//   return (
//     <div>
//       <div className={"flex justify-center w-[100%]"}>
//         <TopContract {...topData} />
//       </div>
//       {!props.hasPaymentLeft || props.amountToPay.toLowerCase() === "$0.00" ? (
//         <div className={"flex flex-col justify-center w-[100%] mt-8"}>
//           <h1 className={"text-2xl text-center"}>
//             No payment is required at this time.
//           </h1>
//           <p className={"text-md text-center"}>
//             If you would like to leave a tip, please leave it with the delivery
//             driver and it will be split among the team.
//           </p>
//         </div>
//       ) : (
//         <>
//           {stripeObject ? (
//             <EmbeddedCheckoutProvider
//               stripe={stripeObject}
//               options={{ clientSecret: props.clientSecret }}
//             >
//               <EmbeddedCheckout />
//             </EmbeddedCheckoutProvider>
//           ) : (
//             <LoadingSpinner />
//           )}
//         </>
//       )}
//     </div>
//   );
// };
//
// OrderPaymentPage.auth = false;
// OrderPaymentPage.getLayout = function getLayout(page: React.ReactNode) {
//   return <>{page}</>;
// };
// export default OrderPaymentPage;
