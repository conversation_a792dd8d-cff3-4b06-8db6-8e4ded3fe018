import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Check } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { Button } from "~/components/ui/button";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { LoadingSpinner } from "~/pages/_app";

interface OnboardingProgressProps {
  currentStep: number;
  steps: string[];
}
export function OnboardingProgress({
  currentStep,
  steps,
}: OnboardingProgressProps) {
  return (
    <div className="flex items-center justify-center w-full mb-8 space-x-4">
      {steps.map((step, index) => (
        <React.Fragment key={step}>
          <div className="flex flex-col items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center border-2 
                ${
                  index < currentStep
                    ? "bg-primary border-primary text-primary-foreground"
                    : index === currentStep
                      ? "border-primary text-primary"
                      : "border-muted-foreground text-muted-foreground"
                }`}
            >
              {index < currentStep ? (
                <Check className="w-4 h-4" />
              ) : (
                <span>{index + 1}</span>
              )}
            </div>
            <span
              className={`text-sm mt-1 ${
                index === currentStep ? "text-primary" : "text-muted-foreground"
              }`}
            >
              {step}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div
              className={`h-[2px] w-16 mt-4 ${
                index < currentStep ? "bg-primary" : "bg-muted"
              }`}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
}
const PricingPage = ({
  staging,
  customerSessionSecret,
}: {
  staging: boolean;
  customerSessionSecret: string;
}) => {
  if (staging) {
    const element = React.createElement("stripe-pricing-table", {
      "pricing-table-id": "prctbl_1QpLeKCA1CWVJ5LNwR5kqnMp",
      "publishable-key":
        "pk_test_51Okx2OCA1CWVJ5LNteAZWtkRsJMCb44NNrSsAIK3u5XilVBM2EURPSEjYZIGi55lU5IbX8eT5jT68UP1mCFQMSd700CbACsdGQ",
      "customer-session-client-secret": customerSessionSecret,
    });

    return <div className="w-full max-w-6xl mx-auto">{element}</div>;
  }
  const element = React.createElement("stripe-pricing-table", {
    "pricing-table-id": "prctbl_1RKVzdCA1CWVJ5LN9WMLBMnl",
    "publishable-key":
      "pk_live_51Okx2OCA1CWVJ5LNpMp6btoB91rpLMkIleg8d75IrB6SP8haA6Kp6rkOA4lweB2slz6VHWgbwQZMPjVLc9dhIC00000d1OgcWO",
    "customer-session-client-secret": customerSessionSecret,
  });

  return <div className="w-full max-w-6xl mx-auto">{element}</div>;
};

const schema = z.object({
  businessName: z.string().min(1, "Business name is required"),
  referrer: z.string().nullish(),
});

const steps = ["Account Details", "Activate Account"];

type SignupPageProps = {
  email: string;
  password: string;
  businessName: string;
  clientSecret: string;
};

const SignupPage = (props: SignupPageProps) => {
  const [clientSecret, setClientSecret] = useState<string>();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [staging, setStaging] = useState<boolean>(false);
  const searchParams = useSearchParams();
  const form = useForm({
    defaultValues: {
      businessName: "",
      referrer: searchParams.get("referrer") ?? "",
    },
    resolver: zodResolver(schema),
  });

  useEffect(() => {
    if (
      window.location.href.includes("staging") ||
      window.location.href.includes("localhost")
    ) {
      console.log("Staging!");
      setStaging(true);
      setClientSecret(props.clientSecret);
    } else {
      console.log("Production!", window.location.href);
      setClientSecret(props.clientSecret);
    }
    const script = document.createElement("script");
    script.src = "https://js.stripe.com/v3/pricing-table.js";
    script.async = true;

    // Add script to document
    document.body.appendChild(script);

    // Cleanup function
    return () => {
      document.body.removeChild(script);
    };
  }, []); // Empty dependency array means this runs once on mount

  const onSubmit = (data: z.infer<typeof schema>) => {
    setLoading(true);
    fetch("/api/signup/createAccount", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: props.email,
        password: props.password,
        businessName: data.businessName,
        referrer: data.referrer,
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        setLoading(false);
        if (data.error) {
          form.setError("businessName", {
            message: data.error,
          });
          return;
        }
        if (data?.sessionId) {
          setClientSecret(data.sessionId);
          setCurrentStep(1);
        }
      });
  };
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="w-full mx-auto space-y-4 max-w-md">
            <FormField
              control={form.control}
              name="businessName"
              rules={{
                required: "Business name is required",
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Business name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your business name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="referrer"
              rules={{
                required: "Please tell us how you found us",
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How did you hear about us?</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g. Google, Friend, Social Media"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end mt-8">
              <Button type="submit" variant={"primary"}>
                Next
              </Button>
            </div>
          </div>
        );
      case 1:
        return (
          <div className="text-center">
            {clientSecret && (
              <PricingPage
                customerSessionSecret={clientSecret}
                staging={staging}
              />
            )}
          </div>
        );
    }
  };
  return (
    <div className="w-full min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full space-y-8">
        <div className="text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            Welcome to Party Rental platform
          </h1>
          <p className="text-sm text-muted-foreground mt-2">
            Let's get your account set up in just a few steps
          </p>
        </div>
        {loading && <LoadingSpinner />}
        <OnboardingProgress currentStep={currentStep} steps={steps} />
        <Form
          {...form}
          onSubmit={onSubmit}
          formItemProps={{
            className: "space-y-4",
          }}
        >
          {renderStep()}
        </Form>
      </div>
    </div>
  );
};

export default SignupPage;
SignupPage.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};
