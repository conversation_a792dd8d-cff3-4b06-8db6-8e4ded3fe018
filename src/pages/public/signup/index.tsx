import { <PERSON><PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import React from "react";
import { Input } from "~/components/ui/input";
import { But<PERSON> } from "~/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import SignupPage from "~/pages/public/signup/components/Signup";
import { Separator } from "~/components/ui/separator";

const CreateUserPage = () => {
  const [submit, setSubmit] = React.useState(false);
  const [clientSecret, setClientSecret] = React.useState<string | null>(null);
  const form = useForm({
    defaultValues: {
      email: "",
      password: "",
      businessName: "",
    },
    resolver: zodResolver(
      z.object({
        email: z.string().email(),
        password: z
          .string()
          .min(8, "Password must be at least 8 characters")
          .max(64, "Password must be less than 64 characters")
          .regex(/^(?=.*[a-z])/, {
            message: "Password must contain at least one lowercase letter",
          })
          .regex(/^(?=.*[A-Z])/, {
            message: "Password must contain at least one uppercase letter",
          })
          .regex(/^(?=.*\d)/, {
            message: "Password must contain at least one number",
          })
          .regex(/^(?=.*[!@#$%^&*()_\-+={}[\]\\|:;"'<>,.?/])/, {
            message: "Password must contain at least one special character",
          }),
        businessName: z
          .string()
          .min(1, "Business name is required")
          .max(255, "Business name is too long")
          .regex(
            /^[a-zA-Z0-9\s]+$/,
            "Business name can only contain letters, numbers, and spaces",
          ),
      }),
    ),
  });

  if (submit && clientSecret) {
    return (
      <SignupPage
        businessName={form.watch("businessName")}
        email={form.watch("email")}
        password={form.watch("password")}
        clientSecret={clientSecret}
      />
    );
  }

  return (
    <div className="w-full min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 items-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            Welcome to Party Rental Platform
          </h1>
          <p className="font-semibold text-muted-foreground mt-2">
            Start Your 14-Day Free Trial
          </p>
        </div>
        <FormProvider {...form}>
          <form
            className={"space-y-4"}
            onSubmit={(e) => {
              form.handleSubmit(
                (data) => {
                  fetch("/api/signup/createAccount", {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                      email: data.email,
                      password: data.password,
                      businessName: data.businessName,
                      referrer: undefined,
                    }),
                  })
                    .then((res) => res.json())
                    .then((data) => {
                      if (data.error) {
                        form.setError("businessName", {
                          message: data.error,
                        });
                        return;
                      }
                      if (data?.sessionId) {
                        setClientSecret(data.sessionId);
                        setSubmit(true);
                      }
                    });
                },
                (err) => {
                  console.error(err);
                },
              )(e);
            }}
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="Email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="Password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Separator orientation={"horizontal"} />
            <FormField
              control={form.control}
              name="businessName"
              rules={{
                required: "Business name is required",
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Your company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className={"w-full justify-end flex"}>
              <Button type="submit" variant={"primary"} className={""}>
                Start Free Trial
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default CreateUserPage;
CreateUserPage.getLayout = (page: React.ReactNode) => {
  return <>{page}</>;
};

CreateUserPage.auth = false;
