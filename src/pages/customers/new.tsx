import React from "react";
import { useRouter } from "next/router";
import CustomerForm from "~/form/CustomerForm";
import { toast } from "sonner";
import Page from "~/components/Page";
import { captureException } from "@sentry/core";
import { CustomerValues } from "~/query/customer";
import { Customer } from "@prisma/client";

export const newCustomerSubmission = async (
  values: CustomerValues,
): Promise<Customer | null> => {
  const loadingToast = toast.loading("Creating customer...", {
    dismissible: false,
    duration: 15_000,
  });

  const response = await fetch("/api/customers/create", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(values),
  });

  toast.dismiss(loadingToast);

  try {
    if (!response.ok) {
      toast.error("Error creating customer");
      return null;
    }

    const data: { success: boolean; customer: Customer; error?: string } =
      await response.json();
    if (data.success) {
      toast.success("Customer created");
      return data.customer;
    } else {
      toast.error(data.error);
      return null;
    }
  } catch (e) {
    captureException(e);
    toast.error("Error creating customer");
    return null;
  }
};

const NewCustomerPage = () => {
  const router = useRouter();

  return (
    <Page title={"New Customer"}>
      <CustomerForm
        onSubmit={(values) => {
          newCustomerSubmission(values).then((value) => {
            if (value) {
              router.push("/customers");
            }
          });
        }}
      />
    </Page>
  );
};

export default NewCustomerPage;
