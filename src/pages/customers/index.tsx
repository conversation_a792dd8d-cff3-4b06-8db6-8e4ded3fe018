import React from "react";
import Page from "~/components/Page";
import { DataTable } from "~/components/ui/datatable/data-table";
import CustomerColumns from "~/table/CustomerTable/columns";
import { useCustomers } from "~/query/customer";

const CustomerListPage = () => {
  const { data: customers, isLoading } = useCustomers();

  return (
    <Page
      title={"Customers"}
      actionButton={{
        newItem: true,
        label: "New Customer",
        href: "/customers/new",
      }}
    >
      <DataTable
        columns={CustomerColumns()}
        data={customers ?? []}
        isLoading={isLoading}
      />
    </Page>
  );
};

export default CustomerListPage;
