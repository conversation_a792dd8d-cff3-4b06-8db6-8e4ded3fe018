import { useRouter } from "next/router";
import React from "react";
import { CustomerValues, useCustomer } from "~/query/customer";
import Page from "~/components/Page";
import CustomerForm from "~/form/CustomerForm";
import { toast } from "sonner";

const EditCustomerPage = () => {
  const router = useRouter();
  const { data, isPending } = useCustomer(router.query.id as string);

  const onSubmit = async (values: CustomerValues) => {
    const response = fetch(`/api/customers/${router.query.id as string}/edit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(response, {
      loading: "Updating customer...",
      success: (data) => {
        if (data.status === 409) {
          return "Customer with that email already exists";
        }
        router.push("/customers");
        return "Customer updated";
      },
      error: "Error updating customer",
    });
  };

  return (
    <Page title={data?.email ?? "Edit"}>
      {!isPending && (
        <CustomerForm onSubmit={onSubmit} initialValues={data ?? undefined} />
      )}
    </Page>
  );
};

export default EditCustomerPage;
