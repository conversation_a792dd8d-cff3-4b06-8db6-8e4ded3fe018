import { CustomerHeader } from "~/components/Customer/view/CustomerHeader";
import {
  useCustomer,
  useCustomerDocuments,
  useCustomerOrders,
  useCustomerReachData,
} from "~/query/customer";
import { useRouter } from "next/router";
import { <PERSON><PERSON>he<PERSON>, PhoneCall } from "lucide-react";
import CustomerOrdersSection from "~/components/Customer/view/CustomerOrderSection";
import { SANE_FORMATTER } from "~/server/lib/time";
import { getCurrencyString } from "~/server/lib/currency";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { CustomerListItem } from "~/components/Customer/view/CustomerListItem";
import Link from "next/link";
import Page from "~/components/Page";

const CustomerViewPage = () => {
  const router = useRouter();

  const { data, isPending } = useCustomer(router.query.id as string);
  const orders = useCustomerOrders(router.query.id as string);
  const reach = useCustomerReachData(router.query.id as string);
  const documents = useCustomerDocuments(router.query.id as string);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
      });
    }
  };

  return (
    <Page
      title={"Customer View"}
      actionButton={{
        label: "Edit",
        onClick: () => {
          router.push(`/customers/${router.query.id as string}/edit`);
        },
      }}
    >
      <CustomerHeader
        loading={isPending}
        name={data?.firstName ? `${data?.firstName} ${data.lastName}` : ""}
        email={data?.email ?? ""}
        phoneNumber={data?.phoneNumber ?? ""}
        company={data?.company ?? ""}
        reference={data?.reference ?? ""}
        isBanned={data?.banned ?? false}
        isTaxExempt={false}
        internalNotes={data?.internalNotes ?? ""}
      />
      <div className="sticky top-0 bg-white border-b z-10">
        <div className="container mx-auto">
          <div className="flex gap-1">
            {["orders", "documents", "contact"].map((section) => (
              <button
                key={section}
                onClick={() => scrollToSection(section)}
                className="px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 whitespace-nowrap capitalize"
              >
                {section}
              </button>
            ))}
          </div>
        </div>
      </div>
      <main className="container mx-auto p-6 space-y-12">
        {/*<section id="overview" className="space-y-6">*/}
        {/*  <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">*/}
        {/*    <ChevronRight className="w-5 h-5" />*/}
        {/*    Overview*/}
        {/*  </div>*/}
        {/*<div className="grid grid-cols-1 md:grid-cols-3 gap-4">*/}
        {/*  <StatCard*/}
        {/*    icon={CreditCard}*/}
        {/*    title="Total Spent"*/}
        {/*    value="$24,500.00"*/}
        {/*    subtitle="Lifetime value"*/}
        {/*  />*/}
        {/*  <StatCard*/}
        {/*    icon={Clock}*/}
        {/*    title="Active Since"*/}
        {/*    value="2 Years"*/}
        {/*    subtitle="Member since 2021"*/}
        {/*  />*/}
        {/*  <StatCard*/}
        {/*    icon={FileCheck}*/}
        {/*    title="Documents"*/}
        {/*    value="5"*/}
        {/*    subtitle="Signed contracts"*/}
        {/*  />*/}
        {/*</div>*/}
        {/*</section>*/}
        <section id="orders" className="space-y-6">
          <CustomerOrdersSection
            isLoading={orders.isPending}
            orders={
              orders?.data
                ?.filter((item) => item.state !== "QUOTE")
                ?.map((item) => {
                  return {
                    id: item.id,
                    date: SANE_FORMATTER.format(new Date(item.startTime)),
                    amount: getCurrencyString(item.finalTotal),
                    status: item.state.toString(),
                  };
                }) || []
            }
          />
        </section>
        <section id="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Signed Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {documents?.data?.map((doc) => (
                  <CustomerListItem
                    key={doc.id}
                    icon={FileCheck}
                    title={doc.title}
                    date={`Signed on ${SANE_FORMATTER.format(
                      new Date(doc.date),
                    )}`}
                    // action={
                    // <Button className="text-blue-600 hover:text-blue-700">
                    //   View
                    // </Button>
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>
        <section id="contact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Recent Contacts
              </CardTitle>
              {reach?.data?.conversationId && (
                <CardDescription>
                  <Link
                    href={`/phone?conversationId=${reach?.data?.conversationId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    View Conversation
                  </Link>
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reach?.data?.recentInteractions?.map((item, index) => (
                  <CustomerListItem
                    key={`${index}`}
                    icon={item.call ? PhoneCall : FileCheck}
                    title={item.call ? "Call" : "Message"}
                    date={SANE_FORMATTER.format(new Date(item.time))}
                    description={item.message}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>
      </main>
    </Page>
  );
};

export default CustomerViewPage;
