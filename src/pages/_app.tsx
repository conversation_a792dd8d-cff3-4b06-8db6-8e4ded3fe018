import "../styles/globals.css";
import { AppProps } from "next/app";
import React, { ReactElement, ReactNode, useEffect, useState } from "react";
import { SessionProvider, useSession } from "next-auth/react";
import { NextPage } from "next";
import DefaultLayout from "~/components/DefaultLayout";
import "@fontsource-variable/inter";
import { useRouter } from "next/router";
import { cn } from "~/lib/utils";
import { PhoneProvider } from "~/providers/phone/PhoneProvider";
import { PostHogProvider, usePostHog } from "posthog-js/react";
import ReactQueryProvider from "~/providers/ReactQueryProvider";
import Head from "next/head";
import posthog from "posthog-js";
import { redirect } from "next/navigation";
import "react-loading-skeleton/dist/skeleton.css";
import dynamic from "next/dynamic";
import { Toaster } from "sonner";

export type NextPageWithLayout<P = object, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
  auth?: boolean;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

const CrispWithNoSSR = dynamic(() => import("../components/CrispChat"));

function Auth({ children }: { children: ReactNode }) {
  // if `{ required: true }` is supplied, `status` can only be "loading" or "authenticated"
  const { data, status } = useSession({ required: true });

  const posthog = usePostHog();

  if (status === "loading") {
    return (
      <div className="fixed inset-0 z-50 bg-white bg-opacity-50 flex items-center justify-center">
        <LoadingSpinner className="text-primary" />
      </div>
    );
  }

  if (status !== "authenticated") {
    return redirect("/auth/login");
  }

  if (data) {
    posthog.identify(data.user.email ?? undefined, {
      email: data.user.email,
      name: data.user.name,
      accountId: data.user.accountId,
      environment: process.env.NODE_ENV,
    });
  }

  return children;
}

// https://www.realtimecolors.com/docs/color-schemes?colors=011C26-fbfbfb-f2b138-6ed3f8-3aa0c5&fonts=Poppins-Poppins

export const LoadingSpinner = ({ size = 24, className, ...props }: any) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      {...props}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={cn("animate-spin", className)}
    >
      <path d="M21 12a9 9 0 1 1-6.219-8.56" />
    </svg>
  );
};
const App = ({
  Component,
  pageProps: { session, ...pageProps },
}: AppPropsWithLayout) => {
  const router = useRouter();

  const getLayout = Component.getLayout ?? DefaultLayout;
  const auth = Component.auth === undefined || Component.auth;
  const [isLoading, setIsLoading] = useState(false);
  const styledPage = getLayout(
    <>
      {isLoading && (
        <div className="fixed inset-0 z-50 bg-white bg-opacity-50 flex items-center justify-center">
          <LoadingSpinner className="text-primary" />
        </div>
      )}
      <Component {...pageProps} />
    </>,
  );

  useEffect(() => {
    if (typeof window !== "undefined") {
      posthog.init(
        process.env.NEXT_PUBLIC_POSTHOG_KEY ||
          "phc_EjU2fvlBYyfpoyftWsFFnjk9FTYxUNQ0xBQndLR97Xe",
        {
          api_host:
            process.env.NEXT_PUBLIC_POSTHOG_HOST || "https://us.i.posthog.com",
          person_profiles: "identified_only",
          // Enable debug mode in development
        },
      );
    }
    const handleRouteChange = () => posthog?.capture("$pageview");

    router.events.on("routeChangeStart", () => {
      console.log("Loading");
      setIsLoading(true);
    });

    router.events.on("routeChangeComplete", () => {
      console.log("complete");
      setIsLoading(false);
      handleRouteChange();
    });

    router.events.on("routeChangeError", () => {
      setIsLoading(false);
    });

    return () => {
      router.events.off("routeChangeComplete", handleRouteChange);
    };
  }, []);

  return (
    <SessionProvider session={session}>
      <Head>
        <title>Dashboard | Party Rental Platform</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </Head>
      <ReactQueryProvider>
        <PostHogProvider client={posthog}>
          {auth ? (
            <Auth>
              <PhoneProvider>
                <>
                  {styledPage}
                  <CrispWithNoSSR />
                </>
              </PhoneProvider>
            </Auth>
          ) : (
            <>{styledPage}</>
          )}
        </PostHogProvider>
      </ReactQueryProvider>
      <Toaster />
    </SessionProvider>
  );
};

export default App;
