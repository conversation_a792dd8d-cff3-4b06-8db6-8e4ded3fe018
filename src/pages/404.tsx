import React from "react";
import { Button } from "~/components/ui/button";
import Image from "next/image";

const NoPageFound = () => {
  return (
    <div className={"content-center justify-center text-center mt-5"}>
      <div className={"flex justify-center"}>
        <Image src={"/wordmark.svg"} alt="Logo" width={400} height={100} />
      </div>
      <p className={"text-2xl"}>Page Not Found</p>
      <Button className={"mt-5"}>
        <a href={"/"}>Help, I'm lost!</a>
      </Button>
    </div>
  );
};

NoPageFound.getLayout = function getLayout(page: React.ReactNode) {
  return <>{page}</>;
};
export default NoPageFound;
