import { useFeatureFlagEnabled } from "posthog-js/react";
import React from "react";
import Page from "~/components/Page";

const ReportsLivePage = () => {
  const reportsFeatureFlag = useFeatureFlagEnabled("reports_page");

  if (!reportsFeatureFlag) {
    return (
      <Page title={"Live Reports"}>
        <div>Live Reports feature is not enabled</div>
      </Page>
    );
  }
};

export default ReportsLivePage;
