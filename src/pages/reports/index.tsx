import Page from "~/components/Page";
import React from "react";
import NextLink from "next/link";

const ReportsPage = () => {
  return (
    <Page title={"Reports"}>
      <ul className="flex flex-col items-center gap-4">
        <li>
          <NextLink href={"/reports/sales"}>
            <button
              className={
                "w-full px-5 py-2 padding-lg border rounded-lg border-gray-200 bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }
            >
              Sales Overview
            </button>
          </NextLink>
        </li>
        <li>
          <NextLink href={"/reports/ordersPlaced"}>
            <button
              className={
                "w-full px-5 py-2 padding-lg border rounded-lg border-gray-200 bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }
            >
              Orders Placed
            </button>
          </NextLink>
        </li>
        <li>
          <NextLink href={"/reports/sales-by-product"}>
            <button
              className={
                "w-full px-5 py-2  padding-lg border rounded-lg border-gray-200 bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }
            >
              Sales By Product
            </button>
          </NextLink>
        </li>
        <li>
          <NextLink href={"/reports/sales-tax-report"}>
            <button
              className={
                "w-full px-5 py-2  padding-lg border rounded-lg border-gray-200 bg-white shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }
            >
              Sales Tax
            </button>
          </NextLink>
        </li>
      </ul>
    </Page>
  );
};

export default ReportsPage;
