import Page from "~/components/Page";
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

const ReportsPage = () => {
  return (
    <Page title={"Reports"}>
      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Business Reports</CardTitle>
            <CardDescription>
              Access detailed analytics and insights about your business performance.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Use the Reports dropdown in the sidebar to navigate to specific reports:
            </p>
            <ul className="mt-3 space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span><strong>Sales Overview</strong> - Track revenue and sales trends</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span><strong>Orders Placed</strong> - Monitor order volume and patterns</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span><strong>Sales By Product</strong> - Analyze product performance</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span><strong>Sales Tax</strong> - Review tax reporting data</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </Page>
  );
};

export default ReportsPage;
