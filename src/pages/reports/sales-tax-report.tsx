import React, { useEffect, useState } from "react";
import Page from "~/components/Page";
import { Calendar } from "~/components/ui/calendar";
import { DateRange } from "react-day-picker";
import { format, startOfDay, subDays } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { CalendarIcon } from "lucide-react";
import { DataTable } from "~/components/ui/datatable/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "~/components/ui/datatable/column-header";
import { getCurrencyValue } from "~/server/lib/currency";

export type SalesTaxLineItem = {
  name: string;
  taxableSales: number;
  taxOwed: number;
};

export type SalesTaxResponse = {
  startDate: string;
  salesTaxLineItems: SalesTaxLineItem[];
};

const SalesTaxReport = () => {
  const [salesTaxReport, setSalesTaxReport] = useState<SalesTaxResponse | null>(
    null,
  );
  const [date, setDate] = useState<DateRange | undefined>({
    from: subDays(startOfDay(new Date()), 30),
    to: startOfDay(new Date()),
  });

  useEffect(() => {
    const fetchSalesTaxReport = async () => {
      const res = await fetch(
        "/api/account/reports/salesTaxReport?startTime=" +
          date?.from?.toISOString() +
          "&endTime=" +
          date?.to?.toISOString(),
      );
      const data = await res.json();
      setSalesTaxReport(data);
    };

    void fetchSalesTaxReport();
  }, [date]);

  const columns: ColumnDef<SalesTaxLineItem>[] = [
    {
      enableGlobalFilter: false,
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="City" />
      ),
      accessorKey: "name",
    },
    {
      enableGlobalFilter: false,
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Taxable Sales" />
      ),
      accessorKey: "taxableSales",
    },
    {
      enableGlobalFilter: false,
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tax Owed" />
      ),
      accessorKey: "taxOwed",
    },
  ];

  if (!salesTaxReport) {
    return <div>Loading...</div>;
  }

  return (
    <Page title="Sales Tax Report">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "justify-start text-left font-normal",
              !date && "text-muted-foreground",
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd")}-{format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
      <p>
        Total Tax Owed:{" "}
        {getCurrencyValue(
          salesTaxReport?.salesTaxLineItems?.reduce(
            (acc, item) => acc + item.taxOwed,
            0,
          ) ?? 0,
        )}
      </p>
      <p>
        Total Taxable Sales:{" "}
        {getCurrencyValue(
          salesTaxReport?.salesTaxLineItems?.reduce(
            (acc, item) => acc + item.taxableSales,
            0,
          ) ?? 0,
        )}
      </p>
      <DataTable columns={columns} data={salesTaxReport.salesTaxLineItems} />
    </Page>
  );
};

export default SalesTaxReport;
