import Page from "~/components/Page";
import ReportsDateRange from "~/components/DatePicker/ReportsDateRange";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";
import React, { useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { subDays, subYears } from "date-fns";
import { useRouter } from "next/router";
import {
  ReportContent,
  ReportGraph,
  ReportHeader,
  ReportPage,
} from "~/components/reports/reports";
import { REPORT_DATE_FORMAT } from "~/server/lib/time";
import { useOrdersPlaced } from "~/query/reports/ordersPlaced";
import { Checkbox } from "~/components/ui/checkbox";
import { Button } from "~/components/ui/button";

const OrdersPlacedReport = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [compare, setCompare] = React.useState<boolean>(false);
  const endDate = searchParams.get("endDate")
    ? new Date(searchParams.get("endDate")!)
    : new Date();
  const startDate = searchParams.get("startDate")
    ? new Date(searchParams.get("startDate")!)
    : subDays(endDate, 30);

  const salesData = useOrdersPlaced({
    startDate,
    endDate,
  });

  const compareData = useOrdersPlaced(
    compare
      ? {
          startDate:
            endDate.getTime() - startDate.getTime() > 60 * 24 * 60 * 60 * 1000
              ? subYears(startDate, 1)
              : subDays(startDate, 30),
          endDate:
            endDate.getTime() - startDate.getTime() > 60 * 24 * 60 * 60 * 1000
              ? subYears(endDate, 1)
              : subDays(endDate, 30),
        }
      : undefined,
  );

  const getMonthFromDate = (dateString: string) => {
    // This assumes ISO format dates like '2025-01-01'
    return dateString.split("-")[1];
  };

  const chartData = useMemo(() => {
    if (!compare) {
      // Just show current sales data
      return salesData?.data?.sales.map((item) => ({
        date: item.date,
        current: item.total,
      }));
    }

    // When comparison is enabled, we need to align both datasets by month
    return salesData?.data?.sales.map((saleItem) => {
      const month = getMonthFromDate(saleItem.date);

      // Find the matching item in the comparison data by month
      const matchingCompare = compareData?.data?.sales.find(
        (compItem) => getMonthFromDate(compItem.date) === month,
      );

      return {
        date: saleItem.date,
        month, // For display on x-axis
        current: saleItem.total,
        previous: matchingCompare?.total || 0,
      };
    });
  }, [compare, salesData, compareData]);

  const renderLines = () => {
    return (
      <>
        <Line
          type="monotone"
          dataKey="current"
          stroke="#8884d8"
          name="Current Period"
        />
        {compare && (
          <Line
            type="monotone"
            dataKey="previous"
            stroke="#82ca9d"
            strokeDasharray="5 5"
            name="Previous Period"
          />
        )}
      </>
    );
  };

  return (
    <Page title={"Orders Placed"}>
      <ReportPage>
        <ReportHeader>
          <ReportsDateRange
            onChange={(startDate, endDate) => {
              router.push({
                search: `?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
              });
            }}
            startDate={startDate}
            endDate={endDate}
          />
          <Button
            variant={"outline"}
            onClick={() => {
              setCompare((curr) => !curr);
            }}
          >
            <Checkbox checked={compare} />
            Compare Previous Period
          </Button>
        </ReportHeader>
        <ReportContent
          hasData={
            !!(salesData.data?.sales?.length || compareData.data?.sales?.length)
          }
          loading={salesData.isPending || compareData.isPending}
          emptyMessage={"No orders placed"}
        >
          <ReportGraph
            title={REPORT_DATE_FORMAT.formatRange(startDate, endDate)}
          >
            <ChartContainer
              config={{
                sales: {
                  label: "Orders",
                  color: "blue",
                },
              }}
              className="aspect-auto h-[250px] w-full"
            >
              <LineChart
                accessibilityLayer
                data={chartData}
                margin={{
                  left: 8,
                  right: 8,
                  top: 5,
                  bottom: 5
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    if (
                      endDate.getTime() - startDate.getTime() <=
                      24 * 60 * 60 * 1000
                    ) {
                      return new Date(value).toLocaleTimeString("en-US", {
                        month: "short",
                        day: "numeric",
                        hour: "numeric",
                        minute: "numeric",
                      });
                    }
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      className="w-[150px]"
                      nameKey="total"
                      labelFormatter={(value) => {
                        if (
                          endDate.getTime() - startDate.getTime() <=
                          24 * 60 * 60 * 1000
                        ) {
                          return new Date(value).toLocaleTimeString("en-US", {
                            month: "short",
                            day: "numeric",
                            hour: "numeric",
                            minute: "numeric",
                          });
                        }
                        return new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        });
                      }}
                    />
                  }
                />
                {renderLines()}
              </LineChart>
            </ChartContainer>
          </ReportGraph>
        </ReportContent>
      </ReportPage>
    </Page>
  );
};

export default OrdersPlacedReport;
