import Page from "~/components/Page";
import ReportsDateRange from "~/components/DatePicker/ReportsDateRange";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";
import { getCurrencyString } from "~/server/lib/currency";
import React from "react";
import { useSearchParams } from "next/navigation";
import { subDays } from "date-fns";
import { useRouter } from "next/router";
import {
  ReportContent,
  ReportFooter,
  ReportGraph,
  ReportHeader,
  ReportPage,
  ReportTable,
} from "~/components/reports/reports";
import { REPORT_DATE_FORMAT } from "~/server/lib/time";
import { useSalesByProductReport } from "~/query/reports/sales-by-product";

const SalesReport = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const endDate = searchParams.get("endDate")
    ? new Date(searchParams.get("endDate")!)
    : new Date();
  const startDate = searchParams.get("startDate")
    ? new Date(searchParams.get("startDate")!)
    : subDays(endDate, 30);

  const salesData = useSalesByProductReport({
    startDate,
    endDate,
  });

  const topFiveNames = salesData.data?.sales;

  return (
    <Page title={"Sales By Product"}>
      <ReportPage>
        <ReportHeader>
          <ReportsDateRange
            onChange={(startDate, endDate) => {
              router.push({
                search: `?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
              });
            }}
            startDate={startDate}
            endDate={endDate}
          />
        </ReportHeader>
        <ReportContent
          hasData={!!salesData?.data?.sales}
          loading={salesData.isPending}
          emptyMessage={"No sales data found"}
        >
          <ReportGraph
            title={REPORT_DATE_FORMAT.formatRange(startDate, endDate)}
            description={"Top 5 Items: Gross Sales"}
          >
            <ChartContainer
              config={{
                sales: {
                  label: "Sales",
                  color: "blue",
                },
              }}
              className="aspect-auto h-[250px] w-full"
            >
              <LineChart
                accessibilityLayer
                data={salesData.data?.sales ?? []}
                margin={{
                  left: 8,
                  right: 8,
                  top: 5,
                  bottom: 5,
                }}
              >
                <CartesianGrid vertical={false} />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    if (
                      endDate.getTime() - startDate.getTime() <=
                      24 * 60 * 60 * 1000
                    ) {
                      return new Date(value).toLocaleTimeString("en-US", {
                        month: "short",
                        day: "numeric",
                        hour: "numeric",
                        minute: "numeric",
                      });
                    }
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      className="w-[150px]"
                      nameKey="total"
                      labelFormatter={(value) => {
                        if (
                          endDate.getTime() - startDate.getTime() <=
                          24 * 60 * 60 * 1000
                        ) {
                          return new Date(value).toLocaleTimeString("en-US", {
                            month: "short",
                            day: "numeric",
                            hour: "numeric",
                            minute: "numeric",
                          });
                        }
                        return new Date(value).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        });
                      }}
                    />
                  }
                />
                {Object.keys(topFiveNames?.at(0) || {})?.map((item, index) => {
                  if (item === "date") {
                    return null;
                  }
                  return (
                    <Line
                      key={item}
                      dataKey={item}
                      type="monotone"
                      stroke={`hsl(var(--chart-${(index % 5) + 1}))`}
                      strokeWidth={2}
                      dot={false}
                    />
                  );
                })}
              </LineChart>
            </ChartContainer>
          </ReportGraph>
          <ReportFooter>
            <ReportTable
              title="Sales By Product Breakdown"
              description="Shows top grossing product sales for the selected date range. Keep in mind the price shown is the cumulative price for the item, not the total order value."
              data={
                salesData.data?.totalByProduct?.map((item) => ({
                  label: (item[0] ?? "") as string,
                  value: getCurrencyString(item[1] ?? 0),
                })) ?? []
              }
            />
          </ReportFooter>
        </ReportContent>
      </ReportPage>
    </Page>
  );
};

export default SalesReport;
