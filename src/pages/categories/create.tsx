import Page from "~/components/Page";
import CategoryForm from "~/form/CategoryForm";
import { toast } from "sonner";
import { CategorySchemaType } from "~/query/category";
import { useRouter } from "next/router";

const CategoryCreatePage = () => {
  const router = useRouter();

  const attachImage = async (categoryId: string, image: string) => {
    const request = await fetch(`/api/categories/${categoryId}/images/attach`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ imageId: image }),
    });
    if (request.ok) {
      return request.json();
    }
    return null;
  };

  const onSubmit = async (values: CategorySchemaType, image: string | null) => {
    const request = fetch("/api/categories/create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(request, {
      loading: "Creating Category...",
      success: (data) => {
        data.json().then((data) => {
          if (image) {
            attachImage(data.id, image).then(() => {
              router.push("/categories");
            });
          }
        });
        return "Successfully created category!";
      },
      error: "Error creating category",
    });
  };

  return (
    <Page title={"Create Category"}>
      <CategoryForm onSubmit={onSubmit} categoryId={null} />
    </Page>
  );
};

export default CategoryCreatePage;
