import React, { useEffect } from "react";
import { useRouter } from "next/router";
import { CategorySchemaType } from "~/query/category";
import { LoadingSpinner } from "~/pages/_app";
import CategoryForm from "~/form/CategoryForm";
import { notFound } from "next/navigation";
import { toast } from "sonner";
import Page from "~/components/Page";
import { captureException } from "@sentry/core";

const CategoryEditPage = () => {
  const router = useRouter();
  const { id } = router.query as { id: string };
  const [category, setCategory] = React.useState<
    CategorySchemaType | undefined
  >(undefined);

  useEffect(() => {
    if (!id) {
      return;
    }

    const fetchCategory = async () => {
      const response = await fetch(`/api/categories/${id}/`);
      if (response.ok) {
        const data = await response.json();
        setCategory(data.category);
      } else {
        console.error(`Failed to fetch category ${id}`);
        captureException(new Error(`Failed to fetch category ${id}`), {
          extra: {
            status: response.status,
            statusText: response.statusText,
          },
        });
      }
    };

    void fetchCategory();
  }, [id]);

  if (!id) {
    return notFound();
  }

  if (!category) {
    return (
      <Page title={"Loading..."}>
        <LoadingSpinner />
      </Page>
    );
  }

  const onSubmit = async (values: CategorySchemaType) => {
    const request = fetch(`/api/categories/${id}/edit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    toast.promise(request, {
      loading: "Updating Category...",
      success: () => {
        router.push("/categories");
        return "Category Updated.";
      },
      error: "Error Updating Category.",
    });
  };

  return (
    <Page title={category.name}>
      <CategoryForm
        onSubmit={onSubmit}
        categoryId={Number(id)}
        initialValues={category}
      />
    </Page>
  );
};

export default CategoryEditPage;
