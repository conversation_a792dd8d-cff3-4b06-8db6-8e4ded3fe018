import React, { useMemo } from "react";
import Page from "~/components/Page";
import { DataTable } from "~/components/ui/datatable/data-table";
import { CategoryTable } from "~/table/CategoryTable/columns";
import { useCategories } from "~/query/category";

const CategoryPage = () => {
  const { isError, data, isPending } = useCategories();

  const columns = useMemo(() => CategoryTable(), []);

  if (isPending) {
    return (
      <Page
        title={"Categories"}
        actionButton={{
          label: "Create Category",
          newItem: true,
          href: "/categories/create",
        }}
      >
        <DataTable isLoading={true} columns={columns} data={[]} />
      </Page>
    );
  }

  if (isError) {
    return (
      <Page
        title={"Categories"}
        actionButton={{
          label: "Create Category",
          newItem: true,
          href: "/categories/create",
        }}
      >
        <div>Failed to load categories</div>
      </Page>
    );
  }

  return (
    <Page
      title={"Categories"}
      actionButton={{
        label: "Create Category",
        newItem: true,
        href: "/categories/create",
      }}
    >
      <DataTable
        linkTo={(row) => {
          return `/categories/${row.id}/edit`;
        }}
        data={data}
        columns={columns}
      />
    </Page>
  );
};

export default CategoryPage;
