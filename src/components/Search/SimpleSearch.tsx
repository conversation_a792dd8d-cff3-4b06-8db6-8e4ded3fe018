import React, { useEffect } from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "../ui/command";
import { PlusCircleIcon, Search, X } from "lucide-react";
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";
import { Command as CommandPrimitive } from "cmdk";

export type SearchableItem = {
  value: string;
  label?: string;
};

type SimpleSearchProps<T extends SearchableItem> = {
  items: T[];
  onChange: (item: string) => void;
  onInputChange?: (value: string) => void;
  defaultValue?: T;
  placeholder?: string;
  creatable?: boolean;
  onCreate?: (value: string) => void;
  renderItem?: (item: T) => React.ReactNode;
  loading?: boolean;
  addNewText?: string;
  clearOnSelect?: boolean;
  selectedItem?: T | null;
  filter?: (value: string, search: string) => number;
};

const SimpleSearch = <T extends SearchableItem>(
  props: SimpleSearchProps<T>,
) => {
  const [searchValue, setSearchValue] = React.useState<T | null>(
    props.defaultValue ?? null,
  );
  const [currentText, setCurrentText] = React.useState<string>("");
  const [interactive, setInteractive] = React.useState<boolean>(false);

  useEffect(() => {
    if (props.selectedItem) {
      setSearchValue(props.selectedItem);
      setCurrentText(props.selectedItem.value);
    } else {
      setSearchValue(null);
      setCurrentText("");
    }
  }, [props.selectedItem]);

  const shouldRenderCreatable =
    props.creatable === true &&
    searchValue !== props.defaultValue &&
    currentText.length > 0;

  if (searchValue) {
    return (
      <div className="focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 focus-visible:ring-ring flex items-center border-b rounded-md p-3 border border-input bg-muted-background ring-offset-background focus-visible:outline-none">
        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <div
          className={cn(
            "flex items-center bg-secondary text-secondary-foreground hover:bg-secondary/80 disabled:cursor-not-allowed disabled:opacity-50 pl-2 text-sm h-10 rounded-sm border-solid cursor-default animate-fadein font-normal",
          )}
        >
          {props.renderItem
            ? props.renderItem(searchValue)
            : searchValue.label ?? searchValue.value}
          <Button
            type="button"
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation(); // Prevent event from bubbling up to the tag span
              setSearchValue(null);
            }}
            className={cn(`py-1 px-3 h-full hover:bg-transparent p-4 w-4`)}
          >
            <X className="h-4 w-4 shrink-0" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Command className="overflow-visible bg-transparent" filter={props.filter}>
      <div
        className="focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 focus-visible:ring-ring flex items-center border-b rounded-md px-3 border border-input bg-muted-background ring-offset-background focus-visible:outline-none"
        cmdk-input-wrapper=""
      >
        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <CommandPrimitive.Input
          className={cn(
            "flex h-10 w-full bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
            "bg-transparent outline-none placeholder:text-muted-foreground flex-1",
          )}
          placeholder={props.placeholder ?? "Type a command or search..."}
          onFocus={() => {
            setInteractive(true);
          }}
          onBlur={() => {
            setTimeout(() => {
              setInteractive(false);
            }, 150);
          }}
          onValueChange={(value) => {
            if (props.onInputChange) {
              props.onInputChange(value);
            }
            setCurrentText(value);
          }}
          value={currentText}
        />
        {props.onCreate && (
          <Button
            variant={"ghost"}
            type={"button"}
            onClick={() => {
              if (props.onCreate) {
                props.onCreate(currentText || "");
              }
            }}
          >
            <PlusCircleIcon className={"h-4 w-4 shrink-0"} />
          </Button>
        )}
      </div>

      <div className="relative">
        {interactive && (
          <CommandList className="absolute w-full z-10 mt-2 top-0  bg-popover text-popover-foreground shadow-md outline-none animate-in">
            <CommandGroup className={"rounded-md border"}>
              {props.items.map((item) => {
                return (
                  <CommandItem
                    key={item.value}
                    onSelect={(value) => {
                      if (!props.clearOnSelect) {
                        setSearchValue(item);
                      } else {
                        setSearchValue(null);
                      }
                      props.onChange(value);
                      setInteractive(false);
                    }}
                    className={cn(
                      "flex items-center gap-2 w-full cursor-pointer",
                    )}
                    value={item.value}
                  >
                    {props.renderItem
                      ? props.renderItem(item)
                      : item.label ?? item.value}
                  </CommandItem>
                );
              })}
              {shouldRenderCreatable && (
                <>
                  <CommandSeparator />
                  <CommandItem
                    value={currentText}
                    className={cn("cursor-pointer")}
                    onSelect={() => {
                      if (props.onCreate) {
                        props.onCreate(currentText);
                      }
                    }}
                  >
                    <PlusCircleIcon className="mr-2 h-4 w-4" />
                    <span>
                      {<span>{props.addNewText ?? "Add New Item"}</span>}
                    </span>
                  </CommandItem>
                </>
              )}
              <CommandEmpty>
                {!props.loading && (
                  <>
                    {shouldRenderCreatable ? (
                      <>
                        <CommandSeparator />
                        <CommandItem
                          className={cn("cursor-pointer")}
                          onSelect={() => {
                            if (props.onCreate) {
                              props.onCreate(currentText || "");
                            }
                          }}
                        >
                          <PlusCircleIcon className="mr-2 h-4 w-4" />
                          <span>{props.addNewText ?? "Add New Item"}</span>
                        </CommandItem>
                      </>
                    ) : (
                      "No Results Found"
                    )}
                  </>
                )}
              </CommandEmpty>
            </CommandGroup>
          </CommandList>
        )}
      </div>
    </Command>
  );
};

export default SimpleSearch;
