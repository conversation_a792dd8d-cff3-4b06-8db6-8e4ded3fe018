import { ReactNode } from "react";
import { cn } from "~/lib/utils";

type FormGroupProps = {
  className?: string;
  children: ReactNode;
};

const FormGroup = (props: FormGroupProps) => {
  return (
    <div
      className={cn(
        "flex md:flex-row flex-col gap-3 mx-1 md:my-1 md:mx-0 flex-wrap items-center",
        props.className,
      )}
    >
      {props.children}
    </div>
  );
};

export default FormGroup;
