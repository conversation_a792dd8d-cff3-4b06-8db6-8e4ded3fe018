import { useForm } from "react-hook-form";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import React from "react";
import { number, object } from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

type SimpleFormProps = {
  title: string;
  defaultValue: number | undefined;
  min?: number;
  max?: number;
  onChange: (value: number) => void;
  action?: string;
};

const SimpleNumberForm = (props: SimpleFormProps) => {
  const form = useForm<{ value: number }>({
    resolver: yupResolver(
      object().shape({
        value: number()
          .required()
          .min(props.min || 0)
          .max(props.max || 1000000),
      }),
    ),
    defaultValues: {
      value: props.defaultValue,
    },
  });

  return (
    <Form
      {...form}
      onSubmit={(values) => {
        props.onChange(values.value);
      }}
    >
      <FormItem
        label={props.title}
        name={"value"}
        render={({ field }) => (
          <Input type={"number"} placeholder={"$0.00"} {...field} />
        )}
      />
      <Button variant={"primary"} type={"submit"} className={"mt-2"}>
        {props.action || "Save"}
      </Button>
    </Form>
  );
};

export default SimpleNumberForm;
