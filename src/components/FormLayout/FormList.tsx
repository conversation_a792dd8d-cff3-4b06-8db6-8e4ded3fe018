import React from "react";
import {
  Control,
  FieldArrayPath,
  FieldValues,
  useFieldArray,
} from "react-hook-form";
import { Button } from "~/components/ui/button";
import { Plus } from "lucide-react";
import { FormListItem } from "./FormListItem";

interface FormListProps<
  TFieldValues extends FieldValues,
  TFieldArrayName extends
    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,
> {
  name: TFieldArrayName;
  control: Control<TFieldValues>;
  defaultValue: Record<string, string>;
  fields: {
    name: string;
    label: string;
    placeholder: string;
    type?: string;
  }[];
}
export function FormList<
  TFieldValues extends FieldValues,
  TFieldArrayName extends
    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,
>({
  name,
  control,
  defaultValue,
  fields,
}: FormListProps<TFieldValues, TFieldArrayName>) {
  const {
    fields: fieldArray,
    append,
    remove,
  } = useFieldArray<TFieldValues, TFieldArrayName>({
    control,
    name,
  });
  return (
    <div className="space-y-6">
      <div className="space-y-3">
        {fieldArray.map((field, index) => (
          <FormListItem
            key={field.id}
            index={index}
            control={control}
            name={name}
            fields={fields}
            onRemove={() => remove(index)}
            canDelete={fieldArray.length > 1}
          />
        ))}
      </div>
      <div className="flex gap-4">
        <Button
          type="button"
          variant="outline"
          className="flex items-center gap-2"
          onClick={() => append(defaultValue as any)}
        >
          <Plus className="h-4 w-4" />
          Add Item
        </Button>
      </div>
    </div>
  );
}
