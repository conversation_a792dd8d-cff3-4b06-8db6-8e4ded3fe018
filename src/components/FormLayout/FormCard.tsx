import { ReactNode } from "react";
import { cn } from "~/lib/utils";

type FormCardProps = {
  className?: string;
  children: ReactNode;
};
/**
 * To be used when you want to group form fields together in a card.
 * If you have a header it's likely you want the Card component instead.
 */
const FormCard = (props: FormCardProps) => {
  return (
    <div
      className={cn(
        props.className,
        "rounded-lg border bg-card text-card-foreground shadow-sm px-4 py-2 pt-3",
      )}
    >
      {props.children}
    </div>
  );
};

export default FormCard;
