import { cn } from "~/lib/utils";
import { ReactNode } from "react";
import { cva } from "class-variance-authority";

const layoutVariants = cva("grid", {
  variants: {
    style: {
      oneThird: "lg:col-span-2 md:col-span-3 col-span-6",
      half: "lg:col-span-3 col-span-6",
      full: "col-span-6",
      twoThirds: "lg:col-span-4 md:col-span-5 col-span-6",
    },
  },

  defaultVariants: {
    style: "twoThirds",
  },
});

export type FormLayoutSectionProps = {
  style?: "oneThird" | "half" | "full" | "twoThirds";
  className?: string;
  children: ReactNode;
};

const FormLayoutSection = (props: FormLayoutSectionProps) => {
  return (
    <div
      className={cn(
        "grid auto-rows-max flex-1 gap-4 lg:gap-8 auto-cols-fr",
        props.className,
        layoutVariants({ style: props.style }),
      )}
    >
      {props.children}
    </div>
  );
};

export default FormLayoutSection;
