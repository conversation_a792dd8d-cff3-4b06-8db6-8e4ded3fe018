import React from "react";
import { Control, FieldArrayPath, FieldValues } from "react-hook-form";
import { But<PERSON> } from "~/components/ui/button";
import { Trash2 } from "lucide-react";
import { Input } from "~/components/ui/input";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";

interface FormListItemProps<
  TFieldValues extends FieldValues,
  TFieldArrayName extends
    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,
> {
  index: number;
  control: Control<TFieldValues>;
  name: TFieldArrayName;
  fields: {
    name: string;
    label: string;
    placeholder: string;
    type?: string;
  }[];
  onRemove: () => void;
  canDelete: boolean;
}
export function FormListItem<
  TFieldValues extends FieldValues,
  TFieldArrayName extends
    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,
>({
  index,
  control,
  name,
  fields,
  onRemove,
  canDelete,
}: FormListItemProps<TFieldValues, TFieldArrayName>) {
  return (
    <div className="group relative p-4 bg-gray-50 border border-gray-200 rounded-lg transition-colors hover:bg-gray-50/80">
      <div className="flex gap-4 items-start">
        <div className="flex-1 grid grid-cols-2 gap-4">
          {fields.map((field) => (
            <FormField
              key={field.name}
              control={control}
              name={`${name}.${index}.${field.name}` as any}
              render={({ field: formField }) => (
                <FormItem>
                  <FormLabel>{field.label}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={field.placeholder}
                      type={field.type}
                      {...formField}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ))}
        </div>
        <Button
          type="button"
          variant="destructive"
          size="icon"
          onClick={onRemove}
          disabled={!canDelete}
          className="mt-8"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
      <div className="absolute -left-2 top-1/2 -translate-y-1/2 w-1 h-8 bg-gray-300 rounded-full" />
    </div>
  );
}
