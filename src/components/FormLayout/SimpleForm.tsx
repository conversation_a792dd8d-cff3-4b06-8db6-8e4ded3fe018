import { useForm } from "react-hook-form";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import React from "react";

type SimpleFormProps = {
  title: string;
  defaultValue: string;
  onChange: (value: string) => void;
};

const SimpleStringForm = (props: SimpleFormProps) => {
  const form = useForm<{ value: string }>({
    defaultValues: {
      value: props.defaultValue,
    },
  });

  return (
    <Form
      {...form}
      onSubmit={(values) => {
        props.onChange(values.value);
      }}
    >
      <FormItem
        label={props.title}
        name={"value"}
        render={({ field }) => <Input {...field} />}
      />
      <Button variant={"primary"} type={"submit"} className={"mt-2"}>
        Save
      </Button>
    </Form>
  );
};

export default SimpleStringForm;
