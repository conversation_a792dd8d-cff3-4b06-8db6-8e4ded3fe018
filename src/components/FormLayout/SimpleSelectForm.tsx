import { useForm } from "react-hook-form";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Button } from "~/components/ui/button";
import React, { ReactNode } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";

type SimpleSelectFormProps = {
  title: string;
  placeholder?: string;
  options: {
    label: ReactNode;
    value: string;
  }[];
  removeOption?: {
    label: ReactNode;
    value: string;
  };
  defaultValue: string | undefined;
  onChange: (value: string | null) => void;
  action?: string;
};

const SimpleSelectForm = (props: SimpleSelectFormProps) => {
  const form = useForm<{ value: string }>({
    defaultValues: {
      value: props.defaultValue,
    },
  });

  return (
    <Form
      {...form}
      onSubmit={(values) => {
        props.onChange(values.value);
      }}
    >
      <FormItem
        label={props.title}
        name={"value"}
        render={({ field }) => (
          <Select
            value={field.value?.toString()}
            onValueChange={(value) => field.onChange(value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={props.placeholder ?? "Select..."} />
            </SelectTrigger>
            <SelectContent>
              {props.options.map((item, index) => (
                <SelectItem key={index} value={item.value}>
                  {item.label}
                </SelectItem>
              ))}
              {props.removeOption && (
                <SelectItem value={props.removeOption.value}>
                  {props.removeOption.label}
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}
      />
      <Button variant={"primary"} type={"submit"} className={"mt-2"}>
        {props.action ?? "Save"}
      </Button>
    </Form>
  );
};

export default SimpleSelectForm;
