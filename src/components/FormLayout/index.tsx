import { FormLayoutSectionProps } from "~/components/FormLayout/FormLayoutSection";
import { ReactElement } from "react";
import { cn } from "~/lib/utils";

export type FormLayoutProps = {
  className?: string;
  children?:
    | ReactElement<FormLayoutSectionProps>[]
    | ReactElement<FormLayoutSectionProps>;
};

const FormLayout = (props: FormLayoutProps) => {
  return (
    <div
      className={cn(
        "grid gap-4 items-start md:grid-cols-[1fr_250px] lg:grid-cols-6 lg:gap-8",
        props.className,
      )}
    >
      {props.children}
    </div>
  );
};

export default FormLayout;
