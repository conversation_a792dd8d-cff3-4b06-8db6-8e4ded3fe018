import React, { ReactNode } from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem as FormItemComponent,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  FieldPath,
  FieldValues,
  UseControllerReturn,
  useFormContext,
} from "react-hook-form";

type FormDescriptionProps = {
  label: string | ReactNode;
  /**
   * @default "bottom"
   */
  position?: "top" | "bottom";
};

type FormItemProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  label: string | ReactNode;
  name: TName;
  removeFormControl?: boolean;
  description?: FormDescriptionProps;
  render: ({
    field,
    fieldState,
    formState,
  }: UseControllerReturn<TFieldValues, TName>) => React.ReactElement;
};

const FormItem = <
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues>,
>(
  props: FormItemProps<TFieldValues, TName>,
) => {
  const { label, description, render } = props;
  const { control } = useFormContext<TFieldValues, TName>();

  const formDescription = (placement: "top" | "bottom") => {
    if (description === undefined) {
      return;
    }
    if (
      description.position !== placement ||
      (description.position === undefined && placement === "top")
    ) {
      return;
    }
    return <FormDescription>{description.label}</FormDescription>;
  };

  return (
    <FormField
      control={control}
      render={(itemProps) => {
        return (
          <FormItemComponent>
            <FormLabel>{label}</FormLabel>
            {formDescription("top")}
            {props.removeFormControl !== true ? (
              <FormControl>{render(itemProps)}</FormControl>
            ) : (
              <div>{render(itemProps)}</div>
            )}
            {formDescription("bottom")}
            <FormMessage />
          </FormItemComponent>
        );
      }}
      name={props.name}
    />
  );
};

export default FormItem;
