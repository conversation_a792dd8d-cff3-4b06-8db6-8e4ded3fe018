import React, { ReactNode, useEffect } from "react";
import { useRouter } from "next/router";
import { PlanLevel } from "~/server/lib/entitlement/types";
import { usePageContext } from "~/providers/PageProvider";

type ActionButton = {
  label: string | ReactNode;
  permission?: string;
  newItem?: boolean;
  minimumPlanLevel?: PlanLevel;
};

export type FunctionBasedAction = {
  href?: never;
  onClick: () => void;
} & ActionButton;

export type LinkBasedAction = {
  href: string;
  onClick?: never;
} & ActionButton;

export type PageProps = {
  title: string | ReactNode;
  subtitle?: string | ReactNode;
  actionButton?: FunctionBasedAction | LinkBasedAction;
  secondaryActions?: (FunctionBasedAction | LinkBasedAction)[];
  children?: ReactNode;
};

const Page = (props: PageProps) => {
  const router = useRouter();
  const {
    path,
    setPath,
    setPageProps,
    pushToHistory,
    clearHistory,
    updateLastHistory,
  } = usePageContext();

  useEffect(() => {
    if (!router.asPath.includes(path)) {
      clearHistory(router.asPath);
    }
    if (path !== router.asPath && router.asPath !== "/") {
      pushToHistory({
        path: router.asPath,
        title: props.title as string,
      });
    } else {
      // if its the same path, update the title
      updateLastHistory({
        title: props.title as string,
        path: router.asPath,
      });
    }
    setPageProps(props);
    setPath(router.asPath);
  }, [props.title]);

  return (
    <div className={""} id={"page"}>
      {props.children}
    </div>
  );
};

export default Page;
