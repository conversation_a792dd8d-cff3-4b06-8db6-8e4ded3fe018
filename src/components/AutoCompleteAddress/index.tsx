import React, { useCallback, useState } from "react";
import debounce from "lodash.debounce";

import {
  AddressComponent,
  PlaceAutocompleteResult,
  PlaceType2,
} from "@googlemaps/google-maps-services-js";
import { AddressType } from "~/server/lib/location/types";
import AutoComplete from "~/components/AutoComplete";
import { captureException } from "@sentry/core";

type AutoCompleteAddressProps = {
  onSelectAddress: (address: AddressType) => void;
  defaultValue?: AddressType;
  onValueChange?: (search: string) => void;
  value?: string;
};

const AutoCompleteAddress = (props: AutoCompleteAddressProps) => {
  const [placePredictions, setPlacePredictions] = useState<
    { value: string; place: PlaceAutocompleteResult }[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const searchAddress = useCallback(
    debounce(async (address) => {
      setIsLoading(true);
      const request = await fetch(
        `/api/backend/addressSearch?query=${address}`,
      );
      const response = (await request.json()) as {
        placeComplete: PlaceAutocompleteResult[];
      };
      if (!request.ok) {
        return;
      }
      if (!response.placeComplete) {
        return;
      }
      setPlacePredictions(
        response.placeComplete.map((place) => {
          return {
            value: place.structured_formatting.main_text,
            place: place,
          };
        }),
      );
      setIsLoading(false);
    }, 300),
    [],
  );

  return (
    <AutoComplete
      onValueChange={(search) => {
        if (props.onValueChange) {
          props.onValueChange(search);
        }
        setIsLoading(true);
        searchAddress(search);
      }}
      loading={isLoading}
      onSelect={async (item) => {
        const prediction = placePredictions.find((prediction) => {
          return (
            prediction.place.description.toLowerCase() === item.toLowerCase()
          );
        });

        if (prediction) {
          const line1 = prediction.place.structured_formatting.main_text;
          const city =
            prediction.place.structured_formatting.secondary_text.split(
              ",",
            )[0] || "";
          const state = (
            prediction.place.structured_formatting.secondary_text.split(
              ",",
            )[1] || ""
          ).trim();

          const address: AddressType = {
            line1: line1,
            city: city,
            state: state,
          };

          props.onSelectAddress(address);
          const request = await fetch(
            `/api/backend/addressDetails?query=${prediction.place.description}`,
          );
          if (!request.ok) {
            return;
          }
          try {
            const response = (await request.json()) as {
              data: {
                longitude: number;
                latitude: number;
                components: AddressComponent[];
              };
            };
            const postalCode = response.data.components.find((component) => {
              return component.types.includes(PlaceType2.postal_code);
            });
            address.postalCode = postalCode?.short_name || "";
            props.onSelectAddress(address);
          } catch (e) {
            captureException(e);
            console.error(e);
          }
        }
      }}
      selectable={placePredictions.map((item) => {
        return {
          value: item.place.description,
          label: item.place.description,
        };
      })}
      value={props.value}
    />
  );
};

export default AutoCompleteAddress;
