import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actNode } from "react";
import { useAccountSwitcher } from "~/providers/TeamProvider";
import { LinkProps } from "next/dist/client/link";
import { ExternalLink } from "lucide-react";
import { useRouter } from "next/router";
import { cn } from "~/lib/utils";
import { toast } from "sonner";

type AccountLinkProps = {
  href: string;
  accountId: number;
  children: ReactNode;
  className?: string;
  onClick?: MouseEventHandler;
} & LinkProps;

export default function AccountLink({
  href,
  accountId,
  children,
  className = "",
  onClick,
  ...rest
}: AccountLinkProps) {
  const { currentTeam, setCurrentTeam, teams } = useAccountSwitcher();
  const router = useRouter();

  const handleClick = async (e: MouseEvent) => {
    if (currentTeam && currentTeam?.id === accountId) {
      if (onClick) {
        onClick(e);
      }
      if (href) {
        router.push(href);
      }
      return;
    }
    if (!teams.some((item) => item.id === accountId)) {
      // If the account is not in the list of teams, do not switch
      toast.error("We couldn't find this account in your list of teams.");
      return;
    }

    // If it's a different account than the current one
    e.preventDefault();

    try {
      // Switch account first
      const loadingPromise = toast.loading("Switching account...");

      await setCurrentTeam(accountId);

      toast.dismiss(loadingPromise);
      toast.success("Account switched successfully!");

      // Call original onClick if provided
      if (onClick) {
        onClick(e);
      }

      if (!href) {
        return;
      }

      // Navigate to the link
      router.push(href);
    } catch (error) {
      console.error("Failed to switch account:", error);
    }
  };

  return (
    <a
      className={cn(
        "inline-flex items-center gap-1 cursor-pointer justify-en hover:underline",
        className,
      )}
      onClick={handleClick}
      {...rest}
    >
      {children}
      {accountId !== currentTeam?.id && (
        <ExternalLink className="text-muted-foreground" size={12} />
      )}
    </a>
  );
}
