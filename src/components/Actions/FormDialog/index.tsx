import {
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalTitle,
} from "~/components/ui/modal";
import type { ReactElement } from "react";
import type { FormProps } from "~/components/ui/form";

type FormDialogProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  form: ReactElement<FormProps<any, any, any>>;
};

const FormDialog = ({ open, setOpen, title, form }: FormDialogProps) => {
  return (
    <Modal
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <div>{form}</div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default FormDialog;
