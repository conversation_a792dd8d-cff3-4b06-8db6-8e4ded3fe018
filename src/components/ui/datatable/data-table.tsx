import * as React from "react";
import { ReactNode, useEffect } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  Table as ReactTable,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { DataTableToolbar } from "~/components/ui/datatable/data-table-toolbar";
import { DataTablePagination } from "~/components/ui/datatable/pagination";
import { DataTableFacetedFilterProps } from "~/components/ui/datatable/data-table-faceted-filter";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { useMediaQuery } from "~/lib/use-media-query";
import Skeleton from "react-loading-skeleton";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  tableFacets?: (
    table: ReactTable<TData>,
  ) => DataTableFacetedFilterProps<TData, TValue>[];
  defaultFilters?: ColumnFiltersState;
  defaultSorting?: SortingState;
  isLoading?: boolean;
  linkTo?: (row: TData) => string;
  hideHeader?: boolean;
  onClickRow?: (row: TData) => void;
  actionBar?: (table: ReactTable<TData>) => ReactNode;
  responsive?: {
    gridAreas: string;
  };
}

export function DataTable<TData, TValue>({
  columns,
  data,
  defaultFilters,
  isLoading,
  tableFacets,
  defaultSorting,
  hideHeader,
  responsive,
  actionBar,
}: DataTableProps<TData, TValue>) {
  const onMobile = useMediaQuery("(max-width: 768px)");
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);

  useEffect(() => {
    if (defaultSorting) {
      setSorting(defaultSorting);
    }
  }, [defaultSorting]);

  useEffect(() => {
    if (defaultFilters) {
      setColumnFilters(defaultFilters);
    }
  }, [defaultFilters]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  if (responsive && onMobile) {
    return (
      <div className={"m-2 w-[90%]"}>
        {!hideHeader && (
          <DataTableToolbar
            table={table}
            tableFacets={
              tableFacets !== undefined ? tableFacets(table) : undefined
            }
          />
        )}
        <ul className="py-2 min-h-[196px] flex flex-wrap gap-2 w-screen">
          {table.getRowModel().rows.map((row) => (
            <li key={row.id} className="p-2 relative w-full border-t">
              <div
                style={{
                  display: "grid",
                  width: "100%",
                  maxWidth: "100%",
                  gap: "0.5rem",
                  gridTemplateAreas: responsive?.gridAreas,
                }}
              >
                {row
                  .getVisibleCells()
                  .filter((cell) => {
                    const name = cell.id.split("_")[1];
                    if (!name) {
                      return false;
                    }
                    return responsive?.gridAreas.includes(name);
                  })
                  .map((cell) => {
                    const name = cell.id.split("_")[1];
                    return (
                      <div
                        key={cell.id}
                        style={{
                          overflow: "hidden",
                          gridArea: name,
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </div>
                    );
                  })}
              </div>
            </li>
          ))}
        </ul>
        <DataTablePagination table={table} />
      </div>
    );
  }

  return (
    <Card>
      {!hideHeader && (
        <CardHeader>
          <DataTableToolbar
            table={table}
            tableFacets={
              tableFacets !== undefined ? tableFacets(table) : undefined
            }
          />
        </CardHeader>
      )}
      <CardContent>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} colSpan={header.colSpan}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      <Skeleton />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <>
                {table?.getRowModel()?.rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </>
            )}
          </TableBody>
        </Table>
      </CardContent>

      <div className="flex flex-col gap-2.5">
        <DataTablePagination table={table} />
        {actionBar &&
          table.getFilteredSelectedRowModel().rows.length > 0 &&
          actionBar(table)}
      </div>
    </Card>
  );
}
