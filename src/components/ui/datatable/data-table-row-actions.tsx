import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import React from "react";
import { MoreHorizontal } from "lucide-react";
import DeleteDialog from "~/components/Actions/DeleteDialog";

interface DataTableRowActionsProps<TData> {
  children: React.ReactNode;
  onOpenChange?: (open: boolean) => void;
  remove?: {
    id: string;
    name: string;

    onDelete: (id: string) => void;
  };
}

export function DataTableRowActions<TData>({
  children,
  remove,
  onOpenChange,
}: DataTableRowActionsProps<TData>) {
  const [open, setOpen] = React.useState(false);

  return (
    <DropdownMenu>
      <DeleteDialog
        open={open}
        onOpenChange={(open) => {
          if (onOpenChange) {
            onOpenChange(open);
          }
          setOpen(open);
        }}
        onDelete={() => {
          if (remove) {
            remove.onDelete(remove.id);
          }
        }}
        item={remove?.name || "item"}
      />
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {children}
        {remove && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setOpen(true);
              }}
            >
              Delete
              <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
