import { Table } from "@tanstack/react-table";
import { Input } from "~/components/ui/input";
import React from "react";
import { Button } from "~/components/ui/button";
import { DataTableViewOptions } from "~/components/ui/datatable/column-toggle";
import { X } from "lucide-react";
import {
  DataTableFacetedFilter,
  DataTableFacetedFilterProps,
} from "~/components/ui/datatable/data-table-faceted-filter";
import { DataTableSortList } from "~/components/data-table/data-table-sort-list";

interface DataTableToolbarProps<TData, TValue> {
  table: Table<TData>;
  tableFacets?: DataTableFacetedFilterProps<TData, TValue>[] | undefined;
}

export function DataTableToolbar<TData, TValue>({
  table,
  tableFacets,
}: DataTableToolbarProps<TData, TValue>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const searchField = table.getAllColumns().find((column) => {
    return column.getCanGlobalFilter();
  });

  return (
    <div className="flex justify-between">
      <div className="flex flex-row flex-wrap gap-2 flex-1 md:items-center">
        {searchField && (
          <div className={"w-[150px] lg:w-[250px]"}>
            <Input
              placeholder={`Filter ${searchField.id}...`}
              value={(searchField?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                searchField?.setFilterValue(event.target.value)
              }
              className="h-8"
            />
          </div>
        )}
        {tableFacets?.map((facet, index) => {
          return (
            <DataTableFacetedFilter
              key={index}
              column={facet.column}
              title={facet.title}
              options={facet.options}
            />
          );
        })}
        {isFiltered && (
          <Button
            variant="outline"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>

      <div className={"flex flex-row md:items-center gap-2"}>
        <DataTableSortList table={table} />
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
