import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2  whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        primary:
          "bg-primary border border-input text-primary-foreground shadow hover:bg-primary/90",
        upgrade:
          "bg-orange border border-input text-orange-foreground shadow hover:bg-orange/90",
        primaryUpgrade:
          "bg-primary border border-input text-input/90 shadow hover:bg-primary",
        destructive:
          "bg-destructive border border-ring text-destructive-foreground shadow hover:bg-destructive-hover",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary border border-input text-secondary-foreground shadow-sm hover:bg-input/90",
        ghost:
          "hover:bg-secondary hover:border text-secondary-foreground/30 border-input hover:text-secondary-foreground", // Really double think before using this. 99% of cases should be primary or secondary.
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        xs: "h-6 rounded-md py-1.5 px-3 text-xs",
        sm: "h-8 rounded-md py-1.5 px-3 text-sm",
        md: "h-8 rounded-md py-1.5 px-3 text-sm",
        lg: "h-10 rounded-md px-8",
        full: "h-7 rounded-md py-1.5 px-3 w-full text-sm border-0",
        unbound: "",
        icon: "h-6 w-6",
        link: "p-0 max-w-fit",
      },
    },
    defaultVariants: {
      variant: "secondary",
      size: "md",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(
          "print:hidden",
          buttonVariants({ variant, size, className }),
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
