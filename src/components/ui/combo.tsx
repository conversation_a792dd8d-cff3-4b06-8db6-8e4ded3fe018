import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { FormControl } from "~/components/ui/form";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { CheckIcon, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command";
import React from "react";

type ComboboxProps = {
  value?: string | React.ReactNode;
  placeholder: string;
  onChange: (value: string) => void;
  items: {
    value: string;
    label: string | React.ReactNode;
  }[];
};

const Combobox = (props: ComboboxProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant="outline"
            role="combobox"
            className={cn(
              "w-[175px] sm:w-[200px] justify-between h-9",
              !props.value && "text-muted-foreground",
            )}
          >
            {props.value ? props.value : props.placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-[150px] sm:w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search..." className="h-9" />
          <CommandList>
            <CommandEmpty>No item found.</CommandEmpty>
            <CommandGroup>
              {props.items.map((item, index) => (
                <CommandItem
                  value={item.value}
                  key={index}
                  onSelect={() => {
                    props.onChange(item.value);
                  }}
                >
                  {item.label}
                  <CheckIcon
                    className={cn(
                      "ml-auto h-4 w-4",
                      item.value === props.value ? "opacity-100" : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default Combobox;
