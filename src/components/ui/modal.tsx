"use client";

import * as React from "react";

import { cn } from "~/lib/utils";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useMediaQuery } from "~/lib/use-media-query";
import { ScrollArea } from "~/components/ui/scroll-area";

interface BaseProps {
  children: React.ReactNode;
}

interface RootModalProps extends BaseProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface ModalProps extends BaseProps {
  className?: string;
  asChild?: true;
}

const desktop = "(min-width: 768px)";

const Modal = ({ children, ...props }: RootModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const Modal = Dialog;

  return <Modal {...props}>{children}</Modal>;
};

const ModalTrigger = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalTrigger = DialogTrigger;

  return (
    <ModalTrigger className={className} {...props}>
      {children}
    </ModalTrigger>
  );
};

const ModalClose = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalClose = DialogClose;

  return (
    <ModalClose className={className} {...props}>
      {children}
    </ModalClose>
  );
};

const ModalContent = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalContent = DialogContent;

  return (
    <ModalContent
      className={cn(
        "max-h-[90vh] max-w-[97vw] min-w-[320px] w-fit h-fit overflow-y-auto p-4 md:p-6",
        // Add min padding around content for small forms
        "data-[state=open]:sm:min-w-[25vw]",
        className,
      )}
      {...props}
    >
      {children}
    </ModalContent>
  );
};

const ModalDescription = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalDescription = DialogDescription;

  return (
    <ModalDescription className={className} {...props}>
      {children}
    </ModalDescription>
  );
};

const ModalHeader = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalHeader = DialogHeader;

  return (
    <ModalHeader className={className} {...props}>
      {children}
    </ModalHeader>
  );
};

const ModalTitle = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalTitle = DialogTitle;

  return (
    <ModalTitle className={className} {...props}>
      {children}
    </ModalTitle>
  );
};

const ModalBody = ({ className, children, ...props }: ModalProps) => {
  return (
    <ScrollArea className={"h-full"}>
      <div className={cn("max-w-[82vw] overflow-hidden", className)} {...props}>
        {children}
      </div>
    </ScrollArea>
  );
};

const ModalFooter = ({ className, children, ...props }: ModalProps) => {
  const isDesktop = useMediaQuery(desktop);
  const ModalFooter = DialogFooter;

  return (
    <ModalFooter className={className} {...props}>
      {children}
    </ModalFooter>
  );
};

export {
  Modal,
  ModalTrigger,
  ModalClose,
  ModalContent,
  ModalDescription,
  ModalHeader,
  ModalTitle,
  ModalBody,
  ModalFooter,
};
