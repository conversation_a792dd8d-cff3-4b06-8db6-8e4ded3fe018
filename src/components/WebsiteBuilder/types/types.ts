import { CustomField } from "@measured/puck";
import { number, object, string } from "yup";

export type FieldProps<K> = {
  name: string;
  field: CustomField<K>;
  value: K;
  onChange: (value: K) => void;
};

export const ProductListSchema = object().shape({
  pullProductsFrom: string()
    .oneOf(["category", "bestSellers", "all"])
    .required(),

  categoryId: number(),
});

export type ProductListValues = typeof ProductListSchema.__outputType;
