import { useEffect } from "react";
import { useProductConfig, useSiteConfig } from "@prp/blocks";

const ProductLookupComponent = () => {
  const productConfig = useProductConfig();
  const siteConfig = useSiteConfig();
  useEffect(() => {
    fetch(`/api/public/${siteConfig.config.name}/products`).then(
      async (response) => {
        const data = await response.json();
        productConfig.setConfig((prevConfig) => ({
          ...prevConfig,
          products: data.products,
          bestSellers: data.products,
        }));
      },
    );
    fetch(`/api/public/${siteConfig.config.name}/categories`).then(
      async (response) => {
        const data = await response.json();
        productConfig.setConfig((prevConfig) => ({
          ...prevConfig,
          categories: data.categories,
        }));
      },
    );
  }, []);

  return null;
};
export default ProductLookupComponent;
