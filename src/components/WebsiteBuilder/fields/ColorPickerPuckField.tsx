import React, { ReactElement } from "react";
import ColorPicker from "~/components/ColorPicker";
import { FieldProps } from "~/components/WebsiteBuilder/types/types";
import { FieldLabel } from "@measured/puck";

const ColorPickerPuckField = ({
  field,
  name,
  value,
  onChange,
}: FieldProps<string> | FieldProps<string | undefined>): ReactElement => {
  return (
    <div className={"flex"}>
      <FieldLabel label={field?.label || name}>
        <ColorPicker
          background={value ?? ""}
          setBackground={(value) => onChange(value)}
        />
      </FieldLabel>
    </div>
  );
};

export default ColorPickerPuckField;
