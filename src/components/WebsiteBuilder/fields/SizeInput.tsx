import { FieldProps } from "~/components/WebsiteBuilder/types/types";
import { useForm } from "react-hook-form";
import { number, object, string } from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import React from "react";
import { Input } from "~/components/ui/input";

const SIZE_INPUT_VALUES = object().shape({
  preset: string(),
  height: number().when("unit", (selected) => {
    if (selected.some((method) => method === "auto")) {
      return string();
    }
    return number().required();
  }),
  width: number().when("unit", (selected) => {
    if (selected.some((method) => method === "auto")) {
      return string();
    }
    return number().required();
  }),
  unit: string().required("Unit is required"),
});

export type SizeInputValues = typeof SIZE_INPUT_VALUES.__outputType;

export const getWidth = (size: SizeInputValues | undefined): string => {
  const measurement = size?.unit === "auto" ? "" : size?.unit || "%";
  return `${size?.width || 100}${measurement}`;
};

export const getHeight = (size: SizeInputValues | undefined): string => {
  const measurement = size?.unit === "auto" ? "" : size?.unit || "%";
  return `${size?.height || 100}${measurement}`;
};

export const getSizeClassName = (size: SizeInputValues | undefined) => {
  return `w-[${size?.width || 100}${size?.unit || "%"}] h-[${
    size?.height || 100
  }${size?.unit || "%"}]`;
};

const SizeInput = ({
  onChange,
  value,
}: FieldProps<SizeInputValues> | FieldProps<SizeInputValues | undefined>) => {
  const handleValuesChange = (values: SizeInputValues) => onChange(values);
  const form = useForm<SizeInputValues>({
    defaultValues: value,
    resolver: yupResolver(SIZE_INPUT_VALUES),
  });
  return (
    <Form
      {...form}
      onSubmit={handleValuesChange}
      formItemProps={{
        onChange: () => {
          form.handleSubmit(handleValuesChange)();
        },
      }}
    >
      <FormField
        control={form.control}
        name="preset"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Preset</FormLabel>
            <Select
              value={field.value}
              onValueChange={(value) => {
                if (value === "small") {
                  form.setValue("width", 4);
                  form.setValue("height", 4);
                  form.setValue("unit", "rem");
                }
                if (value === "medium") {
                  form.setValue("width", 6);
                  form.setValue("height", 6);
                  form.setValue("unit", "rem");
                }
                if (value === "large") {
                  form.setValue("width", 12);
                  form.setValue("height", 12);
                  form.setValue("unit", "rem");
                }
                field.onChange(value);
              }}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a Preset" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem key={"small"} value={"small"}>
                  Small
                </SelectItem>
                <SelectItem key={"medium"} value={"medium"}>
                  Medium
                </SelectItem>
                <SelectItem key={"large"} value={"large"}>
                  Large
                </SelectItem>
                <SelectItem key={"manual"} value={"manual"}>
                  Manual
                </SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      {form.watch("preset") === "manual" && (
        <>
          <FormField
            control={form.control}
            name="unit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Unit</FormLabel>
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a Size Unit" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem key={"px"} value={"px"}>
                      {"px"}
                    </SelectItem>
                    <SelectItem key={"rem"} value={"rem"}>
                      {"rem"}
                    </SelectItem>
                    <SelectItem key={"%"} value={"%"}>
                      {"%"}
                    </SelectItem>
                    <SelectItem key={"auto"} value={"auto"}>
                      {"auto"}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="width"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Width</FormLabel>
                <Input placeholder="10" {...field} />
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="height"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Height</FormLabel>
                <Input placeholder="10" {...field} />
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}
    </Form>
  );
};

export default SizeInput;
