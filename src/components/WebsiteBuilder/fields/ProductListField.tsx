import {
  FieldProps,
  ProductListSchema,
  ProductListValues,
} from "~/components/WebsiteBuilder/types/types";
import React, { ReactElement, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { CategoryWithThumbnailAndProductCount } from "~/pages/api/categories";

const ProductListField = ({
  value,
  onChange,
}:
  | FieldProps<ProductListValues>
  | FieldProps<ProductListValues | undefined>): ReactElement => {
  const form = useForm<ProductListValues>({
    defaultValues: {
      pullProductsFrom: "all",
      categoryId: -1,
      ...value,
    },
    resolver: yupResolver(ProductListSchema),
  });
  const [data, setData] = useState<CategoryWithThumbnailAndProductCount[]>([]);

  useEffect(() => {
    if (data.length > 0) {
      return;
    }
    fetch(`/api/categories`)
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        setData(data.categories);
      });
  }, []);
  const handleValuesChange = (values: ProductListValues) => onChange(values);
  return (
    <Form
      {...form}
      onSubmit={handleValuesChange}
      formItemProps={{
        onChange: () => {
          form.handleSubmit(handleValuesChange)();
        },
      }}
    >
      <FormItem
        label={"Pull Products From"}
        name={"pullProductsFrom"}
        render={({ field }) => {
          return (
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger>
                <SelectValue placeholder={"Select Product Source"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={"all"}>All Products</SelectItem>
                <SelectItem value={"category"}>Category</SelectItem>
                <SelectItem value={"bestSellers"}>Best Sellers</SelectItem>
              </SelectContent>
            </Select>
          );
        }}
      />
      {form.watch("pullProductsFrom") === "category" && (
        <FormItem
          label={"Category ID"}
          name={"categoryId"}
          render={({ field }) => {
            return (
              <Select
                value={field.value?.toString()}
                onValueChange={(value) => field.onChange(Number(value))}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={"Select a Product Source Category"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {data.map((category) => (
                    <SelectItem
                      key={category.id}
                      value={category.id.toString()}
                    >
                      {category.name}
                    </SelectItem>
                  ))}
                  {<SelectItem value={"-1"}>Page Content</SelectItem>}
                </SelectContent>
              </Select>
            );
          }}
        />
      )}
      {form.watch("categoryId") === -1 && (
        <p className={"text-muted-foreground text-sm italic"}>
          This category will be pulled from the page content if available. If
          this is used on a page that does not have a category, no products will
          be displayed.
        </p>
      )}
    </Form>
  );
};

export default ProductListField;
