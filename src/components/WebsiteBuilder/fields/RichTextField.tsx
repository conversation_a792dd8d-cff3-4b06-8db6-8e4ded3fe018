import { FieldProps } from "~/components/WebsiteBuilder/types/types";
import { FieldLabel } from "@measured/puck";
import Editor from "~/components/RichText/editor";
import React from "react";

const RichTextField = ({
  onChange,
  value,
}: FieldProps<string> | FieldProps<string | undefined>) => {
  const isJson = value?.startsWith("{") && value?.endsWith("}");
  return (
    <div className={"flex flex-col"}>
      <FieldLabel label={"Text"} el={"div"}>
        <Editor
          compact={true}
          content={isJson ? JSON.parse(value || "{}") : value}
          onChange={(v, json) => {
            onChange(json ? json : v);
          }}
          placeholder="Rich Text Editor"
        />
      </FieldLabel>
    </div>
  );
};

export default RichTextField;
