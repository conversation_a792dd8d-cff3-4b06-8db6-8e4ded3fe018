import React, { ReactElement } from "react";
import Image from "next/image";
import ImageSelectionDialog from "~/components/ImageSelectionDialog";
import { Button } from "~/components/ui/button";
import { EditIcon, UploadIcon, X } from "lucide-react";

const UploadImagePuckField = ({ value, onChange }: any): ReactElement => {
  return (
    <div className={"flex flex-row"}>
      <div className="hover:bg-border relative">
        <EditIcon
          className={"hidden absolute top-2 right-2 h-4 w-4 hover:absolute"}
        />
        <Image
          src={value}
          alt={"image"}
          width={60}
          height={60}
          objectFit={"cover"}
        />
      </div>
      <div>
        <ImageSelectionDialog
          onSelection={(id: string, url: string) => {
            onChange(url);
          }}
        >
          <Button variant="outline">
            <UploadIcon className="mr-2 h-4 w-4" />
            Change Image
          </Button>
        </ImageSelectionDialog>
        {value && (
          <Button
            variant="outline"
            className="ml-2"
            onClick={() => {
              onChange("");
            }}
          >
            <X className="mr-2 h-4 w-4" />
            Remove Image
          </Button>
        )}
      </div>
    </div>
  );
};

export default UploadImagePuckField;
