import { cn } from "~/lib/utils";
import {
  LocalTabs,
  LocalTabsContent,
  LocalTabsList,
  LocalTabsTrigger,
} from "~/components/ui/local-tabs";
import { Puck, usePuck } from "@measured/puck";
import React, { useEffect } from "react";
import { useMediaQuery } from "~/lib/use-media-query";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Sheet, SheetContent, SheetHeader } from "~/components/ui/sheet";

const WebsiteEditorSidebar = () => {
  const [tab, setTab] = React.useState("editor");
  const { dispatch, appState } = usePuck();
  const isMobile = useMediaQuery("(max-width: 426px)");

  useEffect(() => {
    if (!isMobile) {
      return;
    }
    if (appState.ui.leftSideBarVisible && appState.ui.isDragging) {
      dispatch({
        type: "setUi",
        ui: {
          leftSideBarVisible: false,
        },
      });
    }
  }, [appState.ui.isDragging]);

  const content = (
    <div
      className={cn("bg-white flex flex-col", {
        hidden: !appState.ui.leftSideBarVisible && !isMobile,
        "w-full": isMobile,
        "w-[25%] sm:border-r": !isMobile,
      })}
    >
      {/* Tabs Header */}
      <LocalTabs
        value={tab}
        onValueChange={() => {
          setTab(tab === "editor" ? "components" : "editor");
        }}
      >
        <LocalTabsList className={"ml-2"}>
          <LocalTabsTrigger value="components" className="text-sm">
            Components
          </LocalTabsTrigger>
          <LocalTabsTrigger value="editor" className="text-sm">
            Properties
          </LocalTabsTrigger>
        </LocalTabsList>

        <LocalTabsContent value="components" className="flex-1 overflow-hidden">
          <ScrollArea className="h-[85vh] px-2 pt-2">
            <Puck.Components />
          </ScrollArea>
        </LocalTabsContent>

        <LocalTabsContent value="editor" className="flex-1 overflow-hidden">
          <ScrollArea className="h-[85vh] sm-px-2 py-2">
            <Puck.Fields />
          </ScrollArea>
        </LocalTabsContent>
      </LocalTabs>
    </div>
  );

  if (!isMobile) {
    return content;
  }

  return (
    <Sheet
      open={appState.ui.leftSideBarVisible}
      onOpenChange={() => {
        dispatch({
          type: "setUi",
          ui: {
            leftSideBarVisible: !appState.ui.leftSideBarVisible,
          },
        });
      }}
    >
      <SheetContent side={"left"} className={"w-[80%]"}>
        <SheetHeader>Editing</SheetHeader>
        {content}
      </SheetContent>
    </Sheet>
  );
};

export default WebsiteEditorSidebar;
