import { usePuck } from "@measured/puck";
import { <PERSON>, Settings, Smartphone } from "lucide-react";
import { useMediaQuery } from "~/lib/use-media-query";
import { useEffect } from "react";

const MOBILE_VIEWPORT = {
  label: "Mobile",
  width: 390,
  height: 844, // iPhone 14 Pro
};

const DESKTOP_VIEWPORT = {
  label: "Desktop",
  width: 10_000, // Full width
  height: undefined,
};

const WebsiteEditorSwitcher = () => {
  const { appState, dispatch } = usePuck();
  const isScreenMobile = useMediaQuery("(max-width: 426px)");
  const currentLabel =
    appState.ui.viewports.current.width >= 768 ? "Desktop" : "Mobile";

  useEffect(() => {
    if (isScreenMobile) {
      setViewport("Mobile");
    }
  }, [isScreenMobile]);

  const setViewport = (label: string) => {
    const next = label === "Mobile" ? MOBILE_VIEWPORT : DESKTOP_VIEWPORT;
    if (next) {
      dispatch({
        type: "setUi",
        ui: {
          viewports: {
            ...appState.ui.viewports,
            current: {
              height: next.height ?? "auto",
              width: next.width ?? 10_000,
            },
          },
        },
      });
    }
  };

  const isMobile = currentLabel === "Mobile";

  return (
    <div
      className="relative items-center w-[90px] h-9 bg-muted rounded-full border shadow-inner cursor-pointer transition-colors hidden sm:flex"
      onClick={() => setViewport(isMobile ? "Desktop" : "Mobile")}
    >
      <div
        className={`absolute w-8 h-8 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
          isMobile ? "translate-x-[52px]" : "translate-x-[4px]"
        }`}
      />
      <div className="flex justify-between w-full px-3 text-xs text-muted-foreground z-10 font-medium">
        <span className={isMobile ? "opacity-50" : "opacity-100"}>
          <Monitor className={"w-4 h-4 text-muted-foreground"} />
        </span>
        <span className={isMobile ? "opacity-100" : "opacity-50"}>
          <Smartphone className={"w-4 h-4 text-muted-foreground"} />
        </span>
      </div>
    </div>
  );
};

export default WebsiteEditorSwitcher;
