import { Data, PuckAction, usePuck } from "@measured/puck";
import NextLink from "next/link";
import { But<PERSON> } from "~/components/ui/button";
import {
  ChevronDown,
  PlusCircleIcon,
  Redo,
  Settings,
  SidebarCloseIcon,
  SidebarOpenIcon,
  Undo,
  X,
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "~/components/ui/command";
import React, { useState } from "react";
import { SystemPages, WebsitePage } from "~/pages/website";
import SimpleStringForm from "~/components/FormLayout/SimpleForm";
import FormDialog from "~/components/Actions/FormDialog";
import { toast } from "sonner";
import WebsiteEditorSwitcher from "./WebsiteEditorSwitcher";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import WebsiteCustomCodeForm from "~/form/WebsiteCustomCodeForm";

type WebsiteEditorHeaderProps = {
  pages: WebsitePage[];
  currentSlug: string;
  onReset: () => void;
  onPageChange: (newPage: string, pages: Data) => void;
  onNewPage: (newPage: WebsitePage) => void;
  onPreview: () => void;
  onPublish: () => void;
};

const WebsiteEditorHeader = ({
  pages,
  onPageChange,
  onReset,
  currentSlug,
  onNewPage,
  onPublish,
  onPreview,
}: WebsiteEditorHeaderProps) => {
  const [newPageFormOpen, setNewPageFormOpen] = useState<boolean>(false);
  const [websiteSettingsForm, setWebsiteSettingsForm] =
    useState<boolean>(false);

  const [open, setOpen] = useState<boolean>(false);
  const { appState, dispatch, history } = usePuck();

  const getPageContent = async (slug: string) => {
    const response = await fetch(
      `/api/websites/pages/${encodeURIComponent(slug)}`,
    );
    return response.json();
  };

  const fetchNewPage = (
    slug: string,
    hasHistory: boolean,
    dispatch: (action: PuckAction) => void,
  ) => {
    if (hasHistory) {
      if (
        !confirm("You have unsaved changes, are you sure you want to leave?")
      ) {
        return;
      }
    }
    getPageContent(slug).then((data) => {
      if (Object.keys(data.page).length === 0) {
        console.log("No data found for page, setting initial data");
        onReset();
        return;
      }
      dispatch({
        type: "setData",
        data: data.page,
      });
      onPageChange(slug, data.page);
    });
  };

  const createNewPage = async (pageName: string) => {
    const loading = toast.loading("Creating new page...");
    const request = await fetch("/api/websites/pages/create", {
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      body: JSON.stringify({
        name: pageName,
      }),
    });

    if (!request.ok) {
      toast.error("A page with that name already exists.");
      return;
    }

    const newPage = await request.json();

    onNewPage(newPage.page);

    toast.dismiss(loading);
    toast.success(`${pageName} created successfully.`);
  };

  return (
    <TooltipProvider>
      <section className="w-full bg-white border border-b mb-2">
        <FormDialog
          open={newPageFormOpen}
          setOpen={setNewPageFormOpen}
          title={"New Page"}
          form={
            <SimpleStringForm
              title={"Page Name"}
              onChange={(pageName) => {
                createNewPage(pageName).then(() => {
                  setNewPageFormOpen(false);
                });
              }}
              defaultValue={""}
            />
          }
        />
        <FormDialog
          open={websiteSettingsForm}
          setOpen={setWebsiteSettingsForm}
          title={"Edit Website Settings"}
          form={<WebsiteCustomCodeForm showSaveButton={false} />}
        />
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipContent>Exit Editor</TooltipContent>
                <TooltipTrigger>
                  <NextLink href={"/"}>
                    <Button
                      variant="outline"
                      size="icon"
                      aria-label="Exit"
                      className={"text-foreground"}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </NextLink>
                </TooltipTrigger>
              </Tooltip>

              <Tooltip>
                <TooltipContent>Toggle Sidebar</TooltipContent>
                <TooltipTrigger>
                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Hide Left Sidebar"
                    className={"text-foreground"}
                    onClick={() => {
                      dispatch({
                        type: "setUi",
                        ui: {
                          leftSideBarVisible: !appState.ui.leftSideBarVisible,
                        },
                      });
                    }}
                  >
                    {appState.ui.leftSideBarVisible ? (
                      <SidebarCloseIcon className={"h-4 w-4"} />
                    ) : (
                      <SidebarOpenIcon className={"h-4 w-4"} />
                    )}
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </div>
            <div className="flex-grow flex items-center justify-center gap-2">
              <WebsiteEditorSwitcher />
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setOpen(true);
                    }}
                  >
                    {pages.find((page) => page.slug === currentSlug)?.name ??
                      "Home"}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0">
                  <Command>
                    <CommandInput placeholder="Search website" />
                    <CommandList>
                      <CommandEmpty>No Page Found</CommandEmpty>
                      <CommandGroup heading={"System Pages"}>
                        {pages
                          .filter((page) => {
                            return (
                              page.systemPage &&
                              SystemPages.find(
                                (sysPage) => sysPage.slug === page.slug,
                              )
                            );
                          })
                          .sort(
                            (a, b) =>
                              SystemPages.findIndex(
                                (iA) => iA.slug === a.slug,
                              ) -
                              SystemPages.findIndex((iB) => iB.slug === b.slug),
                          )
                          .map((page) => (
                            <CommandItem
                              key={page.slug}
                              disabled={
                                SystemPages.find(
                                  (sysPage) => sysPage.slug === page.slug,
                                )?.disabled
                              }
                              onSelect={() => {
                                fetchNewPage(
                                  page.slug,
                                  history.historyStore?.hasPast === true,
                                  dispatch,
                                );
                                setOpen(false);
                              }}
                            >
                              <span>{page.name}</span>
                              <span className="text-xs ml-1 text-muted-foreground">
                                {`(${page.slug})`}
                              </span>
                            </CommandItem>
                          ))}
                      </CommandGroup>
                      <CommandSeparator />
                      <CommandGroup heading={"Custom Pages"}>
                        {pages
                          .filter((page) => !page.systemPage)
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map((page) => (
                            <CommandItem
                              key={page.slug}
                              onSelect={() => {
                                fetchNewPage(
                                  page.slug,
                                  history.historyStore?.hasPast === true,
                                  dispatch,
                                );
                                setOpen(false);
                              }}
                            >
                              {page.name}
                            </CommandItem>
                          ))}
                      </CommandGroup>
                      <CommandSeparator />
                      <CommandGroup>
                        <CommandItem
                          className={"text-blue-400 gap-2 flex items-center"}
                          onSelect={() => {
                            setOpen(false);
                            setNewPageFormOpen(true);
                          }}
                        >
                          <PlusCircleIcon className={"h-4 w-4"} />
                          New Page
                        </CommandItem>
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <Tooltip>
                <TooltipContent>Website Settings</TooltipContent>
                <TooltipTrigger>
                  <Button
                    variant="ghost"
                    size="icon"
                    aria-label="Settings"
                    className={"text-foreground"}
                    onClick={() => {
                      setWebsiteSettingsForm(true);
                    }}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
              </Tooltip>
            </div>
            <div className="flex items-center space-x-4 ml-auto">
              <Button
                variant="secondary"
                onClick={() => {
                  onPreview();
                }}
              >
                Preview
              </Button>
              <Button
                variant="primary"
                disabled={history.historyStore?.hasPast !== true}
                onClick={() => {
                  onPublish();
                }}
              >
                Publish
              </Button>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Undo"
                  disabled={!history.historyStore?.hasPast}
                  onClick={history.historyStore?.back}
                >
                  <Undo className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Redo"
                  disabled={!history.historyStore?.hasFuture}
                  onClick={history.historyStore?.forward}
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </TooltipProvider>
  );
};

export default WebsiteEditorHeader;
