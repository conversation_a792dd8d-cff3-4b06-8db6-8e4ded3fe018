import { cn } from "~/lib/utils";
import {
  BlocksIcon,
  Box,
  Boxes,
  FileText,
  GalleryVertical,
  Grid3X3,
  Heading,
  Image,
  Layers3,
  LayoutDashboard,
  Link,
  List,
  Mail,
  MapPin,
  Megaphone,
  MousePointerClick,
  MoveVertical,
  PictureInPicture2Icon,
  PresentationIcon,
  Quote,
  Rows,
  ShoppingCart,
  Tag,
  Terminal,
  Users,
} from "lucide-react";
import React, { ReactNode } from "react";

export const componentIconMap: Record<
  string,
  { icon: ReactNode; description: string; name?: string }
> = {
  ContainerBlock: {
    icon: <Box className="w-5 h-5" />,
    description:
      "A padded layout container for inner content, most content goes inside a container.",
  },
  SpacingBlock: {
    icon: <MoveVertical className="w-5 h-5" />,
    description: "Adds vertical space between sections",
    name: "Spacer",
  },
  TextCarouselBlock: {
    icon: <Quote className="w-5 h-5" />,
    description: "Carousel that rotates through text content",
  },
  HeaderCarouselBlock: {
    icon: <PresentationIcon className="w-5 h-5" />,
    description: "Header with sliding images or text",
  },
  BackgroundHeroBlock: {
    icon: <Image className="w-5 h-5" />,
    description: "Full-width image with text and buttons overlaying",
  },
  AreasWeServeBlock: {
    icon: <MapPin className="w-5 h-5" />,
    description: "Showcase service areas by city, useful to link to city pages",
  },
  ProductCarouselBlock: {
    icon: <ShoppingCart className="w-5 h-5" />,
    description: "Horizontal scroll of featured products",
  },
  ReviewCarouselBlock: {
    icon: <GalleryVertical className="w-5 h-5" />,
    description: "Rotating carousel of customer reviews",
  },
  GridBlock: {
    icon: <LayoutDashboard className="w-5 h-5" />,
    name: "Product Grid",
    description: "Display products in a grid layout with a header",
  },
  FunProductCarouselBlock: {
    icon: <Layers3 className="w-5 h-5" />,
    description: "Multi-color carousel of products with fun animations",
  },
  PageColumnsBlock: {
    icon: <Rows className="w-5 h-5" />,
    description: "Flexible multi-column content layout",
  },
  EmbeddedBlock: {
    icon: <PictureInPicture2Icon className="w-5 h-5" />,
    description: "Embed YouTube, Maps, or other HTML content",
  },
  ButtonBlock: {
    icon: <MousePointerClick className="w-5 h-5" />,
    description: "Clickable call-to-action button",
  },
  ContactUsBlock: {
    icon: <Mail className="w-5 h-5" />,
    description: "Contact form or contact info block",
  },
  HeadingTextBlock: {
    icon: <Heading className="w-5 h-5" />,
    description: "A simple heading text block",
  },
  ImageBlock: {
    icon: <Image className="w-5 h-5" />,
    description: "Static image display block",
  },
  TextBlock: {
    icon: <FileText className="w-5 h-5" />,
    description: "Rich text editor block for text content",
  },
  FAQBlock: {
    icon: <List className="w-5 h-5" />,
    description: "Frequently asked questions section",
  },
  CategoryGridBlock: {
    icon: <Grid3X3 className="w-5 h-5" />,
    description: "Grid of categories or product groups",
  },
  HeroBlock: {
    icon: <Users className="w-5 h-5" />,
    description:
      "Bold headline + action hero section, most cases would prefer the Background Hero",
  },
  BannerBlock: {
    icon: <Megaphone className="w-5 h-5" />,
    description:
      "Banner strip for promos or alerts including a book now button",
  },
  CategoryCarouselBlock: {
    icon: <Tag className="w-5 h-5" />,
    description: "Carousel for product categories",
  },
  ListLinkBlock: {
    icon: <Link className="w-5 h-5" />,
    description: "A simple list of clickable links",
  },
  CartBannerBlock: {
    icon: <Boxes className="w-5 h-5" />,
    description:
      "A cart banner that shows the number of items and date of the cart or a checkout button",
  },
  RawHTML: {
    icon: <Terminal className="w-5 h-5" />,
    description: "Raw HTML block for custom code (Not recommended)",
    name: "Raw HTML",
  },
};

export function WebsiteEditorComponentItem({ name }: { name: string }) {
  const icon = componentIconMap[name]?.icon ?? (
    <BlocksIcon className="w-5 h-5" />
  );

  return (
    <button
      type="button"
      className={cn(
        "flex max-w-full w-full flex-col justify-center border transition-colors items-center text-center text-xs font-medium bg-muted text-muted-foreground rounded-md hover:bg-muted/70",
      )}
    >
      <div className="mb-2">{icon}</div>
      {componentIconMap[name]?.name ??
        name
          .replace(/Block$/, "")
          .replace(/([A-Z])/g, " $1")
          .trim()}
    </button>
  );
}
