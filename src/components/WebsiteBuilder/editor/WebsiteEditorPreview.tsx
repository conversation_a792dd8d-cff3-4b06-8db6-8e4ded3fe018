import { Puck, usePuck } from "@measured/puck";
import React from "react";

const WebsiteEditorPreview = () => {
  const { appState } = usePuck();
  const { width, height } = appState.ui.viewports.current;

  const style = {
    width: width && width < 10_000 ? `${width}px` : "100%",
    height: height === "auto" ? "100%" : `${height}px`,
    border: "1px solid #e5e7eb",
    margin: "0 auto",
    backgroundColor: "#fff",
    boxShadow: "0 0 0 1px rgba(0,0,0,0.05)",
  };
  return (
    <div
      className="border bg-white shadow-sm rounded-md overflow-hidden h-full"
      style={style}
    >
      <Puck.Preview />
    </div>
  );
};

export default WebsiteEditorPreview;
