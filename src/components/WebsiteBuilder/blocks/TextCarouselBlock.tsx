import { ComponentConfig } from "@measured/puck";
import React from "react";
import { RenderTextCarouselBlock, TextCarouselBlockProps } from "@prp/blocks";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";

const TextCarouselBlock: ComponentConfig<TextCarouselBlockProps> = {
  fields: {
    autoplay: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    forceStaticHeight: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    backgroundColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    items: {
      type: "array",
      arrayFields: {
        value: {
          type: "textarea",
        },
      },
      defaultItemProps: {
        value: "Carousel Item",
      },
    },
  },
  defaultProps: {
    items: [
      {
        value: "Carousel Item",
      },
    ],
  },
  render: (props: TextCarouselBlockProps) => {
    return <RenderTextCarouselBlock {...props} />;
  },
};

export default TextCarouselBlock;
