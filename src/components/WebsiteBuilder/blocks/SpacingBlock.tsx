import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import { RenderSpacingBlock } from "@prp/blocks";

export type SpacingProps = {
  background?: string;
  height?: number;
};

export const SpacingBlock: ComponentConfig<SpacingProps> = {
  fields: {
    background: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    height: {
      type: "number",
    },
  },
  defaultProps: {
    background: "#0000",
    height: 100,
  },
  render: ({ background, height }: SpacingProps) => (
    <RenderSpacingBlock background={background} height={height} />
  ),
};

export default SpacingBlock;
