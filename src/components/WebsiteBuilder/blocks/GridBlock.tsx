import { ComponentConfig, PuckContext } from "@measured/puck";
import React from "react";
import ProductListField from "~/components/WebsiteBuilder/fields/ProductListField";
import UploadImagePuckField from "~/components/WebsiteBuilder/fields/UploadImagePuckField";
import { ProductListValues } from "~/components/WebsiteBuilder/types/types";
import { RenderGridBlock } from "@prp/blocks";

export type GridBlockProps = {
  header: string;
  border?: boolean;
  displayPrice: boolean;
  displayCTA: boolean;
  limit: boolean;
  products: ProductListValues;
  image?: string;
};

export const GridBlock: ComponentConfig<GridBlockProps> = {
  fields: {
    header: {
      type: "text",
    },
    border: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    products: {
      type: "custom",
      render: ProductListField,
    },
    displayPrice: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    displayCTA: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    limit: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    image: {
      type: "custom",
      render: UploadImagePuckField,
    },
  },
  defaultProps: {
    header: "Header",
    products: {
      pullProductsFrom: "category",
      categoryId: 1,
    },
    border: false,
    displayPrice: false,
    displayCTA: false,
    limit: false,
  },
  render: ({
    border,
    displayCTA,
    displayPrice,
    limit,
    products,
    header,
    image,
    puck,
  }: GridBlockProps & { puck: PuckContext }) => {
    return (
      <RenderGridBlock
        config={{
          ...products,
          categoryId: products.categoryId ? products.categoryId : 0,
        }}
        border={border}
        displayCTA={displayCTA}
        displayPrice={displayPrice}
        header={header}
        image={image}
        limit={limit}
        editing={puck.isEditing}
      />
    );
  },
};

export default GridBlock;
