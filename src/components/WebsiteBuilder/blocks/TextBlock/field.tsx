import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import RichTextField from "~/components/WebsiteBuilder/fields/RichTextField";
import { RenderTextBlock } from "@prp/blocks";
import React from "react";
import { DEFAULT_THEME, Maily } from "~/lib/email/maily";
import { renderToString } from "react-dom/server";

export type TextProps = {
  background?: string;
  color?: string;
  text?: string;
  render: string;
  stroke?: string;
  strokeWidth?: number;
  fontFamily?: string;
};

const TextBlock: ComponentConfig<TextProps> = {
  fields: {
    text: {
      type: "custom",
      render: (props) => <RichTextField {...props} />,
    },
    render: {
      type: "text",
    },
    background: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    color: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    stroke: {
      type: "custom",
      render: ColorPickerPuck<PERSON>ield,
    },
    strokeWidth: {
      type: "number",
    },
    fontFamily: {
      type: "text",
    },
  },
  resolveFields: async (data, { fields }): Promise<any> => {
    const isJson =
      data.props.text?.startsWith("{") && data.props.text?.endsWith("}");
    let text = isJson ? JSON.parse(data.props.text ?? "{}") : data.props.text;
    if (isJson) {
      const m = new Maily(text);
      m.setTheme({
        ...DEFAULT_THEME,
        colors: {
          paragraph: data.props.color,
          heading: data.props.color,
        },
      });
      text = m.getNodes();

      data.props.render = renderToString(text);
    } else {
      data.props.render = text;
    }
    return {
      ...fields,
      render: undefined,
    };
  },

  defaultProps: {
    background: "#ffffff",
    render: "{}",
  },
  render: (props: TextProps) => {
    return (
      <RenderTextBlock
        background={props.background}
        color={props.color}
        text={props.render}
        stroke={props.stroke}
        strokeWidth={props.strokeWidth}
        fontFamily={props.fontFamily}
      />
    );
  },
};

export default TextBlock;
