import { ComponentConfig } from "@measured/puck";
import SizeInput, {
  getHeight,
  getWidth,
  SizeInputValues,
} from "~/components/WebsiteBuilder/fields/SizeInput";

export type EmbeddedProps = {
  source?: string;
  size?: SizeInputValues;
};

const EmbeddedBlock: ComponentConfig<EmbeddedProps> = {
  fields: {
    source: { type: "text" },
    size: {
      type: "custom",
      render: SizeInput,
    },
  },
  defaultProps: {
    size: {
      width: 900,
      height: 400,
      unit: "px",
    },
    source: "https://partyrentalplatform.com",
  },
  render: ({ source, size }: EmbeddedProps) => (
    <div
      style={{
        height: getHeight(size),
        width: getWidth(size),
      }}
    >
      <iframe className="w-full h-full" src={source} />
    </div>
  ),
};

export default EmbeddedBlock;
