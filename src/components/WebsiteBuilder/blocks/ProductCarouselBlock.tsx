import { ComponentConfig, PuckContext } from "@measured/puck";
import React from "react";
import ProductListField from "~/components/WebsiteBuilder/fields/ProductListField";
import { ProductListValues } from "~/components/WebsiteBuilder/types/types";
import { RenderProductCarouselBlock } from "@prp/blocks";

type ProductCarouselBreakpoint = {
  productsShownOnMobile: number;
  productsShownOnTablet: number;
  productsShownOnDesktop: number;
};

export type ProductCarouselBlockProps = {
  products: ProductListValues;
};

const ProductCarouselBlock: ComponentConfig<ProductCarouselBlockProps> = {
  fields: {
    products: {
      type: "custom",
      render: ProductListField,
    },
  },
  defaultProps: {
    products: {
      pullProductsFrom: "all",
    },
  },
  render: ({
    products,
    puck,
  }: ProductCarouselBlockProps & { puck: PuckContext }) => {
    return (
      <RenderProductCarouselBlock
        breakpoint={{
          productsShownOnMobile: 1,
          productsShownOnTablet: 2,
          productsShownOnDesktop: 4,
        }}
        editing={puck.isEditing}
        config={{
          categoryId: products.categoryId ?? 1,
          pullProductsFrom: products.pullProductsFrom,
        }}
      />
    );
  },
};

export default ProductCarouselBlock;
