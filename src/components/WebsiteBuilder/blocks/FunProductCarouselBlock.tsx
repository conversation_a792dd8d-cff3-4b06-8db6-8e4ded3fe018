import { ComponentConfig, PuckContext } from "@measured/puck";
import React from "react";
import ProductListField from "~/components/WebsiteBuilder/fields/ProductListField";
import {
  FunProductCarouselBlockProps,
  RenderFunProductCarouselBlock,
} from "@prp/blocks";
import ColorPickerPuckField from "../fields/ColorPickerPuckField";

export const FunProductCarouselBlock: ComponentConfig<
  FunProductCarouselBlockProps & { config?: any }
> = {
  fields: {
    title: {
      type: "object",
      objectFields: {
        text: {
          type: "text",
        },
        url: {
          type: "text",
        },
      },
    },
    config: {
      type: "custom",
      render: ProductListField,
    },
    cta: {
      type: "object",
      objectFields: {
        text: {
          type: "text",
        },
        textColor: {
          type: "custom",
          render: ColorPickerPuckField,
        },
        background: {
          type: "custom",
          render: ColorPickerPuckField,
        },
        url: {
          type: "text",
        },
      },
    },
    backgroundColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    shadowBackground: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    autoplay: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    title: {
      text: "Fun Products",
      url: "#",
    },
    cta: {
      text: "Shop Now",
      textColor: "#fff",
      background: "#FF5733",
      url: "#",
    },
    config: {
      pullProductsFrom: "category" as any,
      categoryId: 1,
    },
    backgroundColor: "#fd1414",
    shadowBackground: "#aaab00",
    autoplay: true,
  },
  render: (
    props: FunProductCarouselBlockProps & {
      puck: PuckContext;
    },
  ) => {
    return (
      <RenderFunProductCarouselBlock
        {...props}
        config={{
          ...props.config,
          categoryId: props.config.categoryId ? props.config.categoryId : 0,
        }}
      />
    );
  },
};

export default FunProductCarouselBlock;
