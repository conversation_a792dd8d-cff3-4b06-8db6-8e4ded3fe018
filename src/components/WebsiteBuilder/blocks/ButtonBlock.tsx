import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import { RenderButton, RenderButtonProps } from "@prp/blocks";
import React from "react";

/*
    background: string;
    color: string;
    text: string;
    fontSize: number;
    fontWeight: number;
    borderRadius: number;
    padding: string;
    link?: string;
 */
export const ButtonBlock: ComponentConfig<RenderButtonProps> = {
  fields: {
    background: {
      label: "Background Color",
      type: "custom",
      render: ColorPickerPuckField,
    },
    color: {
      label: "Text Color",
      type: "custom",
      render: ColorPickerPuckField,
    },
    text: {
      label: "Text",
      type: "text",
    },
    fontSize: {
      label: "Font Size",
      type: "number",
    },
    fontWeight: {
      label: "Font Weight",
      type: "number",
    },
    borderRadius: {
      label: "Border Radius",
      type: "number",
    },
    padding: {
      label: "Padding",
      type: "text",
    },
    link: {
      label: "Link",
      type: "text",
    },
  },
  defaultProps: {
    background: "#000",
    color: "#FFF",
    text: "Button",
    fontSize: 16,
    fontWeight: 400,
    borderRadius: 4,
    padding: "10px 20px",
    link: "",
  },
  render: (props: RenderButtonProps) => <RenderButton {...props} />,
};

export default ButtonBlock;
