import { ComponentConfig } from "@measured/puck";
import UploadImage<PERSON>uckField from "~/components/WebsiteBuilder/fields/UploadImagePuckField";
import { Button } from "~/components/ui/button";
import { RenderHeroBlock } from "@prp/blocks";

export type HeroProps = {
  title: string;
  description: string;
  align?: string;
  padding: string;
  image?: {
    mode?: "inline" | "background";
    url?: string;
  };
  buttons: {
    label: string;
    href: string;
    variant?: "primary" | "secondary";
    more?: { text: string }[];
  }[];
};

export const HeroBlock: ComponentConfig<HeroProps> = {
  fields: {
    title: { type: "text" },
    description: { type: "textarea" },
    buttons: {
      type: "array",
      min: 1,
      max: 4,
      getItemSummary: (item) => item.label || "Button",
      arrayFields: {
        label: { type: "text" },
        href: { type: "text" },
        variant: {
          type: "select",
          options: [
            { label: "primary", value: "primary" },
            { label: "secondary", value: "secondary" },
          ],
        },
      },
      defaultItemProps: {
        label: "Button",
        href: "#",
      },
    },
    align: {
      type: "radio",
      options: [
        { label: "left", value: "left" },
        { label: "center", value: "center" },
      ],
    },
    image: {
      type: "object",
      objectFields: {
        url: { type: "custom", render: UploadImagePuckField },
        mode: {
          type: "radio",
          options: [
            { label: "inline", value: "inline" },
            { label: "background", value: "background" },
          ],
        },
      },
    },
    padding: { type: "text" },
  },
  defaultProps: {
    title: "Hero",
    align: "left",
    description: "Description",
    buttons: [{ label: "Learn more", href: "#" }],
    padding: "64px",
  },
  render: ({ align, title, description, buttons, padding, image, puck }) => {
    return (
      <RenderHeroBlock
        align={align}
        title={title}
        description={description}
        buttons={buttons}
        padding={padding}
        image={image}
        editing={puck.isEditing}
      />
    );
  },
};
