import { ComponentConfig, DropZone } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import UploadImagePuckField from "../fields/UploadImagePuckField";

export type ContainerProps = {
  background: string;
  containerColor: string;
  containerRound?: string;
  horizontalPadding?: string;
  verticalPadding?: string;
  backgroundImage?: string;
};

export const ContainerBlock: ComponentConfig<ContainerProps> = {
  fields: {
    background: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    containerColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    containerRound: {
      type: "text",
    },
    horizontalPadding: {
      type: "text",
    },
    verticalPadding: {
      type: "text",
    },
    backgroundImage: {
      type: "custom",
      render: UploadImagePuckField,
    },
  },
  defaultProps: {
    background: "#ffffff",
    containerColor: "#ffffff",
  },
  render: (props: ContainerProps) => (
    <div
      style={{
        background: props.background,
      }}
      className={"container mx-auto md:px-20"}
    >
      <div
        style={{
          background: props.backgroundImage
            ? `url("${props.backgroundImage}") center top / cover no-repeat transparent`
            : props.containerColor,
          borderRadius: props.containerRound,
          paddingTop: props.verticalPadding ? props.verticalPadding : "20px",
          paddingBottom: props.verticalPadding ? props.verticalPadding : "20px",
          paddingLeft: props.horizontalPadding
            ? props.horizontalPadding
            : undefined,
          paddingRight: props.horizontalPadding
            ? props.horizontalPadding
            : undefined,
        }}
      >
        <DropZone
          style={{
            width: "100%",
            height: "100%",
          }}
          zone="container-provider"
        />
      </div>
    </div>
  ),
};

export default ContainerBlock;
