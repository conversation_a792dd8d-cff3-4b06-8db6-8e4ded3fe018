import { ComponentConfig, PuckContext } from "@measured/puck";
import React from "react";
import {
  CategoryCarouselBlockProps,
  RenderCategoryCarouselBlock,
} from "@prp/blocks";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";

export type CategoryCarouselProps = Omit<
  Omit<CategoryCarouselBlockProps, "categories">,
  "editing"
>;

const CategoryCarouselBlock: ComponentConfig<CategoryCarouselProps> = {
  fields: {
    textColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    backgroundColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    autoplay: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    backgroundColor: "#ffffff",
    autoplay: true,
    textColor: "#000000",
  },
  render: ({
    backgroundColor,
    autoplay,
    textColor,
  }: CategoryCarouselProps & { puck: PuckContext }) => {
    return (
      <RenderCategoryCarouselBlock
        editing={true}
        backgroundColor={backgroundColor}
        textColor={textColor}
        autoplay={autoplay}
      />
    );
  },
};

export default CategoryCarouselBlock;
