import { ComponentConfig } from "@measured/puck";
import { RenderAreasWeServe, RenderAreasWeServeProps } from "@prp/blocks";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";

export const AreasWeServeBlock: ComponentConfig<RenderAreasWeServeProps> = {
  fields: {
    title: {
      type: "text",
    },
    baseColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    description: { type: "textarea" },
    note: {
      type: "textarea",
    },
    locations: {
      type: "array",
      arrayFields: {
        name: { type: "text" },
        url: { type: "text" },
      },
      defaultItemProps: {
        name: "City",
        url: "#",
      },
    },
  },
  defaultProps: {
    title: "Areas we Serve",
    description: "Description",
    note: "Note: Some areas may not be available or have increased pricing.",
    baseColor: "#2f5cd6",
    locations: [
      {
        name: "City",
        url: "#",
      },
      {
        name: "City",
        url: "#",
      },
    ],
  },
  render: (props) => {
    return <RenderAreasWeServe {...props} />;
  },
};
