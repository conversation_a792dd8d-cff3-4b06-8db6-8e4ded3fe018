import { ComponentConfig } from "@measured/puck";
import RenderIconList from "@prp/blocks/dist/blocks/RenderLinkList";

export type LinkListProps = {
  links: {
    href: string;
    text: string;
  }[];
};

const ListLinkBlock: ComponentConfig<LinkListProps> = {
  fields: {
    links: {
      type: "array",
      arrayFields: {
        href: { type: "text" },
        text: { type: "text" },
      },
      defaultItemProps: {
        href: "/category/bounce-houses/",
        text: "Bounce Houses 🥳",
      },
    },
  },
  render: (props: LinkListProps) => {
    return <RenderIconList {...props} />;
  },
};

export default ListLinkBlock;
