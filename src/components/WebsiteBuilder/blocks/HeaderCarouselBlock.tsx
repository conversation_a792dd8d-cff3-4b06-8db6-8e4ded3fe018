import { ComponentConfig } from "@measured/puck";
import UploadImage<PERSON>uck<PERSON>ield from "~/components/WebsiteBuilder/fields/UploadImagePuckField";
import React from "react";
import {
  HeaderCarouselBlockProps,
  RenderHeaderCarouselBlock,
} from "@prp/blocks";

const HeaderCarouselBlock: ComponentConfig<HeaderCarouselBlockProps> = {
  fields: {
    horizontalPadding: {
      type: "text",
    },
    horizontalMargin: {
      type: "text",
    },
    verticalPadding: {
      type: "text",
    },
    verticalMargin: {
      type: "text",
    },
    maxHeight: {
      type: "text",
    },
    rounded: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    button: {
      type: "object",
      objectFields: {
        text: { type: "text" },
        textColor: {
          type: "custom",
          render: UploadImagePuckField,
        },
        backgroundColor: {
          type: "custom",
          render: UploadImagePuckField,
        },
        href: { type: "text" },
      },
    },
    renderText: {
      type: "object",
      objectFields: {
        header: { type: "text" },
        description: { type: "text" },
      },
    },
    autoplay: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    forceStaticHeight: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    items: {
      type: "array",
      arrayFields: {
        alt: { type: "text" },
        link: { type: "text" },
        image: {
          type: "custom",
          render: UploadImagePuckField,
        },
      },
      defaultItemProps: {
        alt: "Carousel Item",
        image:
          "https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/b2cca69a-3697-4d43-05e3-8447342e1000",
        link: "/",
      },
    },
  },
  defaultProps: {
    items: [
      {
        alt: "Carousel Item",
        link: "/",
        image:
          "https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/b2cca69a-3697-4d43-05e3-8447342e1000/w=1280",
      },
    ],
    autoplay: true,
    forceStaticHeight: false,
    maxHeight: undefined,
    horizontalPadding: undefined,
    horizontalMargin: undefined,
    verticalPadding: undefined,
    verticalMargin: undefined,
    rounded: false,
    button: undefined,
    renderText: undefined,
  },
  render: (props: HeaderCarouselBlockProps) => {
    return <RenderHeaderCarouselBlock {...props} />;
  },
};

export default HeaderCarouselBlock;
