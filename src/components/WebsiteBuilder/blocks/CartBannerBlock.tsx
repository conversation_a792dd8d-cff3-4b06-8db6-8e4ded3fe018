import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import CartBanner from "@prp/blocks/dist/blocks/RenderCartBanner";

export type CartBannerBlockProps = {
  textColor: string;
  buttonColor: string;
  promptDateSelection: boolean;
};

const CartBannerBlock: ComponentConfig<CartBannerBlockProps> = {
  fields: {
    textColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    buttonColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    promptDateSelection: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    textColor: "#FFF",
    buttonColor: "#96F1FF",
    promptDateSelection: true,
  },
  render: (props: CartBannerBlockProps) => <CartBanner {...props} />,
};

export default CartBannerBlock;
