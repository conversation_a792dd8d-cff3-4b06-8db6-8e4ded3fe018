import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import { TextAlignment } from "~/components/WebsiteBuilder/fields/TextAlignment";
import RenderHeadingTextBlock from "@prp/blocks/dist/blocks/RenderHeadingTextBlock";

export type HeadingTextProps = {
  background: string;
  color: string;
  text: string;
  fontSize: number;
  fontWeight: number;
  textAlignment: "left" | "center" | "right";
};

const HeadingTextBlock: ComponentConfig<HeadingTextProps> = {
  fields: {
    text: { type: "text" },
    background: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    color: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    textAlignment: TextAlignment,
    fontSize: { type: "number" },
    fontWeight: { type: "number" },
  },
  defaultProps: {
    text: "Header",
    background: "#fffffa",
    color: "#000000",
    textAlignment: "center",
    fontSize: 68,
    fontWeight: 600,
  },
  render: ({
    background,
    text,
    color,
    fontSize,
    fontWeight,
    textAlignment,
  }: HeadingTextProps) => {
    return (
      <RenderHeadingTextBlock
        background={background}
        text={text}
        color={color}
        fontSize={fontSize}
        fontWeight={fontWeight}
        textAlignment={textAlignment}
      />
    );
  },
};

export default HeadingTextBlock;
