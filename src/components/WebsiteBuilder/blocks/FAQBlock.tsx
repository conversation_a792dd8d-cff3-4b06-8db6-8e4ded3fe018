import { ComponentConfig } from "@measured/puck";
import React from "react";
import { RenderFAQBlock } from "@prp/blocks";

type FAQProps = {
  question: string;
  answer: string;
};

export type FAQBlockProps = {
  header: string;
  faqs: FAQProps[];
};

const FaqBlock: ComponentConfig<FAQBlockProps> = {
  fields: {
    header: {
      type: "text",
    },
    faqs: {
      type: "array",
      arrayFields: {
        question: { type: "text" },
        answer: { type: "text" },
      },
      defaultItemProps: {
        question: "What is your return Policy?",
        answer: "We have a 30 day return policy.",
      },
    },
  },
  defaultProps: {
    faqs: [
      {
        question: "What is your cancellation policy?",
        answer: "We have a 30 day cancellation policy.",
      },
      {
        question: "Do you offer gift cards?",
        answer: "Yes, we offer gift cards.",
      },
    ],
    header: "Frequently Asked Questions",
  },
  render: (props: FAQBlockProps) => {
    return <RenderFAQBlock {...props} />;
  },
};

export default FaqBlock;
