import { ComponentConfig } from "@measured/puck";
import SizeInput, {
  getHeight,
  getWidth,
  SizeInputValues,
} from "~/components/WebsiteBuilder/fields/SizeInput";
import UploadImagePuckField from "~/components/WebsiteBuilder/fields/UploadImagePuckField";
import { TextAlignment } from "~/components/WebsiteBuilder/fields/TextAlignment";
import { RenderImageBlock } from "@prp/blocks";

export type ImageProps = {
  alt?: string;
  size?: SizeInputValues;
  image?: string;
  alignment: "left" | "right" | "center";
};

const ImageBlock: ComponentConfig<ImageProps> = {
  fields: {
    alt: { type: "text" },
    size: {
      type: "custom",
      render: SizeInput,
    },
    alignment: TextAlignment,
    image: {
      type: "custom",
      render: UploadImagePuckField,
    },
  },
  defaultProps: {
    size: {
      width: 100,
      height: 100,
      unit: "px",
    },
    alignment: "center",
    alt: "image",
    image:
      "https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon",
  },
  render: ({ alt, size, ...props }: ImageProps) => (
    <RenderImageBlock
      {...props}
      alt={alt}
      width={getWidth(size)}
      height={getHeight(size)}
    />
  ),
};

export default ImageBlock;
