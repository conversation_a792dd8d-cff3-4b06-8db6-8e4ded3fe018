import { ComponentConfig, DropZone } from "@measured/puck";
import React from "react";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";

type Column = {
  title?: string;
  span?: number;
  background?: string;
};

export type PageColumnsBlockProps = {
  columns: Column[];
  gap: number;
  background?: string;
};

const PageColumnsBlock: ComponentConfig<PageColumnsBlockProps> = {
  fields: {
    columns: {
      type: "array",
      arrayFields: {
        title: { type: "text" },
        background: {
          type: "custom",
          label: "Background Color",
          render: ColorPickerPuckField,
        },
      },
      min: 2,
      max: 3,
      defaultItemProps: {
        title: "column",
      },
    },
    gap: { type: "number" },
    background: {
      type: "custom",
      label: "Background Color",
      render: ColorPickerPuckField,
    },
  },
  defaultProps: {
    gap: 16,
    columns: [
      { title: "column 1", span: 1 },
      { title: "column 2", span: 1 },
    ],
  },
  render: ({ columns, gap, background }: PageColumnsBlockProps) => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
        gap: gap,
        justifyItems: "center",
        backgroundColor: background,
      }}
    >
      {columns?.map((column: any, index: any) => (
        <div
          key={index}
          style={{ backgroundColor: column?.background }}
          className={"max-w-screen-sm w-full"}
        >
          <DropZone
            style={{ backgroundColor: column?.background }}
            zone={column?.title ?? index}
          />
        </div>
      ))}
    </div>
  ),
};

export default PageColumnsBlock;
