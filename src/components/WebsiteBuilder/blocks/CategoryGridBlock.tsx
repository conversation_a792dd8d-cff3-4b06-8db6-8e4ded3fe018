import { ComponentConfig, PuckContext } from "@measured/puck";
import React from "react";
import { RenderCategoryGrid } from "@prp/blocks";

export type CategoryGridBlockProps = {
  header: string;
  border?: boolean;
  limit: boolean;
};

export const CategoryGridBlock: ComponentConfig<CategoryGridBlockProps> = {
  fields: {
    header: {
      type: "text",
    },
    border: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    limit: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
  },
  defaultProps: {
    header: "Header",
    border: false,
    limit: false,
  },
  render: ({
    border,
    limit,
    header,
    puck,
  }: CategoryGridBlockProps & { puck: PuckContext }) => {
    return (
      <RenderCategoryGrid
        border={border}
        limit={limit}
        header={header}
        editing={puck.isEditing}
      />
    );
  },
};

export default CategoryGridBlock;
