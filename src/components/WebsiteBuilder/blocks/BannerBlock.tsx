import { ComponentConfig } from "@measured/puck";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";
import { BannerBlockProps, RenderBannerBlock } from "@prp/blocks";
import React from "react";

export type BannerBlockSettingsProps = Omit<BannerBlockProps, "products">;

export const BannerBlock: ComponentConfig<BannerBlockSettingsProps> = {
  fields: {
    textColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    search: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    bookNowButton: {
      type: "radio",
      options: [
        { label: "Yes", value: true },
        { label: "No", value: false },
      ],
    },
    color: {
      type: "custom",
      render: ColorPickerPuckField,
    },
  },
  defaultProps: {
    color: "#0000",
    textColor: "#0000",
    search: true,
    bookNowButton: true,
  },
  render: (props: BannerBlockSettingsProps) => <RenderBannerBlock {...props} />,
};

export default BannerBlock;
