import { ComponentConfig, PuckContext } from "@measured/puck";
import { BackgroundHeroProps, RenderBackgroundHeroBlock } from "@prp/blocks";
import UploadImagePuckField from "~/components/WebsiteBuilder/fields/UploadImagePuckField";
import ColorPickerPuckField from "~/components/WebsiteBuilder/fields/ColorPickerPuckField";

export const BackgroundHeroBlock: ComponentConfig<
  Omit<BackgroundHeroProps, "primaryCTA" | "secondaryCTA">
> = {
  fields: {
    title: {
      type: "object",
      objectFields: {
        text: { type: "text" },
        color: { type: "custom", render: ColorPickerPuckField },
        alignment: {
          type: "radio",
          options: [
            { label: "Left", value: "left" },
            { label: "Center", value: "center" },
            { label: "Right", value: "right" },
          ],
        },
      },
    },
    backgroundColor: {
      type: "custom",
      render: ColorPickerPuckField,
    },
    highlightedText: {
      type: "object",
      objectFields: {
        text: { type: "text" },
        highlightedTextColor: { type: "custom", render: ColorPickerPuckField },
      },
    },
    description: { type: "textarea" },
    buttons: {
      type: "array",
      arrayFields: {
        text: { type: "text" },
        href: { type: "text" },
        backgroundColor: { type: "custom", render: ColorPickerPuckField },
        textColor: {
          type: "custom",
          render: ColorPickerPuckField,
        },
      },
      defaultItemProps: {
        text: "Button",
        href: "#",
        color: "linear-gradient(90deg, #FFD700 0%, #FFA500 100%)",
      },
    },
    // primaryCTA: {
    //   type: "object",
    //   objectFields: {
    //     text: { type: "text" },
    //     href: { type: "text" },
    //     color: { type: "custom", render: ColorPickerPuckField },
    //   },
    // },
    // secondaryCTA: {
    //   type: "object",
    //   objectFields: {
    //     text: { type: "text" },
    //     href: { type: "text" },
    //   },
    // },
    backgroundImage: {
      type: "custom",
      render: UploadImagePuckField,
    },
  },
  defaultProps: {
    title: {
      text: "Your Ultimate Party",
      color: "#FFF",
      alignment: "center",
    },
    highlightedText: {
      text: "Rentals",
      highlightedTextColor: "linear-gradient(90deg, #FFD700 0%, #FFA500 100%)",
    },
    description: "Description",
    buttons: [
      {
        text: "Button",
        href: "#",
        backgroundColor: "linear-gradient(90deg, #FFD700 0%, #FFA500 100%)",
        textColor: "#FFF",
      },
      {
        text: "Button",
        href: "#",
        backgroundColor: "#000",
        textColor: "#FFF",
      },
    ],
    backgroundImage: "https://example.com/image.jpg",
  },
  render: ({
    title,
    highlightedText,
    description,
    primaryCTA,
    secondaryCTA,
    buttons,
    backgroundImage,
  }: BackgroundHeroProps & { puck: PuckContext }) => {
    return (
      <RenderBackgroundHeroBlock
        title={title}
        highlightedText={highlightedText}
        description={description}
        buttons={buttons}
        backgroundImage={backgroundImage}
      />
    );
  },
};
