import { ComponentConfig } from "@measured/puck";
import React from "react";
import { RenderReviewCarouselBlock } from "@prp/blocks";

type ReviewProps = {
  review: string;
  author: string;
  stars: number;
  postedAt: number;
};

export type ReviewCarouselBlockProps = {
  header: string;
  reviews: ReviewProps[];
  placeId?: string;
};

const ReviewCarouselBlock: ComponentConfig<ReviewCarouselBlockProps> = {
  fields: {
    header: {
      type: "text",
    },
    placeId: {
      label: "Google Place ID",
      type: "text",
    },
    reviews: {
      type: "array",
      arrayFields: {
        author: { type: "text" },
        review: { type: "text" },
        stars: {
          type: "select",
          options: [
            {
              label: "1",
              value: 1,
            },
            { label: "2", value: 2 },
            { label: "3", value: 3 },
            { label: "4", value: 4 },
            { label: "5", value: 5 },
          ],
        },
        postedAt: { type: "number" },
      },
      defaultItemProps: {
        author: "Author",
        review: "Review",
        stars: 5,
        postedAt: Date.now(),
      },
    },
  },
  defaultProps: {
    reviews: [
      {
        author: "Author",
        review: "Review",
        stars: 5,
        postedAt: Date.now(),
      },
    ],
    header: "Review Carousel",
  },
  render: (props: ReviewCarouselBlockProps) => (
    <RenderReviewCarouselBlock {...props} />
  ),
};

export default ReviewCarouselBlock;
