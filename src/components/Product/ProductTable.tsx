import React from "react";
import { cn } from "~/lib/utils";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { TrashIcon } from "lucide-react";
import Image from "next/image";
import { ProductOrderItem } from "~/server/product/types";
import { fullImageUrl } from "~/server/globalTypes";
import { getCurrencyString } from "~/server/lib/currency";

type ProductTableProps = {
  selectedProducts: ProductOrderItem[];
  onProductUpdate: (product: ProductOrderItem, index: number) => void;
  onSelectionUpdate: (products: ProductOrderItem[]) => void;
};

export const ProductTable: React.FC<ProductTableProps> = (props) => {
  return (
    <>
      {/* Desktop view - standard table */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead className="w-[100px]">Quantity</TableHead>
              <TableHead className="w-[200px]">Price Per Unit</TableHead>
              <TableHead className="text-right">Final Price</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {props.selectedProducts.map((product, index) => (
              <TableRow key={product.id}>
                <TableCell>
                  <div className="flex flex-row items-center space-x-1">
                    {product.productThumbnail && (
                      <div className="bg-background border-1 border-border">
                        <Image
                          src={fullImageUrl(product.productThumbnail)}
                          alt={product.name}
                          width={64}
                          height={64}
                        />
                      </div>
                    )}
                    <span>{product.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Input
                    value={product.quantity}
                    type="number"
                    className={cn({
                      "border-yellow-500": product.available < product.quantity,
                    })}
                    onChange={(event) => {
                      const value = event.target.valueAsNumber;
                      if (value < 1) {
                        return;
                      }
                      product.quantity = value;
                      props.onProductUpdate(product, index);
                    }}
                  />
                  {product.available < product.quantity && (
                    <span className="text-xs text-yellow-600">
                      Available: {product.available}
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  <Input
                    value={product.price}
                    type="number"
                    onChange={(event) => {
                      const value = event.target.valueAsNumber;
                      if (value < 0) {
                        return;
                      }
                      product.price = value;
                      props.onProductUpdate(product, index);
                    }}
                  />
                </TableCell>
                <TableCell className="text-right">
                  {`${product.price * product.quantity}`}
                </TableCell>
                <TableCell className="w-[50px]">
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => {
                      props.onSelectionUpdate(
                        props.selectedProducts.filter(
                          (p) => p.id !== product.id,
                        ),
                      );
                    }}
                  >
                    <span className="sr-only">Remove</span>
                    <TrashIcon className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile view - card-based layout */}
      <div className="block md:hidden">
        {props.selectedProducts.map((product, index) => (
          <div
            key={product.id}
            className="mb-4 p-3 border rounded-lg shadow-sm"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                {product.productThumbnail && (
                  <div className="bg-background border-1 border-border">
                    <Image
                      src={fullImageUrl(product.productThumbnail)}
                      alt={product.name}
                      width={48}
                      height={48}
                    />
                  </div>
                )}
                <span className="font-medium">{product.name}</span>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  props.onSelectionUpdate(
                    props.selectedProducts.filter((p) => p.id !== product.id),
                  );
                }}
              >
                <TrashIcon className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">
                  Quantity
                </label>
                <Input
                  value={product.quantity}
                  type="number"
                  className={cn("h-8", {
                    "border-yellow-500": product.available < product.quantity,
                  })}
                  onChange={(event) => {
                    const value = event.target.valueAsNumber;
                    if (value < 1) return;
                    product.quantity = value;
                    props.onProductUpdate(product, index);
                  }}
                />
                {product.available < product.quantity && (
                  <span className="text-xs text-yellow-600">
                    Available: {product.available}
                  </span>
                )}
              </div>

              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Price</label>
                <Input
                  value={product.price}
                  type="number"
                  className="h-8"
                  onChange={(event) => {
                    const value = event.target.valueAsNumber;
                    if (value < 0) return;
                    product.price = value;
                    props.onProductUpdate(product, index);
                  }}
                />
              </div>
            </div>

            <div className="mt-2 text-right font-sm">
              Total: {getCurrencyString(product.price * product.quantity)}
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default ProductTable;
