import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import Image from "next/image";
import { fullImageUrl } from "~/server/globalTypes";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { TrashIcon } from "lucide-react";
import React from "react";
import { ProductOrderItem } from "~/server/product/types";
import { getCurrencyString } from "~/server/lib/currency";

type ProductsDisplayCardProps = {
  editMode: boolean;
  selectedProducts: ProductOrderItem[];
  onSelectionUpdate?: (products: ProductOrderItem[]) => void;
  checkAvailable?: {
    startDate: Date;
    endDate: Date;
  };
  onProductUpdate?: (product: ProductOrderItem, index: number) => void;
};

const ProductsDisplayCard = (props: ProductsDisplayCardProps) => {
  if (props.selectedProducts.length === 0) {
    return null;
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead className="w-[100px]">Quantity</TableHead>
          <TableHead className="w-[200px]">Price Per Unit</TableHead>
          <TableHead className="text-right">Final Price</TableHead>
          {props.editMode && <TableHead className="w-[50px]"></TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {props.selectedProducts.map((product, index) => {
          return (
            <TableRow key={product.id}>
              <TableCell>
                <div className={"flex flex-row items-center space-x-1"}>
                  {product.productThumbnail && (
                    <div
                      className={
                        "bg-background border-1 border-border size-8 md:size-20 rounded-md overflow-hidden"
                      }
                    >
                      <Image
                        src={fullImageUrl(product.productThumbnail)}
                        alt={"Product Name"}
                        width={64}
                        height={64}
                      />
                    </div>
                  )}
                  <span>{product.name}</span>
                </div>
              </TableCell>
              <TableCell>
                {props.editMode ? (
                  <Input
                    value={product.quantity}
                    type={"number"}
                    onChange={(event) => {
                      const value = event.target.valueAsNumber;
                      if (value < 1) {
                        return;
                      }
                      product.quantity = value;
                      if (props.onProductUpdate !== undefined) {
                        props.onProductUpdate(product, index);
                      }
                    }}
                  />
                ) : (
                  <span>{product.quantity}</span>
                )}
              </TableCell>
              <TableCell>
                {props.editMode ? (
                  <Input
                    value={product.price}
                    type={"number"}
                    onChange={(event) => {
                      const value = event.target.valueAsNumber;
                      if (value < 0) {
                        return;
                      }
                      product.price = value;
                      if (props.onProductUpdate !== undefined) {
                        props.onProductUpdate(product, index);
                      }
                    }}
                  />
                ) : (
                  <span>{product.price}</span>
                )}
              </TableCell>
              <TableCell className="text-right">
                {getCurrencyString(product.price * product.quantity)}
              </TableCell>
              {props.editMode && (
                <TableCell className="w-[50px]">
                  <Button
                    size={"icon"}
                    variant={"ghost"}
                    onClick={() => {
                      if (props.onSelectionUpdate !== undefined) {
                        props.onSelectionUpdate(
                          props.selectedProducts.filter(
                            (p) => p.id !== product.id,
                          ),
                        );
                      }
                    }}
                  >
                    <span className={"sr-only"}>Remove</span>
                    <TrashIcon className={"w-4 h-4"} />
                  </Button>
                </TableCell>
              )}
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};

export default ProductsDisplayCard;
