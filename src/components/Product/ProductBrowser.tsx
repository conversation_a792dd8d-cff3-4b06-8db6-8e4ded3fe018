import ProductSearch from "~/components/Product/ProductSearch";
import FormCard from "~/components/FormLayout/FormCard";
import React from "react";
import { ProductOrderItem } from "~/server/product/types";
import { TooltipProvider } from "~/components/ui/tooltip";
import ProductTable from "~/components/Product/ProductTable";

type ProductBrowserProps = {
  selectedProducts: ProductOrderItem[];
  onSelectionUpdate: (products: ProductOrderItem[]) => void;
  startDate?: Date;
  endDate?: Date;
  onProductUpdate: (product: ProductOrderItem, index: number) => void;
};

const ProductBrowser = (props: ProductBrowserProps) => {
  return (
    <FormCard className={"flex flex-col space-y-2"}>
      <ProductSearch
        onSubmit={(products) => {
          props.onSelectionUpdate(products ?? []);
        }}
        selectedProducts={props.selectedProducts}
        startDate={props.startDate}
        endDate={props.endDate}
      />
      <TooltipProvider>
        {props.selectedProducts.length > 0 && (
          <ProductTable
            selectedProducts={props.selectedProducts}
            onProductUpdate={props.onProductUpdate}
            onSelectionUpdate={props.onSelectionUpdate}
          />
        )}
      </TooltipProvider>
    </FormCard>
  );
};

export default ProductBrowser;
