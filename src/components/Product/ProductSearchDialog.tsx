import Image from "next/image";
import React, { use<PERSON>allback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { ProductOrderItem } from "~/server/product/types";
import {
  Modal,
  ModalBody,
  ModalClose,
  ModalContent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  ModalTitle,
} from "~/components/ui/modal";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Separator } from "~/components/ui/separator";
import { Checkbox } from "~/components/ui/checkbox";
import { getCurrencyString } from "~/server/lib/currency";
import { fullImageUrl } from "~/server/globalTypes";
import { ScrollArea } from "~/components/ui/scroll-area";
import { CardTitle } from "~/components/ui/card";
import { useFilteredProducts } from "~/query/product";
import { cn } from "~/lib/utils";
import { Skeleton } from "~/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { ArrowDownUp } from "lucide-react";

export type ProductSearchProps = {
  onSubmit: (item: ProductOrderItem[] | undefined) => void;
  excludeProducts?: number[];
  selectedProducts?: ProductOrderItem[];
  startDate?: Date | undefined;
  endDate?: Date | undefined;
  open: boolean;
  setOpen: (open: boolean) => void;
};

enum SortOptions {
  "Price ($$ -> $)" = "price_asc",
  "Price ($ -> $$)" = "price_desc",
  Name = "name",
  Available = "available",
}

const ProductSearchDialog = (props: ProductSearchProps) => {
  const [searchTerm, setSearchTerm] = useState<string | undefined>(undefined);
  const { data, isPending } = useFilteredProducts({
    excludedIds: props.excludeProducts,
    search: searchTerm,
    limit: 100,
    startTime: props.startDate,
    endTime: props.endDate,
  });
  const [sortBy, setSortBy] = useState<SortOptions>(SortOptions.Name);
  const [selectedItems, setSelectedItems] = React.useState<ProductOrderItem[]>(
    props.selectedProducts ?? [],
  );

  const sortedData = useMemo(() => {
    if (!data) return [];
    return [...data].sort((a, b) => {
      switch (sortBy) {
        case SortOptions["Price ($$ -> $)"]:
          return b.price - a.price;
        case SortOptions["Price ($ -> $$)"]:
          return a.price - b.price;
        case SortOptions.Available:
          return b.available - a.available;
        case SortOptions.Name:
        default:
          return a.name.localeCompare(b.name);
      }
    });
  }, [data, sortBy]);

  useEffect(() => {
    setSelectedItems(props.selectedProducts ?? []);
  }, [props.selectedProducts]);

  const searchProducts = async (searchTerm: string | undefined) => {
    setSearchTerm(searchTerm ?? "");
  };

  const search = useCallback(_.debounce(searchProducts, 500), []);

  return (
    <Modal
      open={props.open}
      onOpenChange={(open) => {
        props.setOpen(open);
      }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Search Products</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col space-y-3 flex-1">
            <div className={"flex flex-row gap-2"}>
              <Input
                onChange={(value) => {
                  search(value.target.value);
                }}
                placeholder={"Search for products"}
              />
              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value as SortOptions);
                }}
              >
                <SelectTrigger className="w-[30px] min-w-[30%] md:min-w-[1%]">
                  <ArrowDownUp className={"w-4 h-4"} />
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(SortOptions).map(([key, option]) => (
                    <SelectItem key={option} value={option}>
                      {key}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Separator orientation={"horizontal"} />
            {isPending ? (
              <div className={cn("animate-pulse space-y-2 h-56 px-3")}>
                <Skeleton className={"h-4 w-3/4 mb-2"} />
                <Skeleton className={"h-4 w-1/2 mb-2"} />
                <Skeleton className={"h-4 w-full mb-2"} />
                <Skeleton className={"h-4 w-5/6 mb-2"} />
              </div>
            ) : (
              <ScrollArea className={"h-[55vh] px-3 pr-4"}>
                {sortedData.map((item) => {
                  return (
                    <div
                      key={item.id}
                      className="flex items-center space-x-2 py-4 border-b last:border-b-0"
                      onSelect={() => {
                        if (selectedItems.some((i) => i.id === item.id)) {
                          setSelectedItems((prev) =>
                            prev.filter((i) => i.id !== item.id),
                          );
                        } else {
                          setSelectedItems((prev) => [...prev, item]);
                        }
                      }}
                    >
                      <Checkbox
                        checked={selectedItems.some((i) => i.id === item.id)}
                        onCheckedChange={(value) => {
                          if (value) {
                            setSelectedItems((prev) => [...prev, item]);
                          } else {
                            setSelectedItems((prev) =>
                              prev.filter((i) => i.id !== item.id),
                            );
                          }
                        }}
                        aria-label="Select row"
                      />
                      {item.productThumbnail && (
                        <Image
                          src={fullImageUrl(item.productThumbnail)}
                          className="w-12 h-12 object-cover object-center rounded"
                          alt={item.name}
                          width={64}
                          height={64}
                        />
                      )}
                      <div className="flex-1 space-y-1">
                        <CardTitle className="text-sm font-medium">
                          {item.name}
                        </CardTitle>
                        <p
                          className={cn("text-sm text-muted-foreground", {
                            "text-red-500": item.available === 0,
                          })}
                        >
                          {`${item.available} Available`}
                        </p>
                      </div>
                      <span className="font-medium">
                        {getCurrencyString(item.price)}
                      </span>
                    </div>
                  );
                })}
              </ScrollArea>
            )}
          </div>
        </ModalBody>
        <ModalFooter className="bg-opacity-40">
          <ModalClose asChild>
            <Button
              variant="primary"
              disabled={selectedItems.length === 0}
              onClick={() => {
                props.onSubmit(selectedItems);
              }}
            >
              Add
            </Button>
          </ModalClose>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ProductSearchDialog;
