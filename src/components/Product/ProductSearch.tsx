import React from "react";
import ProductSearchDialog, {
  ProductSearchProps,
} from "~/components/Product/ProductSearchDialog";
import { Input } from "~/components/ui/input";
import FormItem from "~/components/FormLayout/FormItem";

const ProductSearch = (
  props: Omit<Omit<ProductSearchProps, "open">, "setOpen">,
) => {
  const [open, setOpen] = React.useState(false);
  return (
    <div>
      <FormItem
        label={"Products"}
        name={"products"}
        render={({ field }) => {
          return (
            <Input
              placeholder={"Search for products"}
              onClick={() => {
                setOpen(true);
              }}
            />
          );
        }}
      />

      <ProductSearchDialog
        open={open}
        setOpen={setOpen}
        startDate={props.startDate}
        endDate={props.endDate}
        {...props}
      />
    </div>
  );
};

export default ProductSearch;
