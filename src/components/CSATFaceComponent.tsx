import { Column, Img, <PERSON>, <PERSON>, <PERSON> } from "@react-email/components";

type CSATFaceComponentProps = {
  link: string;
};

const CSATFaceComponent = ({ link }: CSATFaceComponentProps) => {
  return (
    <>
      <Section>
        <Row>
          <Column className={"w-1/4 pr-[8px]"}>
            <Link href={`${link}?feedback=sad`} target={"_blank"}>
              <Img
                alt="Bad Experience Feedback"
                className="w-full h-auto rounded-md object-cover"
                height={192}
                src="https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/18e6f77b-af92-4924-fb91-8ec0fa246100/512"
              />
            </Link>
          </Column>
          <Column className={"w-1/4 px-[4px]"}>
            <Link href={`${link}?feedback=neutral`} target={"_blank"}>
              <Img
                alt="Neutral Experience Feedback"
                className="w-full h-auto  rounded-md object-cover"
                height={192}
                src="https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/cd36ae1d-a0ff-4649-08a6-e95936657d00/512"
              />
            </Link>
          </Column>
          <Column className={"w-1/4 pl-[8px]"}>
            <Link href={`${link}?feedback=happy`} target={"_blank"}>
              <Img
                alt="Good Experience Feedback"
                className="w-full h-auto rounded-md object-cover"
                height={192}
                src="https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/38321eee-bfa6-478a-a3d3-04a3713cb300/512"
              />
            </Link>
          </Column>
        </Row>
      </Section>
    </>
  );
};

export default CSATFaceComponent;
