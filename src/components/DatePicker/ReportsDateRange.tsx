import {
  endOfDay,
  endOfMonth,
  startOfDay,
  startOfMonth,
  subDays,
  subMonths,
} from "date-fns";
import DateRangePicker from "~/components/DatePicker/DateRangePicker";
import React, { useEffect, useState } from "react";
import { DateRange } from "react-day-picker";

type ReportsDateRangeProps = {
  startDate?: Date;
  endDate: Date;
  onChange: (start: Date, end: Date) => void;
};

const ReportsDateRange = (props: ReportsDateRangeProps) => {
  const [selectedDate, setSelected] = useState<DateRange | undefined>({
    from: props?.startDate
      ? props.startDate
      : subDays(startOfDay(props.endDate), 30),
    to: props.endDate,
  });

  useEffect(() => {
    setSelectedDate({
      from: props?.startDate
        ? props.startDate
        : subDays(startOfDay(props.endDate), 30),
      to: props.endDate,
    });
  }, []);

  const setSelectedDate = (range: DateRange | undefined) => {
    setSelected(range);
    if (!range) return;

    if (range?.from && range?.to) {
      props.onChange(startOfDay(range.from), endOfDay(range.to));
    }
  };

  const startOfToday = startOfDay(new Date());

  return (
    <DateRangePicker
      onChange={setSelectedDate}
      date={selectedDate}
      presets={[
        {
          label: "Today",
          value: {
            from: startOfDay(new Date()),
            to: endOfDay(new Date()),
          },
        },
        {
          label: "Yesterday",
          value: {
            from: subDays(startOfDay(new Date()), 1),
            to: subDays(endOfDay(new Date()), 1),
          },
        },
        {
          label: "This week",
          value: {
            from: subDays(startOfToday, 7),
            to: startOfToday,
          },
        },
        {
          label: "This month",
          value: {
            from: startOfMonth(startOfToday),
            to: endOfMonth(startOfToday),
          },
        },
        {
          label: "Last Month",
          value: {
            from: startOfMonth(subMonths(startOfToday, 1)),
            to: endOfMonth(subMonths(startOfToday, 1)),
          },
        },
        {
          label: "Past 60 Days",
          value: {
            from: subDays(startOfToday, 60),
            to: startOfToday,
          },
        },
        {
          label: "This Year",
          value: {
            from: startOfMonth(new Date(new Date().getFullYear(), 0, 1)),
            to: endOfMonth(new Date(new Date().getFullYear(), 11, 31)),
          },
        },
        {
          label: "Last Year",
          value: {
            from: startOfMonth(new Date(new Date().getFullYear() - 1, 0, 1)),
            to: endOfMonth(new Date(new Date().getFullYear() - 1, 11, 31)),
          },
        },
      ]}
    />
  );
};

export default ReportsDateRange;
