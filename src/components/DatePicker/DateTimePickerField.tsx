import FormItem from "~/components/FormLayout/FormItem";
import React from "react";
import DateTimePicker from "~/components/DatePicker/DateTimePicker";
import { FormControl } from "~/components/ui/form";

type DateTimePickerProps = {
  label: string;
  name: string;
  presets?: Array<{ label: string; value: Date }>;
  onChange?: (date: Date | undefined) => void;
};

const DateTimePickerField = (props: DateTimePickerProps) => {
  return (
    <FormItem
      label={props.label}
      removeFormControl={true}
      name={props.name}
      render={({ field }) => {
        const onChange = (date: Date | undefined) => {
          field.onChange(date);
          if (props.onChange) {
            props.onChange(date);
          }
        };
        return (
          <FormControl>
            <DateTimePicker
              onChange={onChange}
              date={field.value}
              presets={props.presets}
            />
          </FormControl>
        );
      }}
    />
  );
};

export default DateTimePickerField;
