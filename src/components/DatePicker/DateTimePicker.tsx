import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { FormControl } from "~/components/ui/form";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "~/components/ui/calendar";
import TimePickerField from "~/components/DatePicker/TimePickerField";
import React from "react";

type DateTimePicker = {
  presets?: Array<{ label: string; value: Date }>;
  onChange: (date: Date | undefined) => void;
  date: Date | undefined;
};

const DateTimePicker = (props: DateTimePicker) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant={"outline"}
            className={cn(
              "w-[240px] pl-3 text-left font-normal",
              !props.date && "text-muted-foreground",
            )}
          >
            {props.date ? format(props.date, "PPP") : <span>Pick a date</span>}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" side={"bottom"}>
        <div className={"flex flex-row"}>
          <ol className={"hidden md:block p-3 border-r border-border"}>
            {props.presets?.map((preset, index) => {
              return (
                <li key={`preset-${index}`}>
                  <Button
                    key={preset.label}
                    variant="ghost"
                    onClick={() => props.onChange(preset.value)}
                    className="w-fit justify-start"
                  >
                    {preset.label}
                  </Button>
                </li>
              );
            })}
          </ol>
          <div className={"flex flex-col"}>
            <Calendar
              mode="single"
              selected={props.date}
              onSelect={props.onChange}
              showOutsideDays={false}
              initialFocus
            />
            <div className="p-3 border-t border-border">
              <TimePickerField
                date={props.date}
                setDate={props.onChange}
                showAmPm={true}
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DateTimePicker;
