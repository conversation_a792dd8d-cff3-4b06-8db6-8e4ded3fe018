import React from "react";
import { Label } from "~/components/ui/label";
import { TimePickerInput } from "~/components/DatePicker/time-picker-input";
import { Clock } from "lucide-react";
import { Period } from "~/server/lib/time";
import { TimePeriodSelect } from "~/components/DatePicker/period-select";

type TimePickerFieldProps = {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  showSeconds?: boolean;
  showIcon?: boolean;
  showAmPm?: boolean;
};

const TimePickerField = ({ date, setDate, ...props }: TimePickerFieldProps) => {
  const [period, setPeriod] = React.useState<Period>(
    (date?.getHours() || 0) > 12 ? "PM" : "AM",
  );
  const periodRef = React.useRef<HTMLButtonElement>(null);

  const minuteRef = React.useRef<HTMLInputElement>(null);
  const hourRef = React.useRef<HTMLInputElement>(null);
  const secondRef = React.useRef<HTMLInputElement>(null);
  return (
    <div className="flex items-end gap-2">
      <div className="grid gap-1 text-center">
        <Label htmlFor="hours" className="text-xs">
          Hours
        </Label>
        <TimePickerInput
          picker={props.showAmPm ? "12hours" : "hours"}
          period={props.showAmPm ? period : undefined}
          date={date}
          setDate={setDate}
          ref={hourRef}
          onRightFocus={() => minuteRef.current?.focus()}
        />
      </div>
      <div className="grid gap-1 text-center">
        <Label htmlFor="minutes" className="text-xs">
          Minutes
        </Label>
        <TimePickerInput
          picker="minutes"
          date={date}
          setDate={setDate}
          ref={minuteRef}
          onLeftFocus={() => hourRef.current?.focus()}
          onRightFocus={() => {
            if (props.showSeconds) {
              secondRef.current?.focus();
            }
          }}
        />
      </div>
      {props.showSeconds && (
        <div className="grid gap-1 text-center">
          <Label htmlFor="seconds" className="text-xs">
            Seconds
          </Label>
          <TimePickerInput
            picker="seconds"
            date={date}
            setDate={setDate}
            ref={secondRef}
            onLeftFocus={() => minuteRef.current?.focus()}
          />
        </div>
      )}
      {props.showAmPm && (
        <div className="grid gap-1 text-center">
          <Label htmlFor="period" className="text-xs">
            Period
          </Label>
          <TimePeriodSelect
            period={period}
            setPeriod={setPeriod}
            date={date}
            setDate={setDate}
            ref={periodRef}
            onLeftFocus={() => secondRef.current?.focus()}
          />
        </div>
      )}

      {props.showIcon && (
        <div className="flex h-10 items-center">
          <Clock className="ml-2 h-4 w-4" />
          <span>{date?.getHours() && date?.getHours() > 11 ? "PM" : "AM"}</span>
        </div>
      )}
    </div>
  );
};

export default TimePickerField;
