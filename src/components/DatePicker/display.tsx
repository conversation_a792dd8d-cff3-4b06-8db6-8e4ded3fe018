import { But<PERSON> } from "~/components/ui/button";
import { CalendarIcon } from "lucide-react";
import { formatHourRange, formatTimeRange } from "~/server/lib/time";
import { Calendar } from "~/components/ui/calendar";
import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

type DisplayTimeRangeProps = {
  startTime: Date | string | number;
  endTime: Date | string | number | null;
};

const DisplayTimeRange = ({ startTime, endTime }: DisplayTimeRangeProps) => {
  return (
    <div className="flex space-x-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={
              "justify-start text-left font-normal text-muted-foreground max-w-full"
            }
          >
            <CalendarIcon className="mr-2 h-4 w-4" />

            <p className={"truncate text-sm"}>
              {formatTimeRange(
                new Date(startTime),
                endTime ? new Date(endTime) : null,
              )}
            </p>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="range"
            disableNavigation={true}
            interactive={false}
            footer={
              <div>
                <p className={"text-md font-semibold"}>
                  {formatHourRange(
                    new Date(startTime),
                    endTime ? new Date(endTime) : null,
                  )}
                </p>
                <span className={"text-xs text-muted-foreground"}>
                  (Non Interactive)
                </span>
              </div>
            }
            defaultMonth={new Date(startTime)}
            onSelect={() => {
              // nothing
            }}
            selected={{
              from: new Date(startTime),
              to: endTime ? new Date(endTime) : undefined,
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DisplayTimeRange;
