import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { format, isEqual } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "~/components/ui/calendar";
import React, { useMemo } from "react";
import { DateRange } from "react-day-picker";

type DateRangePickerProps = {
  presets?: Array<{ label: string; value: DateRange }>;
  onChange: (range: DateRange | undefined) => void;
  date: DateRange | undefined;
};

const DateRangePicker = (props: DateRangePickerProps) => {
  const dateLabel = useMemo(() => {
    if (!props.date?.from) {
      return "Pick a date";
    }

    const presets = props.presets?.find(
      (preset) =>
        isEqual(
          preset?.value?.from || new Date(),
          props.date?.from || new Date(),
        ) &&
        isEqual(preset?.value?.to || new Date(), props.date?.to || new Date()),
    );
    if (presets) {
      return presets.label;
    }

    if (props.date.from && props.date.to) {
      return `${format(props.date.from, "LLL dd")}-${format(
        props.date.to,
        "LLL dd, y",
      )}`;
    }

    return format(props.date.from, "LLL dd, y");
  }, [props.date]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          size={"md"}
          className={cn(
            "font-normal gap-2 w-fit",
            !props.date && "text-muted-foreground",
          )}
        >
          <CalendarIcon className="mr-auto h-4 w-4 opacity-50" />
          {dateLabel}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" side={"bottom"}>
        <div className={"flex flex-row"}>
          <ol className={"hidden md:block p-3 border-r border-border"}>
            {props.presets?.map((preset, index) => {
              return (
                <li key={`preset-${index}`}>
                  <Button
                    key={preset.label}
                    variant="ghost"
                    onClick={() => props.onChange(preset.value)}
                    className="w-fit justify-start"
                  >
                    {preset.label}
                  </Button>
                </li>
              );
            })}
          </ol>
          <div className={"flex flex-col"}>
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={props.date?.from}
              selected={props.date}
              onSelect={props.onChange}
              numberOfMonths={1}
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DateRangePicker;
