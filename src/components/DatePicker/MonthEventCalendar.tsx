import { cn } from "~/lib/utils";
import { buttonVariants } from "~/components/ui/button";
import { DayPicker } from "react-day-picker";
import * as React from "react";
import { CalendarProps } from "~/components/ui/calendar";

const MonthEventCalendar = ({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) => {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(className, "w-full m-0 p-0")}
      classNames={{
        months: "w-full flex flex-col margin-0 padding-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-semibold",
        nav: "space-x-1 flex flex-row items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "flex-start h-8 w-8 bg-transparent p-0 opacity-50 hover:opacity-100",
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute left-12",
        table: "h-full w-full border-separate",
        head_row: "flex",
        head_cell:
          "w-full text-muted-foreground font-normal border-[1px] border-border ",
        row: "flex w-full h-full rounded-lg",
        cell: "border-[1px] border-border  w-full h-full border-border space-y-1 text-md relative aria-selected:opacity-100 [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn("cursor-default h-20 w-full p-1 font-normal justify-end flex "),
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary/10 border text-accent-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside:
          "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      {...props}
    />
  );
};

export default MonthEventCalendar;
