import { useEffect, useState } from "react";
import { AlertCircle, X } from "lucide-react";
import { differenceInDays } from "date-fns";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";

type BannerMessage = {
  message: string;
  daysRemaining: number;
  bannerColor: string;
  removable: boolean;
};

export function TrialBanner() {
  const { data, status } = useSession();
  const [message, setMessage] = useState<BannerMessage | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const router = useRouter();

  useEffect(() => {
    console.log("TrialBanner useEffect", data?.user);
    if (data?.user?.trialEndDate) {
      const trialEndDate = new Date(data?.user?.trialEndDate);
      const today = new Date();
      const days = differenceInDays(trialEndDate, today);

      switch (days) {
        case 0:
          setMessage({
            message: "Your free trial ends today.",
            daysRemaining: days,
            bannerColor: "bg-red-500",
            removable: false,
          });
          break;
        case 1:
          setMessage({
            message: "Your free trial ends tomorrow.",
            daysRemaining: days,
            bannerColor: "bg-yellow-500",
            removable: true,
          });
          break;
        default:
          if (days > 1 && days <= 14) {
            setMessage({
              message: `Your free trial ends in ${days} days.`,
              daysRemaining: days,
              bannerColor: "bg-blue-500",
              removable: true,
            });
          } else if (days < 0) {
            setMessage({
              message: "Your free trial has ended.",
              daysRemaining: days,
              bannerColor: "bg-red-500",
              removable: false,
            });
          }
          setShowBanner(true);
          break;
      }
    } else {
      setShowBanner(false);
    }
  }, [data?.user]);

  if (status !== "authenticated" || !showBanner || message === null) {
    return null;
  }

  const handleAddPayment = () => {
    router.push("/account?tab=billing");
  };

  return (
    <div
      className={cn(
        "sticky top-0 left-0 right-0 z-50 w-full text-white shadow-md",
        {
          [message.bannerColor]: message,
          hidden: !showBanner,
          "bg-opacity-90": message?.removable,
        },
      )}
    >
      <div className="max-w-7xl mx-auto py-2 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between flex-wrap">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-white mr-2" />
            <p className="text-sm font-medium">
              <span className="font-bold">{message.message}</span>
            </p>
          </div>
          <div className="flex items-center justify-center gap-2">
            <Button
              variant={"secondary"}
              onClick={handleAddPayment}
              size={"xs"}
            >
              Add Payment Method
            </Button>
            {message.removable && (
              <Button
                variant={"ghost"}
                size={"icon"}
                onClick={() => setShowBanner(false)}
                aria-label="Dismiss"
              >
                <span className="text-xs">
                  <X className={"w-4 h-4 text-muted-foreground"} />
                </span>
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
