import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "~/components/ui/table";

type ItemListProps<T> = {
  items: ColumnPair<T>[];
  value: T;
};

export type ColumnPair<T> = {
  name: string;
  render: (item: T) => React.ReactNode;
};

const SidewaysItemList = <T extends object>({
  items,
  value,
}: ItemListProps<T>) => {
  return (
    <Table>
      <TableBody>
        {items.map((column, index) => {
          return (
            <TableRow key={`row-${index}-side`}>
              <TableHead key={`header-${column.name}`}>{column.name}</TableHead>
              <TableCell key={`data-${column.name}`}>
                {column.render(value)}
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};

export default SidewaysItemList;
