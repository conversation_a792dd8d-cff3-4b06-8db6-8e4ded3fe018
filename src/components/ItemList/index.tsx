import React from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";

type ItemListProps<T> = {
  items: T[];
  columns: Array<Column<T>>;
  onClick?: (item: T) => void;
};

export type Column<T> = {
  name: string;
  render: (item: T) => React.ReactNode;
};

const ItemList = <T extends object>({
  items,
  columns,
  onClick,
}: ItemListProps<T>) => {
  return (
    <Table>
      <TableCaption>Item List</TableCaption>

      <TableHeader>
        <TableRow>
          {columns.map((column) => (
            <TableHead key={`header-${column.name}`}>{column.name}</TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item, index) => (
          <TableRow
            key={`row-${index}`}
            className={onClick && "hover:bg-gray-100 cursor-pointer"}
            onClick={() => {
              onClick && onClick(item);
            }}
          >
            {columns.map((column, index) => (
              <TableCell key={`col-${index}`}>{column.render(item)}</TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default ItemList;
