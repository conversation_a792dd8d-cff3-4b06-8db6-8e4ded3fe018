import React, { Fragment, useCallback, useRef, useState } from "react";
import { ImageUploadSimpleType } from "~/components/image/image";
import {
  Dialog,
  DialogClose,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Trigger,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Upload } from "lucide-react";
import { Checkbox } from "~/components/ui/checkbox";
import { LoadingSpinner } from "~/pages/_app";
import Image from "next/image";
import { IMAGE_KEY, useImages } from "~/query/images";
import { useQueryClient } from "@tanstack/react-query";
import Skeleton from "react-loading-skeleton";

type ImageGalleryProps = {
  onSelection: (id: string, url: string) => void;
  children?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
};

const ImageThumbnail = ({
  src,
  fileName,
  fileType,
  selected,
  onCheckChange,
}: {
  src: string | undefined;
  fileName: string | undefined;
  fileType: string | undefined;
  selected: boolean;
  onCheckChange: () => void;
}) => (
  <li
    className={
      "hover:bg-accent/80 hover:border-accent hover:text-accent-foreground cursor-pointer rounded-md"
    }
    onClick={onCheckChange}
  >
    <div className={"relative"}>
      <div
        className={
          "flex items-center justify-center border bg-border/15 rounded-md p-2"
        }
      >
        {src ? (
          <Image
            src={
              src ||
              "https://raw.githubusercontent.com/magicpatterns/catalog/main/public/placeholder.png"
            }
            className={"aspect-square object-center object-contain w-32 h-32"}
            alt={fileName || "Image"}
            width={150}
            height={150}
          />
        ) : (
          <Skeleton className={"aspect-square w-32 h-32"} />
        )}
      </div>
      <Checkbox
        className="absolute top-1 left-1"
        checked={selected}
        onCheckedChange={onCheckChange}
      />
    </div>
    <div className="text-center mt-2">
      <p className="text-sm truncate">{fileName || <Skeleton />}</p>
      <p className="text-xs text-gray-500">{fileType || <Skeleton />}</p>
    </div>
  </li>
);
const ImageSelectionDialog = (props: ImageGalleryProps) => {
  const { data, fetchNextPage, hasNextPage, isLoading, isFetching } = useImages(
    props.open ?? true,
  );
  const queryClient = useQueryClient();
  const [selection, setSelection] = useState<ImageUploadSimpleType | undefined>(
    undefined,
  );
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const observer = useRef<IntersectionObserver>();

  const lastElementRef = useCallback(
    (node: HTMLDivElement) => {
      if (isLoading) {
        return;
      }

      if (observer.current) {
        observer.current.disconnect();
      }

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0]?.isIntersecting && hasNextPage && !isFetching) {
          void fetchNextPage();
        }
      });

      if (node) {
        observer.current.observe(node);
      }
    },
    [fetchNextPage, hasNextPage, isFetching, isLoading],
  );

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };
  const handleFileUpload = async (file: File) => {
    setUploading(true);
    const response = await fetch(`/api/images/upload?fileName=${file.name}`, {
      method: "POST",
      body: file,
      headers: {
        "Content-Type": file.type,
      },
    });
    setUploading(false);
    return (await response.json()) as {
      token: string;
      image: ImageUploadSimpleType;
    };
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer?.files[0]) {
      // Handle file drop
      const file = e.dataTransfer.files[0];
      if (!file) {
        return;
      }
      handleFileUpload(file).then((data) => {
        setSelection(data.image);
        queryClient.invalidateQueries({ queryKey: [IMAGE_KEY] });
      });
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const file = e.target.files[0];
      if (!file) {
        return;
      }
      handleFileUpload(file).then((data) => {
        setSelection(data.image);
        queryClient.invalidateQueries({ queryKey: [IMAGE_KEY] });
      });
    }
  };

  return (
    <Dialog
      open={props.open}
      onOpenChange={(open) => {
        if (!open) {
          props.onClose && props.onClose();
        }
        setSelection(undefined);
      }}
    >
      {props.children && (
        <DialogTrigger asChild>{props.children}</DialogTrigger>
      )}
      <DialogContent className={"max-w-screen-md"}>
        <DialogHeader>
          <DialogTitle>Select Image</DialogTitle>
        </DialogHeader>
        <div>
          <div
            className={`text-center p-3 m-4 border-2 border-dashed rounded-lg shadow-sm hover:bg-accent bg-border/30 cursor-pointer ${
              dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Input
              type="file"
              id="file-upload"
              className="hidden"
              onChange={handleChange}
            />
            <Label
              htmlFor="file-upload"
              className=" flex flex-col items-center justify-center h-full"
            >
              <Upload className="w-16 h-16 text-gray-500" />
              <span className="text-lg font-medium text-gray-700 mt-4">
                Upload Image
              </span>
              <p className={"text-xs text-gray-400"}>
                Drag and drop images here or click to upload.{" "}
              </p>
            </Label>
          </div>
          <ul
            className={
              "grid grid-cols-2 md:grid-cols-4 gap-4 p-4 max-h-96 overflow-y-auto"
            }
          >
            {uploading && (
              <li
                className={
                  "hover:bg-accent hover:text-accent-foreground cursor-pointer"
                }
              >
                <div className={"relative"}>
                  <div className={"object-center object-contain"}>
                    <Skeleton className={"w-16 h-16"} />
                  </div>
                  <LoadingSpinner />
                </div>
              </li>
            )}
            {data?.pages?.map((item, index) => {
              return (
                <Fragment key={index}>
                  {item.images.map((item, i) => {
                    return (
                      <div ref={lastElementRef} key={i}>
                        <ImageThumbnail
                          src={item.url}
                          fileName={item.name}
                          fileType={"image"}
                          selected={selection?.id === item.id}
                          onCheckChange={() => {
                            if (selection?.id === item.id) {
                              setSelection(undefined);
                            } else {
                              setSelection(item);
                            }
                          }}
                        />
                      </div>
                    );
                  })}
                </Fragment>
              );
            })}
            {isFetching &&
              Array.from({ length: 4 }).map((_, i) => (
                <li
                  key={i}
                  className={
                    "hover:bg-accent hover:border-accent hover:text-accent-foreground cursor-pointer rounded-md"
                  }
                >
                  <div className={"relative"}>
                    <div
                      className={
                        "flex items-center justify-center border bg-border/15 rounded-md p-2"
                      }
                    >
                      <Skeleton className={"aspect-square w-32 h-32"} />
                    </div>
                  </div>
                </li>
              ))}
          </ul>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              Close
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              variant={"primary"}
              disabled={selection === undefined}
              onClick={() => {
                if (selection) {
                  props.onSelection(selection.id, selection.url);
                }
              }}
            >
              Save
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImageSelectionDialog;
