import * as React from "react";
import { cn } from "~/lib/utils";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "~/components/ui/table";

const ReportPage: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <div className="flex flex-col gap-4 lg:gap-8">{children}</div>;
};

type ReportEmptyProps = {
  message: string;
};

const ReportEmpty: React.FC<ReportEmptyProps> = ({ message }) => {
  return (
    <div className={"w-full flex justify-center items-center"}>
      <Card className={"w-fit justify-center items-center"}>
        <CardHeader></CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">{message}</div>
        </CardContent>
        <CardFooter></CardFooter>
      </Card>
    </div>
  );
};

type ReportContentProps = {
  hasData: boolean;
  loading?: boolean;
  children: React.ReactNode;
  emptyMessage?: string;
};

const ReportContent: React.FC<ReportContentProps> = ({
  hasData,
  loading,
  children,
  emptyMessage = "No data available",
}) => {
  return (
    <div className="flex flex-col gap-4 lg:gap-8">
      {hasData ? (
        children
      ) : loading ? (
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
        </div>
      ) : (
        <ReportEmpty message={emptyMessage} />
      )}
    </div>
  );
};

const ReportHeader: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className={cn("flex flex-col md:flex-row md:flex-wrap gap-2")}>
      {children}
    </div>
  );
};

type ReportGraphProps = {
  title: string;
  description?: string;
  children: React.ReactNode;
};

const ReportGraph: React.FC<ReportGraphProps> = ({
  title,
  description,
  children,
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="px-2 sm:p-6">{children}</CardContent>
    </Card>
  );
};

type ReportTableProps = {
  title: string;
  description?: string;
  data: { label: string; value: string | number }[];
};

const ReportTable: React.FC<ReportTableProps> = ({
  title,
  description,
  data,
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <Table>
          <TableBody>
            {data.map((item, index) => (
              <TableRow key={index}>
                <TableHead>{item.label}</TableHead>
                <TableCell className="text-right">{item.value}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

const ReportFooter: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="report-footer flex flex-col md:flex-row gap-2">
      {children}
    </div>
  );
};

export {
  ReportPage,
  ReportHeader,
  ReportGraph,
  ReportTable,
  ReportFooter,
  ReportEmpty,
  ReportContent,
};
