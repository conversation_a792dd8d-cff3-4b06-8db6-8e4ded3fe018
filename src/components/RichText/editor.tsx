import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, useEditor } from "@tiptap/react";

import EditorToolbar from "./toolbar/editor-toolbar";
import BaseHeading from "@tiptap/extension-heading";
import { mergeAttributes } from "@tiptap/core";
import {
  Header1,
  Header2,
  Header2<PERSON><PERSON><PERSON>,
  Header4,
  Header5,
  Header6,
} from "~/components/ui/typography";
import { cn } from "~/lib/utils";
import { extensions as defaultExtensions } from "./extensions";
import { EditorBubbleMenu } from "~/components/RichText/extra/EditorBubbleMenu";
import { ImageBubbleMenu } from "~/components/RichText/extra/ImageBubbleMenu";
import { Variables } from "~/components/RichText/extensions/variable-extension";
import { SlashCommandItem } from "~/components/RichText/extensions/slash-command";

type Levels = 1 | 2 | 3 | 4 | 5 | 6;

const classes: Record<Levels, string> = {
  1: Header1,
  2: <PERSON><PERSON><PERSON>,
  3: Header2<PERSON><PERSON><PERSON>,
  4: <PERSON>er4,
  5: Header5,
  6: Header6,
};

export const Heading = BaseHeading.configure({
  levels: [1, 2, 3, 4, 5, 6],
}).extend({
  renderHTML({ node, HTMLAttributes }) {
    const hasLevel = this.options.levels.includes(node.attrs.level);
    const level: Levels = hasLevel ? node.attrs.level : this.options.levels[0];

    return [
      `h${level}`,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: `${classes[level]} font-sans antialiased`,
      }),
      0,
    ];
  },
});
interface EditorProps {
  content: string | JSONContent;
  placeholder?: string;
  onChange: (value: string, json: string) => void;
  compact?: boolean;
  variables?: Variables;
  commands?: SlashCommandItem[];
}

const Editor = ({
  content,
  placeholder,
  onChange,
  compact,
  ...props
}: EditorProps) => {
  const editor = useEditor({
    editorProps: {
      attributes: {
        class: cn(
          `focus-visible:outline-none max-h-screen rounded-md overflow-y-auto min-h-20`,
        ),
        // spellCheck: spellCheck ? "true" : "false",
      },
      handleDOMEvents: {
        keydown: (_view, event) => {
          // prevent default event listeners from firing when slash command is active
          if (["ArrowUp", "ArrowDown", "Enter"].includes(event.key)) {
            const slashCommand = document.querySelector("#slash-command");
            if (slashCommand) {
              return true;
            }
          }
        },
      },
    },
    // onCreate: ({ editor }) => {
    //   onCreate?.(editor);
    // },
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML(), JSON.stringify(editor.getJSON()));
    },
    extensions: [
      Heading,
      ...defaultExtensions({
        variables: props.variables || [],
        slashCommands: props.commands || [],
      }),
    ],
    content: content,
    autofocus: false,
  });

  if (!editor) return <></>;

  return (
    <div className="w-full border rounded-md bg-background">
      <EditorToolbar editor={editor} compact={compact === true} />

      <div className="editor bg-muted-background p-2 rounded-md mt-4">
        <EditorBubbleMenu editor={editor} />
        <ImageBubbleMenu editor={editor} />
        <EditorContent editor={editor} placeholder={placeholder} />
      </div>
    </div>
  );
};

export default Editor;
