import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useState,
} from "react";
import { NodeViewP<PERSON>, NodeViewWrapper, React<PERSON>enderer } from "@tiptap/react";
import { SuggestionOptions } from "@tiptap/suggestion";
import tippy, { GetReferenceClientRect } from "tippy.js";
import { AlertTriangle } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Input } from "~/components/ui/input";
import { Variables } from "~/components/RichText/extensions/variable-extension";
import { cn } from "~/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { TooltipProvider } from "@radix-ui/react-tooltip";

export const VariableList = forwardRef((props: any, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = props.items[index];

    if (item) {
      props.command({ id: item });
    }
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === "ArrowUp") {
        setSelectedIndex(
          (selectedIndex + props.items.length - 1) % props.items.length,
        );
        return true;
      }

      if (event.key === "ArrowDown") {
        setSelectedIndex((selectedIndex + 1) % props.items.length);
        return true;
      }

      if (event.key === "Enter") {
        selectItem(selectedIndex);
        return true;
      }

      return false;
    },
  }));

  return (
    <div className="z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
      {props?.items?.length ? (
        props?.items?.map((item: string, index: number) => (
          <button
            key={index}
            onClick={() => selectItem(index)}
            className={cn(
              "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
              index === selectedIndex ? "bg-gray-200" : "bg-white",
            )}
          >
            {item}
          </button>
        ))
      ) : (
        <button className="flex w-full space-x-2 rounded-md bg-white px-2 py-1 text-left text-sm text-gray-900 hover:bg-gray-100">
          No result
        </button>
      )}
    </div>
  );
});

VariableList.displayName = "VariableList";

export function getVariableSuggestions(
  variables: Variables = [],
): Omit<SuggestionOptions, "editor"> {
  const defaultVariables = variables.map((variable) => variable.name);

  return {
    items: ({ query }) => {
      return defaultVariables
        .filter((item) => item.toLowerCase().startsWith(query.toLowerCase()))
        .slice(0, 5);
    },

    render: () => {
      let component: ReactRenderer<any>;
      let popup: InstanceType<any> = null;

      return {
        onStart: (props) => {
          component = new ReactRenderer(VariableList, {
            props,
            editor: props.editor,
          });

          if (!props.clientRect) {
            return;
          }

          popup = tippy("body", {
            getReferenceClientRect: props.clientRect as GetReferenceClientRect,
            appendTo: () => document.body,
            content: component.element,
            showOnCreate: true,
            interactive: true,
            trigger: "manual",
            placement: "bottom-start",
          });
        },

        onUpdate(props) {
          component.updateProps(props);

          if (!props.clientRect) {
            return;
          }

          popup?.[0]?.setProps({
            getReferenceClientRect: props.clientRect as GetReferenceClientRect,
          });
        },

        onKeyDown(props) {
          if (props.event.key === "Escape") {
            popup?.[0].hide();

            return true;
          }

          return component.ref?.onKeyDown(props);
        },

        onExit() {
          if (!popup?.[0] || !component) {
            return;
          }

          popup?.[0].destroy();
          component.destroy();
        },
      };
    },
  };
}

export function VariableComponent(props: NodeViewProps) {
  const { node, selected, updateAttributes } = props;
  const { id, fallback } = node.attrs;

  const { variables = [] } = { variables: [] } as { variables: Variables };
  const isRequired =
    variables.find((variable) => variable.name === id)?.required ?? true;

  const [isOpen, setIsOpen] = useState(false);

  return (
    <NodeViewWrapper
      className={cn(
        "react-component",
        selected && "ProseMirror-selectednode",
        "inline-block leading-none",
      )}
      draggable="false"
    >
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger>
          <TooltipProvider delayDuration={100}>
            <span
              tabIndex={-1}
              className="inline-flex items-center gap-1 rounded-md border border-blue-200 bg-blue-50 px-2 py-1 leading-none text-blue-800"
            >
              {id}
              {isRequired && !fallback && (
                <Tooltip>
                  <TooltipContent>
                    <p>
                      This variable is missing a fallback value, which in most
                      cases is a mistake!
                    </p>
                  </TooltipContent>
                  <TooltipTrigger asChild={true}>
                    <AlertTriangle className="h-3 w-3 shrink-0 stroke-[2.5]" />
                  </TooltipTrigger>
                </Tooltip>
              )}
            </span>
          </TooltipProvider>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="space-y-2"
          onOpenAutoFocus={(e) => e.preventDefault()}
          onCloseAutoFocus={(e) => e.preventDefault()}
        >
          <label className="block w-full space-y-1.5 leading-none">
            <span className="text-xs font-normal leading-none">
              Variable Name
            </span>
            <Input
              placeholder="Variable Name"
              value={id || ""}
              disabled={true}
              onChange={(e) => {
                updateAttributes({
                  id: e.target.value,
                });
              }}
            />
          </label>
          <label className="block w-full space-y-1.5 leading-none">
            <span className="text-xs font-normal leading-none">
              Fallback Value
            </span>
            <Input
              placeholder="Fallback Value"
              value={fallback || ""}
              onChange={(e) => {
                updateAttributes({
                  fallback: e.target.value,
                });
              }}
            />

            <p className="text-xs text-gray-500">
              If the variable doesn't exist, this fallback value will be used.
            </p>
          </label>
        </PopoverContent>
      </Popover>
    </NodeViewWrapper>
  );
}
