import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import { AlignCenterIcon, AlignLeftIcon, AlignRightIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { cn } from "~/lib/utils";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import {
  allowedButtonBorderRadius,
  allowedButtonVariant,
  AllowedButtonVariant,
} from "~/components/RichText/extensions/button-extensions";
import ColorPicker from "~/components/ColorPicker";
import { ToggleGroupMini } from "~/components/ToggleGroupMini";

const alignments = {
  left: AlignLeftIcon,
  center: AlignCenterIcon,
  right: AlignRightIcon,
};

const items = {
  style(props: NodeViewProps) {
    return allowedButtonVariant.map((variant) => ({
      name: variant,
      isActive: props.node.attrs.variant === variant,
      onClick: () => {
        props.updateAttributes({
          variant: variant,
        });
      },
    }));
  },
  cornerRadius(props: NodeViewProps) {
    return allowedButtonBorderRadius.map((radius) => ({
      name: radius,
      isActive: props.node.attrs.borderRadius === radius,
      onClick: () => {
        props.updateAttributes({
          borderRadius: radius,
        });
      },
    }));
  },
  alignment(props: NodeViewProps) {
    return Object.entries(alignments).map(([alignment, Icon]) => ({
      name: alignment,
      icon: Icon,
      isActive: props.node.attrs.alignment === alignment,
      onClick: () => props.updateAttributes({ alignment }),
    }));
  },
};

export function ButtonComponent(props: NodeViewProps) {
  const {
    url,
    text,
    alignment,
    variant,
    borderRadius: _radius,
    buttonColor,
    textColor,
  } = props.node.attrs;
  const { getPos, editor } = props;

  return (
    <NodeViewWrapper
      className={`react-component ${
        props.selected && "ProseMirror-selectednode"
      }`}
      draggable="true"
      data-drag-handle=""
      style={{
        textAlign: alignment,
      }}
    >
      <Popover open={props.selected}>
        <PopoverTrigger asChild>
          <div>
            <button
              className={cn(
                "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-white transition-colors disabled:pointer-events-none disabled:opacity-50",
                "h-10 px-4 py-2",
                "px-[32px] py-[20px] font-semibold no-underline",
                {
                  "!rounded-full": _radius === "round",
                  "!rounded-md": _radius === "smooth",
                  "!rounded-none": _radius === "sharp",
                },
              )}
              tabIndex={-1}
              style={{
                backgroundColor:
                  variant === "filled" ? buttonColor : "transparent",
                color: textColor,
                borderWidth: 2,
                borderStyle: "solid",
                borderColor: buttonColor,
              }}
              onClick={(e) => {
                e.preventDefault();
                const pos = getPos();
                editor.commands.setNodeSelection(pos);
              }}
            >
              {text}
            </button>
          </div>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          sideOffset={10}
          onOpenAutoFocus={(e) => e.preventDefault()}
          onCloseAutoFocus={(e) => e.preventDefault()}
          className="w-[280px] space-y-3 rounded-lg border bg-white p-4 shadow-lg"
        >
          {/* Text Input */}
          <Input
            placeholder="Add text here"
            value={text}
            onChange={(e) => props.updateAttributes({ text: e.target.value })}
          />

          {/* Link Input */}
          <Input
            placeholder="Add link here"
            value={url}
            onChange={(e) => props.updateAttributes({ url: e.target.value })}
          />

          {/* Style Toggle */}
          <div>
            <p className="text-xs font-medium text-muted-foreground mb-1 mt-3">
              Style
            </p>
            <ToggleGroupMini
              ariaLabel="Button Style"
              items={items.style(props).map((item) => ({
                label: item.name,
                value: item.name,
                isActive: item.isActive,
                onClick: item.onClick,
              }))}
            />
          </div>

          {/* Corner Radius */}
          <div>
            <p className="text-xs font-medium text-muted-foreground mb-1 mt-3">
              Corner Radius
            </p>
            <ToggleGroupMini
              ariaLabel="Corner Radius"
              items={items.cornerRadius(props).map((item) => ({
                label: item.name,
                value: item.name,
                isActive: item.isActive,
                onClick: item.onClick,
              }))}
            />
          </div>

          <div className="flex justify-between gap-4">
            <div className="flex-1">
              <p className="text-xs font-medium text-muted-foreground mb-1 mt-3">
                Alignment
              </p>

              <ToggleGroupMini
                ariaLabel="Text Alignment"
                size="icon"
                items={items.alignment(props).map((item) => ({
                  label: item.name,
                  value: item.name,
                  isActive: item.isActive,
                  onClick: item.onClick,
                  icon: <item.icon size={16} />,
                }))}
              />
            </div>

            <div className="flex-1">
              <p className="text-xs font-medium text-muted-foreground mb-2">
                Color
              </p>
              <div className="flex gap-1">
                <BackgroundColorPickerPopup
                  variant={variant}
                  color={buttonColor}
                  onChange={(color) =>
                    props.updateAttributes({ buttonColor: color })
                  }
                />
                <TextColorPickerPopup
                  color={textColor}
                  onChange={(color) =>
                    props.updateAttributes({ textColor: color })
                  }
                />
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </NodeViewWrapper>
  );
}

type ColorPickerProps = {
  variant?: AllowedButtonVariant;
  color: string;
  onChange: (color: string) => void;
};

export function BackgroundColorPickerPopup(props: ColorPickerProps) {
  const { color, onChange, variant } = props;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" className="" size="sm" type="button">
          <div
            className="h-4 w-4 rounded border border-muted-foreground shadow-md"
            style={{
              background: variant === "filled" ? color : "transparent",
            }}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full rounded-none border-0 !bg-transparent !p-0 shadow-none drop-shadow-md">
        <ColorPicker
          background={color}
          setBackground={(newColor) => {
            // HACK: This is a workaround for a bug in tiptap
            // https://github.com/ueberdosis/tiptap/issues/3580
            //
            //     ERROR: flushSync was called from inside a lifecycle
            //
            // To fix this, we need to make sure that the onChange
            // callback is run after the current execution context.
            queueMicrotask(() => {
              onChange(newColor);
            });
          }}
        />
      </PopoverContent>
    </Popover>
  );
}

export function TextColorPickerPopup(props: ColorPickerProps) {
  const { color, onChange } = props;
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" type="button">
          <div className="flex flex-col items-center justify-center gap-[1px]">
            <span className="font-bolder font-mono text-xs text-slate-700">
              A
            </span>
            <div className="h-[2px] w-3" style={{ backgroundColor: color }} />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full rounded-none border-0 !bg-transparent !p-0 shadow-none drop-shadow-md">
        <ColorPicker
          background={color}
          setBackground={(color) => {
            queueMicrotask(() => {
              onChange(color);
            });
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
