import StarterKit from "@tiptap/starter-kit";
import TiptapLink from "@tiptap/extension-link";
import Paragraph from "@tiptap/extension-paragraph";
import Placeholder from "@tiptap/extension-placeholder";
import TextAlign from "@tiptap/extension-text-align";
// import TextStyle from "@tiptap/extension-text-style";
import Heading from "@tiptap/extension-heading";
import Underline from "@tiptap/extension-underline";
import { HorizontalRule } from "./horizontal-rule";
import { getVariableSuggestions } from "../nodes/variable";
import {
  getSlashCommandSuggestions,
  SlashCommand,
  SlashCommandItem,
} from "./slash-command";
import { Variable, Variables } from "./variable-extension";
import { ResizableImageExtension } from "./image-resize";
import { LinkCardExtension } from "./link-card";
import { ButtonExtension } from "~/components/RichText/extensions/button-extensions";
import { Color } from "@tiptap/extension-color";
import { TextStyle } from "@tiptap/extension-text-style";
import { ListItem } from "@tiptap/extension-list-item";
import { OrderProducts } from "~/components/RichText/extensions/order-products";
import { CsatSurvey } from "~/components/RichText/extensions/csat-survey";

type ExtensionsProps = {
  variables?: Variables;
  slashCommands?: SlashCommandItem[];
};

export function extensions(props: ExtensionsProps) {
  const { variables, slashCommands } = props;

  return [
    StarterKit.configure({
      heading: false,
      dropcursor: {
        color: "#555",
        width: 3,
      },
      paragraph: {
        HTMLAttributes: {
          class: "leading-7",
        },
      },

      code: {
        HTMLAttributes: {
          class:
            "px-1 py-0.5 bg-[#efefef] text-sm rounded-md tracking-normal font-normal",
        },
      },
      horizontalRule: false,
      blockquote: {
        HTMLAttributes: {
          class: "border-l-2 pl-6",
        },
      },
      bulletList: {
        HTMLAttributes: {
          class: "ml-6 list-disc",
        },
      },
      orderedList: {
        HTMLAttributes: {
          class: "ml-6 list-decimal",
        },
      },
      codeBlock: {
        HTMLAttributes: {
          class:
            "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",
        },
      },
    }),
    Underline,
    // TiptapLogoExtension,
    Color.configure({ types: [TextStyle.name, ListItem.name] }),
    TextStyle.configure(),
    TextAlign.configure({ types: [Paragraph.name, Heading.name] }),
    HorizontalRule,
    Placeholder.configure({
      placeholder: ({ node }: any) => {
        if (node.type.name === "heading") {
          return `Heading ${node.attrs.level}`;
        } else if (
          [
            "columns",
            "column",
            "button",
            "section",
            "orderProducts",
            "show",
            "blockquote",
          ].includes(node.type.name)
        ) {
          return "";
        }

        return "Write something or / to see commands";
      },
      includeChildren: true,
    }),
    // Spacer,
    // Footer,
    Variable.configure({
      suggestion: getVariableSuggestions(variables),
    }),
    OrderProducts.configure({}),
    CsatSurvey.configure({}),
    SlashCommand.configure({
      suggestion: getSlashCommandSuggestions(slashCommands),
    }),
    TiptapLink.configure({
      HTMLAttributes: {
        target: "_blank",
        rel: "noopener noreferrer nofollow",
      },
      openOnClick: false,
    }),
    ButtonExtension,
    ResizableImageExtension,
    LinkCardExtension,
  ];
}
