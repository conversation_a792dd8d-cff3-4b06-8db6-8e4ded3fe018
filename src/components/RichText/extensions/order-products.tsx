import { mergeAttributes, Node } from "@tiptap/core";
import ContractItemList, {
  ContractItemListProps,
} from "~/components/Contract/ItemList";
import { getCurrencyString, getPaymentInfo } from "~/server/lib/currency";
import {
  NodeViewProps,
  NodeViewWrapper,
  ReactNodeViewRenderer,
} from "@tiptap/react";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    orderProducts: {
      setOrderProducts: () => ReturnType;
    };
  }
}

export const OrderProducts = Node.create({
  name: "orderProducts",

  group: "block",

  content: "block*",
  draggable: true,

  addAttributes() {
    return {
      mailyComponent: {
        default: "button",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: `a[data-maily-component="${this.name}"]`,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(
        {
          "data-maily-component": this.name,
        },
        HTMLAttributes,
      ),
    ];
  },

  addCommands() {
    return {
      setOrderProducts:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              mailyComponent: this.name,
            },
          });
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(Item);
  },
});

const Item = (props: NodeViewProps) => {
  const { getPos, editor } = props;
  const receiptProps: ContractItemListProps = {
    accountTimezone: "America/Chicago",
    paidItems: [
      {
        method: "Stripe-Webhook",
        methodId: "123",
        amount: 100,
      },
    ],
    contractSharedItems: {
      ...getPaymentInfo({
        baseTotal: 100,
        discounts: [],
        damageWaiver: {
          percentage: 5,
        },
        fees: [
          {
            type: "TRAVEL_FEE",
            name: null,
            amount: 17.32,
            taxable: true,
          },
        ],
        taxRate: {
          percentage: 6.25,
        },
        totalPaid: 100,
      }),
      rentalStartDate: new Date(),
      rentalEndDate: new Date(),
    },
    contractItems: [
      {
        name: "Bounce House",
        pricePerUnit: getCurrencyString(100),
        quantity: 1,
        total: getCurrencyString(100),
      },
    ],
    depositPercentage: 25,
  };
  return (
    <NodeViewWrapper
      className={`react-component ${
        props.selected && "ProseMirror-selectednode"
      }`}
      draggable="true"
      data-drag-handle=""
    >
      <div
        className={"w-full"}
        onClick={(e) => {
          e.preventDefault();
          const pos = getPos();
          editor.commands.setNodeSelection(pos);
        }}
      >
        <ContractItemList {...receiptProps} email={true} />
      </div>
    </NodeViewWrapper>
  );
};
