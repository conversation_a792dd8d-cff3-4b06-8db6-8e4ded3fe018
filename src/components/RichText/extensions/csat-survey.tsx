import { mergeAttributes, Node } from "@tiptap/core";
import {
  NodeViewProps,
  NodeViewWrapper,
  ReactNodeViewRenderer,
} from "@tiptap/react";
import CSATFaceComponent from "~/components/CSATFaceComponent";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    csatSurvey: {
      setCsatSurvey: () => ReturnType;
    };
  }
}

export const CsatSurvey = Node.create({
  name: "csatSurvey",

  group: "block",

  content: "block*",
  draggable: true,

  addAttributes() {
    return {
      mailyComponent: {
        default: "button",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: `a[data-maily-component="${this.name}"]`,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(
        {
          "data-maily-component": this.name,
        },
        HTMLAttributes,
      ),
    ];
  },

  addCommands() {
    return {
      setCsatSurvey:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              mailyComponent: this.name,
            },
          });
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(Item);
  },
});

const Item = (props: NodeViewProps) => {
  const { getPos, editor } = props;
  return (
    <NodeViewWrapper
      className={`react-component ${
        props.selected && "ProseMirror-selectednode"
      }`}
      draggable="true"
      data-drag-handle=""
    >
      <div
        className={"w-full"}
        onClick={(e) => {
          e.preventDefault();
          const pos = getPos();
          editor.commands.setNodeSelection(pos);
        }}
      >
        <CSATFaceComponent link={"#"} />
      </div>
    </NodeViewWrapper>
  );
};
