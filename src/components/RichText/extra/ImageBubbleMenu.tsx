import { BubbleMenu } from "@tiptap/react";
import {
  AlignCenterIcon,
  AlignLeftIcon,
  AlignRightIcon,
  ArrowUpRight,
  ImageIcon,
} from "lucide-react";

import { BubbleMenuButton } from "./BubbleMenuButton";
import { BubbleMenuItem, EditorBubbleMenuProps } from "./EditorBubbleMenu";
import ImageSelectionDialog from "~/components/ImageSelectionDialog";
import { useState } from "react";

export const allowedLogoSize = ["sm", "md", "lg"] as const;
export type AllowedLogoSize = (typeof allowedLogoSize)[number];

export const allowedLogoAlignment = ["left", "center", "right"] as const;
export type AllowedLogoAlignment = (typeof allowedLogoAlignment)[number];

export function ImageBubbleMenu(props: EditorBubbleMenuProps) {
  const { editor } = props;
  const [open, setOpen] = useState(false);

  const icons = [AlignLeftIcon, AlignCenterIcon, AlignRightIcon];

  const alignmentItems: BubbleMenuItem[] = allowedLogoAlignment.map(
    (alignment, index) => ({
      name: alignment,
      isActive: () => editor?.isActive("image", { alignment }) || false,
      shouldShow: () => editor?.isActive("image") ?? false,
      command: () => {
        props?.editor
          ?.chain()
          .focus()
          .updateAttributes("image", { alignment })
          .run();
      },
      icon: icons[index],
    }),
  );

  const items: BubbleMenuItem[] = [
    ...alignmentItems,
    {
      name: "image-url",
      isActive: () => false,
      shouldShow: () => editor?.isActive("image") ?? false,
      command: () => {
        const { editor } = props;
        editor?.commands.updateAttributes("image", {
          data: "update",
        });
        setOpen(true);
      },
      icon: ImageIcon,
    },
    {
      name: "image-external-url",
      isActive: () => false,
      shouldShow: () => editor?.isActive("image") ?? false,
      command: () => {
        const { editor } = props;
        const externalLink = editor?.getAttributes("image")?.externalLink;

        const url = window.prompt(
          "Update Image External URL",
          externalLink || "",
        );

        editor?.commands.updateAttributes("image", { externalLink: url || "" });
      },
      icon: ArrowUpRight,
    },
  ];

  const bubbleMenuProps: EditorBubbleMenuProps = {
    ...props,
    shouldShow: ({ editor }) => {
      console.log(editor.isActive("image"), open);
      return editor.isActive("image") && !open;
    },
    tippyOptions: {
      maxWidth: "100%",
      moveTransition: "transform 0.15s ease-out",
      zIndex: 26,
    },
  };

  return (
    <div>
      <ImageSelectionDialog
        open={open}
        onClose={() => {
          setOpen(false);
        }}
        onSelection={(id: string, url: string) => {
          props.editor?.commands.setImage({
            src: url,
          });
          setOpen(false);
        }}
      />
      <BubbleMenu
        {...bubbleMenuProps}
        className="flex gap-1 rounded-md border border-slate-200 bg-white p-1 shadow-md"
      >
        {items
          .filter((item) => item.shouldShow?.())
          .map((item, index) => {
            return <BubbleMenuButton key={index} {...item} />;
          })}
      </BubbleMenu>
    </div>
  );
}
