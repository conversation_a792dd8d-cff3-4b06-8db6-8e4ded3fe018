import { Editor } from "@tiptap/react";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Header1,
  Header2,
  Header2U<PERSON>line,
  Header4,
  Header5,
  Header6,
  Paragraph,
} from "~/components/ui/typography";
import React from "react";

type FormatTypeProps = {
  editor: Editor;
  compact: boolean;
};

type FormatOption = {
  label: string;
  compactLabel?: string;
  value: string;
  isCurrentlyActive: (editor: Editor) => boolean;
  activate: (editor: Editor) => void;
  className?: string;
};

const FORMAT_OPTIONS: FormatOption[] = [
  {
    label: "Paragraph",
    compactLabel: "Aa",
    value: "paragraph",
    className: Paragraph,
    isCurrentlyActive: (editor) => editor.isActive("paragraph"),
    activate: (editor) => editor.chain().focus().setParagraph().run(),
  },
  {
    label: "H1",
    value: "h1",
    className: Header1,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 1 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    label: "H2",
    value: "h2",
    className: Header2,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 2 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    label: "H3",
    value: "h3",
    className: Header2Underline,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 3 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  {
    label: "H4",
    value: "h4",
    className: Header4,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 4 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 4 }).run(),
  },
  {
    label: "H5",
    value: "h5",
    className: Header5,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 5 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 5 }).run(),
  },
  {
    label: "H6",
    value: "h6",
    className: Header6,
    isCurrentlyActive: (editor) => editor.isActive("heading", { level: 6 }),
    activate: (editor) =>
      editor.chain().focus().toggleHeading({ level: 6 }).run(),
  },
  {
    label: "Quote",
    value: "blockquote",
    isCurrentlyActive: (editor) => editor.isActive("blockquote"),
    activate: (editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  {
    label: "Code",
    value: "code",
    isCurrentlyActive: (editor) => editor.isActive("codeBlock"),
    activate: (editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
];

export function FormatType({ editor, compact }: FormatTypeProps) {
  const value = () => {
    const activeOption = FORMAT_OPTIONS.find((option) =>
      option.isCurrentlyActive(editor),
    );
    if (!activeOption) {
      return FORMAT_OPTIONS[0]?.value;
    }
    return activeOption.value;
  };

  const onChange = (value: string) => {
    const selectedOption = FORMAT_OPTIONS.find(
      (option) => option.value === value,
    );
    if (!selectedOption) {
      return;
    }
    editor.chain().focus().unsetBlockquote().run();
    selectedOption.activate(editor);
  };

  return (
    <Select onValueChange={onChange} defaultValue={value()} value={value()}>
      <SelectTrigger
        className={`h-8 ${compact ? "w-20" : "w-48"} invisible sm:visible`}
      >
        <SelectValue placeholder="Select format" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {FORMAT_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <span>{option.label}</span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
