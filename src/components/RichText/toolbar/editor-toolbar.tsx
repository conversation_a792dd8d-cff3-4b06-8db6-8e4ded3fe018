import React from "react";
import { Editor } from "@tiptap/react";
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Minus,
  Redo,
  Strikethrough,
  Undo,
} from "lucide-react";

import { Toggle } from "~/components/ui/toggle";
import { ToggleGroup, Toolbar } from "~/components/ui/toolbar";

import { FormatType } from "./format-type";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Button } from "~/components/ui/button";

interface EditorToolbarProps {
  editor: Editor;
  compact: boolean;
}

const SimpleTooltip = ({
  name,
  children,
}: {
  name: string;
  children: React.ReactNode;
}) => (
  <Tooltip>
    <TooltipTrigger asChild>{children}</TooltipTrigger>
    <TooltipContent>
      <p>{name}</p>
    </TooltipContent>
  </Tooltip>
);

const EditorToolbar = ({ editor, compact }: EditorToolbarProps) => {
  return (
    <Toolbar
      className="m-0 flex items-center justify-between px-2 py-1 bg-accent/80 rounded-md rounded-b-none"
      aria-label="Formatting options"
    >
      <TooltipProvider delayDuration={100}>
        <ToggleGroup
          className="flex flex-row items-center gap-1"
          type="multiple"
        >
          <FormatType editor={editor} compact={compact} />
          <SimpleTooltip name={"Bold"}>
            <Toggle
              size="sm"
              variant={"toolbar"}
              onPressedChange={() => editor.chain().focus().toggleBold().run()}
              disabled={!editor.can().chain().focus().toggleBold().run()}
              pressed={editor.isActive("bold")}
            >
              <Bold className="h-4 w-4" />
            </Toggle>
          </SimpleTooltip>

          <SimpleTooltip name={"Italic"}>
            <Toggle
              size="sm"
              variant={"toolbar"}
              onPressedChange={() =>
                editor.chain().focus().toggleItalic().run()
              }
              disabled={!editor.can().chain().focus().toggleItalic().run()}
              pressed={editor.isActive("italic")}
              value="italic"
            >
              <Italic className="h-4 w-4" />
            </Toggle>
          </SimpleTooltip>

          <SimpleTooltip name={"Strikethrough"}>
            <Toggle
              size="sm"
              variant={"toolbar"}
              onPressedChange={() =>
                editor.chain().focus().toggleStrike().run()
              }
              disabled={!editor.can().chain().focus().toggleStrike().run()}
              pressed={editor.isActive("strike")}
            >
              <Strikethrough className="h-4 w-4" />
            </Toggle>
          </SimpleTooltip>

          <SimpleTooltip name={"Bullet List"}>
            <Toggle
              size="sm"
              variant={"toolbar"}
              onPressedChange={() =>
                editor.chain().focus().toggleBulletList().run()
              }
              pressed={editor.isActive("bulletList")}
            >
              <List className="h-4 w-4" />
            </Toggle>
          </SimpleTooltip>

          <SimpleTooltip name={"Numbered List"}>
            <Toggle
              size="sm"
              variant={"toolbar"}
              onPressedChange={() =>
                editor.chain().focus().toggleOrderedList().run()
              }
              pressed={editor.isActive("orderedList")}
            >
              <ListOrdered className="h-4 w-4" />
            </Toggle>
          </SimpleTooltip>

          {!compact && (
            <SimpleTooltip name={"Line Break"}>
              <Button
                size="icon"
                variant={"outline"}
                type={"button"}
                onClick={() => editor.chain().focus().setHorizontalRule().run()}
              >
                <Minus className="h-4 w-4" />
              </Button>
            </SimpleTooltip>
          )}
        </ToggleGroup>

        {!compact && (
          <ToggleGroup
            className="flex flex-row items-center invisible sm:visible gap-1"
            type="multiple"
          >
            <SimpleTooltip name={"Undo"}>
              <Button
                size="icon"
                variant={"ghost"}
                type={"button"}
                onClick={() => editor.chain().focus().undo().run()}
                disabled={!editor.can().chain().focus().undo().run()}
              >
                <Undo className="h-4 w-4" />
              </Button>
            </SimpleTooltip>

            <SimpleTooltip name={"Redo"}>
              <Button
                size="icon"
                variant={"ghost"}
                type={"button"}
                onClick={() => editor.chain().focus().redo().run()}
                disabled={!editor.can().chain().focus().redo().run()}
              >
                <Redo className="h-4 w-4" />
              </Button>
            </SimpleTooltip>
          </ToggleGroup>
        )}
      </TooltipProvider>
    </Toolbar>
  );
};

export default EditorToolbar;
