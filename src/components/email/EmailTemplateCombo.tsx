import { useEmails } from "~/query/email";
import Combobox from "~/components/ui/combo";
import FormItem from "~/components/FormLayout/FormItem";
import React from "react";

type EmailTemplateComboProps = {
  name: string;
};

const EmailTemplateCombo = (props: EmailTemplateComboProps) => {
  const emails = useEmails();

  return (
    <FormItem
      label={"Email Template"}
      name={props.name}
      removeFormControl={true}
      render={({ field }) => {
        return (
          <Combobox
            placeholder={"Select Email Template"}
            onChange={(value) => {
              field.onChange(Number(value));
            }}
            value={emails.data?.find((email) => email.id === field.value)?.name}
            items={
              emails.data?.map((email) => {
                return {
                  label: email.name,
                  value: email.id.toString(),
                };
              }) || []
            }
          />
        );
      }}
    />
  );
};

export default EmailTemplateCombo;
