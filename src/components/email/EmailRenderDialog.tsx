import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "~/components/ui/dialog";
import { JSONContent } from "@tiptap/react";
import React, { useMemo } from "react";
import { Maily } from "~/lib/email/maily";
import { DialogDescription } from "@radix-ui/react-dialog";
import { Monitor, SmartphoneIcon } from "lucide-react";
import { CustomerValues } from "~/query/customer";
import { OrderState } from ".prisma/client";
import { TooltipProvider } from "~/components/ui/tooltip";
import EmailPreview from "~/components/email/EmailPreview";
import {
  LocalTabs,
  LocalTabsContent,
  LocalTabsList,
  LocalTabsTrigger,
} from "~/components/ui/local-tabs";
import { getCurrencyString, getPaymentInfo } from "~/server/lib/currency";
import { ContractItemListProps } from "~/components/Contract/ItemList";

type EmailRenderDialogProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  jsonContent: JSONContent;
};

const EmailRenderDialog = (props: EmailRenderDialogProps) => {
  const [mobileView, setMobileView] = React.useState(false);

  const innerHTML = useMemo(() => {
    const emailProvider = new Maily(props.jsonContent);

    emailProvider.setVariableValues({
      FirstName: "John",
      LastName: "Smith",
      Email: "<EMAIL>",
      OrderNumber: "#8312",
      OrderPaymentLink: "https://example.com/payment",
      OrderContractLink: "https://example.com/contract",
      ORDER_SURVEY_LINK: `#/post-order/survey/`,
    });

    const customer: CustomerValues = {
      firstName: "John",
      lastName: "Smith",
      email: "<EMAIL>",
      points: 150,
    };

    const order = {
      id: 1,
      startTime: new Date(),
      endTime: new Date(),
      state: OrderState.ACTIVE,
      finalTotal: 312.35,
    };

    const product = {
      id: 1,
      name: "Product",
      price: 100,
    };

    const receiptProps: ContractItemListProps = {
      accountTimezone: "America/Chicago",
      paidItems: [
        {
          method: "Stripe-Webhook",
          methodId: "123",
          amount: 100,
        },
      ],
      contractSharedItems: {
        ...getPaymentInfo({
          baseTotal: 100,
          discounts: [],
          damageWaiver: {
            percentage: 5,
          },
          fees: [
            {
              type: "TRAVEL_FEE",
              name: null,
              amount: 17.32,
              taxable: true,
            },
            {
              type: "SURFACE_FEE",
              name: "Grass",
              amount: 5.32,
              taxable: true,
            },
          ],
          taxRate: {
            percentage: 6.25,
          },
          totalPaid: 100,
        }),
        rentalStartDate: new Date(),
        rentalEndDate: new Date(),
      },
      contractItems: [
        {
          name: "Bounce House",
          pricePerUnit: getCurrencyString(100),
          quantity: 1,
          total: getCurrencyString(100),
        },
      ],
      depositPercentage: 25,
    };

    emailProvider.setContractItems(receiptProps);
    emailProvider.setVariableValuesFromCustomer(customer);
    emailProvider.setVariableValuesFromOrder(order);
    emailProvider.setVariableValuesFromProduct(product);

    return emailProvider.renderSync({});
  }, [props.jsonContent]);

  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent className={`w-full min-w-0 max-w-[776px] overflow-hidden`}>
        <LocalTabs
          defaultValue={"desktop"}
          onValueChange={(value) => {
            setMobileView(value === "mobile");
          }}
        >
          <TooltipProvider>
            <DialogHeader>
              <DialogTitle>Email Preview</DialogTitle>
              <DialogDescription className={"flex items-center gap-2"}>
                <LocalTabsList>
                  <LocalTabsTrigger value={"mobile"}>
                    <SmartphoneIcon
                      className={`h-5 w-5 ${
                        !mobileView ? "text-gray-200" : "text-black"
                      }`}
                    />
                  </LocalTabsTrigger>
                  <LocalTabsTrigger value={"desktop"}>
                    <Monitor
                      className={`h-5 w-5 ${
                        mobileView ? "text-gray-200" : "text-black"
                      }`}
                    />
                  </LocalTabsTrigger>
                </LocalTabsList>
              </DialogDescription>
            </DialogHeader>
          </TooltipProvider>
          <div className="relative animation-none bg-muted min-h-[75vh] p-0 max-[680px]:h-full max-[680px]:rounded-none max-[680px]:border-0 max-[680px]:shadow-none">
            <LocalTabsContent value={"desktop"}>
              <EmailPreview activeView={"desktop"} html={innerHTML} />
            </LocalTabsContent>
            <LocalTabsContent value={"mobile"}>
              <EmailPreview activeView={"mobile"} html={innerHTML} />
            </LocalTabsContent>
          </div>
        </LocalTabs>
      </DialogContent>
    </Dialog>
  );
};

export default EmailRenderDialog;
