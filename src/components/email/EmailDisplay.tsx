import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Button } from "~/components/ui/button";
import { ClipboardIcon, MailIcon } from "lucide-react";
import { toast } from "sonner";
import React from "react";

type EmailDisplayProps = {
  email: string;
  onClickSendEmail?: () => void;
};

const EmailDisplay = ({ email, onClickSendEmail }: EmailDisplayProps) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={"link"} size={"link"} className={"justify-start mt-2"}>
          {email}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {onClickSendEmail && (
          <DropdownMenuItem
            onClick={() => {
              onClickSendEmail();
            }}
          >
            <MailIcon className="mr-2 h-4 w-4" />
            Send Email
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            toast.success("Copied to Clipboard!");
            navigator.clipboard.writeText(email || "");
          }}
        >
          <ClipboardIcon className={"mr-2 h-4 w-4"} />
          Copy
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default EmailDisplay;
