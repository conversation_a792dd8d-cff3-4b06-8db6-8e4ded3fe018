import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../ui/modal";
import { useEmail, useEmails } from "~/query/email";
import { Skeleton } from "~/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useState } from "react";
import { Button } from "~/components/ui/button";
import { JSONContent } from "@tiptap/react";
import EmailRenderDialog from "~/components/email/EmailRenderDialog";

type EmailSelectionModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  onEmailSelected: (templateId: number) => void;
};

const EmailSelectionModal = (props: EmailSelectionModalProps) => {
  const { data: emails, isPending } = useEmails();
  const [templateId, setTemplateId] = useState<number | undefined>(undefined);
  const [previewO<PERSON>, setPreview<PERSON>pen] = useState(false);
  const { data: email } = useEmail(previewOpen ? templateId : undefined);

  return (
    <Modal
      open={props.open}
      onOpenChange={(value) => {
        if (!value) {
          setTemplateId(undefined);
          setPreviewOpen(false);
        }
        props.setOpen(value);
      }}
    >
      <EmailRenderDialog
        open={previewOpen}
        setOpen={setPreviewOpen}
        jsonContent={
          email?.text ? (JSON.parse(email?.text) as JSONContent) : {}
        }
      />
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Select an email template</ModalTitle>
        </ModalHeader>
        <ModalBody>
          {isPending ? (
            <Skeleton className={"w-20 h-2"} />
          ) : (
            <div className={"flex flex-row gap-3 items-center m-3"}>
              <Select onValueChange={(value) => setTemplateId(Number(value))}>
                <SelectTrigger>
                  <SelectValue placeholder={"Select an email template"} />
                </SelectTrigger>
                <SelectContent>
                  {emails?.map((email) => (
                    <SelectItem key={email.id} value={email.id.toString()}>
                      {email.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={() => setPreviewOpen(true)}
                disabled={templateId === undefined}
              >
                Preview
              </Button>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button onClick={() => props.setOpen(false)}>Cancel</Button>
          <Button
            variant={"primary"}
            disabled={templateId === undefined}
            onClick={() => {
              if (templateId === undefined) {
                return;
              }
              props.onEmailSelected(templateId);
              props.setOpen(false);
            }}
          >
            Send
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default EmailSelectionModal;
