import * as React from "react";
import { cn } from "~/lib/utils";

interface EmailPreviewProps {
  activeView: string;
  className?: string;
  html: string;
}

export const EmailPreview = ({
  activeView,
  className,
  html,
}: EmailPreviewProps) => {
  const iframeRef = React.useRef<HTMLIFrameElement>(null);

  React.useEffect(() => {
    const handleResize = () => {
      if (iframeRef.current) {
        const iframeDocument = iframeRef.current.contentDocument;
        if (iframeDocument) {
          const body = iframeDocument.body;
          const htmlFrame = iframeDocument.documentElement;
          const height = Math.max(
            body.scrollHeight,
            body.offsetHeight,
            htmlFrame.clientHeight,
            htmlFrame.scrollHeight,
            htmlFrame.offsetHeight,
          );
          const h = Math.min(height, 700);
          iframeRef.current.style.height = `${h}px`;
        }
      }
    };

    const iframe = iframeRef.current;
    if (iframe) {
      iframe.addEventListener("load", handleResize);

      handleResize();

      return () => {
        iframe.removeEventListener("load", handleResize);
      };
    }
  }, []);

  return (
    <iframe
      className={cn(
        "m-auto flex h-full bg-white rounded-lg",
        activeView === "mobile" ? "w-[360px]" : "w-full",
        className,
      )}
      ref={iframeRef}
      srcDoc={html}
      title="Component preview"
    />
  );
};

export default EmailPreview;
