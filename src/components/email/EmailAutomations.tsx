import { useFieldArray, useForm } from "react-hook-form";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import React, { useEffect } from "react";
import { Form, FormControl } from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  AUTOMATIONS_SCHEMA,
  AutomationsBody,
  useEmailAutomations,
  useUpdateEmailAutomations,
} from "~/query/emailAutomations";
import { EmailAction } from "@prisma/client";
import { yupResolver } from "@hookform/resolvers/yup";
import SettingSwitch from "~/components/settings/SettingSwitch";
import EmailTemplateCombo from "~/components/email/EmailTemplateCombo";
import FormItem from "~/components/FormLayout/FormItem";
import { Button } from "~/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { formatDuration } from "~/server/lib/time";
import { Accordion } from "~/components/ui/accordion";

const actionMapping: Record<
  EmailAction,
  {
    label: string;
    description: string;
    minutesAfterConfig?: {
      timeOptions: number[];
      defaultValue: number;
      format: string;
    };
    hide?: boolean;
  }
> = {
  RECEIPT_EMAIL: {
    label: "Receipt Email",
    description: "Sent to a customer immediately after a payment has been made",
  },
  ORDER_FOLLOW_UP: {
    label: "Follow Up Email",
    description:
      "Sent to a customer after their order has ended to ask for feedback.",
    minutesAfterConfig: {
      timeOptions: [240, 1200, 1440, 2880],
      defaultValue: 1200,
      format: "%s after order end.",
    },
  },
  ORDER_REMINDER: {
    label: "Upcoming Order Reminder",
    description:
      "Sent a customer ahead of their rental start date reminding them of their order.",
    minutesAfterConfig: {
      timeOptions: [180, 1440, 2880],
      defaultValue: 1440,
      format: "%s before order start.",
    },
  },
  ANNIVERSARY_DATE: {
    label: "Anniversary Reminder",
    description:
      "Marketing email sent to a customer on the anniversary of their first order.",
    hide: true,
  },
  ORDER_UPDATE: {
    label: "Update Notification",
    description: "Notifies the customer of any changes to their order.",
  },
  ORDER_CANCELLATION: {
    label: "Cancellation Notification",
    description: "Sent to a customer when their order is cancelled.",
  },
  ABANDON_CART: {
    label: "Abandoned Cart",
    description: "Sent to a customer when they have an abandoned cart.",
    minutesAfterConfig: {
      timeOptions: [10, 30, 60, 120, 480, 1440],
      defaultValue: 30,
      format: "%s after cart abandonment.",
    },
  },
};

const EmailAutomations = () => {
  const automations = useEmailAutomations();
  const updateAutomations = useUpdateEmailAutomations();
  const form = useForm<AutomationsBody>({
    resolver: yupResolver(AUTOMATIONS_SCHEMA),
    defaultValues: {
      automations: Object.values(EmailAction).map((item) => ({
        action: item,
        enabled: false,
        minutesAfter: 0,
        emailTemplateId: -1,
      })),
    },
    mode: "onChange",
  });
  const { fields } = useFieldArray({
    control: form.control,
    name: "automations",
  });

  useEffect(() => {
    if (automations.data) {
      form.reset({ automations: automations.data });
    }
  }, [automations.data]);

  const onSubmit = (data: AutomationsBody) => {
    updateAutomations.mutate(data);
  };

  return (
    <Form {...form} onSubmit={onSubmit}>
      <FormLayout>
        <FormLayoutSection>
          <Card className={"max-w-xs md:max-w-3xl"}>
            <CardHeader className="pb-4">
              <CardTitle>Order Automations</CardTitle>
              <CardDescription>
                Email automations that are triggered when an order is created.
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0 pb-4">
              <Accordion className={"w-full"} type={"multiple"}>
                {fields.map((field, index) => {
                  const actionConfig =
                    actionMapping[field.action as EmailAction];
                  if (actionConfig.hide === true) {
                    return null;
                  }
                  return (
                    <div className={"mt-2"} key={field.id}>
                      <SettingSwitch
                        settingName={`automations.${index}.enabled`}
                        settingLabel={actionConfig.label}
                        settingDescription={actionConfig.description}
                      >
                        <EmailTemplateCombo
                          name={`automations.${index}.emailTemplateId`}
                        />
                        {actionConfig.minutesAfterConfig && (
                          <FormItem
                            label={"Send Delay"}
                            name={`automations.${index}.minutesAfter`}
                            render={({ field }) => {
                              return (
                                <Select
                                  onValueChange={(value) =>
                                    field.onChange(Number(value))
                                  }
                                  value={field.value.toString()}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Time Delay" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {actionConfig?.minutesAfterConfig?.timeOptions?.map(
                                      (role) => (
                                        <SelectItem
                                          key={role}
                                          value={role.toString()}
                                        >
                                          {actionConfig?.minutesAfterConfig?.format.replace(
                                            "%s",
                                            role === 0
                                              ? "Immediately"
                                              : formatDuration(role * 60_000),
                                          )}
                                        </SelectItem>
                                      ),
                                    )}
                                  </SelectContent>
                                </Select>
                              );
                            }}
                          />
                        )}
                      </SettingSwitch>
                    </div>
                  );
                })}
              </Accordion>
            </CardContent>
            <CardFooter className="pt-0">
              <Button variant={"primary"} type={"submit"}>
                Save
              </Button>
            </CardFooter>
          </Card>
        </FormLayoutSection>
      </FormLayout>
    </Form>
  );
};

export default EmailAutomations;
