import { ReactNode } from "react";
import {
  Card as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import { cn } from "~/lib/utils";
import { TrendingDown as TrendingDownIcon, TrendingUp as TrendingUpIcon } from "lucide-react";
import { Badge } from "~/components/ui/badge";

type StatCardProps = {
  positive: boolean | undefined;
  value: number | undefined;
  percentage: string | number | undefined;
  loading: boolean;
  label: string | ReactNode;
  tooltip?: string;
};

const StatCardTitle = (props: StatCardProps) => {
  return (
    <CardHeader className="flex flex-row items-center justify-between space-y-0 py-2 px-4">
      <CardTitle className="text-sm font-medium">
        {props.loading ? (
          <Skeleton className="h-4 w-[250px]" />
        ) : (
          <span>{props.label}</span>
        )}
      </CardTitle>
      {props.loading ? (
        <Skeleton className="h-5 w-16" />
      ) : props.percentage !== undefined && (
        <Badge
          variant={props.positive ? "success" : "danger"}
          className="ml-2 flex items-center"
        >
          {props.positive ? (
            <TrendingUpIcon className="inline-block mr-1 w-3 h-3" />
          ) : (
            <TrendingDownIcon className="inline-block mr-1 w-3 h-3" />
          )}
          <span>{`${props.percentage}%`}</span>
        </Badge>
      )}
    </CardHeader>
  );
};

const StatCardContent = ({
  value,
}: {
  value: ReactNode | string;
}) => {
  return (
    <>
      <div className="text-2xl font-bold">{value}</div>
    </>
  );
};

const StatCard = (props: StatCardProps) => {
  return (
    <ShadCard>
      <StatCardTitle {...props} />
      <CardContent className="px-4 py-2">
        <StatCardContent
          value={
            props.loading ? <Skeleton className="h-8 w-[25px]" /> : props.value
          }
        />
      </CardContent>
    </ShadCard>
  );
};

export default StatCard;
