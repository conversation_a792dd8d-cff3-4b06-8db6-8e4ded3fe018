import {
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
} from "~/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";
import { ChevronRight } from "lucide-react";
import { BasicSidebarItem, SidebarItem } from "~/components/nav/items/types";
import SubSidebarItem from "~/components/nav/items/SubSidebarItem";
import Link from "next/link";
import { useRouter } from "next/router";
import { useState, useEffect } from "react";

export type CollapsibleSidebarItemProps = {
  items?: BasicSidebarItem[];
  action?: {
    onClick: () => void;
    label: string | React.ReactNode;
  };
  newItem?: boolean;
} & SidebarItem;

const CollapsibleSidebarItem = (item: CollapsibleSidebarItemProps) => {
  const router = useRouter();
  const [open, setOpen] = useState(item.isActive || false);

  // Check if the current path matches the item URL or if the item URL is a parent path
  const isCurrentPage = () =>
    router.pathname === item.url ||
    (item.url !== "/" && router.pathname.startsWith(item.url));

  // Set initial open state based on whether this is the active page
  // We only want this to run once on initial render
  useEffect(() => {
    if (item?.items) {
      const isActive =
        router.pathname === item.url ||
        (item.url !== "/" && router.pathname.startsWith(item.url)) ||
        item?.items?.some((subItem) => router.pathname === subItem.url);

      if (isActive) {
        setOpen(true);
      }
    }
  }, []);

  const handleItemClick = (e: React.MouseEvent) => {
    if (item?.items) {
      e.preventDefault();

      // Check if we need to navigate
      const needsNavigation = !isCurrentPage();

      if (needsNavigation) {
        // If we're navigating to a new page, always ensure the dropdown is open
        setOpen(true);

        // Use setTimeout to ensure the state update happens before navigation
        setTimeout(() => {
          router.push(item.url);
        }, 0);
      } else {
        // Only toggle the dropdown if we're already on the page
        setOpen(!open);
      }
    }
  };

  return (
    <Collapsible
      key={item.title}
      asChild
      open={open}
      onOpenChange={setOpen}
      defaultOpen={item.isActive}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <SidebarMenuButton
          asChild
          tooltip={item.title}
          isActive={item.isActive}
          onClick={item?.items ? handleItemClick : undefined}
        >
          <Link href={item.url}>
            {item.icon && <item.icon className={"size-4"} />}
            {item.title}
          </Link>
        </SidebarMenuButton>
        {item?.items && (
          <CollapsibleTrigger asChild>
            <SidebarMenuAction
              className="data-[state=open]:rotate-90"
              onClick={(e) => {
                // Stop propagation to prevent the parent click handler from firing
                e.stopPropagation();

                // Only toggle the dropdown, never navigate
                setOpen(!open);
              }}
            >
              <ChevronRight />
              <span className="sr-only">Toggle</span>
            </SidebarMenuAction>
          </CollapsibleTrigger>
        )}
        {item?.action && (
          <SidebarMenuAction onClick={item.action.onClick}>
            {item.action.label}
          </SidebarMenuAction>
        )}
        {item?.items && (
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem) => (
                <SubSidebarItem key={subItem.url} {...subItem} />
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        )}
      </SidebarMenuItem>
    </Collapsible>
  );
};

export default CollapsibleSidebarItem;
