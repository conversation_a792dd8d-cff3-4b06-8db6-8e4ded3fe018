import {
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "~/components/ui/sidebar";
import Link from "next/link";
import { BasicSidebarItem } from "~/components/nav/items/types";
import { useRouter } from "next/router";

const SubSidebarItem = (subItem: BasicSidebarItem) => {
  const router = useRouter();
  return (
    <SidebarMenuSubItem key={subItem.title}>
      <SidebarMenuSubButton asChild isActive={router.pathname === subItem.url}>
        <Link href={subItem.url}>{subItem.title}</Link>
      </SidebarMenuSubButton>
    </SidebarMenuSubItem>
  );
};

export default SubSidebarItem;
