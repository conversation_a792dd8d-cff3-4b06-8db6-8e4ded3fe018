import { SidebarMenuButton, SidebarMenuItem } from "~/components/ui/sidebar";
import Link from "next/link";
import { SidebarItem } from "~/components/nav/items/types";

const SimpleSidebarItem = (item: SidebarItem) => {
  return (
    <SidebarMenuItem>
      <SidebarMenuButton size={"sm"} asChild tooltip={item.title}>
        <Link href={item.url}>
          {item.icon && <item.icon className={"size-4"} />}
          {item.title}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
};

export default SimpleSidebarItem;
