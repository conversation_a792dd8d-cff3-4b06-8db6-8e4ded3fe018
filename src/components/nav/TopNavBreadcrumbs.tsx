import { SidebarTrigger } from "~/components/ui/sidebar";
import { Separator } from "~/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../ui/breadcrumb";
import { usePageContext } from "~/providers/PageProvider";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import React, { useMemo } from "react";
import { useRouter } from "next/router";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { useSession } from "next-auth/react";
import { stringPermissionCheck } from "~/pages/api/permissionsUtil";

const CurrentPageBreadcrumb = ({ title }: { href: string; title: string }) => {
  return (
    <BreadcrumbItem>
      <BreadcrumbPage className="font-semibold">{title}</BreadcrumbPage>
    </BreadcrumbItem>
  );
};

const LinkBreadcrumb = ({ href, title }: { href: string; title: string }) => {
  return (
    <BreadcrumbItem>
      <BreadcrumbLink asChild className={"text-primary"}>
        <Link href={href}>{title}</Link>
      </BreadcrumbLink>
      <BreadcrumbSeparator />
    </BreadcrumbItem>
  );
};

const TopNavBreadcrumbs = () => {
  const session = useSession();
  const { history, pageProps } = usePageContext();
  const router = useRouter();

  const isPrimaryActionDisabled = useMemo(() => {
    if (!pageProps?.actionButton) {
      return false;
    }
    if (pageProps.actionButton.permission === undefined) {
      return false;
    }

    const roles = session.data?.user?.roles || [];

    const node = pageProps.actionButton.permission
      .split(".")
      .slice(0, -1)
      .join(".");

    return !stringPermissionCheck(roles, node, "write");
  }, [session, pageProps]);

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 justify-between mr-10 print:hidden">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb className={"gap-0"}>
          <BreadcrumbList className={"gap-1 sm:gap-1"}>
            {[
              {
                title: "Dashboard",
                path: "/",
              },
              ...history,
            ].map((item, index) => {
              const isCurrent = index === history.length;
              const Comp = !isCurrent ? LinkBreadcrumb : CurrentPageBreadcrumb;

              return (
                <Comp
                  title={item.title}
                  href={!isCurrent ? item.path : "#"}
                  key={item.title}
                />
              );
            })}
          </BreadcrumbList>
        </Breadcrumb>
        <DropdownMenu>
          <DropdownMenuTrigger
            className={cn("flex items-center gap-1", {
              hidden: !pageProps?.secondaryActions?.length,
              "print:hidden": true,
            })}
            asChild={true}
          >
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 data-[state=open]:bg-accent"
            >
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {pageProps?.secondaryActions?.map((action, index) => (
              <DropdownMenuItem
                key={index}
                onSelect={() => {
                  if (action.onClick) {
                    action.onClick();
                  } else {
                    router.push(action.href);
                  }
                }}
              >
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div>
        {pageProps?.actionButton && !isPrimaryActionDisabled && (
          <Button
            variant="primary"
            onClick={pageProps.actionButton.onClick}
            asChild={pageProps?.actionButton?.href !== undefined}
          >
            <Link href={pageProps.actionButton.href ?? "#"}>
              {pageProps.actionButton.label}
            </Link>
          </Button>
        )}
        {pageProps?.actionButton && isPrimaryActionDisabled && (
          <Button variant="secondary" disabled>
            {pageProps.actionButton.label}
          </Button>
        )}
      </div>
    </header>
  );
};

export default TopNavBreadcrumbs;
