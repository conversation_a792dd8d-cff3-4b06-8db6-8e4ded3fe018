"use client";

import {
  <PERSON><PERSON>hart,
  MailIcon,
  Monitor,
  PackageOpen,
  PhoneIcon,
  Plus,
  Settings2,
  ShoppingCart,
  TicketIcon,
  Truck,
  Users,
} from "lucide-react";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
} from "~/components/ui/sidebar";
import CollapsibleSidebarItem, {
  CollapsibleSidebarItemProps,
} from "./items/CollapsibleSidebarItem";
import { useSession } from "next-auth/react";
import { stringPermissionCheckAll } from "~/pages/api/permissionsUtil";
import { useRouter } from "next/router";
import { useContext } from "react";
import { PhoneContext } from "~/providers/phone/PhoneProvider";

type NavData = {
  navMain: (CollapsibleSidebarItemProps & { permission?: string })[];
  navAdmin: (CollapsibleSidebarItemProps & { permission: string })[];
};

const data: NavData = {
  navMain: [
    {
      title: "Orders",
      url: "/orders",
      icon: ShoppingCart,
    },
    {
      title: "Reach",
      url: "/phone",
      icon: PhoneIcon,
      permission: "com.partyrentalplatform.api.phone.read",
      action: {
        onClick: () => {
          console.log("Phone action");
        },
        label: <Plus />,
      },
    },
    {
      title: "Inventory",
      url: "/products",
      icon: PackageOpen,
      permission: "com.partyrentalplatform.api.product.read",
      items: [
        {
          title: "Products",
          url: "/products",
        },
        {
          title: "Categories",
          url: "/categories",
        },
        {
          title: "Setup Surface",
          url: "/surface",
        },
      ],
    },
    {
      title: "Customers",
      url: "/customers",
      icon: Users,
      permission: "com.partyrentalplatform.api.customer.read",
    },
    {
      title: "Coupons",
      url: "/coupons",
      icon: TicketIcon,
      permission: "com.partyrentalplatform.api.coupons.read",
    },
  ],
  navAdmin: [
    {
      title: "Delivery Manager",
      url: "/orders/driver/calendar",
      icon: Truck,
      permission: "com.partyrentalplatform.api.delivery.read",
    },
    {
      title: "Emails",
      url: "/email",
      permission: "com.partyrentalplatform.api.email.read",
      icon: MailIcon,
      items: [
        {
          title: "Automations",
          url: "/email?tab=automations",
        },
      ],
    },
    {
      title: "Reports",
      url: "/reports",
      icon: BarChart,
      permission: "com.partyrentalplatform.api.reports.read",
      items: [
        {
          title: "Sales Tax",
          url: "/reports/sales-tax-report",
        },
      ],
    },
    {
      title: "Website Builder",
      url: "/website",
      icon: Monitor,
      permission: "com.partyrentalplatform.api.website.read",
    },
    {
      title: "Team",
      url: "/staff",
      icon: Users,
      permission: "com.partyrentalplatform.api.staff.others.write",
    },
    {
      title: "Settings",
      url: "/account",
      icon: Settings2,
      permission: "com.partyrentalplatform.api.account.read",
      items: [
        {
          title: "Schedule",
          url: "/schedule",
        },
        {
          title: "Website Settings",
          url: "/account?tab=website",
        },
        {
          title: "Billing",
          url: "/account?tab=billing",
        },
        {
          title: "Reach",
          url: "/account?tab=voice",
        },
      ],
    },
  ],
};

export function NavMain() {
  const session = useSession();
  const router = useRouter();
  const { hasPhoneService, openOutboundCall } = useContext(PhoneContext);

  const phoneNav = data.navMain.find((item) => item.title === "Reach");

  if (phoneNav) {
    if (hasPhoneService()) {
      phoneNav.action = {
        onClick: openOutboundCall,
        label: <Plus />,
      };
    } else {
      phoneNav.action = undefined;
    }
  }

  const permissiveAdminNav = data.navAdmin.filter((item) =>
    stringPermissionCheckAll(session?.data?.user?.roles || [], item.permission),
  );

  return (
    <>
      <SidebarGroup>
        <SidebarGroupLabel>Platform</SidebarGroupLabel>
        <SidebarMenu>
          {data.navMain.map((item) => (
            <CollapsibleSidebarItem
              key={`mainnav-${item.title}`}
              {...item}
              isActive={
                router.pathname === item.url ||
                item?.items?.some((subItem) => router.pathname === subItem.url)
              }
            />
          ))}
        </SidebarMenu>
      </SidebarGroup>

      {permissiveAdminNav.length > 0 && (
        <SidebarGroup>
          <SidebarGroupLabel>Back Office</SidebarGroupLabel>
          <SidebarMenu>
            {permissiveAdminNav.map((item) => (
              <CollapsibleSidebarItem
                key={item.title}
                {...item}
                isActive={router.pathname === item.url}
              />
            ))}
          </SidebarMenu>
        </SidebarGroup>
      )}
    </>
  );
}
