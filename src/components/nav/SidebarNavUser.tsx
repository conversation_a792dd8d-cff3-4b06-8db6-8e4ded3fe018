"use client";

import {
  Badge<PERSON><PERSON><PERSON>,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
} from "lucide-react";

import { Avatar, AvatarFallback } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "~/components/ui/sidebar";
import { signOut, useSession } from "next-auth/react";
import Link from "next/link";
import React from "react";
import { stringPermissionCheckAll } from "~/pages/api/permissionsUtil";
import {
  getNextPlanLevel,
  planLevelFromString,
} from "~/server/lib/entitlement/types";

type UpgradeItemProps = {
  currentPlan: string | undefined;
  isAdmin: boolean;
};

const UpgradeItem = (props: UpgradeItemProps) => {
  if (!props.isAdmin || !props.currentPlan) {
    return null;
  }

  const currentPlanLevel = planLevelFromString(props.currentPlan || "");
  const nextPlanLevel = getNextPlanLevel(currentPlanLevel);

  if (!nextPlanLevel) {
    return null;
  }

  return (
    <>
      <DropdownMenuItem>
        <Sparkles />
        Upgrade to{" "}
        {nextPlanLevel
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())}
      </DropdownMenuItem>
      <DropdownMenuSeparator />
    </>
  );
};

export function NavUser() {
  const session = useSession();
  const { isMobile } = useSidebar();

  const isAdmin = stringPermissionCheckAll(
    session?.data?.user?.roles || [],
    "com.partyrentalplatform.api.account.write",
  );

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground w-[90%]"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                {/*<AvatarImage src={user.avatar} alt={user.name} />*/}
                <AvatarFallback className="rounded-lg">
                  {session?.data?.user?.name?.substr(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {session?.data?.user.name}
                </span>
                <span className="truncate text-xs">
                  {session?.data?.user.email}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  {/*<AvatarImage src={session?.data?.user?.avatar} alt={user.name} />*/}
                  <AvatarFallback className="rounded-lg">
                    {session?.data?.user?.name?.substr(0, 2)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {session?.data?.user.name}
                  </span>
                  <span className="truncate text-xs">
                    {session?.data?.user.email}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <UpgradeItem
              currentPlan={session?.data?.user?.planLevel}
              isAdmin={isAdmin}
            />
            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href={"/account"}>
                  <BadgeCheck />
                  Account
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={"/account?tab=billing"}>
                  <CreditCard />
                  Billing
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem disabled={true}>
                <Bell />
                Notifications
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <button
                onClick={() => {
                  signOut({
                    callbackUrl: "/auth/login",
                    redirect: true,
                  });
                }}
              >
                <LogOut />
                Log out
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
