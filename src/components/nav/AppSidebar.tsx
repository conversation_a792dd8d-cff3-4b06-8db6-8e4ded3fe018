import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
} from "../ui/sidebar";
import { CalendarDaysIcon, HomeIcon, Search } from "lucide-react";
import { TeamSwitcher } from "~/components/nav/SidebarNavTeamSwitcher";
import { NavMain } from "~/components/nav/SidebarNavMain";
import { NavUser } from "~/components/nav/SidebarNavUser";
import { ComponentProps } from "react";
import SimpleSidebarItem from "~/components/nav/items/SimpleSidebarItem";

const topNavItems = [
  {
    title: "Search",
    icon: Search,
    url: "/",
  },
  {
    title: "Home",
    icon: HomeIcon,
    url: "/",
  },
  {
    title: "Calendar",
    icon: CalendarDaysIcon,
    url: "/orders/calendar",
  },
];

export function AppSidebar({ ...props }: ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" variant={"inset"} {...props}>
      <SidebarHeader>
        <TeamSwitcher />
        <SidebarMenu>
          {topNavItems.map((item) => (
            <SimpleSidebarItem key={item.title} {...item} />
          ))}
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
