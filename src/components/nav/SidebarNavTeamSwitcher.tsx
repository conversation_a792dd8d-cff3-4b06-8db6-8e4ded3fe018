"use client";

import * as React from "react";
import { ChevronsUpDown, Command, Plus } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "~/components/ui/sidebar";
import Image from "next/image";
import { cn } from "~/lib/utils";
import { Team, useAccountSwitcher } from "~/providers/TeamProvider";

export function TeamSwitcher() {
  const teams = useAccountSwitcher();
  const { isMobile } = useSidebar();

  if (!teams) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton>Loading...</SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size={"lg"}
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              {teams.currentTeam.logo ? (
                <Image
                  src={teams.currentTeam.logo}
                  className={"size-8 rounded-lg aspect-square border "}
                  width={48}
                  height={48}
                  alt={"Team logo"}
                />
              ) : (
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <Command className="size-4" />
                </div>
              )}
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {teams.currentTeam.name}
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  {teams.currentTeam.plan}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>

          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Accounts
            </DropdownMenuLabel>
            {teams.teams.length === 0 && (
              <DropdownMenuItem className="gap-2 p-2">
                <div className="font-medium text-muted-foreground">
                  No accounts found
                </div>
              </DropdownMenuItem>
            )}
            {teams.teams.map((team, index) => {
              return (
                <AccountSwitcher
                  org={false}
                  key={team.name}
                  team={team}
                  index={index}
                  setActiveTeam={teams.setCurrentTeam}
                />
              );
            })}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                <Plus className="size-4" />
              </div>
              <div className="font-medium text-muted-foreground">Add team</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

type AccountSwitcherProps = {
  team: Team;
  org: boolean;
  index: number;
  setActiveTeam: (teamId: number) => void;
};

const AccountSwitcher = ({
  team,
  index,
  setActiveTeam,
  org = false,
}: AccountSwitcherProps) => {
  return (
    <DropdownMenuItem
      key={team.name}
      onClick={() => setActiveTeam(team?.id || -1)}
      className={cn("gap-2 p-2", {
        "ml-2 border-l border-rounded-none": org,
      })}
    >
      {team.logo ? (
        <Image
          src={team.logo}
          className={"size-6 rounded-lg aspect-square border "}
          width={24}
          height={24}
          alt={"Team logo"}
        />
      ) : (
        <div className="flex size-6 items-center justify-center rounded-sm border">
          <Command className="size-4" />
        </div>
      )}
      {team?.name?.slice(0, 18)}
      {(team?.name?.length || 0) > 18 ? "..." : ""}
      <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
    </DropdownMenuItem>
  );
};
