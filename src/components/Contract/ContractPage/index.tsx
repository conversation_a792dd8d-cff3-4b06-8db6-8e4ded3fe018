import React, { useRef } from "react";
import { LegalTextPage } from "~/components/Contract/legalText";
import SignaturePad from "react-signature-canvas";
import SignatureCanvas from "react-signature-canvas";
import { toast } from "sonner";
import { Button } from "~/components/ui/button";

type ContractCopyPageProps = {
  companyName: string;
  cleaningFee: string;
  companyState: string;
  bigBox: boolean;
  copy: LegalTextPage;
  onSave?: () => void;
};

const ContractCopyPage = (props: ContractCopyPageProps) => {
  const smallPad = useRef<SignatureCanvas>(null);
  const clear = (pad: React.RefObject<SignatureCanvas>) => {
    pad.current?.clear();
  };

  const trim = (pad: React.RefObject<SignatureCanvas>) => {
    console.log("saved");
    toast.success("Contract Saved");
    if (props.onSave !== undefined) {
      props.onSave();
    }
  };

  const replacements: Record<string, any> = {
    "%{companyName}": props.companyName,
    "%{cleaningFee}": props.cleaningFee,
    "%{companyState}": props.companyState,
  };

  return (
    <div>
      {props.copy.sections.map((section) => {
        let text = section.text;
        Object.entries(replacements).forEach((entry) => {
          text = text.replaceAll(entry[0], entry[1]);
        });
        return (
          <p key={section.title}>
            <span
              key={`title-${section.title}`}
              style={{
                fontWeight: 600,
                fontSize: 18,
                textDecoration: "underline",
              }}
            >
              <strong>{section.title + ": "}</strong>
            </span>
            <span
              key={`body-${section.title}`}
              dangerouslySetInnerHTML={{ __html: text }}
            />
            <br />
            <br />
          </p>
        );
      })}
      <div className={`flex flex-col gap-2`}>
        <p className={"font-bold"}>
          {props.bigBox ? "Lessee Signature: " : "Lessee Initials: "}
        </p>
        <div
          className={`border border-black rounded-md print:hidden ${
            props.bigBox ? "w-[500px]" : "w-[250px]"
          } h-[150px]`}
        >
          <SignaturePad
            canvasProps={{ width: props.bigBox ? 500 : 250, height: 150 }}
            ref={smallPad}
          />
        </div>
        <div className={"flex flex-row no-print print:hidden"}>
          <Button variant={"secondary"} onClick={() => clear(smallPad)}>
            Clear
          </Button>
          <Button
            onClick={() => trim(smallPad)}
            type={"button"}
            variant={"primary"}
          >
            Save
          </Button>
        </div>
      </div>

      <br />
    </div>
  );
};

export default ContractCopyPage;
