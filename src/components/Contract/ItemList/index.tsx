import ItemList, { Column as ItemColumn } from "~/components/ItemList";
import SidewaysItemList, { ColumnPair } from "~/components/SidewaysItemList";
import React from "react";
import {
  ContractSharedItems,
  OrderContractItems,
  OrderPaidItems,
} from "~/components/Contract/Top/types";
import {
  convertFeeTypeToPaymentInfoLineItemName,
  getCurrencyString,
  getCurrencyValue,
  getMinimumDeposit,
} from "~/server/lib/currency";
import { Column, Hr, Row, Section, Text } from "@react-email/components";
import { formatTimeRange } from "~/server/lib/time";

export type ContractItemListProps = {
  contractItems: OrderContractItems[];
  paidItems: OrderPaidItems[];
  contractSharedItems: ContractSharedItems;
  refundAmount?: number;
  email?: boolean;
  cancel?: boolean;
  accountTimezone: string;
  depositPercentage: number | null;
};

const ContractItemList = (props: ContractItemListProps) => {
  const itemTableColumns = [
    {
      name: "Item Name",
      render: (item) => {
        return <span>{item.name}</span>;
      },
    },
    {
      name: "Quantity",
      render: (item) => {
        return <span>{item.quantity}</span>;
      },
    },
    {
      name: "Price",
      render: (item) => {
        return <span>{item.pricePerUnit}</span>;
      },
    },
    {
      name: "Total",
      render: (item) => {
        return <span>{item.total}</span>;
      },
    },
  ] as Array<ItemColumn<OrderContractItems>>;

  let randomItems = [
    {
      name: "Rental Start Date",
      render: (item) => {
        return (
          <span>
            {new Date(item.rentalStartDate).toLocaleString("en-US", {
              timeZone: props.accountTimezone,
            })}
          </span>
        );
      },
    },
    {
      name: "Rental End Date",
      render: (item) => {
        return (
          <span>
            {new Date(item.rentalEndDate).toLocaleString("en-us", {
              timeZone: props.accountTimezone,
            })}
          </span>
        );
      },
    },
  ] as ColumnPair<ContractSharedItems>[];

  if (props.email === true) {
    randomItems = [];
  }

  if (props.contractSharedItems.fees) {
    props.contractSharedItems.fees
      .filter((item) => item.taxable)
      .forEach((fee) => {
        randomItems.push({
          name: `${convertFeeTypeToPaymentInfoLineItemName(fee.type)}${
            fee.name ? `: (${fee.name})` : ""
          }`,
          render: () => {
            return getCurrencyString(fee.amount);
          },
        });
      });
  }

  if (props.contractSharedItems.tax) {
    randomItems.push({
      name: `Tax (${getCurrencyValue(
        props.contractSharedItems.tax.percentage,
      )}%)`,
      render: () => {
        return getCurrencyString(props.contractSharedItems.tax?.amount || 0);
      },
    });
  }

  if (props.contractSharedItems.fees) {
    props.contractSharedItems.fees
      .filter((item) => !item.taxable)
      .forEach((fee) => {
        randomItems.push({
          name: `${convertFeeTypeToPaymentInfoLineItemName(fee.type)}${
            fee.name ? `: (${fee.name})` : ""
          }`,
          render: () => {
            return getCurrencyString(fee.amount);
          },
        });
      });
  }

  if (
    props.contractSharedItems.discounts &&
    props.contractSharedItems.discounts.length > 0
  ) {
    randomItems.push({
      name: "Coupon",
      render: (item) => {
        return getCurrencyString(
          item.discounts
            .filter((item) => item.type === "COUPON")
            .reduce((acc, discount) => {
              let amount = discount.amount;
              if (discount.chargeType === "PERCENTAGE") {
                amount = (item.baseTotal * discount.amount) / 100;
              }
              return acc + amount;
            }, 0) * -1,
        );
      },
    });
  }

  if (props.contractSharedItems.damageWaiver) {
    randomItems.push({
      name: "Rental Protection",
      render: () => {
        return getCurrencyString(
          props.contractSharedItems?.damageWaiver?.amount || 0,
        );
      },
    });
  }
  const due =
    props.contractSharedItems.finalTotal - props.contractSharedItems.totalPaid;

  if (!props.cancel && props.depositPercentage) {
    const depositAmount = getMinimumDeposit(due, props.depositPercentage);
    if (due > depositAmount) {
      randomItems.push({
        name: "Minimum Payment Amount",
        render: () => {
          return getCurrencyString(depositAmount);
        },
      });
    }
  }

  randomItems.push({
    name: "Total",
    render: (item) => {
      return getCurrencyString(item.finalTotal);
    },
  });

  const finalizedSidewaysTable = [...randomItems];

  if (props.paidItems) {
    props.paidItems.forEach((item) => {
      finalizedSidewaysTable.push({
        name: `${item.method === "Tip" ? "Tip" : "Payment"}: ${item.methodId}`,
        render: () => {
          return getCurrencyString(item.amount);
        },
      });
    });
  }

  if (!props.cancel) {
    finalizedSidewaysTable.push({
      name: "Due",
      render: (item) => {
        return getCurrencyString(item.finalTotal - item.totalPaid);
      },
    });
  }

  if (props.refundAmount) {
    finalizedSidewaysTable.push({
      name: "Refund",
      render: () => {
        return `-${getCurrencyString(props.refundAmount!)}`;
      },
    });
  }

  if (props.email === true) {
    return (
      <>
        <Section>
          {props.contractItems.map((item, index) => {
            return (
              <Row key={`${index}-contractItems`}>
                <Column key={`${index}-column-items`}>
                  <Text
                    key={`${index}-text-items`}
                    className={"text-lg  m-0 p-0"}
                  >
                    {item.name}
                  </Text>
                  <Text
                    key={`${index}-qty-items`}
                    className={"text-xs text-gray-400 m-0 p-0"}
                  >
                    Qty {item.quantity} @ {item.pricePerUnit}
                  </Text>
                </Column>

                <Column key={`${index}-column-total`}>
                  <Text
                    key={`${index}-column-total-text`}
                    className={"font-semibold m-0 text-right"}
                  >
                    {item.total}
                  </Text>
                </Column>
              </Row>
            );
          })}
        </Section>
        <Hr className={"mt-[20px]"} />
        <Section>
          <Row>
            <Column className={"table-cell"}>
              <Text className={"text-sm font-medium pr-[10px] text-left"}>
                Party Date
              </Text>
            </Column>
            <Column className={"table-cell"}>
              <Text className={"text-sm font-medium ml-[10px] text-right"}>
                {formatTimeRange(
                  props.contractSharedItems.rentalStartDate,
                  props.contractSharedItems.rentalEndDate,
                  props.accountTimezone,
                )}
              </Text>
            </Column>
          </Row>
          {finalizedSidewaysTable.map((column, index) => {
            return (
              <Row key={`row-${index}`} className={"m-0 p-0"}>
                <Column
                  key={`column-payments-${index}`}
                  className={"table-cell m-0 p-0"}
                >
                  <Text
                    key={`payment-name-${index}`}
                    className={"text-sm font-medium pr-[30px] text-left"}
                  >
                    {column.name}
                  </Text>
                </Column>
                <Column
                  key={`shared-items-${index}`}
                  className={"table-cell text-right m-0 p-0"}
                >
                  <Text
                    key={`rendering-items-${index}`}
                    className={"text-sm font-medium ml-[30px] text-right"}
                  >
                    {column.render(props.contractSharedItems)}
                  </Text>
                </Column>
              </Row>
            );
          })}
        </Section>
        <Hr className={"mt-[10px]"} />
      </>
    );
  }

  return (
    <div>
      <div className={"mt-2"}>
        <ItemList items={props.contractItems} columns={itemTableColumns} />
      </div>
      <div className={"mt-2"}>
        <SidewaysItemList
          items={finalizedSidewaysTable}
          value={props.contractSharedItems}
        />
      </div>
    </div>
  );
};

export default ContractItemList;
