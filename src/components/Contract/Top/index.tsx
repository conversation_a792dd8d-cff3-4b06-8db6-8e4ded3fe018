import React from "react";
import { TopContractProps } from "~/components/Contract/Top/types";
import ContractItemList from "~/components/Contract/ItemList";

const TopContract = (props: TopContractProps) => {
  return (
    <div>
      <div className={"bg-background w-full rounded-md mb-3 print:hidden "}>
        <div className={"flex items-center text-center justify-center w-full"}>
          <h1 className={"text-black font-semibold"}>
            {props.header ?? "CONTRACT"}
          </h1>
        </div>
      </div>
      <div className={"flex"}>
        <div className={"flex flex-col space-x-0 text-xs line-clamp-1"}>
          <p>{`Order #${props.invoiceNumber}`}</p>
          <p>{props.customerName}</p>
          <p>{props.eventAddress.line1}</p>
          <p>{`${props.eventAddress.city}, ${props.eventAddress.state} ${props.eventAddress.postalCode}`}</p>
          {props.phoneNumber && <p>{props.phoneNumber}</p>}
          {props.email && <p>{props.email}</p>}
          {props.surface && <p>{`Setup Surface: ${props.surface}`}</p>}
        </div>
      </div>
      <ContractItemList
        contractItems={props.contractItems}
        paidItems={props.paidItems}
        contractSharedItems={props.contractSharedItems}
        accountTimezone={props.accountTimezone}
        depositPercentage={props.minimumDeposit}
      />
    </div>
  );
};

export default TopContract;
