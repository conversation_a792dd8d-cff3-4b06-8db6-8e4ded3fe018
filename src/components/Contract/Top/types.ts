import { PaymentInfo } from "~/server/lib/currency";
import type { AddressType } from "~/server/lib/location/types";
import { Address } from "@prisma/client";

export type OrderContractItems = {
  name: string;
  quantity: number;
  pricePerUnit: string;
  total: string;
};

export type OrderPaidItems = {
  method: string;
  amount: number;
  methodId?: string;
};

export type ContractSharedItems = {
  rentalStartDate: Date;
  rentalEndDate: Date;
} & PaymentInfo;

export type TopContractProps = {
  header?: string;
  invoiceNumber: string;
  customerName: string;
  eventAddress: AddressType | Address;
  phoneNumber?: string;
  email: string;
  minimumDeposit: number;
  surface?: string;
  contractItems: OrderContractItems[];
  paidItems: OrderPaidItems[];

  contractSharedItems: ContractSharedItems;
  accountTimezone: string;
};
