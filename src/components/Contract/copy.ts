import {
  LegalTextPage,
  LegalTextSection,
} from "~/components/Contract/legalText";

const TERMS_OF_LEASE: LegalTextSection = {
  title: "TERMS OF LEASE",
  text: 'This agreement is made between the Lessee, as detailed below, and %{companyName}, hereinafter referred to as the "Lessor". The Lessee hereby agrees to the terms and conditions outlined in this agreement for the rental of specified equipment.',
  underline: true,
};

const PROPERTY_DESCRIPTION: LegalTextSection = {
  title: "Property Description and Rental Term",
  text: 'The Lessor agrees to rent to the Lessee the items listed in the table above (hereinafter the “Property”). The date(s) listed above named "Rental Start Date" and "Rental End Date" describe the duration of the rental (hereinafter the “Term”).',
  underline: true,
};

const DELIVERY: LegalTextSection = {
  title: "Delivery/Operation/Payments",
  text: "Delivery will be to the address specified by the Lessee. The Lessee grants the Lessor and its employees/contractors the right to enter the specified property for the delivery and return of rented equipment at approximate times. All payments must be made at the time of delivery. No refunds will be made after the equipment has been delivered. Delivery and Pickup may may be completed before or after the date of the event, we guarantee delivery by the start time of the event and pickup after the end time of the event, but we may deliver up to 24 hours before the event and pickup up to 48 hours after the event.",
  underline: true,
};

const CANCELLATION: LegalTextSection = {
  title: "General Rules for Safe Operation",
  text: "The units must be operated over a smooth, compatible surface such as grass or a hard top surface. <strong>Never</strong> operate the unit on rough surfaces like rocks, brick, or glass. The unit must <strong>not</strong> be moved by the lessee after being placed by the Lessor’s staff and <strong>must</strong> be properly anchored at all times. Use of the unit during adverse weather conditions, like high winds or thunderstorms, should cease immediately for safety.",
  underline: true,
};

const SAFETY_RULES: LegalTextSection = {
  title: "Additional Safety Rules",
  text: "Before entering the unit, users must remove shoes, eyeglasses, belt buckles, and any sharp objects. <strong>Never</strong> allow play or entry into a partially inflated/deflated unit. <strong>Always</strong> adhere to the number of riders and rules posted on the unit. <strong>Do not</strong> plug or unplug the motor repeatedly to prevent damage.",
  underline: true,
};

const RISK_RELEASE: LegalTextSection = {
  title: "Risk Assumption and Release",
  text: "The Lessee assumes all risks associated with the use of the Property, acknowledging the inherent dangers involved. The Lessee hereby releases and holds harmless the Lessor from any claims or damages that may arise out of the use of the Property.",
  underline: true,
};

const ADDITIONAL_TERMS: LegalTextSection = {
  title: "Additional Terms of Lease",
  text: `The Lessor is not responsible for bad weather or disruption of electrical service. <strong>Absolutely no</strong> silly string or similar items are permitted in or around the unit at any time. The Lessee will be held liable for any damages and will be responsible for the full replacement value of the Property and/or accessed a %{cleaningFee} cleaning fee if the unit is not determined to be permanently damaged.`,
  underline: true,
};

const HOLD_HARMLESS: LegalTextSection = {
  title: "Hold Harmless Provisions",
  text:
    "Lessee hereby irrevocably and forever releases, holds harmless, and discharges\n" +
    "the Lessor, any of Lessor’s agents, employees, contractors, officers, shareholders, and\n" +
    "representatives of any kind, together with their predecessors, successors, assigns, subsidiaries,\n" +
    "parent companies, affiliates, attorneys, officers, directors, partners, members, and managers, as\n" +
    "well as any other corporation or limited liability companies owned by the Lessor, whether\n" +
    "present or past, from and against any and all causes of action, judgments, claims, liens, rights,\n" +
    "damages, charges, liabilities, and demands of any nature whatsoever, whether known or\n" +
    "unknown, existing, suspected or otherwise, arising out of or directly or indirectly relating to the\n" +
    "rental of the Property for use by Lessee. This Agreement and the release contained herein are\n" +
    "full and final and apply to all unknown and unanticipated damages and/or injuries arising out of\n" +
    "the Lessee’s use of the Property, whether known now or later discovered.",
  underline: true,
};

const DISCLAIMER_OF_WARRANTIES: LegalTextSection = {
  title: "Disclaimer of Warranties",
  text: "The Lessor makes no warranties, express or implied, as to the condition of the Property or its fitness for any particular purpose. The Lessor shall not be liable for any damage or injury to persons or property arising from the use of the Property by the Lessee or any other person. Lessee agrees to <strong>immediately</strong> cease use of the equipment and contact %{companyName} if any of the lease equipment developers any indication defect or improper working conditions. Lessee agrees to use the equipment <strong>at Lessees own risk.</strong>",
  underline: true,
};

const BREACH_INDEMNITY_ARBITRATION: LegalTextSection = {
  title: "Breach/Indemnity/Arbitration",
  text: "In the event of a breach by the Lessee, the Lessee will be responsible for all consequential damages and costs incurred by the Lessor. The agreement is governed by and construed in accordance with the laws of the State of %{companyState}.",
  underline: true,
};

const ENTIRE_AGREEMENT: LegalTextSection = {
  title: "Acknowledgment and Signature",
  text: "BY SIGNING BELOW, I, the Lessee, acknowledge that I have completely read, understood, and agreed to the terms of this lease agreement. I affirmatively state that I understand all the provisions of this agreement and enter into it freely, knowingly, and voluntarily.",
  underline: true,
};

export const MiddleCopy: LegalTextPage = {
  sections: [
    TERMS_OF_LEASE,
    PROPERTY_DESCRIPTION,
    DELIVERY,
    CANCELLATION,
    SAFETY_RULES,
  ],
};

export const BottomCopy: LegalTextPage = {
  sections: [
    RISK_RELEASE,
    ADDITIONAL_TERMS,
    HOLD_HARMLESS,
    DISCLAIMER_OF_WARRANTIES,
    BREACH_INDEMNITY_ARBITRATION,
    ENTIRE_AGREEMENT,
  ],
};
