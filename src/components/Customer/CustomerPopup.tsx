import { Button } from "~/components/ui/button";
import {
  Modal,
  ModalBody,
  ModalClose,
  ModalContent,
  ModalDescription,
  ModalFooter,
  ModalHeader,
  ModalTitle,
} from "~/components/ui/modal";
import { newCustomerSubmission } from "~/pages/customers/new";
import CustomerForm from "~/form/CustomerForm";
import { CustomerResultProps, CustomerValues } from "~/query/customer";
import { useQueryClient } from "@tanstack/react-query";

type CustomerPopupProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (customerId: string, customerValue: CustomerResultProps) => void;
  email?: string;
  initialValues?: CustomerValues;
};

const CustomerPopup = ({
  open,
  setOpen,
  onSubmit,
  email,
  ...props
}: CustomerPopupProps) => {
  const queryClient = useQueryClient();
  return (
    <Modal open={open} onOpenChange={setOpen}>
      <ModalContent className={"h-screen"}>
        <ModalHeader className="text-left">
          <ModalTitle>New Customer</ModalTitle>
          <ModalDescription>
            Create a new customer and attach them to this order.
          </ModalDescription>
        </ModalHeader>
        <ModalBody>
          <CustomerForm
            dialog={true}
            showBottomForm={false}
            initialValues={{
              firstName: "",
              lastName: "",
              points: 0,
              ...props.initialValues,
              email: email || "",
            }}
            onSubmit={(values) => {
              newCustomerSubmission(values).then((response) => {
                if (response) {
                  queryClient.invalidateQueries({
                    queryKey: ["customers"],
                  });
                  onSubmit(response.id, {
                    ...response,
                  });
                  return;
                }
                return;
              });
            }}
          />
        </ModalBody>

        <ModalFooter className="pt-2">
          <Button type="submit" variant={"primary"} form={"custom-form"}>
            Save
          </Button>
          <ModalClose asChild>
            <Button variant="outline">Cancel</Button>
          </ModalClose>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CustomerPopup;
