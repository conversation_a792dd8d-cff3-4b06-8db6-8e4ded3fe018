import React, { useMemo } from "react";
import SimpleSearch from "~/components/Search/SimpleSearch";
import { CustomerResultProps, useCustomers } from "~/query/customer";
import Skeleton from "react-loading-skeleton";
import { displayPrettyPhone } from "~/server/lib/phone";

type CustomerSearchProps = {
  onSubmit: (item: CustomerItemProps | undefined) => void;
  onAddCustomer: (item: string) => void;
  defaultCustomer?: CustomerResultProps;
  selectedCustomerId?: string;
  loading?: boolean;
};

export type CustomerItemProps = {
  value: string;
  label: string;
} & CustomerResultProps;

const CustomerSearch = (props: CustomerSearchProps) => {
  const customers = useCustomers();

  const customerResult: CustomerItemProps[] = useMemo(() => {
    return (
      customers.data?.map((customer) => ({
        id: customer.id,
        firstName: customer.firstName,
        lastName: customer.lastName,
        company: customer.company,
        email: customer.email,
        phoneNumber: customer.phoneNumber,
        value: customer.email.toLowerCase(),
        label: customer.email,
      })) || []
    );
  }, [customers.data]);

  const renderCustomer = ({
    firstName,
    lastName,
    email,
  }: {
    firstName: string;
    lastName: string;
    email: string;
  }) => {
    return (
      <div className="flex flex-col ">
        <p className="text-sm font-medium leading-none">
          {`${firstName} ${lastName}`}
        </p>
        <p className="text-xs leading-none text-muted-foreground">{email}</p>
      </div>
    );
  };

  if (customers.isPending) {
    return <Skeleton height={"32px"} />;
  }

  return (
    <SimpleSearch
      filter={(value: string, search: string) => {
        const customer = customerResult.find(
          (customer) => customer.email.toLowerCase() === value.toLowerCase(),
        );
        if (!value || !search || !customer) return 0; // No match if either is empty

        const sanitizedSearch = search.toLowerCase();
        const fields = [
          customer.firstName,
          customer.lastName,
          customer.firstName && customer.lastName
            ? `${customer.firstName} ${customer.lastName}`
            : null,
          customer.email,
          displayPrettyPhone(customer.phoneNumber ?? ""),
          customer.company,
        ].filter(Boolean) as string[];

        // Check for exact matches first (highest priority)
        for (const field of fields) {
          if (field.toLowerCase() === sanitizedSearch) return 1;
        }

        // Check for starts-with matches (high priority)
        for (const field of fields) {
          if (field.toLowerCase().startsWith(sanitizedSearch)) return 0.8;
        }

        // Check for contains matches (medium priority)
        for (const field of fields) {
          if (field.toLowerCase().includes(sanitizedSearch)) return 0.6;
        }

        // No match
        return 0;
      }}
      items={customerResult}
      creatable={true}
      loading={customers.isPending}
      defaultValue={
        props.defaultCustomer
          ? {
              label: props.defaultCustomer.email,
              value: props.defaultCustomer.email,
              ...props.defaultCustomer,
            }
          : undefined
      }
      addNewText={"Create New Customer"}
      onChange={(item) => {
        props.onSubmit(
          customerResult?.find(
            (i) => i.email.toLowerCase() === item.toLowerCase(),
          ) ?? undefined,
        );
      }}
      onCreate={(value) => props.onAddCustomer(value)}
      selectedItem={customerResult.find(
        (i) => i.id === props.selectedCustomerId,
      )}
      renderItem={(item) => {
        return renderCustomer({
          firstName: item.firstName || "",
          lastName: item.lastName || "",
          email: item.email,
        });
      }}
      clearOnSelect={false}
      placeholder={"Search or create a customer"}
    />
  );
};

export default CustomerSearch;
