import NextLink from "next/link";

type CustomerColumnCellProps = {
  company: string | null;
  firstName: string | null;
  lastName: string | null;
  email: string;
  id: string;
};

const CustomerColumnCell = (props: CustomerColumnCellProps) => {
  let customerName = props.email;
  if (props.firstName || props.lastName) {
    customerName = `${props.firstName} ${props.lastName}`;
  }
  if (props.company) {
    customerName = `${props.company} (${customerName})`;
  }
  return (
    <div>
      <NextLink href={`/customers/${props.id}/`} className={"hover:underline"}>
        {customerName}
      </NextLink>
    </div>
  );
};

export default CustomerColumnCell;
