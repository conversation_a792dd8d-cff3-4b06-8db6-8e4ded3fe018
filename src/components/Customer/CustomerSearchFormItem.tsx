import CustomerPopup from "~/components/Customer/CustomerPopup";
import React from "react";
import { useCustomerResultPop } from "~/query/customer";
import CustomerSearch from "~/components/Customer/CustomerSearch";

type CustomerSearchFormItemProps = {
  onCustomerChange: (customerId: string) => void;
  customerId: string | undefined;
};

const CustomerSearchFormItem = (props: CustomerSearchFormItemProps) => {
  const [customerId, setCustomerId] = React.useState<string | undefined>(
    props.customerId,
  );

  const customerData = useCustomerResultPop(customerId);
  const [popupOpen, setPopupOpen] = React.useState<boolean>(false);
  const [customerEmail, setCustomerEmail] = React.useState<
    string | undefined
  >();

  const changeCustomer = (custId: string) => {
    setCustomerId(custId);
    props.onCustomerChange(custId);
  };

  return (
    <>
      <CustomerPopup
        open={popupOpen}
        setOpen={(value) => {
          if (!value) {
            setPopupOpen(false);
          }
        }}
        email={customerEmail}
        onSubmit={(customerId, customerValue) => {
          changeCustomer(customerId);
          setPopupOpen(false);
        }}
      />
      <CustomerSearch
        loading={customerData?.isPending}
        defaultCustomer={customerData?.data ?? undefined}
        onSubmit={(item) => {
          changeCustomer(item?.id ?? "");
        }}
        selectedCustomerId={customerId}
        onAddCustomer={(item) => {
          setPopupOpen(true);
          setCustomerEmail(item);
        }}
      />
    </>
  );
};

export default CustomerSearchFormItem;
