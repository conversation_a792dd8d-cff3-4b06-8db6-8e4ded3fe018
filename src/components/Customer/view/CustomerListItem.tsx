import React from "react";
import { LucideIcon } from "lucide-react";

interface ListItemProps {
  icon: LucideIcon;
  title: string;
  date: string;
  description?: string;
  action?: React.ReactNode;
}
export function CustomerListItem({
  icon: Icon,
  title,
  date,
  description,
  action,
}: ListItemProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-3">
        <Icon className="w-5 h-5 text-gray-500" />
        <div>
          <p className="font-medium">{title}</p>
          <p className="text-sm text-gray-500">{date}</p>
          {description && <p className="text-sm mt-1">{description}</p>}
        </div>
      </div>
      {action}
    </div>
  );
}
