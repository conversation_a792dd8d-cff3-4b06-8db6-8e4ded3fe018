import React, { useMemo, useState } from "react";
import { ChevronDown } from "lucide-react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { Skeleton } from "~/components/ui/skeleton";
import NextLink from "next/link";

interface Order {
  id: number;
  date: string;
  amount: string;
  status: string;
}
interface OrdersSectionProps {
  orders: Order[];
  isLoading: boolean;
}
export function CustomerOrdersSection({
  orders,
  isLoading,
}: OrdersSectionProps) {
  const [showAll, setShowAll] = useState<boolean>(false);
  const hasMoreOrders = orders.length > 5;

  const displayedOrders = useMemo(() => {
    return (
      isLoading
        ? Array.of(undefined, undefined, undefined)
        : showAll
          ? orders
          : orders.slice(0, 5)
    ).map((order) => {
      if (isLoading || !order) {
        return {
          id: <Skeleton className="h-4 w-24" />,
          date: <Skeleton className="h-4 w-24" />,
          amount: <Skeleton className="h-4 w-20" />,
          status: <Skeleton className="h-6 w-16" />,
        };
      }
      return {
        id: (
          <NextLink
            href={"/orders/[id]"}
            as={`/orders/${order.id}`}
            className={"hover:underline"}
          >
            #{order.id.toString().padStart(6, "0")}
          </NextLink>
        ),
        date: order.date,
        amount: order.amount,
        status: <Badge>{order.status}</Badge>,
      };
    });
  }, [orders, showAll, isLoading]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">Recent Orders</CardTitle>
      </CardHeader>
      <CardContent className="relative px-4 py-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order ID</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayedOrders.map((order, index) => (
              <TableRow key={index}>
                <TableCell>{order.id}</TableCell>
                <TableCell>{order.date}</TableCell>
                <TableCell>{order.amount}</TableCell>
                <TableCell>{order.status}</TableCell>
              </TableRow>
            ))}
            {!isLoading && orders.length === 0 && (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  No orders found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        {hasMoreOrders && !showAll && (
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white to-transparent flex items-end justify-center pb-4">
            <Button
              variant="secondary"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => setShowAll(true)}
            >
              <ChevronDown className="w-4 h-4" />
              Show All Orders
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default CustomerOrdersSection;
