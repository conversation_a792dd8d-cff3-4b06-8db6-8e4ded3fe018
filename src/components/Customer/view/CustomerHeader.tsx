import React from "react";
import {
  AlertTriangle,
  Building2,
  Mail,
  Phone,
  Receipt,
  StickyNote,
} from "lucide-react";
import { cn } from "~/lib/utils";
import { Badge } from "~/components/ui/badge";
import { Skeleton } from "~/components/ui/skeleton";
import { displayPrettyPhone } from "~/server/lib/phone";

interface CustomerHeaderProps {
  loading?: boolean;
  name: string;
  email: string;
  phoneNumber?: string | null;
  company?: string | null;
  reference?: string | null;
  internalNotes?: string | null;
  isBanned: boolean;
  isTaxExempt: boolean;
}
export function CustomerHeader({
  loading,
  name,
  email,
  phoneNumber,
  company,
  reference,
  internalNotes,
  isBanned,
  isTaxExempt,
}: CustomerHeaderProps) {
  return (
    <div className={cn("border-b", isBanned ? "bg-red-50" : "bg-white")}>
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-semibold">
                {loading ? <Skeleton className="w-32 h-6" /> : name}
              </h1>
              {isBanned && (
                <Badge
                  variant="destructive"
                  className="flex items-center gap-1"
                >
                  <AlertTriangle className="w-3 h-3" />
                  Banned
                </Badge>
              )}
            </div>
            {loading ? (
              <Skeleton className="w-32 h-6" />
            ) : (
              <div className="flex items-center gap-2 mt-1 text-gray-600">
                <Mail className="w-4 h-4" />
                <span>{email}</span>
              </div>
            )}
          </div>
          <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
              {isTaxExempt && <Badge variant="success">Tax Exempt</Badge>}
            </div>
            {internalNotes && (
              <div className="flex items-start gap-2 max-w-xs mt-2 text-sm bg-yellow-50 p-2 rounded-md">
                <StickyNote className="w-4 h-4 text-yellow-600 mt-0.5" />
                <p className="text-yellow-800">{internalNotes}</p>
              </div>
            )}
          </div>
        </div>
        {(phoneNumber || company || reference) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            {phoneNumber && (
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{displayPrettyPhone(phoneNumber)}</span>
              </div>
            )}
            {company && (
              <div className="flex items-center gap-2">
                <Building2 className="w-4 h-4 text-gray-500" />
                <span>{company}</span>
              </div>
            )}
            {reference && (
              <div className="flex items-center gap-2">
                <Receipt className="w-4 h-4 text-gray-500" />
                <span>Reference: {reference}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
