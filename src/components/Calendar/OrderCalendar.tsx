import React from "react";
import MonthEventCalendar from "~/components/DatePicker/MonthEventCalendar";
import { Badge } from "~/components/ui/badge";
import FormCard from "~/components/FormLayout/FormCard";
import { useOrderCalendar } from "~/query/order";
import { cn } from "~/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";

export type OrderDate = {
  date: Date;
  schedule?: OrderSchedule;
  ordersStarting: number;
  ordersEnding: number;
  ordersOut: number;
};

export type OrderSchedule = {
  activeSchedules: number;
  openTime: Date | null;
  closeTime: Date | null;
  closed: boolean;
};

type OrderCalendarProps = {
  date: Date;
  month?: Date;
  small?: boolean;
  onDateChange: (date: Date) => void;
  onClick: (date: Date) => void;
};

const OrderCalendar = (props: OrderCalendarProps) => {
  const { data } = useOrderCalendar(props.month ?? props.date);

  return (
    <div>
      <FormCard>
        <MonthEventCalendar
          initialFocus
          month={props.month ?? props.date}
          onMonthChange={(date) => {
            props.onDateChange(date);
          }}
          selected={props.small == true ? props.date : undefined}
          footer={
            <div className="flex flex-row justify-end w-full gap-2 pt-1">
              <Badge className="bg-green-200 text-green-800">
                Orders Starting
              </Badge>
              <Badge className="bg-red-200 text-red-800">Orders Ending</Badge>
              <Badge className="bg-blue-200 text-blue-800">Orders Out</Badge>
            </div>
          }
          components={{
            IconLeft: ({ ...props }) => <ChevronLeft className="h-6 w-6" />,
            IconRight: ({ ...props }) => <ChevronRight className="h-6 w-6" />,
            DayContent: ({ date }) => {
              const eventsOnDay = data?.find((event) => {
                return event.date.toDateString() === date.toDateString();
              });
              const startingCount = eventsOnDay?.ordersStarting || 0;
              const endingCount = eventsOnDay?.ordersEnding || 0;
              const outCount = eventsOnDay?.ordersOut || 0;

              return (
                <div
                  className={cn("flex flex-col justify-between h-full w-full", {
                    "lg:flex-row": props.small !== true,
                  })}
                >
                  <div
                    className={cn("flex flex-row justify-center", {
                      "lg:justify-start": props.small !== true,
                    })}
                  >
                    <p>{date.getDate()}</p>
                  </div>
                  <div className={"flex flex-col justify-end items-center"}>
                    {startingCount > 0 && (
                      <Badge className="bg-green-200 text-green-800">
                        {startingCount}
                      </Badge>
                    )}
                    {endingCount > 0 && (
                      <Badge className="bg-red-200 text-red-800">
                        {endingCount}
                      </Badge>
                    )}
                    {outCount > 0 && (
                      <Badge className="bg-blue-200 text-blue-800">
                        {outCount}
                      </Badge>
                    )}
                  </div>
                </div>
              );
            },
          }}
          onSelect={(singleDateSelector) => {
            if (!singleDateSelector) {
              return;
            }

            props.onClick(singleDateSelector);
          }}
          mode={"single"}
        />
      </FormCard>
    </div>
  );
};

export default OrderCalendar;
