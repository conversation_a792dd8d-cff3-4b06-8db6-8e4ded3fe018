import React from "react";
import { cn } from "~/lib/utils";

interface ToggleGroupMiniProps {
  items: {
    label: string;
    value: string;
    icon?: React.ReactNode;
    isActive: boolean;
    onClick: () => void;
  }[];
  ariaLabel?: string;
  size?: "default" | "icon";
}

export function ToggleGroupMini({
  items,
  ariaLabel,
  size = "default",
}: ToggleGroupMiniProps) {
  return (
    <div
      className={cn("flex gap-1", size === "icon" && "justify-start gap-2")}
      role="radiogroup"
      aria-label={ariaLabel}
    >
      {items.map((item) => (
        <button
          key={item.value}
          onClick={item.onClick}
          type="button"
          aria-pressed={item.isActive}
          className={cn(
            "rounded-md border text-xs transition-colors anim",
            size === "icon"
              ? "w-6 h-6 flex items-center justify-center"
              : "px-3 py-1.5 flex-1 capitalize",
            item.isActive
              ? "bg-primary text-white border-primary shadow-sm hover:bg-primary/90"
              : "bg-muted/70 text-muted-foreground border border-transparent hover:bg-muted hover:text-muted-foreground/80",
          )}
        >
          {item.icon ?? item.label}
        </button>
      ))}
    </div>
  );
}
