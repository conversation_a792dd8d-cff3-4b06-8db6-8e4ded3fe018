import {
  FormDescription,
  FormField,
  FormItem as FormValueItem,
} from "~/components/ui/form";
import { Switch } from "~/components/ui/switch";
import React from "react";
import { useFormContext } from "react-hook-form";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";

type SettingSwitchProps = {
  settingName: string;
  settingLabel: string | React.ReactNode;
  settingDescription?: string;
  children: React.ReactNode;
};

const SettingSwitch = (props: SettingSwitchProps) => {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={props.settingName}
      render={({ field }) => (
        <AccordionItem
          className={"rounded-lg border p-3 pb-0"}
          value={props.settingName}
        >
          <FormValueItem className="">
            <AccordionTrigger
              className={
                "flex items-start justify-start gap-4 p-3 hover:no-underline"
              }
            >
              <div className={"text-left flex-1"}>
                <h3 className="text-lg font-semibold">{props.settingLabel}</h3>
                {props.settingDescription && (
                  <FormDescription>{props.settingDescription}</FormDescription>
                )}
              </div>

              <Switch
                checked={field.value}
                onCheckedChange={(v) => {
                  field.onChange(v);
                }}
              />
            </AccordionTrigger>
          </FormValueItem>
          <AccordionContent>
            <div
              className={
                "px-6 border-l-2 border-gray-200 grid md:grid-cols-2 mb-2"
              }
            >
              {props.children}
            </div>
          </AccordionContent>
        </AccordionItem>
      )}
    />
  );
};

export default SettingSwitch;
