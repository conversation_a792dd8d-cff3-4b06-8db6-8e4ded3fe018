import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem as FormValueItem,
  FormLabel,
} from "~/components/ui/form";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormItem from "~/components/FormLayout/FormItem";
import { Button } from "~/components/ui/button";
import { Skeleton } from "~/components/ui/skeleton";
import {
  PhoneSettings,
  PhoneSettingsSchema,
  usePhoneSettings,
  useSavePhoneSettings,
} from "~/query/phone";
import { TabsContent } from "~/components/ui/tabs";
import { Switch } from "~/components/ui/switch";
import { Textarea } from "~/components/ui/textarea";
import { usePhone } from "~/providers/phone/PhoneProvider";
import { useStaff } from "~/query/staff/query";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { formatStandardPhone } from "~/server/lib/phone";
import { SlackFormField } from "~/components/SlackFormField";

const PhoneSettingsForm = () => {
  const { phoneInfo } = usePhone();
  const staff = useStaff();
  const { data: phoneSettings, isPending } = usePhoneSettings();
  const savePhoneSettings = useSavePhoneSettings();

  const form = useForm<PhoneSettings>({
    resolver: yupResolver(PhoneSettingsSchema),
    defaultValues: {
      voicemailText: "",
      showCallerId: false,
      forwardIncomingCallsToMobile: false,
      forwardIncomingCallsTo: [],
      forwardIncomingMessagesToMobile: false,
      forwardIncomingMessagesTo: [],
      forwardIncomingMessagesToSlack: false,
    },
  });

  useEffect(() => {
    if (phoneSettings?.settings) {
      form.reset(phoneSettings.settings);
    }
  }, [phoneSettings]);

  const onSubmit = async (data: PhoneSettings) => {
    savePhoneSettings.mutate(data);
  };

  return (
    <TabsContent value={"voice"}>
      <Form {...form} onSubmit={onSubmit}>
        <FormLayout>
          <FormLayoutSection>
            <Card>
              <CardHeader>
                <CardTitle>Voice Settings</CardTitle>
                <CardDescription>
                  <p>Configure general settings related to the phone system</p>
                  <p className={"text-xs"}>{`Current Phone Number: ${
                    phoneInfo?.number ?? "None Selected"
                  }`}</p>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormItem
                  label={"Voicemail Text"}
                  name={"voicemailText"}
                  description={{
                    label:
                      "The text that will be read to callers when they are sent to voicemail.",
                    position: "top",
                  }}
                  render={({ field }) =>
                    isPending ? <Skeleton /> : <Textarea {...field} />
                  }
                />
                <FormField
                  control={form.control}
                  name="mobileClickToCall"
                  render={({ field }) => (
                    <FormValueItem className="rounded-lg border p-4 flex flex-row items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Mobile Click to Call
                        </FormLabel>
                        <FormDescription>
                          When enabled, outbound calls from the mobile browser
                          will be routed through your mobile phone.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormValueItem>
                  )}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Forwarding Settings</CardTitle>
                <CardDescription>
                  Configure how incoming calls and messages are handled
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormLayoutSection>
                  <div className={"rounded-lg border p-4  pb-0"}>
                    <FormField
                      control={form.control}
                      name="forwardIncomingCallsToMobile"
                      render={({ field }) => (
                        <FormValueItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Send Calls to Mobile
                            </FormLabel>
                            <FormDescription>
                              When enabled, incoming calls will be forwarded to
                              a mobile phone as well as the browser.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormValueItem>
                      )}
                    />
                    {form.watch("forwardIncomingCallsToMobile") && (
                      <div
                        className={
                          "px-8 border-l-2 border-gray-200 space-y-5 mb-2"
                        }
                      >
                        <FormField
                          control={form.control}
                          name="showCallerId"
                          render={({ field }) => (
                            <FormValueItem className="flex flex-row items-center justify-between">
                              <div className="space-y-0.5">
                                <FormLabel>Show Caller ID</FormLabel>
                                <FormDescription>
                                  Show the incoming caller ID when forwarding
                                  calls.
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormValueItem>
                          )}
                        />
                        <FormItem
                          label={"Forward To Number"}
                          name={"forwardIncomingCallsTo"}
                          description={{
                            position: "bottom",
                            label: (
                              <div className={"flex flex-col"}>
                                <p>
                                  The number incoming calls will be forwarded
                                  to.
                                </p>
                                <span className={"italic"}>
                                  Your customers will never see this number.
                                </span>
                              </div>
                            ),
                          }}
                          render={({ field }) =>
                            isPending ? (
                              <Skeleton />
                            ) : (
                              <Select
                                value={field.value[0]}
                                onValueChange={(value) => {
                                  field.onChange([value]);
                                }}
                              >
                                <SelectTrigger>
                                  <SelectValue
                                    placeholder={"Select a number"}
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {staff?.data?.staff.map((member) => (
                                    <SelectItem
                                      key={member.id}
                                      value={formatStandardPhone(
                                        member.phoneNumber ?? "",
                                      )}
                                    >
                                      {member.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )
                          }
                        />
                      </div>
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="forwardIncomingMessagesToMobile"
                    render={({ field }) => (
                      <div className={"rounded-lg border p-4 pb-0"}>
                        <FormValueItem className="flex flex-row items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Send Texts to Mobile
                            </FormLabel>
                            <FormDescription>
                              When enabled, incoming messages will be forwarded
                              to a mobile phone as well as the browser.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormValueItem>
                        {field.value && (
                          <div
                            className={
                              "px-8 border-l-2 border-gray-200 space-y-5"
                            }
                          >
                            <FormItem
                              label={"Forward Incoming Messages To"}
                              name={"forwardIncomingMessagesTo"}
                              description={{
                                position: "bottom",
                                label: (
                                  <div className={"flex flex-col"}>
                                    <p>
                                      The number incoming messages will be
                                      forwarded to.
                                    </p>
                                    <span className={"italic"}>
                                      Your customers will never see this number.
                                    </span>
                                  </div>
                                ),
                              }}
                              render={({ field }) =>
                                isPending ? (
                                  <Skeleton />
                                ) : (
                                  <Select
                                    value={field.value}
                                    onValueChange={(value) => {
                                      field.onChange(value);
                                    }}
                                  >
                                    <SelectTrigger>
                                      <SelectValue
                                        placeholder={"Select a number"}
                                      />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {staff?.data?.staff.map((member) => (
                                        <SelectItem
                                          key={member.id}
                                          value={formatStandardPhone(
                                            member.phoneNumber ?? "",
                                          )}
                                        >
                                          {member.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                )
                              }
                            />
                          </div>
                        )}
                      </div>
                    )}
                  />

                  <SlackFormField form={form} isPending={false} />
                </FormLayoutSection>
              </CardContent>
            </Card>
          </FormLayoutSection>
        </FormLayout>
        <div className={"mt-2"}>
          <Button variant={"primary"}>Submit</Button>
        </div>
      </Form>
    </TabsContent>
  );
};

export default PhoneSettingsForm;
