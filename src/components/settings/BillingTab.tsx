import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import { Button } from "~/components/ui/button";
import { toast } from "sonner";
import React from "react";
import { useRouter } from "next/router";
import { AccountSettingsWithStripe } from "~/server/account/types";

type BillingTabProps = {
  planLevel: string | undefined;
  accountSettings: AccountSettingsWithStripe | undefined;
};

const BillingTab = ({ planLevel, accountSettings }: BillingTabProps) => {
  const router = useRouter();
  return (
    <FormLayout>
        <FormLayoutSection style={"half"}>
          <Card>
            <CardHeader>
              <CardTitle>PRP Current Plan</CardTitle>
              <CardDescription>
                View and manage your current plan for Party Rental Platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <span className={"text-lg font-semibold"}>
                    {"Current Plan: "}
                  </span>
                  {planLevel === undefined && <Skeleton className={"h-8 w-3"} />}
                  {planLevel !== undefined && <span>{planLevel}</span>}
                </div>
                <Button
                  variant={"primary"}
                  onClick={() => {
                    toast.promise(
                      fetch("/api/account/upgrade", {}).then(async (response) => {
                        const data = await response.json();
                        router.push(data.url);
                      }),
                      {
                        loading: "Redirecting...",
                        success: "Redirecting...",
                        error: "Failed to redirect.",
                      },
                    );
                  }}
                >
                  Manage Plan
                </Button>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Payment Processing</CardTitle>
              <CardDescription>
                Set up your payment processing for Party Rental Platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <span className={"text-lg font-semibold"}>
                    {`Payment Processor: ${
                      accountSettings?.stripeConfigured
                        ? "Active"
                        : "Not Configured"
                    }`}
                  </span>
                </div>
                <Button
                  variant={"primary"}
                  onClick={() => {
                    if (accountSettings?.stripeConfigured) {
                      toast.success("Redirecting to Stripe...");
                      fetch("/api/backend/stripe/dashboard").then(
                        async (response) => {
                          const data = await response.json();
                          await router.push(data.url);
                        },
                      );
                      return;
                    }
                    toast.success("Redirecting to Stripe...");
                    fetch("/api/backend/signup/stripe").then(async (response) => {
                      const data = await response.json();
                      await router.push(data.url);
                    });
                  }}
                >
                  {accountSettings?.stripeConfigured ? "Dashboard" : "Enroll"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </FormLayoutSection>
      </FormLayout>
  );
};

export default BillingTab;
