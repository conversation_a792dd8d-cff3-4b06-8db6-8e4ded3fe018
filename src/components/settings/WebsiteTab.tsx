import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>it<PERSON>,
} from "~/components/ui/card";
import Image from "next/image";
import { Skeleton } from "~/components/ui/skeleton";
import { LoadingSpinner } from "~/pages/_app";
import { ImageIcon, UploadIcon } from "lucide-react";
import ImageSelectionDialog from "~/components/ImageSelectionDialog";
import { Button } from "~/components/ui/button";
import React, { useEffect, useState } from "react";
import { fullImageUrl } from "~/server/globalTypes";
import { ImageType } from "@prisma/client";
import { toast } from "sonner";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import { Label } from "~/components/ui/label";
import WebsiteCustomCodeForm from "~/form/WebsiteCustomCodeForm";

const WebsiteSettingsTab = () => {
  const [logo, setLogo] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(false);

  const getLogo = async () => {
    setLoading(true);
    const response = await fetch("/api/images/logo");
    const data = await response.json();
    setLogo(fullImageUrl(data.image?.url));
    setLoading(false);
  };

  const writeLogo = async (id: string) => {
    const body = {
      imageId: id,
      classification: ImageType.LOGO,
    };
    const response = fetch("/api/images/classify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    toast.promise(response, {
      loading: "Saving...",
      success: () => {
        response
          .then((a) => a.json())
          .then((data) => {
            setLogo(data.url);
          });
        return "Logo saved";
      },
      error: "Failed to save logo",
    });
  };

  useEffect(() => {
    getLogo();
  }, []);

  return (
    <FormLayout>
        <FormLayoutSection>
          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
              <CardDescription>
                These are key images that are critical to your branding.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Label>Logo</Label>
              <div className="mt-4 flex items-center space-x-4">
                {logo ? (
                  <Image src={logo} width={80} height={80} alt={"Logo"} />
                ) : (
                  <>
                    {loading ? (
                      <Skeleton className={"relative h-12 w-12"}>
                        <div className={"absolute"}>
                          <LoadingSpinner />
                        </div>
                      </Skeleton>
                    ) : (
                      <ImageIcon className="h-12 w-12" />
                    )}
                  </>
                )}

                <div>
                  <ImageSelectionDialog
                    onSelection={(id: string) => {
                      void writeLogo(id);
                    }}
                  >
                    <Button variant="outline">
                      <UploadIcon className="mr-2 h-4 w-4" />
                      Upload Logo
                    </Button>
                  </ImageSelectionDialog>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Recommended size: 80x80 pixels
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <WebsiteCustomCodeForm showSaveButton={true} />
        </FormLayoutSection>
      </FormLayout>
  );
};

export default WebsiteSettingsTab;
