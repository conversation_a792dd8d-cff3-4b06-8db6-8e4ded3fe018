import { useForm } from "react-hook-form";
import { ACCOUNT_SETTINGS, AccountSettings } from "~/server/account/types";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form } from "~/components/ui/form";
import { toast } from "sonner";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import FormGroup from "~/components/FormLayout/FormGroup";
import { Button } from "~/components/ui/button";
import { TabsContent } from "~/components/ui/tabs";
import React, { useEffect, useState } from "react";
import AutoCompleteAddress from "~/components/AutoCompleteAddress";
import { PercentIcon } from "lucide-react";
import { TIMEZONE_LIST } from "~/server/lib/location/types";
import Combobox from "~/components/ui/combo";

type SettingsTabProps = {
  defaultSettings: AccountSettings | undefined;
};

const SettingsTab = ({ defaultSettings }: SettingsTabProps) => {
  const [address, setAddress] = useState<string | undefined>(undefined);

  const form = useForm<AccountSettings>({
    resolver: yupResolver(ACCOUNT_SETTINGS),
    defaultValues: {
      billingAddress: {},
    },
  });

  useEffect(() => {
    if (defaultSettings) {
      form.reset(defaultSettings);
      setAddress(defaultSettings.billingAddress?.line1 ?? "");
    }
  }, [defaultSettings]);

  const onSubmit = async (data: AccountSettings) => {
    const response = await fetch("/api/account/edit", {
      method: "POST",
      body: JSON.stringify(data),
      headers: {
        "Content-Type": "application/json",
      },
    });
    return await response.json();
  };

  return (
    <TabsContent value={"settings"}>
      <Form
        {...form}
        onSubmit={(values) => {
          toast.promise(onSubmit(values), {
            loading: "Saving...",
            success: "Settings saved.",
            error: "Failed to save settings.",
          });
        }}
      >
        <FormLayout>
          <FormLayoutSection>
            <Card>
              <CardHeader>
                <CardTitle>Order Configuration</CardTitle>
                <CardDescription>
                  These items affect the price structure of your orders.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormItem
                  label={"Rental Protection Percentage"}
                  name={"damageWaiverRate"}
                  render={({ field }) => {
                    return <Input {...field} type="number" />;
                  }}
                />
                <FormGroup>
                  <div>
                    <FormItem
                      label={"Free Travel Radius"}
                      name={"freeTravelRadius"}
                      description={{
                        label:
                          "Enter the fee you will charge per mile for travel after the free travel radius.",
                        position: "bottom",
                      }}
                      render={({ field }) => {
                        return <Input {...field} type="number" />;
                      }}
                    />
                  </div>
                  <div>
                    <FormItem
                      label={"Travel Fee Per Mile"}
                      name={"travelFeePerMile"}
                      description={{
                        label:
                          "Enter the fee you will charge per mile for travel after the free travel radius.",
                        position: "bottom",
                      }}
                      render={({ field }) => {
                        return <Input {...field} type="number" />;
                      }}
                    />
                  </div>
                </FormGroup>
                <FormGroup>
                  <FormItem
                    label={"Minimum Order Payment Percentage"}
                    name={"minimumOrderPaymentPercentage"}
                    description={{
                      label:
                        "The percentage of the order offered as the minimum payment to book on the website.",
                      position: "bottom",
                    }}
                    render={({ field }) => {
                      return (
                        <Input
                          {...field}
                          type="number"
                          extraEnd={<PercentIcon size={18} />}
                        />
                      );
                    }}
                  />
                </FormGroup>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  Displayed on your website and used for your customers to
                  contact you.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormItem
                  label={"Business Phone"}
                  name={"businessPhone"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Business Email"}
                  name={"businessEmail"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Google Review Link"}
                  name={"googleReviewLink"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Custom Domain"}
                  name={"customDomain"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Business Timezone"}
                  name={"businessTimezone"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <Combobox
                        placeholder={"Select Timezone"}
                        onChange={field.onChange}
                        value={field.value}
                        items={TIMEZONE_LIST.map((timezone) => {
                          return {
                            label: timezone.replace("/", " ").replace("_", " "),
                            value: timezone,
                          };
                        })}
                      />
                    );
                  }}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Payment Address</CardTitle>
                <CardDescription>
                  Used for for customer billing and payment processing.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className={"pt-3"}>
                  <div className="flex flex-col justify-center pb-3 gap-1">
                    <p className="text-sm font-medium">Line 1</p>
                    <AutoCompleteAddress
                      defaultValue={form.watch("billingAddress")}
                      onValueChange={(search) => {
                        setAddress(search);
                        form.setValue("billingAddress.line1", search);
                      }}
                      value={address}
                      onSelectAddress={(address) => {
                        const formatted = {
                          line1: address.line1,
                          line2: address.line2,
                          city: address.city,
                          state: address.state,
                          postalCode: address.postalCode,
                          country: address.country || "US",
                        };
                        form.setValue("billingAddress", formatted);
                        setAddress(formatted.line1 ?? "");
                      }}
                    />
                  </div>
                  {form.watch("billingAddress.line1") !== "" && (
                    <div>
                      <FormItem
                        label={"Line 2"}
                        name={"billingAddress.line2"}
                        render={({ field }) => {
                          return <Input {...field} />;
                        }}
                      />
                      <FormGroup>
                        <FormItem
                          label={"City"}
                          name={"billingAddress.city"}
                          render={({ field }) => {
                            return <Input {...field} />;
                          }}
                        />
                        <FormItem
                          label={"State"}
                          name={"billingAddress.state"}
                          render={({ field }) => {
                            return <Input {...field} />;
                          }}
                        />
                        <FormItem
                          label={"Zip Code"}
                          name={"billingAddress.postalCode"}
                          render={({ field }) => {
                            return <Input type={"number"} {...field} />;
                          }}
                        />
                      </FormGroup>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </FormLayoutSection>
        </FormLayout>

        <div className={"mt-2"}>
          <Button variant={"primary"}>Submit</Button>
        </div>
      </Form>
    </TabsContent>
  );
};

export default SettingsTab;
