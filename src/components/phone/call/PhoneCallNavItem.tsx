import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { MicOff, PhoneCallIcon, PhoneIcon, PhoneOff } from "lucide-react";
import React, { useContext, useEffect, useMemo } from "react";
import { Button } from "~/components/ui/button";
import { LoadingSpinner } from "~/pages/_app";
import { PhoneContext } from "~/providers/phone/PhoneProvider";

const PhoneCallNavItem = () => {
  const {
    token,
    twilioDevice,
    unregisterDevice,
    requestVoiceToken,
    activeCall,
    openOutboundCall,
  } = useContext(PhoneContext);
  const [muted, setMuted] = React.useState<boolean>(false);
  const [phoneLoading, setPhoneLoading] = React.useState<boolean>(false);

  const phoneEnabled = useMemo(() => {
    return token !== null;
  }, [token]);

  useEffect(() => {
    if (phoneLoading) {
      setPhoneLoading(false);
    }
  }, [token]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={"ghost"} className="relative items-center ml-2">
          {phoneEnabled ? (
            <>
              {twilioDevice?.isBusy ? (
                <div className="absolute right-0 top-0 h-3 w-3 rounded-full bg-yellow-500" />
              ) : (
                <div className="absolute right-0 top-0 h-3 w-3 rounded-full bg-green-500" />
              )}
            </>
          ) : (
            <div className="absolute right-0 top-0 h-3 w-3 rounded-full bg-red-500" />
          )}

          {phoneLoading && (
            <LoadingSpinner className={"absolute right-0 top-0 h-3 w-3"} />
          )}

          <PhoneIcon className={"w-6 h-6"} />
          {activeCall !== null && <p className={"pl-2"}>Ongoing Call</p>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuLabel className={"font-normal"}>{`Voice Status: ${
          phoneEnabled ? "Online" : "Offline"
        }`}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {activeCall === null && (
          <>
            {phoneEnabled && (
              <>
                <DropdownMenuItem
                  onClick={() => {
                    openOutboundCall();
                  }}
                >
                  <PhoneCallIcon className="mr-2 h-4 w-4" />
                  <p>New Call</p>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={() => {
                setPhoneLoading(true);
                if (phoneEnabled) {
                  unregisterDevice();
                } else {
                  requestVoiceToken();
                }
              }}
            >
              {phoneEnabled ? "Disable" : "Enable"} Voice
            </DropdownMenuItem>
          </>
        )}
        {activeCall !== null && (
          <>
            <DropdownMenuItem
              onClick={() => {
                activeCall?.disconnect();
              }}
            >
              <PhoneOff className="w-4 h-4 mr-2" />
              <span>End Call</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setMuted((prev) => {
                  const newVal = !prev;
                  activeCall.mute(newVal);
                  return newVal;
                });
              }}
            >
              {muted ? (
                <MicOff className="mr-2 w-4 h-4 text-red-500" />
              ) : (
                <MicOff className="mr-2 w-4 h-4" />
              )}
              <span>{muted ? "Un-Mute" : "Mute"}</span>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PhoneCallNavItem;
