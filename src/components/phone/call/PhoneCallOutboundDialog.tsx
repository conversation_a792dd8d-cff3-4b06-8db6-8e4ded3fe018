import {
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import React, { useEffect, useState } from "react";
import { cn } from "~/lib/utils";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import { Label } from "~/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Info } from "lucide-react";
import { formatStandardPhone } from "~/server/lib/phone";

type PhoneCallOutboundDialogProps = {
  prefillPhoneNumber?: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  outboundCall: (phoneNumber: string, forwardToMobile: boolean) => void;
  forwardToMobileConfigured: boolean;
};

const PhoneCallOutboundDialog = ({
  open,
  setO<PERSON>,
  outboundCall,
  forwardToMobileConfigured,
  prefillPhoneNumber,
}: PhoneCallOutboundDialogProps) => {
  const [phoneNumber, setPhoneNumber] = useState<string>(
    prefillPhoneNumber || "",
  );
  const [forwardToMobile, setForwardToMobile] = useState<boolean>(
    forwardToMobileConfigured,
  );
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setPhoneNumber(prefillPhoneNumber || "");
  }, [prefillPhoneNumber]);

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Outbound Call</DialogTitle>
        </DialogHeader>
        <Input
          className={cn("mt-4", error && "border-red-500")}
          placeholder={"Phone Number"}
          value={phoneNumber}
          onChange={(e) => {
            setPhoneNumber(e.target.value);
          }}
        />
        {error && <p className={"text-red-500"}>{error}</p>}

        <div className="border-2 border-prpLightGray rounded-md p-1.5 mb-2 flex flex-row items-center justify-between">
          <div className="flex flex-col p-2">
            <div className={"flex flex-row gap-1 items-center "}>
              <Label>Forward To Mobile</Label>
              {!forwardToMobileConfigured && (
                <TooltipProvider delayDuration={0}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className={"size-3 text-muted-foreground"} />
                    </TooltipTrigger>
                    <TooltipContent sideOffset={4}>
                      Forward to Mobile is not configured.
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            <p className={"text-xs text-muted-foreground"}>
              {forwardToMobile
                ? "Call will be placed using your cell phone"
                : "Call will be placed using your browser"}
            </p>
          </div>
          <Switch
            checked={forwardToMobile}
            onCheckedChange={setForwardToMobile}
            disabled={!forwardToMobileConfigured}
          />
        </div>
        <DialogFooter className={"gap-2"}>
          <DialogClose asChild>
            <Button variant={"secondary"} size={"sm"}>
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant={"primary"}
            size={"sm"}
            onClick={() => {
              if (!phoneNumber) {
                setError("Phone number is required");
                return;
              }
              const number = formatStandardPhone(phoneNumber);
              // check if the phone number is valid, if it's close we'll make it valid

              if (!/^\+?[1-9]\d{1,14}$/.test(number)) {
                setError("Invalid phone number");
                return;
              }
              setError(null);
              outboundCall(number, forwardToMobile);
              setOpen(false);
            }}
          >
            {forwardToMobile ? "Call Using Cell" : "Call Using Browser"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PhoneCallOutboundDialog;
