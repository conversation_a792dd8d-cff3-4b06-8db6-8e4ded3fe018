import { AlertDialog, AlertDialogContent } from "~/components/ui/alert-dialog";
import { Button } from "~/components/ui/button";
import { Call } from "@twilio/voice-sdk";
import { useEffect, useMemo, useState } from "react";
import { useCustomer } from "~/query/customer";
import { Skeleton } from "~/components/ui/skeleton";
import { displayPrettyPhone } from "~/server/lib/phone";

type PhoneCallIncomingDialogProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  pendingCall: Call | null;
  onResponse: (call: Call, accepted: boolean) => void;
};

const PhoneCallIncomingDialog = ({
  open,
  setOpen,
  pendingCall,
  onResponse,
}: PhoneCallIncomingDialogProps) => {
  const [customerId, setCustomerId] = useState<string | null>(null);
  const customer = useCustomer(customerId ?? undefined);

  useEffect(() => {
    if (pendingCall?.customParameters?.get("customerId")) {
      setCustomerId(pendingCall.customParameters.get("customerId") ?? null);
    }
    if (!pendingCall) {
      setCustomerId(null);
    }
  }, [pendingCall]);

  const callerId = useMemo(() => {
    if (customer.isPending) {
      return <Skeleton className={"w-24 h-6"} />;
    }
    const incomingCallFrom = pendingCall?.customParameters.get("From");
    if (customer.data) {
      return `${customer.data.firstName} ${
        customer.data.lastName
      } - ${displayPrettyPhone(
        incomingCallFrom ?? customer.data.phoneNumber ?? "",
      )}`;
    }
    if (incomingCallFrom) {
      return displayPrettyPhone(incomingCallFrom);
    }
    return displayPrettyPhone(pendingCall?.parameters.From || "");
  }, [pendingCall, customer]);

  return (
    <AlertDialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen && pendingCall) {
          pendingCall.ignore();
        }
        setOpen(newOpen);
      }}
    >
      <AlertDialogContent>
        {pendingCall && (
          <div className={"text-center"}>
            <h1 className={"text-2xl"}>Incoming Call</h1>
            <h2 className={"text-lg"}>
              <span>{"From "}</span> {callerId}
            </h2>
            <div className={"mt-2 flex flex-row justify-evenly"}>
              <Button
                onClick={() => {
                  pendingCall.accept();
                  setOpen(false);
                  onResponse(pendingCall, true);
                }}
                variant={"primary"}
              >
                Accept
              </Button>
              <Button
                variant={"destructive"}
                onClick={() => {
                  pendingCall.disconnect();
                  setOpen(false);
                  onResponse(pendingCall, false);
                }}
              >
                Reject
              </Button>
            </div>
          </div>
        )}
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default PhoneCallIncomingDialog;
