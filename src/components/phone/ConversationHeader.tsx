import { But<PERSON> } from "~/components/ui/button";
import { PhoneIcon, X } from "lucide-react";
import { displayPrettyPhone } from "~/server/lib/phone";
import React from "react";
import { CustomerDetails } from "~/pages/api/phone/text/[conversationId]";
import { usePhone } from "~/providers/phone/PhoneProvider";

type ConversationHeaderProps = {
  customerDetails: CustomerDetails | undefined;
  setSelectedConversation: (conversationId: number | undefined) => void;
};

const ConversationHeader = (props: ConversationHeaderProps) => {
  const { makeOutboundCall } = usePhone();
  return (
    <div
      className={
        "flex justify-between items-center mb-2 pb-2 w-full border-b pt-4"
      }
    >
      <div className={"flex flex-row items-center gap-2"}>
        <Button
          variant={"ghost"}
          size={"icon"}
          onClick={() => {
            props.setSelectedConversation(undefined);
          }}
        >
          <X className={"h-6 w-6"} />
        </Button>

        <h2 className={"font-semibold text-lg truncate"}>
          {props.customerDetails?.displayName ??
            displayPrettyPhone(props.customerDetails?.phoneNumber ?? "")}
        </h2>
      </div>
      <div className={"flex flex-row"}>
        <Button
          variant={"ghost"}
          size={"sm"}
          onClick={() => {
            if (!props.customerDetails?.phoneNumber) return;
            makeOutboundCall(props.customerDetails?.phoneNumber);
          }}
        >
          <PhoneIcon className={"h-5 w-5"} />
        </Button>
      </div>
    </div>
  );
};

export default ConversationHeader;
