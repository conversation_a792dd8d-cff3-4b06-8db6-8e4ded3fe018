import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Button } from "~/components/ui/button";
import { displayPrettyPhone } from "~/server/lib/phone";
import { ClipboardIcon, PhoneIcon, TextIcon } from "lucide-react";
import { toast } from "sonner";
import React from "react";
import { usePhone } from "~/providers/phone/PhoneProvider";
import { useRouter } from "next/router";

type PhoneNumberDisplayProps = {
  phoneNumber: string;
};

const PhoneNumberDisplay = (props: PhoneNumberDisplayProps) => {
  const { hasPhoneService, makeOutboundCall, isMessagingEnabled } = usePhone();
  const router = useRouter();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={"link"} size={"link"} className={"justify-start mt-2"}>
          {displayPrettyPhone(props.phoneNumber)}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          disabled={!hasPhoneService()}
          onClick={() => {
            if (!props.phoneNumber) {
              return;
            }
            makeOutboundCall(props.phoneNumber);
          }}
        >
          <PhoneIcon className="mr-2 h-4 w-4" />
          Call
        </DropdownMenuItem>
        <DropdownMenuItem
          disabled={!hasPhoneService() || !isMessagingEnabled()}
          onClick={() => {
            toast.success("Copied to Clipboard!");

            router.push("/phone");
            navigator.clipboard.writeText(props.phoneNumber || "");
          }}
        >
          <TextIcon className="mr-2 h-4 w-4" />
          Text
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => {
            toast.success("Copied to Clipboard!");
            navigator.clipboard.writeText(props.phoneNumber || "");
          }}
        >
          <ClipboardIcon className={"mr-2 h-4 w-4"} />
          Copy
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PhoneNumberDisplay;
