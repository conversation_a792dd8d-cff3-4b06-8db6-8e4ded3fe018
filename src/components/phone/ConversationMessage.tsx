import {
  PhoneIncomingIcon,
  PhoneMissedIcon,
  PhoneOutgoingIcon,
} from "lucide-react";
import React from "react";
import { MessageHistory } from "~/pages/phone";
import { SANE_FORMATTER } from "~/server/lib/time";
import Image from "next/image";
import { Modal, ModalContent } from "~/components/ui/modal";

type ConversationMessageProps = React.HTMLAttributes<HTMLDivElement> & {
  conversation: MessageHistory;
};

const ConversationMessage = React.forwardRef<
  HTMLDivElement,
  ConversationMessageProps
>(({ conversation }, ref) => {
  const [imageOpen, setImageOpen] = React.useState<boolean>(false);

  const formatCallMessage = (conversation: MessageHistory) => {
    if (conversation.from === "customer") {
      if (conversation.message === "Missed") {
        return (
          <span className={"flex flex-row gap-1"}>
            <PhoneMissedIcon className={"w-4 h-4 text-red-500"} />
            Missed call
          </span>
        );
      }
      return (
        <span className={"flex flex-row gap-1"}>
          <PhoneIncomingIcon className={"w-4 h-4 text-green-500"} />
          {`Incoming call - ${conversation.message}`}
        </span>
      );
    }
    return (
      <span className={"flex flex-row gap-1"}>
        <PhoneOutgoingIcon className={"w-4 h-4 text-blue-500"} />
        {`Outgoing call - ${conversation.message}`}
      </span>
    );
  };

  return (
    <div
      className={
        "flex flex-col gap-1 " +
        (conversation.from === "customer" ? "items-start" : "items-end")
      }
      ref={ref}
    >
      <Modal
        open={imageOpen}
        onOpenChange={(open) => (!open ? setImageOpen(false) : undefined)}
      >
        <ModalContent>
          {conversation.mediaUrl && (
            <Image
              src={conversation.mediaUrl}
              alt={"media"}
              className={"max-w-full h-full"}
            />
          )}
        </ModalContent>
      </Modal>
      <div
        className={
          "p-2 rounded-lg shadow-sm " +
          (conversation.from === "customer"
            ? "bg-secondary text-secondary-foreground"
            : "bg-primary text-orange-foreground")
        }
      >
        {conversation.call && (
          <p className={"flex flex-row justify-center items-center gap-2"}>
            {formatCallMessage(conversation)}
          </p>
        )}
        {conversation.voicemail && (
          <div>
            <audio controls>
              <source
                src={`/api/phone/voice/${conversation.voicemail.url}/voicemail`}
                type="audio/mpeg"
              />
              Your browser does not support the audio element.
            </audio>

            {conversation?.voicemail?.transcription && (
              <p className={"text-sm"}>{`Transcript: ${
                conversation?.voicemail?.transcription ?? "Unavailable"
              }`}</p>
            )}
          </div>
        )}
        {conversation.mediaUrl && (
          <Image
            src={conversation.mediaUrl}
            alt={"media"}
            className={"max-w-full h-32"}
            onClick={() => setImageOpen(true)}
          />
        )}
        {!conversation.call && conversation.message && (
          <p className={"whitespace-pre-line"}>{conversation.message}</p>
        )}
      </div>
      <p className={"text-xs text-gray-500"}>
        {SANE_FORMATTER.format(new Date(conversation.time))}
      </p>
    </div>
  );
});

export default ConversationMessage;
