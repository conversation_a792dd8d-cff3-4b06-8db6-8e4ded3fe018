import { Input } from "~/components/ui/input";
import { But<PERSON> } from "~/components/ui/button";
import { X } from "lucide-react";
import React from "react";
import { cn } from "~/lib/utils";
import { captureMessage } from "@sentry/nextjs";
import TextMessageInputForm from "~/components/phone/TextMessageInputForm";
import { useCreateNewMessage } from "~/query/phone";
import { formatStandardPhone } from "~/server/lib/phone";

type NewConversationBoxProps = {
  close: () => void;
  selectConversation: (id: number) => void;
  displayTitle: boolean;
  displayClose: boolean;
};

const NewConversationBox = (props: NewConversationBoxProps) => {
  const [phoneNumber, setPhoneNumber] = React.useState<string>("");
  const [error, setError] = React.useState<string | null>(null);
  const [messageError, setMessageError] = React.useState<string | null>(null);
  const createMessageMutation = useCreateNewMessage();
  const sendMessage = async (
    phoneNumber: string,
    message: string,
  ): Promise<boolean> => {
    setError(null);
    setMessageError(null);
    if (!phoneNumber) {
      setError("Phone number is required");
      return false;
    }
    if (!message) {
      setMessageError("Message is required");
      return false;
    }
    const number = formatStandardPhone(phoneNumber);

    if (!/^\+?[1-9]\d{1,14}$/.test(number)) {
      setError("Invalid phone number");
      return false;
    }

    createMessageMutation.mutate(
      {
        phoneNumber: number.toString(),
        message,
      },
      {
        onSuccess: (request) => {
          if (request.error !== undefined || request.id === undefined) {
            setError(request.error || "Failed to send message");
            captureMessage("Failed to send message", {
              extra: {
                error: request.error,
                id: request.id,
              },
            });
            return;
          }
          props.selectConversation(request.id);
        },
      },
    );
    return true;
  };

  return (
    <div className="flex flex-col w-full h-full bg-white rounded-lg">
      <div className="flex items-center justify-between p-4 border-b">
        <div className={"w-full py-2"}>
          {error && <p className={"text-red-500"}>{error}</p>}
          <Input
            type="text"
            placeholder="Enter a phone number..."
            className={cn(
              props.displayClose && "w-1/2",
              error && "border-red-500",
            )}
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
        </div>
        {props.displayClose && (
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={() => {
              props.close();
            }}
          >
            <X />
          </Button>
        )}
      </div>
      {props.displayTitle && (
        <div
          className={
            "flex flex-col gap-2 p-4 bg-white shadow-sm h-[80%] overflow-y-auto justify-center items-center text-gray-400"
          }
        >
          <p>New Message</p>
        </div>
      )}
      {messageError && <p className={"text-red-500"}>{messageError}</p>}
      <TextMessageInputForm
        onSubmit={(message) => {
          return sendMessage(phoneNumber, message);
        }}
        isDisabled={false}
      />
    </div>
  );
};

export default NewConversationBox;
