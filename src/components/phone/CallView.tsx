import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
} from "~/components/ui/sidebar";
import { cn } from "~/lib/utils";
import {
  LocalTabsContent,
  LocalTabsList,
  LocalTabsTrigger,
} from "~/components/ui/local-tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Button } from "~/components/ui/button";
import { Phone } from "lucide-react";
import CustomerInfo from "~/components/phone/customer/CustomerInfo";
import AddCustomerCard from "~/components/phone/customer/AddCustomerCard";
import React, { useContext } from "react";
import { PhoneContext } from "~/providers/phone/PhoneProvider";
import { usePhoneConversation, useRecentCalls } from "~/query/phone";
import CallPhoneListTile from "~/components/phone/sidebar/CallPhoneListTile";

const CallView = () => {
  const { openOutboundCall, makeOutboundCall } = useContext(PhoneContext);
  const [selectedConversation, setSelectedConversation] = React.useState<
    number | undefined
  >(undefined);
  const recentCalls = useRecentCalls();
  const { data } = usePhoneConversation(selectedConversation);

  return (
    <LocalTabsContent value={"calls"}>
      <div
        className={
          "flex flex-col lg:flex-row md:h-[80vh] bg-white rounded-md shadow-sm overflow-hidden border"
        }
      >
        <Sidebar
          variant={"sidebar"}
          collapsible={"none"}
          className={cn("lg:w-1/4 bg-white shadow-sm p-4 border-r w-full", {
            "hidden md:block": selectedConversation !== undefined,
          })}
        >
          <SidebarHeader className="gap-3.5 border-b p-4 coll">
            <div className="flex w-full items-center justify-between">
              <div>
                <LocalTabsList>
                  <LocalTabsTrigger value={"messages"}>
                    Messages
                  </LocalTabsTrigger>
                  <LocalTabsTrigger value={"calls"}>Calls</LocalTabsTrigger>
                </LocalTabsList>
              </div>
              <TooltipProvider delayDuration={0}>
                <div className={"flex flex-row gap-2"}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size={"icon"}
                        variant={"outline"}
                        className={"ml-auto rounded-full p-4"}
                        onClick={openOutboundCall}
                      >
                        <Phone />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent sideOffset={10}>New Call</TooltipContent>
                  </Tooltip>
                </div>
              </TooltipProvider>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup className="px-0">
              <SidebarGroupContent>
                {recentCalls?.data?.map((convo) => (
                  <CallPhoneListTile
                    conversation={convo}
                    onSelectConversation={setSelectedConversation}
                    isSelected={selectedConversation === convo.conversationSid}
                    key={convo.id}
                  />
                ))}
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>
        {selectedConversation !== undefined && (
          <div className={"w-full my-2 mx-3"}>
            {data?.customerDetails?.customerId ? (
              <CustomerInfo customerId={data?.customerDetails?.customerId} />
            ) : (
              <>
                <AddCustomerCard
                  conversationId={selectedConversation}
                  phoneNumber={data?.customerDetails?.phoneNumber}
                />
                <div className={"flex flex-row mt-2"}>
                  <Button
                    onClick={() => {
                      makeOutboundCall(
                        data?.customerDetails?.phoneNumber ?? "",
                      );
                    }}
                  >
                    Call
                  </Button>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </LocalTabsContent>
  );
};

export default CallView;
