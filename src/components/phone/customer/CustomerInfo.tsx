import {
  CheckCircleIcon,
  InfoIcon,
  MailIcon,
  PhoneIcon,
  XCircleIcon,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import React from "react";
import { useCustomer, useCustomerOrders } from "~/query/customer";
import { Skeleton } from "~/components/ui/skeleton";
import { useOrder } from "~/query/order";
import { OrderState } from ".prisma/client";
import Link from "next/link";
import { getCurrencyString } from "~/server/lib/currency";
import PhoneNumberDisplay from "~/components/phone/PhoneNumberDisplay";

type CustomerInfoProps = {
  customerId: string;
};

const CustomerInfo = (props: CustomerInfoProps) => {
  const customerInfo = useCustomer(props.customerId);
  const orders = useCustomerOrders(props.customerId);
  const now = new Date().getTime();
  const activeOrder = useOrder(
    orders?.data
      ?.sort((a, b) => {
        const aToDate = new Date(a.startTime).getTime();
        const bToDate = new Date(b.startTime).getTime();
        return Math.abs(aToDate - now) - Math.abs(bToDate - now);
      })
      .find((order) => order.state === OrderState.ACTIVE)?.id,
  );

  const phoneNumberMarkup = () => {
    if (customerInfo.isPending) {
      return <Skeleton className={"w-12 h-3"} />;
    }

    if (customerInfo.isError) {
      return <span>Error loading customer info</span>;
    }

    if (!customerInfo?.data?.phoneNumber) {
      return <span>No phone number</span>;
    }
    return <PhoneNumberDisplay phoneNumber={customerInfo?.data?.phoneNumber} />;
  };

  const emailMarkup = () => {
    if (customerInfo.isPending) {
      return <Skeleton className={"w-6 h-3"} />;
    }

    if (customerInfo.isError) {
      return <span>Error loading customer info</span>;
    }

    return (
      <span className={"truncate"}>
        {customerInfo?.data?.email ? customerInfo?.data?.email : ""}
      </span>
    );
  };

  const companyMarkup = () => {
    if (customerInfo.isPending) {
      return <Skeleton className={"w-6 h-3"} />;
    }

    if (customerInfo.isError) {
      return <span>Error loading customer info</span>;
    }

    return (
      <span className={"truncate"}>
        {customerInfo?.data?.company ? customerInfo?.data?.company : ""}
      </span>
    );
  };

  const activeOrderMarkup = () => {
    if (activeOrder.isPending) {
      return <Skeleton className={"w-6 h-3"} />;
    }

    if (activeOrder.isError) {
      return <span>Error loading customer orders</span>;
    }

    if (!activeOrder.data?.raw) {
      return <span>No active order</span>;
    }

    const paidOff =
      activeOrder.data.raw.totalPaid >= activeOrder.data.raw.finalTotal;

    return (
      <div>
        <div className="flex justify-between items-center mb-2 overflow-hidden">
          <span className="font-semibold hover:underline">
            <Link
              href={`/orders/${activeOrder.data.raw.id}`}
              target={"_blank"}
            >{`Order #${activeOrder.data.raw.id}`}</Link>
          </span>
          {paidOff ? (
            <span className="text-green-600 flex items-center">
              <CheckCircleIcon className="w-5 h-5 mr-1" />
              Paid
            </span>
          ) : (
            <span className={"text-red-600 flex items-center"}>
              <XCircleIcon className="w-5 h-5 mr-1" />
              Pending
            </span>
          )}
        </div>
        <ul className="list-disc list-inside mb-2">
          {activeOrder.data.order.products.map((product, index) => (
            <li key={index}>{product.name}</li>
          ))}
        </ul>
        <div className="text-sm text-gray-600">
          {`Delivery Date: ${new Date(
            activeOrder.data.raw.startTime,
          ).toDateString()}`}
        </div>
      </div>
    );
  };

  return (
    <div className={"text-sm overflow-y-scroll h-full pt-4"}>
      <div
        className={
          "flex items-center justify-center gap-2 mb-2 pb-2 w-full border-b p-4"
        }
      >
        <InfoIcon className={"h-4 w-4"} />
        <h2 className={"font-semibold text-lg"}>Customer Info</h2>
      </div>
      <div>
        <h3 className={"font-semibold text-lg mb-2"}>
          {customerInfo.isPending ? (
            <Skeleton className={"w-12 h-3"} />
          ) : (
            <>{`${customerInfo?.data?.firstName} ${customerInfo?.data?.lastName}`}</>
          )}
        </h3>
      </div>
      <Card className="bg-white rounded-lg shadow-md mb-6">
        <CardHeader>
          <CardTitle>Contact</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 px-4 py-2">
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center">
              <PhoneIcon className="w-4 h-4 mr-2 text-gray-500" />
              {phoneNumberMarkup()}
            </div>
            <div className="flex items-center">
              <MailIcon className="w-4 h-4 mr-2 text-gray-500" />
              {emailMarkup()}
            </div>
            <div className="flex items-center ml-2">{companyMarkup()}</div>
          </div>
        </CardContent>
      </Card>

      {activeOrder.data && (
        <Card className="bg-white rounded-lg shadow-md mb-6">
          <CardHeader>
            <CardTitle>Active Order</CardTitle>
          </CardHeader>
          <CardContent className="border rounded-lg px-4 py-2 space-y-2">
            {activeOrderMarkup()}
          </CardContent>
        </Card>
      )}

      <Card className="bg-white rounded-lg shadow-md">
        <CardHeader>
          <CardTitle>Order History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Id</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.data
                ?.sort((a, b) => {
                  return b.id - a.id;
                })
                ?.map((item, index) => {
                  return (
                    <TableRow key={index}>
                      <TableCell className={"hover:underline"}>
                        <Link href={`/orders/${item.id}`} target={"_blank"}>
                          {`#${item.id}`}
                        </Link>
                      </TableCell>
                      <TableCell>
                        {new Date(item.startTime).toDateString()}
                      </TableCell>
                      <TableCell>{item.state}</TableCell>
                      <TableCell>
                        {getCurrencyString(item.finalTotal)}
                      </TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerInfo;
