import { Header6 } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { PlusCircle } from "lucide-react";
import React from "react";
import CustomerPopup from "~/components/Customer/CustomerPopup";
import { useQueryClient } from "@tanstack/react-query";
import { PHONE_CONVERSATION_QUERY } from "~/query/phone";

type AddCustomerCardProps = {
  phoneNumber: string | undefined;
  conversationId: number;
};

const AddCustomerCard = (props: AddCustomerCardProps) => {
  const [open, setOpen] = React.useState(false);
  const reactQuery = useQueryClient();
  return (
    <div className={"flex h-full justify-center items-center"}>
      <div
        className={
          "flex flex-col justify-center items-center text-center gap-2"
        }
      >
        <h5 className={Header6}>No Customer Info Found</h5>
        <CustomerPopup
          open={open}
          setOpen={setOpen}
          initialValues={{
            firstName: "",
            lastName: "",
            email: "",
            points: 0,
            phoneNumber: props.phoneNumber,
          }}
          onSubmit={() => {
            reactQuery.invalidateQueries({
              queryKey: [PHONE_CONVERSATION_QUERY, props.conversationId],
            });
          }}
        />
        <Button
          variant={"primary"}
          onClick={() => {
            setOpen(true);
          }}
        >
          <PlusCircle className={"mr-2 w-4 h-4"} />
          New Customer
        </Button>
      </div>
    </div>
  );
};

export default AddCustomerCard;
