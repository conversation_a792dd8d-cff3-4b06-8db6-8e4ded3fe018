import { cn } from "~/lib/utils";
import React from "react";
import { RecentConversation } from "~/query/phone";
import { Avatar, AvatarFallback } from "~/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import { PhoneIcon } from "lucide-react";

type PhoneListTileProps = {
  conversation: RecentConversation;
  onSelectConversation: (id: number) => void;
  isSelected: boolean;
  outbound: boolean;
};

const PhoneListTile = ({
  conversation,
  onSelectConversation,
  outbound,
  isSelected,
}: PhoneListTileProps) => {
  const getInitials = (name: string) => {
    if (name.startsWith("+") || name.startsWith("(")) {
      return <PhoneIcon className={"w-4 h-4"} />;
    }
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };
  return (
    <a
      className={cn(
        "cursor-pointer rounded-md flex flex-col items-start gap-2 whitespace-nowrap border-b p-4 text-xs leading-tight last:border-b-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors",
        {
          "bg-blue-50/50": !conversation.read,
          "bg-sidebar-accent": isSelected,
        },
      )}
      onClick={() => {
        onSelectConversation(conversation.id);
      }}
    >
      <div className="flex gap-3 w-full">
        <Avatar className="h-7 w-7">
          <AvatarFallback>{getInitials(conversation.name)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-1">
            <div className="flex items-center gap-2">
              <span className="font-medium truncate">{conversation.name}</span>
              {!conversation.read && (
                <span className="unread-dot flex-shrink-0" />
              )}
            </div>
            <span className="text-xs text-muted-foreground whitespace-nowrap">
              {formatDistanceToNow(
                new Date(conversation.lastMessageTime) || new Date(),
                { addSuffix: true },
              )}
            </span>
          </div>
          <p className="line-clamp-2 whitespace-break-spaces text-xs text-muted-foreground truncate">
            {outbound && (
              <span className="text-xs bg-muted px-1 rounded mr-1">You:</span>
            )}
            {conversation.lastMessage}
          </p>
        </div>
      </div>
    </a>
  );
};

export default PhoneListTile;
