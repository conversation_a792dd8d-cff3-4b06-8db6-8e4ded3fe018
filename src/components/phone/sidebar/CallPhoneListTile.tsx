import { cn } from "~/lib/utils";
import React from "react";
import { Avatar, AvatarFallback } from "~/components/ui/avatar";
import { formatDistanceToNow, formatDuration } from "date-fns";
import {
  PhoneIcon,
  PhoneIncomingIcon,
  PhoneMissedIcon,
  PhoneOutgoingIcon,
} from "lucide-react";
import { RecentCall } from "~/pages/api/phone/calls";

type PhoneListTileProps = {
  conversation: RecentCall;
  onSelectConversation: (id: number) => void;
  isSelected: boolean;
};

const PhoneListTile = ({
  conversation,
  onSelectConversation,
  isSelected,
}: PhoneListTileProps) => {
  const getInitials = (name: string) => {
    if (name.startsWith("+") || name.startsWith("(")) {
      return <PhoneIcon className={"w-4 h-4"} />;
    }
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getPrefix = () => {
    if (conversation.incoming) {
      if (conversation.answered) {
        return (
          <span className={"flex flex-row gap-1"}>
            <PhoneIncomingIcon className={"w-4 h-4 text-green-500"} />
            {`Incoming call - ${formatDuration({
              seconds: conversation.duration ?? 0,
            })}`}
          </span>
        );
      }
      return (
        <span className={"flex flex-row gap-1"}>
          <PhoneMissedIcon className={"w-4 h-4 text-red-500"} />
          Missed call
        </span>
      );
    }
    return (
      <span className={"flex flex-row gap-1"}>
        <PhoneOutgoingIcon className={"w-4 h-4 text-blue-500"} />
        {`Outgoing call - ${formatDuration({
          seconds: conversation.duration ?? 0,
        })}`}
      </span>
    );
  };
  return (
    <a
      className={cn(
        "cursor-pointer rounded-md flex flex-col items-start gap-2 whitespace-nowrap border-b p-4 text-xs leading-tight last:border-b-0 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors",
        {
          "bg-sidebar-accent": isSelected,
        },
      )}
      onClick={() => {
        onSelectConversation(conversation.conversationSid);
      }}
    >
      <div className="flex gap-3 w-full">
        <Avatar className="h-7 w-7">
          <AvatarFallback>{getInitials(conversation.name)}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-1">
            <div className="flex items-center gap-2">
              <span className="font-medium truncate">{conversation.name}</span>
            </div>
            <span className="text-xs text-muted-foreground whitespace-nowrap">
              {formatDistanceToNow(
                new Date(conversation.createdAt) || new Date(),
                { addSuffix: true },
              )}
            </span>
          </div>
          <p className="line-clamp-2 whitespace-break-spaces text-xs text-muted-foreground truncate">
            {getPrefix()}
          </p>
        </div>
      </div>
    </a>
  );
};

export default PhoneListTile;
