import Page from "~/components/Page";
import { Button } from "~/components/ui/button";
import { toast } from "sonner";
import { useRouter } from "next/router";

const PhoneUpsellPage = () => {
  const router = useRouter();
  const createTwilioAccount = async () => {
    const request = fetch("/api/phone/create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    });
    toast.promise(request, {
      loading: "Creating Phone Account",
      success: () => {
        router.reload();
        return "Phone Account created!";
      },
      error: "Failed to create account.",
    });
  };

  return (
    <Page title={"Customer Communications"}>
      <div className={"flex flex-col items-center justify-center h-full"}>
        <h1 className={"text-3xl font-semibold"}>
          Communicate clearly with your customers anytime, anywhere.
        </h1>
        <p className={"text-lg text-gray-500"}>
          Know your customers and grow your community with clear and constant
          communication.
        </p>
        <div className={"flex flex-col items-center gap-2"}>
          <Button
            variant={"upgrade"}
            size={"lg"}
            onClick={() => {
              createTwilioAccount();
            }}
          >
            Get Started
          </Button>
          <Button
            variant={"outline"}
            size={"lg"}
            onClick={() => {
              // todo marketing site for phone
            }}
          >
            Learn More
          </Button>
        </div>
      </div>
    </Page>
  );
};

export default PhoneUpsellPage;
