import { Textarea } from "~/components/ui/textarea";
import { But<PERSON> } from "~/components/ui/button";
import { CornerDownLeft } from "lucide-react";
import React from "react";
import { cn } from "~/lib/utils";

type TextMessageInputFormProps = {
  onSubmit: (message: string) => Promise<boolean>;
  isDisabled: boolean;
  className?: string;
};

const TextMessageInputForm = (props: TextMessageInputFormProps) => {
  const [messageToSubmit, setMessageToSubmit] = React.useState<string>("");
  const [loading, setLoading] = React.useState<boolean>(false);
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (loading) {
          return;
        }
        setLoading(true);
        props.onSubmit(messageToSubmit).then((status) => {
          setLoading(false);
          if (status) {
            setMessageToSubmit("");
          }
        });
      }}
      className={cn(
        "relative overflow-hidden rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring gap-2 m-2",
        props.className,
      )}
    >
      <Textarea
        name={"body"}
        placeholder={"Type a message"}
        disabled={props.isDisabled || loading}
        className="min-h-12 resize-none border-0 p-3 shadow-none focus-visible:ring-0 focus:ring-0"
        value={messageToSubmit}
        onChange={(e) => setMessageToSubmit(e.target.value)}
      />
      <div className="flex items-center p-3 pt-0">
        <Button
          type="submit"
          size="sm"
          variant={"upgrade"}
          className="ml-auto gap-1.5 md:mb-2"
          disabled={props.isDisabled || loading}
        >
          Send
          <CornerDownLeft className="size-3.5" />
        </Button>
      </div>
    </form>
  );
};

export default TextMessageInputForm;
