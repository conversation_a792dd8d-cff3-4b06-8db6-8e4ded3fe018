import React from "react";

export type A2PMessagingStatus =
  | "draft"
  | "pending-review"
  | "pending-trust-review"
  | "representative_registered"
  | "brand-approval"
  | "approved"
  | "rejected"
  | "suspended";

type PhoneA2PStatusProps = {
  /**
   * Is the phone service enabled at all.
   */
  enabled: boolean;
  /**
   * Is the messaging service started, if so, what status is it in.
   */
  messagingStatus: string | null;

  children?: React.ReactNode;
};

export const A2P_Status: Record<A2PMessagingStatus, string | null> = {
  draft:
    "You started the application process, but it is not complete yet. To submit your application <NAME_EMAIL>.",
  approved: null,
  rejected:
    "We are sorry, but your application to send A2P messages has been rejected. Submit a <NAME_EMAIL> for more information.",
  "pending-review": "Your application is pending review. Please wait.",
  representative_registered:
    "There was an issue submitting your address, <NAME_EMAIL>",
  "pending-trust-review":
    "Your application is pending trust review. Please wait.",
  "brand-approval": "Your application is pending brand approval. Please wait.",
  suspended:
    "Your application has been suspended. Submit a <NAME_EMAIL> for more information.",
};

const PhoneA2PStatus = (props: PhoneA2PStatusProps) => {
  if (!props.enabled) {
    return (
      <div className={"text-center"}>
        <h1 className={"text-2xl font-bold"}>Phone Service Disabled</h1>
        <p>You have not enabled the phone service, please do that first.</p>
      </div>
    );
  }

  if (props.messagingStatus === null) {
    return props?.children || null;
  }
  const value = A2P_Status[props.messagingStatus as A2PMessagingStatus];

  if (value === null) {
    return props?.children || null;
  }

  return (
    <div className={"text-center"}>
      <h1 className={"text-2xl font-bold"}>A2P Messaging Status</h1>
      <p>{value}</p>
    </div>
  );
};

export default PhoneA2PStatus;
