import { Form, FormControl } from "~/components/ui/form";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import FormLayout from "~/components/FormLayout";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import FormGroup from "~/components/FormLayout/FormGroup";
import { object, string } from "yup";
import { useSession } from "next-auth/react";
import { Button } from "~/components/ui/button";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";

export const A2P_JOB_POSITIONS: string[] = [
  "CEO",
  "<PERSON>",
  "<PERSON><PERSON>",
  "General Council",
  "VP",
  "Director",
  "Other",
];

export const A2P_REP_REQUEST_SCHEMA = object().shape({
  business_title: string().required(),
  email: string().email().required(),
  first_name: string().required(),
  last_name: string().required(),
  job_position: string().oneOf(A2P_JOB_POSITIONS).required(),
  phone_number: string().required(),
});

export type A2PRepRegistrationRequest =
  typeof A2P_REP_REQUEST_SCHEMA.__outputType;

type PhoneA2PRepresentativeProps = {
  /**
   * Is the phone service enabled at all.
   */
  enabled: boolean;
  /**
   * Is the messaging service started, if so, what status is it in.
   */
  messagingStatus: string | null;
  next: () => void;
};

const PhoneA2PRepresentative = (props: PhoneA2PRepresentativeProps) => {
  const auth = useSession();
  const form = useForm<A2PRepRegistrationRequest>({
    resolver: yupResolver(A2P_REP_REQUEST_SCHEMA),
    defaultValues: {
      email: auth?.data?.user?.email ?? undefined,
    },
  });

  const submitForm = async (values: A2PRepRegistrationRequest) => {
    const request = await fetch("/api/phone/text/a2p/registerRepresentative", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    if (request.ok) {
      props.next();
    }
  };

  return (
    <Form {...form} onSubmit={submitForm}>
      <FormLayout>
        <FormLayoutSection>
          <Card>
            <CardHeader>
              <CardTitle>Representative Information</CardTitle>
              <CardDescription>
                The representative information for your business that can be
                used for text messaging verification.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormGroup>
                <FormItem
                  label={"First Name"}
                  name={"first_name"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Last Name"}
                  name={"last_name"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
              </FormGroup>
              <FormGroup>
                <FormItem
                  label={"Business Title"}
                  name={"business_title"}
                  description={{
                    position: "bottom",
                    label:
                      "This your exact title in the company (Founder, CEO, Owner, etc...).",
                  }}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Job Position"}
                  name={"job_position"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <Select
                        value={field?.value?.toString() || ""}
                        onValueChange={(value) => {
                          field.onChange(value);
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={"Choose your job title."}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {A2P_JOB_POSITIONS.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </FormGroup>
              <FormGroup>
                <FormItem
                  label={"Phone Number"}
                  name={"phone_number"}
                  description={{
                    position: "bottom",
                    label:
                      "A phone number you can receive text messages on, this will only be used for verification purposes (it will not be shown to customers).",
                  }}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Email"}
                  name={"email"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
              </FormGroup>
            </CardContent>
            <CardFooter>
              <Button type={"submit"} variant={"primary"}>
                Next (2/3)
              </Button>
            </CardFooter>
          </Card>
        </FormLayoutSection>
      </FormLayout>
    </Form>
  );
};

export default PhoneA2PRepresentative;
