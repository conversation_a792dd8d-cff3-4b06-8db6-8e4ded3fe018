import { Form, FormControl } from "~/components/ui/form";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import FormLayout from "~/components/FormLayout";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import FormGroup from "~/components/FormLayout/FormGroup";
import { object, string } from "yup";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import { Button } from "~/components/ui/button";
import PhoneA2PStatus from "~/components/phone/register/PhoneA2PStatus";

export const A2P_REGIONS: string[] = [
  "USA_AND_CANADA",
  "EUROPE",
  "AFRICA",
  "ASIA",
  "LATIN_AMERICA",
];

export const A2P_BUSINESS_TYPES: string[] = [
  "Corporation",
  "Limited_Liability_Corporation",
  "Non-profit_Coporation",
  "Partnership",
  "Co-operative",
];

export const A2P_REQUEST_SCHEMA = object().shape({
  business_name: string().required(
    "You must include the name of your business (including LLC, Inc, etc.)",
  ),
  business_regions_of_operation: string()
    .oneOf(A2P_REGIONS)
    .required("Please select the region of operation."),
  business_registration_number: string()
    .matches(/^\d{2}-\d{7}$/, "Your EIN must match the format 12-3456789")
    .required("A business EIN is required."),
  business_type: string()
    .oneOf(A2P_BUSINESS_TYPES)
    .required("You must select your registered business type."),
  social_media_profile_urls: string().url(),
  website_url: string()
    .url()
    .required("The website your customers visit is required."),
});

export type A2PRegistrationRequest = typeof A2P_REQUEST_SCHEMA.__outputType;

type PhoneA2PFirstProps = {
  /**
   * Is the phone service enabled at all.
   */
  enabled: boolean;
  /**
   * Is the messaging service started, if so, what status is it in.
   */
  messagingStatus: string | null;
  next: () => void;
};

const PhoneA2PFirst = (props: PhoneA2PFirstProps) => {
  const form = useForm<A2PRegistrationRequest>({
    resolver: yupResolver(A2P_REQUEST_SCHEMA),
  });

  const submitForm = async (values: A2PRegistrationRequest) => {
    const request = await fetch("/api/phone/text/a2p/register", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    if (request.ok) {
      props.next();
    } else {
      // router.reload();
    }
  };

  return (
    <PhoneA2PStatus
      messagingStatus={props.messagingStatus}
      enabled={props.enabled}
    >
      <Form
        {...form}
        onSubmit={(values) => {
          submitForm(values);
        }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Business Information</CardTitle>
            <CardDescription>
              Information about your business that can be used for text
              messaging verification.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormLayout>
              <FormLayoutSection>
                <FormGroup>
                  <FormItem
                    label={"Business Name"}
                    name={"business_name"}
                    description={{
                      position: "bottom",
                      label:
                        "This is your legal business name, including LLC, Inc, etc.",
                    }}
                    render={({ field }) => {
                      return <Input {...field} />;
                    }}
                  />
                  <FormItem
                    label={"Business Type"}
                    name={"business_type"}
                    removeFormControl={true}
                    render={({ field }) => {
                      return (
                        <Select
                          value={field?.value?.toString() || ""}
                          onValueChange={(value) => {
                            field.onChange(value);
                          }}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={"Choose a Business Type"}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {A2P_BUSINESS_TYPES.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type.replace("_", " ")}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      );
                    }}
                  />
                </FormGroup>
                <FormGroup>
                  <FormItem
                    label={"Website URL"}
                    name={"website_url"}
                    render={({ field }) => {
                      return <Input {...field} />;
                    }}
                  />
                  <FormItem
                    label={"Social Media Profile URLs"}
                    name={"social_media_profile_urls"}
                    description={{
                      position: "bottom",
                      label:
                        "Please provide 1 social media page for your business. This makes registration easier, but is not required.",
                    }}
                    render={({ field }) => {
                      return <Input {...field} />;
                    }}
                  />
                </FormGroup>
                <FormItem
                  label={"EIN"}
                  name={"business_registration_number"}
                  description={{
                    position: "bottom",
                    label: (
                      <div className={"space-y-2"}>
                        <p>
                          This is the 9 digit number assigned to your business
                          by the IRS, this is required for A2P messaging.
                        </p>
                        <p className={"text-sm font-semibold"}>
                          PRP does not store this information, it is sent to our
                          business partner Twilio for A2P 10DLC verification. If
                          you have any questions about this process please
                          contact <EMAIL>.
                        </p>
                      </div>
                    ),
                  }}
                  render={({ field }) => {
                    return <Input className={"max-w-sm"} {...field} />;
                  }}
                />
                <FormItem
                  label={"Business Location"}
                  name={"business_regions_of_operation"}
                  removeFormControl={true}
                  render={({ field }) => {
                    return (
                      <Select
                        value={field?.value?.toString() || ""}
                        onValueChange={(value) => {
                          field.onChange(value);
                        }}
                      >
                        <FormControl>
                          <SelectTrigger className={"max-w-sm"}>
                            <SelectValue
                              placeholder={
                                "Choose a business operation regions."
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {A2P_REGIONS.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    );
                  }}
                />
              </FormLayoutSection>
            </FormLayout>
          </CardContent>
          <CardFooter>
            <Button type="submit" variant={"primary"}>
              Next (1/3)
            </Button>
          </CardFooter>
        </Card>
      </Form>
    </PhoneA2PStatus>
  );
};

export default PhoneA2PFirst;
