import { Form } from "~/components/ui/form";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import FormLayout from "~/components/FormLayout";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import FormGroup from "~/components/FormLayout/FormGroup";
import { object, string } from "yup";
import { Button } from "~/components/ui/button";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";

export const A2P_ADDRESS_REQUEST_SCHEMA = object().shape({
  city: string().required(),
  customer_name: string().required(),
  iso_country: string().required(),
  postal_code: string().required(),
  region: string().required(),
  street: string().required(),
});

export type A2PAddressRegistrationRequest =
  typeof A2P_ADDRESS_REQUEST_SCHEMA.__outputType;

type PhoneA2pAddressProps = {
  /**
   * Is the phone service enabled at all.
   */
  enabled: boolean;
  /**
   * Is the messaging service started, if so, what status is it in.
   */
  messagingStatus: string | null;
  next: () => void;
};

const PhoneA2pAddress = (props: PhoneA2pAddressProps) => {
  const form = useForm<A2PAddressRegistrationRequest>({
    resolver: yupResolver(A2P_ADDRESS_REQUEST_SCHEMA),
    defaultValues: {
      iso_country: "US",
    },
  });

  const submitForm = async (values: A2PAddressRegistrationRequest) => {
    const request = await fetch("/api/phone/text/a2p/registerAddress", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    });
    if (request.ok) {
      props.next();
    }
  };

  return (
    <Form {...form} onSubmit={submitForm}>
      <FormLayout>
        <FormLayoutSection>
          <Card>
            <CardHeader>
              <CardTitle>Business Address</CardTitle>
              <CardDescription>
                The address of your business that can be used for text messaging
                verification, this address needs to be tied to your business.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormItem
                label={"Business Name"}
                name={"customer_name"}
                description={{
                  position: "bottom",
                  label:
                    "The name of your business as it appears on official documents.",
                }}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormItem
                label={"Address Street"}
                name={"street"}
                render={({ field }) => {
                  return <Input {...field} />;
                }}
              />
              <FormGroup>
                <FormItem
                  label={"City"}
                  name={"city"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"State (Region)"}
                  name={"region"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Postal Code"}
                  name={"postal_code"}
                  render={({ field }) => {
                    return <Input {...field} />;
                  }}
                />
                <FormItem
                  label={"Country"}
                  name={"iso_country"}
                  description={{
                    position: "bottom",
                    label:
                      "The 2-digit country code for your country (Example: US, MX, CA)",
                  }}
                  render={({ field }) => {
                    return <Input maxLength={2} {...field} />;
                  }}
                />
              </FormGroup>
            </CardContent>
            <CardFooter>
              <Button type={"submit"} variant={"primary"}>
                Submit for Verification (3/3)
              </Button>
            </CardFooter>
          </Card>
        </FormLayoutSection>
      </FormLayout>
    </Form>
  );
};

export default PhoneA2pAddress;
