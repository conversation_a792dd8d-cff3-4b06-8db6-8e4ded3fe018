import React, { ReactElement } from "react";
import { Inter } from "next/font/google";
import { cn } from "~/lib/utils";
import { AppSidebar } from "~/components/nav/AppSidebar";
import { SidebarInset, SidebarProvider } from "~/components/ui/sidebar";
import TopNavBreadcrumbs from "~/components/nav/TopNavBreadcrumbs";
import { PageProvider } from "~/providers/PageProvider";
import { TeamProvider } from "~/providers/TeamProvider";
import { TrialBanner } from "~/components/TrialBanner";

const fontSans = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});
const DefaultLayout = (page: ReactElement) => {
  return (
    <section className={cn("bg-muted/25", fontSans.className)}>
      <PageProvider>
        <TeamProvider>
          <SidebarProvider>
            <AppSidebar className={"print:hidden"} />
            <SidebarInset
              className={
                "print:border-0 print:border-none print:shadow-none print:rounded-none print:overflow-visible print:max-w-full print:ml-0 print:block"
              }
            >
              <TrialBanner />
              <TopNavBreadcrumbs />
              <main className={"p-4 pt-0"}>{page}</main>
            </SidebarInset>
          </SidebarProvider>
        </TeamProvider>
      </PageProvider>
    </section>
  );
};

export default DefaultLayout;
