import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { boolean, object } from "yup";
import { Button } from "~/components/ui/button";
import FormDialog from "~/components/Actions/FormDialog";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Switch } from "~/components/ui/switch";
import { Settings } from "lucide-react";

const featureFlagSchema = object().shape({
  aiAutoRoute: object().shape({
    enabled: boolean().required(),
  }),
});

type AutoRouteSettingsFormValues = {
  aiAutoRoute: {
    enabled: boolean;
  };
};

type AutoRouteSettingsModalProps = {
  initialValues?: AutoRouteSettingsFormValues;
  onSubmit: (values: AutoRouteSettingsFormValues) => void;
};

const AutoRouteSettingsModal = ({
  initialValues = { aiAutoRoute: { enabled: false } },
  onSubmit,
}: AutoRouteSettingsModalProps) => {
  const [open, setOpen] = React.useState<boolean>(false);
  const form = useForm<AutoRouteSettingsFormValues>({
    resolver: yupResolver(featureFlagSchema),
    defaultValues: initialValues,
  });

  return (
    <>
      <Button
        variant={"secondary"}
        size={"sm"}
        onClick={() => {
          setOpen(true);
        }}
      >
        <Settings className={"w-4 h-4"} />
      </Button>
      <FormDialog
        open={open}
        setOpen={setOpen}
        title="Auto Route Settings"
        form={
          <Form
            {...form}
            onSubmit={(values) => {
              onSubmit(values);
            }}
          >
            <FormItem
              label="AI Auto Route"
              name="aiAutoRoute.enabled"
              render={({ field }) => (
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              )}
            />
            <div className="flex flex-row justify-end gap-2 mt-4">
              <Button
                variant="secondary"
                type="button"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                onClick={() => {
                  setOpen(false);
                }}
              >
                Save
              </Button>
            </div>
          </Form>
        }
      />
    </>
  );
};

export default AutoRouteSettingsModal;
