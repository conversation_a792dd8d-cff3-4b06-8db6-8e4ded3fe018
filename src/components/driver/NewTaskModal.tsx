import {
  <PERSON>dal,
  ModalBody,
  <PERSON>dal<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>rigger,
} from "~/components/ui/modal";
import LogisticTaskForm from "~/form/LogisticTaskForm";
import { LogisticTaskItem } from "~/components/driver/types";
import React from "react";
import { Button } from "~/components/ui/button";

type NewTaskModalProps = {
  date: Date;
  onSubmit: (task: LogisticTaskItem) => void;
  children: React.ReactNode;
};

const NewTaskModal = (props: NewTaskModalProps) => {
  return (
    <Modal>
      <ModalTrigger asChild={true}>{props.children}</ModalTrigger>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>New Task</ModalTitle>
        </ModalHeader>

        <ModalBody>
          <LogisticTaskForm date={props.date} onSubmit={props.onSubmit} />
        </ModalBody>
        <ModalFooter>
          <ModalClose>
            <Button>Cancel</Button>
          </ModalClose>
          <Button variant={"primary"} type="submit" form={"logistic-form"}>
            Submit
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NewTaskModal;
