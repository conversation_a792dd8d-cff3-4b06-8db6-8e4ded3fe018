import { Address } from "@prisma/client";

/**
 * Data used within the Timeline to determine the stats for the global timeline, not per task.
 *
 * The id is not used.
 */
export type TimelineStats = {
  id: number;
  totalTimeMinutes: number;
  completionTime: Date | null;
};

/**
 * TaskTimeData used within the Timeline post calculation to display the time taken for each task
 */
export type TaskTimeData = {
  color: string;
  distanceFromPreviousLocation: number;
  departFromPreviousLocation: Date;
  arriveAtLocation: Date;
  completeAction: Date;
};

/**
 * This is the actual task item that are used within the Timeline.
 * A version similar to this is stored in the LogisticTimeline in the database, we hydrate the
 * tasks from the database to this format using Orders.
 */
export type LogisticTaskItem = {
  id: string;
  index: number; // The position in the timeline
  displayAction: string; // this is the action to display and should not be used in logic.
  timelineId: string;
  durationMinutes: number;
  durationAffectsTime: boolean;
  actionTimeFlexibilityMinutes: number;
  actionTime: Date;
  orderId: number | null;
  notes: string | null;
  address: Address;
};

/**
 * This is what is sent to the auto route system for a timeline
 */
export type HermesRouteTimeline = {
  id: string;
  start_time: number;
  time_at_depot: number;
  max_time: number;
};

export type HermesRouteTask = {
  id: string;
  taskId: string;
  setup_minutes: number;
  distance_from_depot: number;
  end_time_window: number;
  start_time_window: number;
  raw_time_window: number;
  lvst: number;
  fvst: number;
  distances_to_other_locations: Record<string, number>;
};

export type AIRouteTask = {
  id: string;
  taskId: string;
  setup_minutes: number;
  distance_from_depot: number;
  last_valid_arrival_time: string; // ISO string
  first_valid_arrival_time: string; // ISO string
  distances_to_other_locations: Record<string, number>;
};
