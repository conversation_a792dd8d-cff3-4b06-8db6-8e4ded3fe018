import { Button } from "~/components/ui/button";
import { useForm } from "react-hook-form";
import React from "react";
import { Form } from "~/components/ui/form";
import TimePickerField from "~/components/DatePicker/TimePickerField";
import FormItem from "~/components/FormLayout/FormItem";
import { Card, CardContent, CardFooter } from "~/components/ui/card";
import { MultiSelect } from "~/components/Search/MultiSelect";
import { useStaff } from "~/query/staff/query";

type EditStartTimeFormProps = {
  startTime: Date;
  staff: string[];
  onSubmit: (values: { startTime: Date; staff: string[] }) => void;
};

const EditStartTimeForm = ({
  startTime,
  staff,
  onSubmit,
}: EditStartTimeFormProps) => {
  const { data } = useStaff();

  const form = useForm({
    defaultValues: {
      startTime: startTime,
      staff: staff,
    },
  });

  return (
    <Form {...form} onSubmit={onSubmit}>
      <Card>
        <CardContent>
          <FormItem
            label={""}
            name={"startTime"}
            render={({ field }) => (
              <TimePickerField
                date={new Date(field.value) || startTime}
                setDate={field.onChange}
                showAmPm={true}
                showSeconds={false}
              />
            )}
          />
          <FormItem
            label={"Assigned Staff"}
            name={"staff"}
            render={({ field }) => (
              <MultiSelect
                options={
                  data?.staff?.map((item) => ({
                    label: item.email,
                    value: item.id,
                  })) || []
                }
                modalPopover={true}
                onValueChange={(value) => {
                  field.onChange(value);
                  console.log(value);
                }}
                defaultValue={staff}
                placeholder="Select Staff"
                variant="inverted"
                animation={2}
                maxCount={3}
              />
            )}
          />
        </CardContent>
        <CardFooter>
          <Button type={"submit"} variant={"primary"}>
            Save
          </Button>
        </CardFooter>
      </Card>
    </Form>
  );
};

export default EditStartTimeForm;
