import { cn } from "~/lib/utils";
import { Draggable } from "react-beautiful-dnd";
import React, { useMemo, useState } from "react";
import { getConciseAddress } from "~/server/lib/location/types";
import { LogisticTaskItem, TaskTimeData } from "~/components/driver/types";
import { formatDuration, HOUR_FORMAT } from "~/server/lib/time";
import { Button } from "~/components/ui/button";
import { FlagIcon, InfoIcon } from "lucide-react";
import LogisticTaskViewSheet from "~/components/driver/LogisticTaskViewSheet";
import { getTimeWindowForTask } from "~/components/driver/Timeline";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

type TimelineTaskProps = {
  task: LogisticTaskItem;
  index: number;
  timeData: TaskTimeData | undefined;
  matchingTasks?: LogisticTaskItem[]; // Array of tasks that belong to the same order
  pairColor?: string; // Color to use for this task pair
};

const TimelineTask = ({ task, index, ...props }: TimelineTaskProps) => {
  const [open, setOpen] = useState(false);

  const { firstValidArrivalTime, lastValidArrivalTime } = useMemo(() => {
    return getTimeWindowForTask(task);
  }, [task]);

  // Check if this task has matching tasks (same order ID)
  const hasMatchingTasks = useMemo(() => {
    return props.matchingTasks && props.matchingTasks.length > 0;
  }, [props.matchingTasks]);

  return (
    <Draggable key={task.id} draggableId={`task-${task.id}`} index={index}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`flex-shrink-0 bg-gray-50 p-3 border rounded-lg max-w-sm min-w-48 sm:min-w-64 ${
            open ? "animate-highlight-border" : ""
          } ${hasMatchingTasks && props.pairColor ? `border-l-4` : ""}`}
          style={{
            ...provided.draggableProps.style,
            ...(hasMatchingTasks && props.pairColor ? { borderLeftColor: props.pairColor } : {})
          }}
        >
          <div className="flex flex-col justify-between gap-1">
            <div className={"flex flex-col md:flex-row justify-between pb-1"}>
              <h3 className={"font-semibold"}>
                {`${task.displayAction} #${
                  task.orderId !== null ? `${task.orderId}` : ""
                }`}
              </h3>
              <p className={"text-sm font-medium"}>{`${HOUR_FORMAT.format(
                new Date(task.actionTime),
              )}`}</p>
            </div>
            {props.timeData && (
              <>
                <div
                  className={"flex flex-col md:flex-row justify-between gap-1"}
                >
                  <p className={"text-xs"}>{`Travel: ${formatDuration(
                    (props.timeData?.distanceFromPreviousLocation || 0) *
                      60 *
                      1000,
                  )}`}</p>
                  <p className={"text-xs"}>{`${
                    task.displayAction === "Pickup" ? "Teardown" : "Setup"
                  }: ${formatDuration(
                    Math.round(task.durationMinutes) * 60 * 1000,
                  )}`}</p>
                </div>
                <div
                  className={"flex flex-col md:flex-row justify-between gap-1"}
                >
                  <p className={cn("text-xs", props.timeData?.color)}>
                    {`Arrive: ${HOUR_FORMAT.format(
                      new Date(props.timeData?.arriveAtLocation ?? ""),
                    )}`}
                  </p>
                  <p className={"text-xs"}>
                    {`Leave: ${HOUR_FORMAT.format(
                      new Date(props.timeData?.completeAction ?? ""),
                    )}`}
                  </p>
                </div>
              </>
            )}
            {!props.timeData && (
              <>
                <p className={"text-xs"}>{`${
                  task.displayAction === "Pickup" ? "Teardown" : "Setup"
                }: ${Math.round(task.durationMinutes)} minutes`}</p>
                <div className={"flex flex-col"}>
                  <p className={cn("text-xs")}>
                    {`Arrival: ${HOUR_FORMAT.format(
                      new Date(firstValidArrivalTime),
                    )} - ${HOUR_FORMAT.format(new Date(lastValidArrivalTime))}`}
                  </p>
                </div>
              </>
            )}
            <div className="flex justify-between items-center pt-1">
              <p className={"text-xs flex-1 mr-1"}>{getConciseAddress(task.address)}</p>
              <div className={"flex flex-row items-center gap-1 ml-auto"}>
                {task.notes && (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant={"ghost"} size={"icon"} className="h-6 w-6 p-0">
                        <FlagIcon className={"h-3 w-3"} />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-32">
                      <p className={"text-xs"}>{task.notes}</p>
                    </PopoverContent>
                  </Popover>
                )}
                <LogisticTaskViewSheet task={task} onOpenChange={setOpen}>
                  <Button variant={"ghost"} size={"icon"} className="h-6 w-6 p-0">
                    <InfoIcon className={"h-3 w-3"} />
                  </Button>
                </LogisticTaskViewSheet>
              </div>
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default TimelineTask;