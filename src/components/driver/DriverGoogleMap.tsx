import {
  DirectionsRenderer,
  DirectionsService,
  GoogleMap,
  LoadScript,
  Marker,
} from "@react-google-maps/api";
import React, { useCallback, useMemo, useState } from "react";

type DriverGoogleMapProps = {
  businessLat: number;
  businessLng: number;
  markerLocations: { lat: number; lng: number; title: string; hover: string }[];
  renderRoute: { lat: number; lng: number }[] | null;
  renderChildren?: React.ReactNode;
};

const Router = (props: {
  origin: {
    lat: number;
    lng: number;
  };
  markerLocations: { lat: number; lng: number }[] | null;
}) => {
  const [response, setResponse] = useState<google.maps.DirectionsResult | null>(
    null,
  );

  const directionsCallback = useCallback(
    (
      result: google.maps.DirectionsResult | null,
      status: google.maps.DirectionsStatus,
    ) => {
      if (result !== null) {
        if (status === google.maps.DirectionsStatus.OK) {
          setResponse(result);
        }
      }
    },
    [],
  );

  const directionsServiceOptions =
    useMemo<google.maps.DirectionsRequest>(() => {
      if (!props.origin || !props.markerLocations) {
        return {} as google.maps.DirectionsRequest;
      }
      const locations = [...props.markerLocations];
      const lastMarker = locations.pop();
      if (!lastMarker) {
        return {} as google.maps.DirectionsRequest;
      }
      return {
        destination: {
          lat: lastMarker.lat,
          lng: lastMarker.lng,
        },
        waypoints: locations.map((location) => ({
          location: { lat: location.lat, lng: location.lng },
        })),
        origin: {
          lat: props.origin.lat,
          lng: props.origin.lng,
        },
        travelMode: window.google.maps.TravelMode.DRIVING,
      };
    }, [props.markerLocations, props.origin]);

  const directionsResult = useMemo(() => {
    return {
      directions: response,
    };
  }, [response]);

  return (
    <>
      <DirectionsService
        options={directionsServiceOptions}
        callback={directionsCallback}
      />

      {directionsResult.directions && (
        <DirectionsRenderer options={directionsResult} />
      )}
    </>
  );
};

const DriverGoogleMap = (props: DriverGoogleMapProps) => {
  return (
    <LoadScript googleMapsApiKey="AIzaSyAQZP0aLfoF79PLnEQE48U3cbDDYat24tA">
      <GoogleMap
        mapContainerStyle={{
          borderRadius: "8px",
          height: "320px",
        }}
        center={{
          lat: props.businessLat,
          lng: props.businessLng,
        }}
        zoom={12}
      >
        <Marker
          position={{
            lat: props.businessLat,
            lng: props.businessLng,
          }}
        />
        {props.renderRoute === null &&
          props.markerLocations.map((location, index) => (
            <Marker
              key={index}
              title={location.hover}
              label={location.title}
              position={{
                lat: location.lat,
                lng: location.lng,
              }}
            />
          ))}
        <Router
          origin={{
            lat: props.businessLat,
            lng: props.businessLng,
          }}
          markerLocations={props.renderRoute}
        />
      </GoogleMap>
      {props.renderChildren}
    </LoadScript>
  );
};
export default DriverGoogleMap;
