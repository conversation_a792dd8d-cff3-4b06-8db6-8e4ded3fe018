import { LogisticTaskItem } from "~/components/driver/types";
import React, { useMemo, useState } from "react";
import FormLayout from "~/components/FormLayout";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import { useCustomer } from "~/query/customer";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import NextLink from "next/link";
import { Skeleton } from "~/components/ui/skeleton";
import { getTimeWindowForTask } from "~/components/driver/Timeline";
import { formatDuration, SANE_FORMATTER } from "~/server/lib/time";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import EmailDisplay from "~/components/email/EmailDisplay";
import PhoneNumberDisplay from "~/components/phone/PhoneNumberDisplay";
import { useOrder } from "~/query/order";

type LogisticItemViewSheetProps = {
  task: LogisticTaskItem;
  children: React.ReactNode;
  onOpenChange?: (open: boolean) => void;
};

const LogisticTaskViewSheet = (props: LogisticItemViewSheetProps) => {
  const [open, setOpen] = useState(false);
  const orderData = useOrder(
    open && props.task?.orderId ? props.task?.orderId : undefined,
  );
  const customerData = useCustomer(
    open && orderData.data?.order?.customerId
      ? orderData.data?.order?.customerId
      : undefined,
  );
  const handleOpenChange = (value: boolean) => {
    setOpen(value);
    if (props.onOpenChange) {
      props.onOpenChange(value);
    }
  };

  const arrivalTime = useMemo(() => {
    return getTimeWindowForTask(props.task);
  }, [props.task]);

  return (
    <Sheet onOpenChange={handleOpenChange} open={open}>
      <SheetTrigger asChild={true}>{props.children}</SheetTrigger>
      <SheetContent side={"right"} className={"overflow-scroll"}>
        <FormLayout className={"py-4"}>
          <FormLayoutSection style={"full"}>
            <Card>
              <CardContent className={"flex flex-col gap-4 text-sm pt-6"}>
                <div className={"text-xl font-semibold"}>
                  {`#${props.task.orderId !== null ? `${props.task.orderId}` : ""} - ${props.task.displayAction}`}
                </div>

                <div className={"flex flex-col gap-2"}>
                  <div className={"font-semibold"}>Event Location</div>
                  <address className="grid gap-0.5 not-italic">
                    <span>{props.task.address.line1}</span>
                    {props.task.address.line2 && <span>{props.task.address.line2}</span>}
                    <span>
                      {props.task.address.city}, {props.task.address.state}{" "}
                      {props.task.address.postalCode}
                    </span>
                  </address>
                </div>

                <div className={"flex flex-col gap-2"}>
                  <div className={"font-semibold"}>Order Time</div>
                  <div>
                    {props.task.displayAction === "Drop Off"
                      ? new Date(props.task.actionTime).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })
                      : `${new Date(arrivalTime.firstValidArrivalTime).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })} - ${new Date(arrivalTime.lastValidArrivalTime).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}`
                    }
                  </div>
                </div>

                <div className={"flex flex-col gap-2"}>
                  <div className={"font-semibold"}>
                    {props.task.displayAction === "Pickup" ? "Teardown Time" : "Setup Time"}
                  </div>
                  <span>
                    {formatDuration(
                      Math.round(props.task.durationMinutes) * 60 * 1000,
                    )}
                  </span>
                </div>

                <div className={"flex flex-col gap-2"}>
                  <div className={"font-semibold"}>Valid Arrival Time</div>
                  <div>
                    {new Date(arrivalTime.firstValidArrivalTime).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                    {' - '}
                    {new Date(arrivalTime.lastValidArrivalTime).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>

                <div className={"flex flex-col gap-2"}>
                  <div className={"font-semibold"}>Notes</div>
                  <span>
                    {props.task.notes ??
                      orderData?.data?.extraInfo?.internalNote ??
                      orderData?.data?.extraInfo?.customerNote ??
                      "No notes"}
                  </span>
                </div>

                {props.task.orderId && (
                  <div className={"flex flex-col gap-2 pt-2 border-t"}>
                    <div className={"font-semibold"}>Customer</div>
                    {!customerData.isPending && customerData?.data ? (
                      <div className={"flex flex-col"}>
                        <Button variant={"link"} size={"link"} className="p-0 h-auto justify-start">
                          <NextLink
                            href={`/customers/${orderData?.data?.order?.customerId}/`}
                          >
                            {customerData?.data?.firstName}{" "}
                            {customerData?.data?.lastName}
                          </NextLink>
                        </Button>

                        <EmailDisplay email={customerData?.data?.email} />
                        {customerData?.data?.phoneNumber ? (
                          <PhoneNumberDisplay
                            phoneNumber={customerData?.data?.phoneNumber}
                          />
                        ) : (
                          <p className={"text-xs text-muted-foreground"}>
                            No Phone Number
                          </p>
                        )}
                      </div>
                    ) : (
                      <>
                        <Skeleton className={"h-4 w-20"} />
                        <Skeleton className={"h-4 w-[30px]"} />
                        <Skeleton className={"h-4 w-20"} />
                        <Skeleton className={"h-4 w-[30px]"} />
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </FormLayoutSection>
        </FormLayout>
      </SheetContent>
    </Sheet>
  );
};

export default LogisticTaskViewSheet;