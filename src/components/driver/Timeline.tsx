import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { EditIcon, X } from "lucide-react";
import { AddressType, getFullAddress } from "~/server/lib/location/types";
import { Droppable } from "react-beautiful-dnd";
import React, { useMemo } from "react";
import TimelineTask from "~/components/driver/TimelineTask";
import {
  LogisticTaskItem,
  TaskTimeData,
  TimelineStats,
} from "~/components/driver/types";
import { DeliveryDistanceLocations } from "~/pages/api/driver/distance";
import { isAfter, isBefore } from "date-fns";
import { LogisticTimeline } from "~/pages/api/driver/timelines";
import FormDialog from "~/components/Actions/FormDialog";
import EditStartTimeForm from "~/components/driver/EditStartTimeForm";
import { formatDuration, HOUR_FORMAT } from "~/server/lib/time";
import { useMedia<PERSON><PERSON>y } from "~/lib/use-media-query";
import { useStaff } from "~/query/staff/query";

type TimelineProps = {
  id: string;
  startTime: Date;
  origin?: AddressType;
  tasks: LogisticTaskItem[];
  staff: string[];
  routeDistance: Record<string, DeliveryDistanceLocations>;
  removeTimeline: (timelines: LogisticTimeline[]) => void;
  updateTimeline: (newStartTime: Date, staff: string[]) => void;
  handleNewTimeline?: () => void;
  allTasks?: LogisticTaskItem[]; // All tasks across all timelines
  getColorForOrderId?: (orderId: number | null) => string | undefined; // Function to get color for orderId
};

const Timeline = (props: TimelineProps) => {
  const { data } = useStaff();
  const isMobile = useMediaQuery("(max-width: 426px)");
  const [changeStartTime, setChangeStartTime] = React.useState(false);
  const removeTimeline = async () => {
    const request = await fetch(`/api/driver/timelines/${props.id}/delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        startTime: props.startTime,
      }),
    });
    if (!request.ok) {
      return;
    }
    const response = await request.json();
    props.removeTimeline(response.timelines);
  };

  // Function to generate a unique color based on orderId
  const getColorForOrderId = (orderId: number | null) => {
    if (!orderId) return null;

    // List of distinct colors that are visually different from each other
    const colors = [
      '#FF5733', // Red-Orange
      '#33FF57', // Green
      '#3357FF', // Blue
      '#FF33A8', // Pink
      '#33FFF5', // Cyan
      '#F5FF33', // Yellow
      '#A833FF', // Purple
      '#FF8C33', // Orange
      '#33FFAA', // Mint
      '#8CFF33', // Lime
      '#FF3333', // Red
      '#33AAFF', // Sky Blue
      '#FF33F5', // Magenta
      '#FFAA33', // Amber
      '#AA33FF', // Violet
    ];

    // Use the orderId to select a color from the array
    return colors[orderId % colors.length];
  };

  const [timelineStats, taskTimeData]: [
    TimelineStats,
    Record<string, TaskTimeData>,
  ] = useMemo<[TimelineStats, Record<string, TaskTimeData>]>(() => {
    if (props.tasks.length === 0 || !props.startTime) {
      return [
        {
          id: 1,
          totalTimeMinutes: 0,
          completionTime: null,
        },
        {},
      ];
    }
    const stats: TimelineStats = {
      id: 1,
      totalTimeMinutes: 0,
      completionTime: new Date(),
    };
    let totalTimeMinutes = 0;
    let completionTime: Date | null = null;
    let lastTask: LogisticTaskItem | null = null;
    const taskTimeData: Record<string, TaskTimeData> = {};
    for (const task of props.tasks) {
      let distance;
      if (lastTask) {
        distance =
          props.routeDistance[task.address.id]?.distance_to_other_locations[
            lastTask.address.id
          ];
      } else {
        distance =
          props.routeDistance[task.address.id]?.distance_from_depot?.find(
            (item) => item.line1 === props.origin?.line1,
          )?.distance || 0;
      }

      const taskTime = handleTaskTimeData(
        task,
        completionTime || props.startTime,
        distance || 0,
      );
      taskTimeData[task.id] = taskTime;

      if (distance) {
        totalTimeMinutes += distance;
      }

      completionTime = taskTime.completeAction;
      lastTask = task;
    }
    if (lastTask && completionTime) {
      const distance =
        props.routeDistance[lastTask.address.id]?.distance_from_depot?.find(
          (item) => item.line1 === props.origin?.line1,
        )?.distance || 0;
      totalTimeMinutes += distance;
      completionTime = new Date(
        completionTime.getTime() + distance * 60 * 1000,
      );
    }

    stats.totalTimeMinutes = totalTimeMinutes;
    if (completionTime) {
      stats.completionTime = completionTime;
    }

    return [stats, taskTimeData];
  }, [props.tasks, props.routeDistance]);

  return (
    <Card>
      <FormDialog
        open={changeStartTime}
        setOpen={setChangeStartTime}
        title={"Edit Timeline"}
        form={
          <EditStartTimeForm
            startTime={props.startTime}
            staff={props.staff || []}
            onSubmit={(values) => {
              setChangeStartTime(false);
              props.updateTimeline(values.startTime, values.staff);
            }}
          />
        }
      />
      <CardHeader>
        <div className={"flex flex-row justify-between"}>
          <CardTitle>
            Timeline
            <Button
              variant={"ghost"}
              size={"icon"}
              onClick={() => {
                setChangeStartTime(true);
              }}
            >
              <EditIcon className={"w-4 h-4"} />
            </Button>
          </CardTitle>
          <Button
            variant={"ghost"}
            size={"icon"}
            onClick={() => {
              removeTimeline();
            }}
          >
            <X className={"w-4 h-4"} />
          </Button>
        </div>
        <CardDescription>
          <p className={"flex flex-row items-center gap-0.5"}>
            {`Start Time: ${HOUR_FORMAT.format(props.startTime)}`}
          </p>
          <p>{`Origin: ${getFullAddress(props.origin || null)}`}</p>
          {props.staff && props.staff.length > 0 && (
            <p>{`Staff: ${data?.staff
              ?.filter((item) => (props.staff || []).includes(item.id))
              ?.map((item) => item.name || item.email)
              ?.join(", ")}`}</p>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Droppable
          direction={isMobile ? "vertical" : "horizontal"}
          droppableId={`timeline-${props.id}`}
        >
          {(provided) => (
            <div
              className="bg-gray-200 rounded-lg mb-4 flex flex-col sm:flex-row overflow-y-auto sm:overflow-x-auto sm:space-x-4 gap-4 p-4 overflow-x-hidden"
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {props.tasks.map((item) => {
                // Find matching tasks (tasks with the same orderId) from all tasks if available
                const matchingTasks = item.orderId
                  ? (props.allTasks || props.tasks).filter(
                      task => task.orderId === item.orderId && task.id !== item.id
                    )
                  : [];

                // Get color for this task pair based on orderId
                // Use the provided getColorForOrderId function if available, otherwise use the local one
                const pairColor = item.orderId
                  ? (props.getColorForOrderId ? props.getColorForOrderId(item.orderId) : getColorForOrderId(item.orderId))
                  : undefined;

                return (
                  <TimelineTask
                    task={item}
                    index={item.index}
                    key={item.id}
                    timeData={taskTimeData[item.id]}
                    matchingTasks={matchingTasks}
                    pairColor={pairColor ?? undefined}
                  />
                );
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
        <div className="grid-rows-3 md:grid-rows-1 sm:grid-cols-3 gap-4 hidden sm:grid">
          <div className="bg-gray-50 p-3 border rounded-lg">
            <h3 className="font-semibold text-center">Drive Time</h3>
            <p className="text-center mt-1">
              {formatDuration(timelineStats.totalTimeMinutes * 60 * 1000)}
            </p>
          </div>

          <div className="bg-gray-50 p-3 border rounded-lg">
            <h3 className="font-semibold text-center">Total Time</h3>
            {timelineStats.completionTime !== null && (
              <p className="text-center mt-1">
                {formatDuration(
                  timelineStats.completionTime.getTime() -
                    props.startTime.getTime(),
                )}
              </p>
            )}
          </div>

          <div className="bg-gray-50 p-3 border border-gray-200 rounded-lg">
            <h3 className="font-semibold text-center">Finish Time</h3>
            {timelineStats.completionTime !== null && (
              <p className="text-center mt-1">
                {HOUR_FORMAT.format(new Date(timelineStats.completionTime))}
              </p>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className={"justify-end gap-2"}>
        {props.handleNewTimeline && (
          <Button
            onClick={() => {
              if (props.handleNewTimeline) {
                props.handleNewTimeline();
              }
            }}
          >
            Add Timeline
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export const getTimeWindowForTask = (task: LogisticTaskItem) => {
  // Keep in Mind, these are VALID times. So if it takes 30 minutes to setup and starts at 10am, 9:30 is the LATEST valid start time.

  // we determine this first because it will affect the first valid arrival time.
  let lastValidArrivalTime = Math.max(
    task.actionTime.getTime(),
    task.actionTime.getTime() + task.actionTimeFlexibilityMinutes * 60 * 1000,
  );

  // if the duration affects the time, it will only affect the Last Valid Arrival Time
  if (task.durationAffectsTime) {
    lastValidArrivalTime -= task.durationMinutes * 60 * 1000;
  }

  const firstValidArrivalTime = Math.min(
    task.actionTime.getTime(),
    task.actionTime.getTime() + task.actionTimeFlexibilityMinutes * 60 * 1000,
  );

  return { firstValidArrivalTime, lastValidArrivalTime };
};

const handleTaskTimeData = (
  task: LogisticTaskItem,
  previousTaskTime: Date,
  distanceFromPrevious: number,
) => {
  const { firstValidArrivalTime, lastValidArrivalTime } =
    getTimeWindowForTask(task);
  let color = "text-green-500";
  let realDepartureTime = previousTaskTime;
  let arrivalTime = new Date(
    (realDepartureTime?.getTime() ?? 0) + distanceFromPrevious * 60 * 1000,
  );

  if (isBefore(arrivalTime, new Date(firstValidArrivalTime))) {
    color = "text-yellow-500";
    realDepartureTime = new Date(
      firstValidArrivalTime - distanceFromPrevious * 60 * 1000,
    );
    // instead of adding the arrival time, we should subtract the time from the departure time so that we arrive at the first valid arrival time
    arrivalTime = new Date(firstValidArrivalTime);
  }

  const completionTime = new Date(
    arrivalTime.getTime() + task.durationMinutes * 60 * 1000,
  );

  if (isAfter(arrivalTime, new Date(lastValidArrivalTime))) {
    color = "text-red-500";
  }

  const taskTime: TaskTimeData = {
    color: color,
    departFromPreviousLocation: realDepartureTime,
    arriveAtLocation: arrivalTime,
    completeAction: completionTime,
    distanceFromPreviousLocation: distanceFromPrevious,
  };
  return taskTime;
};

export default Timeline;