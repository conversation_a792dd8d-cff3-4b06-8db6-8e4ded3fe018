import {
  Modal,
  <PERSON>dalBody,
  Modal<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "~/components/ui/modal";
import { Button } from "~/components/ui/button";
import { LoadingSpinner } from "~/pages/_app";

type TemporaryPasswordDialogProps = {
  open: boolean;
  onClose: () => void;
  temporaryPassword?: string;
};

const TemporaryPasswordDialog = (props: TemporaryPasswordDialogProps) => {
  return (
    <Modal
      open={props.open}
      onOpenChange={(open: boolean) => {
        if (!open) {
          props.onClose();
        }
      }}
    >
      <ModalContent>
        <ModalTitle>{`Temporary Password`}</ModalTitle>
        <ModalBody>
          {props.temporaryPassword ? (
            <p>{`Temporary password: ${props.temporaryPassword}`}</p>
          ) : (
            <LoadingSpinner />
          )}
        </ModalBody>
        <ModalFooter>
          <ModalClose>
            <Button onClick={props.onClose} variant="secondary">
              Close
            </Button>
          </ModalClose>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default TemporaryPasswordDialog;
