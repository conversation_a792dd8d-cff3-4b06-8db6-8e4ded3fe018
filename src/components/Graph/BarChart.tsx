import {
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import React from "react";
import { Skeleton } from "~/components/ui/skeleton";
import { getCurrencyString, getCurrencyValue } from "~/server/lib/currency";

export type BarChartItem = {
  name: string;
  totalReceived?: number | string;
  totalCommitted?: number | string;
  total: number | string;
};

type BarChartProps = {
  money?: boolean;
  items: BarChartItem[];
  loading?: boolean;
  tooltipLabelFormatter?: (value: string | number) => string;
};

const BarChart = (props: BarChartProps) => {
  const data = props.items;
  if (props.loading) {
    return (
      <ResponsiveContainer width="100%" height={350}>
        <div className={"w-full h-full flex"}>
          <div className="w-[40px] h-full flex items-center justify-center">
            <Skeleton key={"left"} className="h-full w-8" />
          </div>
          <div className="flex-1 flex flex-col justify-end">
            <Skeleton key={"bottom"} className="h-4 w-[92%]" />
          </div>
        </div>
      </ResponsiveContainer>
    );
  }
  return (
    <ResponsiveContainer width="100%" height={350}>
      <ReChartsBarChart
        data={data}
        margin={{ left: 30, right: 5, top: 5, bottom: 5 }}
      >
        <XAxis
          dataKey="name"
          stroke="#888888"
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          tickLine={false}
          axisLine={false}
          width={30}
          tickFormatter={(value) => {
            // Format more compactly for y-axis
            if (props.money === true) {
              // For larger numbers, use K notation
              if (value >= 1000) {
                return `$${(value / 1000).toFixed(1)}K`;
              }
              return `$${value}`;
            }
            return `${value}`;
          }}
        />
        <Tooltip
          cursor={{ fill: "transparent" }}
          labelFormatter={props.tooltipLabelFormatter}
          formatter={(value: string | number, type) => {
            if (type === "totalCommitted") {
              return [
                `Committed: ${
                  props.money === true
                    ? `${getCurrencyString(Number(value))}`
                    : value
                }`,
              ];
            }

            return [
              `Received: ${
                props.money === true
                  ? `${getCurrencyString(Number(value))}`
                  : value
              }`,
            ];
          }}
        />

        <Bar
          dataKey="totalReceived"
          stackId="a"
          fill="currentColor"
          radius={[4, 4, 0, 0]}
          className="fill-primary"
        />
        <Bar
          dataKey="totalCommitted"
          stackId="a"
          legendType={"cross"}
          fill="currentColor"
          radius={[4, 4, 0, 0]}
          className="fill-primary/30"
        />
      </ReChartsBarChart>
    </ResponsiveContainer>
  );
};

export default BarChart;
