import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import React from "react";
import DisplayTimeRange from "~/components/DatePicker/display";
import { Button } from "~/components/ui/button";
import { MoreHorizontal } from "lucide-react";

type OrderEventLogProps = {
  orderId: number;
};

const OrderEventLog = (props: OrderEventLogProps) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Sent At</TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody className={"overflow-scroll"}>
        <TableRow>
          <TableCell>Test</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminde asd asd asd asd ass dasd as dar</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Order Reminder</TableCell>
          <TableCell>
            <DisplayTimeRange startTime={new Date()} endTime={null} />
          </TableCell>
          <TableCell>
            <Button
              variant="ghost"
              className="flex h-8 w-8 justify-center p-0 data-[state=open]:bg-muted float-right"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
};

export default OrderEventLog;
