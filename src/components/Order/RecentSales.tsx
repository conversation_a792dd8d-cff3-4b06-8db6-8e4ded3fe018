import { Avatar, AvatarFallback } from "~/components/ui/avatar";
import { RecentSaleObject } from "~/pages/api/account/stats/recentSales";
import { Skeleton } from "~/components/ui/skeleton";
import NextLink from "next/link";
import { formatTimeRange } from "~/server/lib/time";

type RecentSalesProps = {
  loading: boolean;
  orders: RecentSaleObject[];
};

const RecentSale = (props: {
  sale: RecentSaleObject | null;
  loading?: boolean;
}) => {
  return (
    <div className="flex items-center">
      <Avatar className="h-9 w-9">
        <AvatarFallback>
          {props.loading ? (
            <Skeleton className={"h-3 w-4"} />
          ) : (
            props?.sale?.customer.displayName.substr(0, 2)
          )}
        </AvatarFallback>
      </Avatar>
      <div className="ml-4 space-y-1">
        <p className="text-sm font-medium leading-none">
          {props.loading ? (
            <Skeleton className={"h-3 w-20"} />
          ) : (
            <NextLink
              href={`/customers/${props?.sale?.customer.id}/`}
              className={"text-primary hover:underline"}
            >
              {props?.sale?.customer.displayName}
            </NextLink>
          )}
        </p>
        <p className="text-sm text-muted-foreground">
          {props.loading ? (
            <Skeleton className={"h-3 w-20"} />
          ) : (
            <NextLink
              href={`/orders/${props?.sale?.id}/`}
              className={"text-primary/80 hover:underline"}
            >
              {`Order #${props.sale?.id} - ${formatTimeRange(
                new Date(props.sale?.startTime || ""),
                null,
              )}`}
            </NextLink>
          )}
        </p>
      </div>
      <div className="ml-auto font-medium">{props.sale?.amount}</div>
    </div>
  );
};

const RecentSales = (props: RecentSalesProps) => {
  const orders = props.orders;
  if (props.loading) {
    return (
      <div className="space-y-8">
        <RecentSale sale={null} loading={true} />
        <RecentSale sale={null} loading={true} />
        <RecentSale sale={null} loading={true} />
        <RecentSale sale={null} loading={true} />
      </div>
    );
  }
  return (
    <div className="space-y-8">
      {orders.map((order, index) => (
        <RecentSale key={index} sale={order} />
      ))}
    </div>
  );
};

export default RecentSales;
