import { PaymentsResponse } from "~/pages/api/orders/[id]/payment";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Input } from "~/components/ui/input";
import { getCurrencyString } from "~/server/lib/currency";
import { Button } from "~/components/ui/button";
import React from "react";
import FormItem from "../FormLayout/FormItem";
import { Form } from "../ui/form";
import { number, object, string } from "yup";
import { ModalFooter } from "~/components/ui/modal";

export const ORDER_PAYMENT_REFUND_SCHEMA = object().shape({
  amount: number().required(),
  reason: string().required("You must specify a reason for the refund."),
});

export type OrderPaymentRefund =
  typeof ORDER_PAYMENT_REFUND_SCHEMA.__outputType;

export type RefundActionProps = {
  onSubmit: (values: OrderPaymentRefund) => void;
  payment?: PaymentsResponse;
};

const RefundAction = (props: RefundActionProps) => {
  const form = useForm<OrderPaymentRefund>({
    resolver: yupResolver(ORDER_PAYMENT_REFUND_SCHEMA),
  });

  if (!props.payment) {
    return null;
  }

  const amountAlreadyRefunded =
    props.payment.PaymentRefund?.reduce(
      (acc, refund) => acc + refund.amount,
      0,
    ) ?? 0;

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormItem
        label={"Refund Amount"}
        name={"amount"}
        render={({ field }) => {
          return <Input placeholder="10.00" {...field} prefix={"$"} />;
        }}
      />
      <FormItem
        label={"Reason"}
        name={"reason"}
        render={({ field }) => {
          return <Input {...field} />;
        }}
      />
      <div>
        <div>
          <span className={"font-bold"}>Total Paid: </span>
          <span>
            {getCurrencyString(props.payment.amount + (props.payment.tip ?? 0))}
          </span>
        </div>
        <div>
          <span className={"font-bold"}>Available For Refund: </span>
          <span>
            {getCurrencyString(
              props.payment.amount +
                (props.payment.tip ?? 0) -
                amountAlreadyRefunded,
            )}
          </span>
        </div>
      </div>
      <ModalFooter>
        <Button
          type="submit"
          variant={"primary"}
          disabled={
            Number(form.watch("amount") || 0) <= 0 ||
            isNaN(Number(form.watch("amount")))
          }
        >
          {`Submit Refund of ${getCurrencyString(
            Number(form.watch("amount")),
          )}`}
        </Button>
      </ModalFooter>
    </Form>
  );
};

export default RefundAction;
