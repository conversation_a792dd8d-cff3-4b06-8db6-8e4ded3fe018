import {
  Modal,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalTitle,
} from "~/components/ui/modal";
import React from "react";
import RefundAction, {
  RefundActionProps,
} from "~/components/Order/RefundAction";

type PayRefundAlertProps = RefundActionProps & {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};
const RefundActionModal = (props: PayRefundAlertProps) => {
  return (
    <Modal
      open={props.open}
      onOpenChange={(isOpen) => {
        props.onOpenChange(isOpen);
      }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Refund payment</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col space-y-3 -mx-3 px-6 flex-1 py-2">
            <RefundAction
              {...props}
              onSubmit={(values) => {
                props.onSubmit(values);
                props.onOpenChange(false);
              }}
            />
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default RefundActionModal;
