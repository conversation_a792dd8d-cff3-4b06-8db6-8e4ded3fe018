import { boolean, number, object, string } from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { getCurrencyString } from "~/server/lib/currency";
import { Switch } from "~/components/ui/switch";
import { ModalFooter } from "~/components/ui/modal";

export type PaymentMethod = "cash" | "check" | "cardOnFile" | "otherCard";

export const ORDER_PAYMENT_SCHEMA = object().shape({
  paymentMethod: string<PaymentMethod>().required("Payment method is required"),
  paymentMethodId: string().when("paymentMethod", (paymentMethod, field) => {
    if (
      paymentMethod.some(
        (method) => method === "check" || method === "otherCard",
      )
    ) {
      return field.required("Payment Method ID Is required");
    }
    return field;
  }),
  paymentAmount: number()
    .min(0, "Payment amount must be greater than 0")
    .required("Payment amount is required"),
  tipAmount: number()
    .min(0, "Tip amount must be greater than 0")
    .max(500, "Tip amount must be less than 500"),
  silent: boolean().required("Silent is required"),
});

export type OrderPaymentValues = typeof ORDER_PAYMENT_SCHEMA.__outputType;

export type PayActionProps = {
  onSubmit: (values: OrderPaymentValues) => void;
  totalPrice: number;
  amountDue: number;
};

const PayAction = (props: PayActionProps) => {
  const form = useForm<OrderPaymentValues>({
    defaultValues: {
      paymentMethod: "cash",
      paymentMethodId: "",
      paymentAmount: 0,
      tipAmount: 0,
      silent: false,
    },
    resolver: yupResolver(ORDER_PAYMENT_SCHEMA),
  });

  return (
    <Form {...form} onSubmit={props.onSubmit}>
      <FormField
        control={form.control}
        name="paymentMethod"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Payment Method</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a payment method" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="check">Check</SelectItem>
                <SelectItem value="cardOnFile">Card On File</SelectItem>
                <SelectItem value="otherCard">Manual Card</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      {(form.watch("paymentMethod") === "check" ||
        form.watch("paymentMethod") === "otherCard") && (
        <FormField
          control={form.control}
          name="paymentMethodId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {form.watch("paymentMethod") === "check"
                  ? "Check Number"
                  : "Last 4 of CC"}
              </FormLabel>
              <Input placeholder="0001" {...field} />
              <FormMessage />
            </FormItem>
          )}
        />
      )}
      <FormField
        control={form.control}
        name="paymentAmount"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Payment Amount</FormLabel>
            <Input placeholder="122.05" {...field} prefix={"$"} />
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="tipAmount"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Tip Amount</FormLabel>
            <Input placeholder="10.00" {...field} prefix={"$"} />
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="flex space-x-4">
        <div className={"flex justify-together gap-x-2"}>
          <span className={"font-bold"}>Total: </span>
          <span>{getCurrencyString(props.totalPrice)}</span>
        </div>
        <div className={"flex justify-together gap-x-2"}>
          <span className={"font-bold"}>Due: </span>
          <span>{getCurrencyString(props.amountDue)}</span>
        </div>
      </div>

      <FormField
        control={form.control}
        name="silent"
        render={({ field }) => (
          <FormItem className="border-2 border-prpLightGray rounded-md p-1.5 mb-2">
            <div className="flex space-x-1 items-center space-y-0.5 ">
              <FormLabel>Silent</FormLabel>

            <Switch checked={field.value} onCheckedChange={field.onChange} />
            </div>
            <FormDescription>
              If silent is checked, the customer will not be notified of the
              payment.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
      <ModalFooter>
        <Button
          type="submit"
          variant={"primary"}
          disabled={
            form.watch("paymentAmount") + (form.watch("tipAmount", 0) ?? 0) <= 0
          }
        >
          {`Submit Payment of ${getCurrencyString(
            Number(form.watch("paymentAmount")) +
              Number(form.watch("tipAmount", 0) || 0),
          )}`}
        </Button>
      </ModalFooter>
    </Form>
  );
};

export default PayAction;
