import { boolean, number, object, string } from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Form } from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import React from "react";
import FormDialog from "~/components/Actions/FormDialog";
import { Switch } from "~/components/ui/switch";
import { DollarSign } from "lucide-react";

type CustomFeeFormProps = {
  open: boolean;
  onOpenChange: (value: boolean) => void;
  fee: CustomFee;
  onSubmit: (values: CustomFee) => void;
};

export type CustomFee = {
  name: string;
  value: number;
  taxable: boolean;
};

const CustomFeeFormDialog = (props: CustomFeeFormProps) => {
  const form = useForm<CustomFee>({
    resolver: yupResolver(
      object().shape({
        name: string().required("Required"),
        value: number().required(),
        taxable: boolean().required("Required"),
      }),
    ),
    defaultValues: {
      ...props.fee,
    },
  });

  return (
    <FormDialog
      open={props.open}
      setOpen={props.onOpenChange}
      title={`Editing Custom Fee`}
      form={
        <Form
          {...form}
          onSubmit={(values) => {
            props.onSubmit(values);
          }}
        >
          <FormItem
            label={"Name"}
            name={"name"}
            render={({ field }) => <Input {...field} />}
          />
          <FormItem
            label={"Value"}
            name={"value"}
            render={({ field }) => (
              <Input
                type={"number"}
                placeholder={"$0.00"}
                {...field}
                startIcon={DollarSign}
              />
            )}
          />

          <FormItem
            label={"Taxable"}
            name={"taxable"}
            render={({ field }) => (
              <Switch checked={field.value} onCheckedChange={field.onChange} />
            )}
          />
          <Button variant={"primary"} type={"submit"} className={"mt-2"}>
            Save
          </Button>
        </Form>
      }
    />
  );
};

export default CustomFeeFormDialog;
