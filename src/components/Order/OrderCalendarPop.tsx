import { But<PERSON> } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { CalendarIcon } from "lucide-react";
import { Skeleton } from "~/components/ui/skeleton";
import { REPORT_DATE_FORMAT } from "~/server/lib/time";
import OrderCalendar from "~/components/Calendar/OrderCalendar";
import React, { useState } from "react";
import { useMediaQuery } from "~/lib/use-media-query";
import { Sheet, SheetContent, SheetTrigger } from "~/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { startOfMonth } from "date-fns";

type OrderCalendarPopProps = {
  loading: boolean;
  currentDate: Date;
  setCurrentDate: (date: Date) => void;
};

const OrderCalendarPop = ({
  loading,
  currentDate,
  setCurrentDate,
}: OrderCalendarPopProps) => {
  const [month, setMonth] = useState<Date>(startOfMonth(currentDate));

  const isMobile = useMediaQuery("(max-width: 768px)");

  const PopItem = isMobile ? Sheet : Popover;
  const PopTrigger = isMobile ? SheetTrigger : PopoverTrigger;
  const PopContent = isMobile ? SheetContent : PopoverContent;

  return (
    <PopItem>
      <PopTrigger asChild>
        <Button
          variant={"outline"}
          size={"md"}
          className={cn("font-normal gap-2 w-fit")}
        >
          <CalendarIcon className="mr-auto h-4 w-4 text-muted-foreground" />
          {loading ? (
            <Skeleton className={"w-8 h-2"} />
          ) : (
            REPORT_DATE_FORMAT.format(currentDate)
          )}
        </Button>
      </PopTrigger>
      <PopContent className="w-auto max-w-full p-0">
        <OrderCalendar
          small={true}
          onClick={(date) => {
            setCurrentDate(date);
          }}
          date={currentDate}
          month={month}
          onDateChange={(date) => {
            setMonth(date);
          }}
        />
      </PopContent>
    </PopItem>
  );
};

export default OrderCalendarPop;
