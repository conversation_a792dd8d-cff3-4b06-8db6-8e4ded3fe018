import {
  Dialog,
  Di<PERSON>Close,
  Di<PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>escription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "~/components/ui/dialog";
import { PencilIcon } from "lucide-react";
import { Button } from "~/components/ui/button";
import React, { useState } from "react";
import { Textarea } from "~/components/ui/textarea";

type InternalNoteEditorPopoverProps = {
  currentNote?: string;
  onSubmit: (note: string) => void;
};

const InternalNoteEditorPopover = (props: InternalNoteEditorPopoverProps) => {
  const [open, setOpen] = useState(false);
  const [internalNote, setInternalNote] = useState<string>(
    props.currentNote ?? "",
  );
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={"ghost"} size={"icon"}>
          <PencilIcon className={"h-4 w-4"} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Internal Note</DialogTitle>
          <DialogDescription>
            Edit the note for you and your staff, the customer won't see this.
          </DialogDescription>
        </DialogHeader>
        <Textarea
          value={internalNote}
          onChange={(event) => {
            setInternalNote(event.target.value);
          }}
        />
        <DialogFooter>
          <DialogClose>
            <Button variant={"secondary"}>Cancel</Button>
          </DialogClose>
          <Button
            variant={"primary"}
            onClick={() => {
              props.onSubmit(internalNote);
              setOpen(false);
            }}
          >
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InternalNoteEditorPopover;
