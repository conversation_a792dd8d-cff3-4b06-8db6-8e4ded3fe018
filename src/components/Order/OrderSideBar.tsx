import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "~/components/ui/card";
import FormLayoutSection from "~/components/FormLayout/FormLayoutSection";
import React from "react";
import InternalNoteEditorPopover from "~/components/Order/InternalNoteEditorPopover";

type OrderSideBarProps = {
  initialValues?: {
    internalNote: string | undefined;
    customerNote: string | undefined;
  };
  onInternalNoteChange: (note: string) => void;
};

const OrderSideBar = (props: OrderSideBarProps) => {
  return (
    <FormLayoutSection style={"oneThird"}>
      <Card>
        <CardHeader className={"flex flex-row justify-between items-center"}>
          <CardTitle>Customer Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <p className={"text-sm text-muted-foreground"}>
            {props?.initialValues?.customerNote ?? "No customer notes"}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className={"flex flex-row justify-between items-center"}>
          <CardTitle>Internal Notes</CardTitle>
          <InternalNoteEditorPopover
            currentNote={props?.initialValues?.internalNote}
            onSubmit={props.onInternalNoteChange}
          />
        </CardHeader>
        <CardContent>
          <p className={"text-sm text-muted-foreground"}>
            {props?.initialValues?.internalNote ?? "No internal notes"}
          </p>
        </CardContent>
      </Card>
    </FormLayoutSection>
  );
};

export default OrderSideBar;
