import React, { useCallback, useState } from "react";
import {
  <PERSON>ertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  MapPin,
  XCircle,
} from "lucide-react";
import { OrderWithCustomerAndPaymentsAndAddress } from "~/pages/api/orders/day";
import { getConciseAddress } from "~/server/lib/location/types";
import NextLink from "next/link";
import { HOUR_FORMAT } from "~/server/lib/time";
import { isSameDay } from "date-fns";

interface PacklistOrderViewProps {
  order: OrderWithCustomerAndPaymentsAndAddress;
  products:
    | {
        id: number;
        name: string;
      }[]
    | undefined;
  isItemPacked: (orderId: number, itemId: number) => boolean;
  toggleItemPacked: (orderId: number, itemId: number) => void;
}

export const PacklistOrderView = ({
  order,
  products,
  isItemPacked,
  toggleItemPacked,
}: PacklistOrderViewProps) => {
  const [expandedItems, setExpandedItems] = useState<Record<number, boolean>>(
    {},
  );
  const toggleItemsExpanded = (orderId: number) => {
    setExpandedItems((prev) => ({
      ...prev,
      [orderId]: !prev[orderId],
    }));
  };

  const getPaymentStatus = (order: OrderWithCustomerAndPaymentsAndAddress) => {
    // Calculate payment status based on totalPaid vs finalTotal
    if (order.totalPaid >= order.finalTotal) {
      return "paid";
    } else if (order.totalPaid > 0) {
      return "partial";
    } else {
      return "none";
    }
  };

  const getPaymentIcon = (status: string) => {
    const icons = {
      paid: CheckCircle,
      partial: AlertCircle,
      none: XCircle,
    };
    const Icon = icons[status as keyof typeof icons] || XCircle;
    return <Icon className="h-4 w-4" />;
  };

  const getPaymentStatusStyle = (status: string) => {
    const styles = {
      paid: "bg-green-50 text-green-700 border-green-100",
      partial: "bg-yellow-50 text-yellow-700 border-yellow-100",
      none: "bg-red-50 text-red-700 border-red-100",
    };
    return (
      styles[status as keyof typeof styles] ||
      "bg-gray-50 text-gray-700 border-gray-100"
    );
  };

  const getPaymentLabel = (status: string) => {
    const labels = {
      paid: "Paid",
      partial: "Partial",
      none: "Unpaid",
    };
    return labels[status as keyof typeof labels] || "Unknown";
  };

  // Map order products to display items with product details
  const getOrderItems = () => {
    if (!products) return [];

    return order.OrderProduct.map((orderProduct) => {
      const productDetails = products.find(
        (p) => p.id === orderProduct.productId,
      );

      return {
        id: orderProduct.productId,
        name: productDetails?.name || `Product #${orderProduct.productId}`,
        quantity: orderProduct.quantity,
        // Since we don't have category information, we'll use a default
        // category: "Unknown"
      };
    });
  };

  const orderItems = getOrderItems();
  const paymentStatus = getPaymentStatus(order);

  const renderItems = useCallback(
    (items: any[], isExpanded: boolean) => {
      const previewItems = isExpanded ? items : items.slice(0, 2);
      return (
        <div className="mt-2 print:mt-2 grid grid-cols-2 gap-2">
          {previewItems.map((item) => (
            <div
              key={item.id}
              className={`bg-gradient-to-br from-white to-gray-50 rounded-md p-3 print:p-2 transition-all duration-200 hover:translate-y-[-2px] hover:shadow-sm ${
                isItemPacked(order.id, item.id)
                  ? "border border-green-100 print:bg-white print:border-gray-300"
                  : "border border-gray-100"
              }`}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-start">
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {item.name}
                      </h4>
                      {/* Category display is commented out since we don't have that data
                    <div className="ml-2 flex items-center">
                      <div className={`h-2 w-2 rounded-full bg-gray-400`}></div>
                      <span className="text-xs text-gray-500 ml-1">
                        {item.category}
                      </span>
                    </div>
                    */}
                    </div>
                    <p className="text-xs text-gray-500">
                      Quantity:{" "}
                      <span className="font-medium text-gray-900">
                        {item.quantity}
                      </span>
                    </p>
                  </div>
                </div>
                <div className="print:hidden">
                  <button
                    onClick={() => toggleItemPacked(order.id, item.id)}
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                      isItemPacked(order.id, item.id)
                        ? "bg-green-50 text-green-700 border border-green-100"
                        : "bg-gray-50 text-gray-700 border border-gray-100"
                    }`}
                  >
                    {isItemPacked(order.id, item.id) ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Packed
                      </>
                    ) : (
                      "Not Packed"
                    )}
                  </button>
                </div>
                <div className="hidden print:block text-base">
                  {isItemPacked(order.id, item.id) ? "✓" : "□"}
                </div>
              </div>
            </div>
          ))}
          {items.length > 2 && (
            <button
              onClick={() => toggleItemsExpanded(order.id)}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium flex items-center justify-center w-full py-1 print:hidden"
            >
              {!isExpanded ? (
                <>
                  <span className="mr-1">
                    Show {items.length - 2} more items
                  </span>
                  <ChevronDown className="h-3 w-3" />
                </>
              ) : (
                <>
                  <span className="mr-1">Show less</span>
                  <ChevronUp className="h-3 w-3" />
                </>
              )}
            </button>
          )}
        </div>
      );
    },
    [expandedItems, isItemPacked, order.id, toggleItemPacked],
  );

  return (
    <>
      <div className="p-4 print:p-4 print:break-inside-avoid">
        <div className="flex justify-between items-start mb-4 print:mb-2">
          <div>
            <div className="flex items-center gap-2">
              <NextLink
                href={"/orders/[id]"}
                as={`/orders/${order.id}`}
                className={"text-primary hover:underline text-md font-semibold"}
              >
                Order #{order.id}
              </NextLink>
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border bg-blue-50 text-blue-700 border-blue-100">
                {isSameDay(order.startTime, order.endTime)
                  ? HOUR_FORMAT.formatRange(
                      new Date(order.startTime),
                      new Date(order.endTime),
                    )
                  : HOUR_FORMAT.format(new Date(order.startTime))}
              </span>
              <span
                className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full border ${getPaymentStatusStyle(
                  paymentStatus,
                )}`}
              >
                {getPaymentIcon(paymentStatus)}
                {getPaymentLabel(paymentStatus)}
              </span>
            </div>
            <div className="mt-2 space-y-1">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-1" />
                <span>{getConciseAddress(order.eventAddress)}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <span className="font-medium">Setup Surface:</span>{" "}
                <span className="ml-1">{order.setupSurface.name}</span>
              </div>
              <div className="text-sm text-gray-600">
                <span className="font-medium">Customer:</span>{" "}
                {`${order.Customer.firstName} ${order.Customer.lastName}`}
                {order.Customer.company && ` (${order.Customer.company})`}
              </div>
            </div>
          </div>
          <div className="text-xs bg-gray-50 px-2 py-1 rounded-md border border-gray-100 print:border print:border-gray-300 print:bg-white">
            {orderItems.length} {orderItems.length === 1 ? "item" : "items"}
          </div>
        </div>
        {renderItems(orderItems, expandedItems[order.id] || false)}
      </div>
    </>
  );
};

export default PacklistOrderView;
