import React, { useMemo } from "react";
import { Clock, Package, ShoppingCart } from "lucide-react";
import { HOUR_FORMAT } from "~/server/lib/time";

type PacklistDeliverySummaryProps = {
  orders: {
    startTime: string;
    endTime: string;
    items: {
      quantity: number;
    }[];
  }[];
  loading?: boolean;
};

export const PacklistDeliverySummary = (
  props: PacklistDeliverySummaryProps,
) => {
  const totalOrders = useMemo(() => {
    return props.orders.length;
  }, [props.orders]);

  const totalItems = useMemo(() => {
    return props.orders.reduce(
      (sum, order) =>
        sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0),
      0,
    );
  }, [props.orders]);

  const timeValue = useMemo(() => {
    if (props.orders.length === 0) {
      return "-";
    }
    const allTimes = props.orders.flatMap((order) => [
      new Date(order.startTime),
      new Date(order.endTime),
    ]);

    const earliestTime = new Date(
      Math.min(
        ...allTimes.map((time: string | Date) => new Date(time).getTime()),
      ),
    );
    const latestTime = new Date(
      Math.max(
        ...allTimes.map((time: string | Date) => new Date(time).getTime()),
      ),
    );

    if (!earliestTime || !latestTime) {
      return "-";
    }

    const formattedStartTime = HOUR_FORMAT.format(earliestTime);
    const formattedEndTime = HOUR_FORMAT.format(latestTime);
    return `${formattedStartTime} - ${formattedEndTime}`;
  }, [props.orders]);

  if (props.loading) {
    return (
      <div className="grid grid-cols-2 gap-2 mb-4">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="bg-white rounded-lg border border-gray-100 shadow-sm p-3"
          >
            <div className="flex items-center">
              <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse mr-2"></div>
              <div className="flex-1">
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="mb-6 print:p-4 print:shadow-none ">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 print:gap-2 print:w-full">
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-100 shadow-sm p-4 transition-all duration-200 hover:translate-y-[-2px] hover:shadow print:bg-white print:border print:border-gray-300">
          <div className="flex items-center">
            <div className="flex items-center justify-center h-10 w-10 rounded-full text-primary bg-primary/10 mr-3 print:text-black">
              <ShoppingCart className="h-5 w-5" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Total Orders</p>
              <p className="text-md font-semibold print:text-base">
                {totalOrders}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-100 shadow-sm p-4 transition-all duration-200 hover:translate-y-[-2px] hover:shadow print:bg-white print:border print:border-gray-300">
          <div className="flex items-center">
            <div className="flex items-center justify-center h-10 w-10 rounded-full text-primary bg-primary/10 mr-3 print:text-black">
              <Package className="h-5 w-5" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Total Products</p>
              <p className="text-md font-semibold print:text-base">
                {totalItems}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-100 shadow-sm p-4 transition-all duration-200 hover:translate-y-[-2px] hover:shadow print:bg-white print:border print:border-gray-300">
          <div className="flex items-center">
            <div className="flex items-center justify-center h-10 w-10 rounded-full text-primary bg-primary/10 mr-3 print:text-black">
              <Clock className="h-5 w-5" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Delivery Window</p>
              <p className="text-md font-semibold print:text-xs">{timeValue}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
