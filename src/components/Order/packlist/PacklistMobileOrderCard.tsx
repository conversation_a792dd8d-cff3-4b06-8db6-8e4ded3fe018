import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ChevronDown,
  ChevronUp,
  Clock,
  MapPin,
} from "lucide-react";
import NextLink from "next/link";
import { Button } from "~/components/ui/button";

interface OrderItem {
  id: number;
  name: string;
  quantity: number;
}
interface OrderProps {
  setupSurface: string;
  id: number;
  customer: string;
  address: string;
  startTime: string;
  endTime: string;
  items: OrderItem[];
  isItemPacked: (orderId: number, itemId: number) => boolean;
  toggleItemPacked: (orderId: number, itemId: number) => void;
}
export const PacklistMobileOrderCard = ({
  setupSurface,
  id,
  customer,
  address,
  startTime,
  endTime,
  items,
  isItemPacked,
  toggleItemPacked,
}: OrderProps) => {
  const [expanded, setExpanded] = useState(false);
  const [showAllItems, setShowAllItems] = useState(false);
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
    });
  };

  const displayItems = showAllItems ? items : items.slice(0, 2);
  return (
    <div className="bg-white border border-gray-100 rounded-lg shadow-sm mb-3 overflow-hidden transition-all duration-300">
      <div className="p-3 flex justify-between items-center ">
        <div className="flex-1">
          <div className="flex items-center">
            <NextLink
              href={"/orders/[id]"}
              as={`/orders/${id}`}
              className={"text-primary text-sm font-medium"}
            >
              Order #{id}
            </NextLink>
            <span className="ml-2 text-xs bg-blue-50 text-blue-700 px-2 py-0.5 rounded-full">
              {items.length} {items.length === 1 ? "item" : "items"}
            </span>
          </div>
          <div className="flex items-center mt-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            <span>
              {formatTime(startTime)} - {formatTime(endTime)}
            </span>
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            <span className="font-medium">Setup:</span>
            <span className="ml-1">{setupSurface}</span>
          </div>
        </div>
        {expanded ? (
          <Button
            onClick={() => setExpanded(!expanded)}
            variant={"ghost"}
            size={"icon"}
            className="text-gray-400"
          >
            <ChevronUp className="h-5 w-5 text-gray-400 flex-shrink-0" />
          </Button>
        ) : (
          <Button
            onClick={() => setExpanded(!expanded)}
            variant={"ghost"}
            size={"icon"}
            className="text-gray-400"
          >
            <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />
          </Button>
        )}
      </div>
      <div className="px-3 pb-3">
        <div className="space-y-2">
          {displayItems.map((item) => (
            <div
              key={item.id}
              className={`bg-gray-50 rounded-md p-2 border transition-all ${
                isItemPacked(id, item.id)
                  ? "border-green-100"
                  : "border-gray-100"
              }`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center">
                    <h5 className="text-xs font-medium">{item.name}</h5>
                  </div>
                  <div className="flex items-center mt-1">
                    <p className="text-xs text-gray-500">
                      Qty: <span className="font-medium">{item.quantity}</span>
                    </p>
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleItemPacked(id, item.id);
                  }}
                  className={`text-xs px-2 py-1 rounded-full border flex items-center ${
                    isItemPacked(id, item.id)
                      ? "bg-green-50 text-green-700 border-green-100"
                      : "bg-white text-gray-700 border-gray-200"
                  }`}
                >
                  {isItemPacked(id, item.id) ? (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Packed
                    </>
                  ) : (
                    "Pack"
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
        {items.length > 2 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowAllItems(!showAllItems);
            }}
            className="mt-2 text-xs text-blue-600 font-medium flex items-center justify-center w-full py-1"
          >
            {showAllItems ? (
              <>
                <span>Show less</span>
                <ChevronUp className="h-3 w-3 ml-1" />
              </>
            ) : (
              <>
                <span>Show {items.length - 2} more items</span>
                <ChevronDown className="h-3 w-3 ml-1" />
              </>
            )}
          </button>
        )}
      </div>
      {expanded && (
        <div className="px-3 pb-3 border-t border-gray-100 pt-2 ">
          <div className="mb-2 text-xs animate-in slide-in-from-top duration-300 text-muted-foreground">
            <span className="font-medium">Customer:</span> {customer}
          </div>
          <div className="flex items-center mt-1 text-xs animate-in slide-in-from-top duration-300 text-muted-foreground">
            <MapPin className="h-3 w-3 mr-1" />
            <span className="max-w-[75%]">{address}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PacklistMobileOrderCard;
