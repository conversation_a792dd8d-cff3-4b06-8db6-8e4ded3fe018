import React, { useState } from "react";
import { Search } from "lucide-react";
import { Input } from "~/components/ui/input";

interface SearchFilterBarProps {
  onSearch: (term: string) => void;
}
export const SearchFilterBar = ({ onSearch }: SearchFilterBarProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    onSearch(e.target.value);
  };

  return (
    <div className="sticky top-0 bg-white z-10 border-b border-gray-100 print:hidden p-3">
      <Input
        value={searchTerm}
        onChange={handleSearchChange}
        placeholder="Search orders, items, or customers..."
        startIcon={Search}
      />
      {/*<button*/}
      {/*  onClick={() => setShowFilters(!showFilters)}*/}
      {/*  className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 ${*/}
      {/*    activeFilter ? "text-blue-600" : "text-gray-400"*/}
      {/*  }`}*/}
      {/*>*/}
      {/*  <Filter className="h-4 w-4" />*/}
      {/*</button>*/}
    </div>
  );
};

export default SearchFilterBar;
