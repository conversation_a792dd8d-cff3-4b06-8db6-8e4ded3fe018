import PayAction, { PayActionProps } from "~/components/Order/PayAction";
import {
  Modal,
  ModalBody,
  ModalContent,
  ModalHeader,
  ModalTitle,
} from "~/components/ui/modal";
import React from "react";

type PayActionAlertProps = PayActionProps & {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};
const PayActionModal = (props: PayActionAlertProps) => {
  return (
    <Modal
      open={props.open}
      onOpenChange={(isOpen) => {
        props.onOpenChange(isOpen);
      }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Add payment</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col space-y-3 -mx-3 px-6 flex-1 py-2">
            <PayAction
              {...props}
              onSubmit={(values) => {
                props.onOpenChange(false);
                props.onSubmit(values);
              }}
            />
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default PayActionModal;
