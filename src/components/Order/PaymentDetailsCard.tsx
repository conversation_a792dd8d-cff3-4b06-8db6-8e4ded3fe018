import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  convertPaymentInfoLineItemNameToFeeType,
  getCurrencyString,
  PaymentInfo,
  PaymentInfoLineItemName,
} from "~/server/lib/currency";
import React, { useEffect } from "react";
import { OrderPaidItems } from "~/components/Contract/Top/types";
import FormDialog from "~/components/Actions/FormDialog";
import { Button } from "~/components/ui/button";
import { OrderFeeType } from "@prisma/client";
import SimpleNumberForm from "~/components/FormLayout/SimpleNumberForm";
import SimpleSelectForm from "~/components/FormLayout/SimpleSelectForm";
import { CouponListValues, useCoupons } from "~/query/coupon/query";
import { Trash } from "lucide-react";
import CustomFeeFormDialog, {
  CustomFee,
} from "~/components/Order/CustomFeeFormDialog";

type PaymentDetailsCardProps = {
  originalPaymentInfo?: PaymentInfo;
  paymentInfo: PaymentInfo;
  due?: number;
  paidItems?: OrderPaidItems[];
  subHeader?: string;
  onChange?: (
    item: {
      type: OrderFeeType;
      name: string | null;
      taxable: boolean;
    },
    value: number,
  ) => void;
  edit?: boolean;
  onCouponChange?: (coupon: CouponListValues) => void;
  removeCoupon?: () => void;
  coupon?: CouponListValues | null;
};

const PaymentDetailsCard = (props: PaymentDetailsCardProps) => {
  const coupons = useCoupons();
  const [editedItem, setEditedItem] = React.useState<OrderFeeType | null>(null);
  const [customFee, setCustomFee] = React.useState<CustomFee | null>(null);
  const [editingCoupon, setEditingCoupon] = React.useState<boolean>(false);
  const [couponCode, setCouponCode] = React.useState<CouponListValues | null>(
    null,
  );

  useEffect(() => {
    if (props.coupon) {
      setCouponCode(props?.coupon);
    }
  }, [props.coupon]);

  return (
    <Card>
      {customFee && (
        <CustomFeeFormDialog
          open={true}
          onOpenChange={() => {
            setCustomFee(null);
          }}
          fee={customFee}
          onSubmit={(fee) => {
            props.onChange?.(
              {
                type: OrderFeeType.CUSTOM_FEE,
                name: fee.name,
                taxable: fee.taxable,
              },
              fee.value,
            );
            setCustomFee(null);
          }}
          {...customFee}
        />
      )}
      <FormDialog
        open={editedItem !== null}
        setOpen={(value) => {
          setEditedItem(value ? editedItem : null);
        }}
        title={`Editing ${editedItem}`}
        form={
          <SimpleNumberForm
            title={editedItem || OrderFeeType.TRAVEL_FEE}
            defaultValue={
              props.paymentInfo.fees.find((item) => item.type === editedItem)
                ?.amount ?? 0
            }
            onChange={(value) => {
              const item = editedItem || OrderFeeType.TRAVEL_FEE;

              props.onChange?.(
                {
                  type: item,
                  name: null,
                  taxable: true,
                },
                value,
              );
              setEditedItem(null);
            }}
          />
        }
      />
      <FormDialog
        open={editedItem !== null}
        setOpen={(value) => {
          setEditedItem(value ? editedItem : null);
        }}
        title={`Editing ${editedItem}`}
        form={
          <SimpleNumberForm
            title={editedItem || OrderFeeType.TRAVEL_FEE}
            defaultValue={
              props.paymentInfo.fees.find((item) => item.type === editedItem)
                ?.amount ?? 0
            }
            onChange={(value) => {
              const item = editedItem || OrderFeeType.TRAVEL_FEE;

              props.onChange?.(
                {
                  type: item,
                  name: null,
                  taxable: true,
                },
                value,
              );
              setEditedItem(null);
            }}
          />
        }
      />
      <FormDialog
        open={editingCoupon}
        setOpen={(value) => {
          if (!value) {
            setEditingCoupon(value);
          }
        }}
        title={`Editing Coupon`}
        form={
          <SimpleSelectForm
            title={"Select Coupon"}
            options={
              coupons.data?.map((item) => ({
                label: item.formattedDisplayName,
                value: item.id.toString(),
              })) ?? []
            }
            removeOption={{
              label: "Remove Coupon",
              value: "-remove",
            }}
            defaultValue={couponCode?.id?.toString()}
            onChange={(value) => {
              if (value === "-remove") {
                if (props.removeCoupon) {
                  props.removeCoupon();
                }
                setCouponCode(null);
              } else {
                const coupon = coupons.data?.find(
                  (item) => item.id === Number(value),
                );
                if (coupon) {
                  setCouponCode(coupon);
                  if (props.onCouponChange) {
                    props.onCouponChange(coupon);
                  }
                }
              }
              setEditingCoupon(false);
            }}
          />
        }
      />

      <CardHeader>
        <CardTitle>Payment Details</CardTitle>
        {props.subHeader && (
          <CardDescription>{props.subHeader}</CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              <TableHead>Price</TableHead>
              <TableHead className="text-right">Final Price</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {props.paymentInfo.displayItems.map((item, index) => {
              const originalItem = props.originalPaymentInfo?.displayItems.find(
                (originalItem) =>
                  originalItem.name === item.name &&
                  item.detail?.name === originalItem.detail?.name,
              );
              const itemAmount = getCurrencyString(item.itemAmount);
              const amountAfter = getCurrencyString(item.amountAfter);
              const conversion = convertPaymentInfoLineItemNameToFeeType(
                item.name,
              );
              const isCoupon = item.name === PaymentInfoLineItemName.CouponCode;

              const editable = props.edit && (conversion !== null || isCoupon);

              const diff =
                item.amountAfter -
                (originalItem?.amountAfter || item.amountAfter);
              const diffAmount =
                item.itemAmount - (originalItem?.itemAmount || item.itemAmount);
              const diffPositive = diff > 0;
              const diffItemAmount =
                diffAmount !== 0 ? originalItem?.itemAmount : null;
              const diffAmountAfter =
                diff !== 0 ? originalItem?.amountAfter : null;

              return (
                <TableRow key={index}>
                  <TableCell>
                    {editable ? (
                      <Button
                        variant={"link"}
                        size={"link"}
                        type={"button"}
                        onClick={() => {
                          if (conversion === OrderFeeType.CUSTOM_FEE) {
                            setCustomFee({
                              name: item.detail?.name ?? "",
                              value: item.itemAmount,
                              taxable: item.detail?.taxable ?? true,
                            });
                            return;
                          }
                          if (isCoupon) {
                            setEditingCoupon(true);
                          } else {
                            setEditedItem(conversion);
                          }
                        }}
                      >
                        {`${item.name}${
                          item.detail?.name ? ` (${item.detail?.name})` : ""
                        }`}
                      </Button>
                    ) : (
                      <>
                        {`${item.name}${
                          item.detail?.name ? ` (${item.detail?.name})` : ""
                        }`}
                      </>
                    )}
                  </TableCell>

                  <TableCell className={"space-x-1"}>
                    {diffAmount !== 0 && diffItemAmount && (
                      <span className={"text-muted-foreground text-xs"}>
                        {getCurrencyString(diffItemAmount)}
                      </span>
                    )}
                    <span>{itemAmount}</span>
                    {diffItemAmount && diffAmount !== 0 && (
                      <span className={"text-muted-foreground text-xs ml-1"}>
                        {`(${diffPositive ? "+" : ""}${getCurrencyString(
                          diffAmount,
                        )})`}
                      </span>
                    )}
                  </TableCell>

                  <TableCell
                    className={"text-right items-center justify-end flex gap-1"}
                  >
                    {diff !== 0 && diffAmountAfter && (
                      <span className={"text-muted-foreground text-xs"}>
                        {getCurrencyString(diffAmountAfter)}
                      </span>
                    )}
                    <span>{amountAfter}</span>
                    {diff !== 0 && diff && (
                      <span className={"text-muted-foreground text-xs"}>
                        {`(${diffPositive ? "+" : ""}${getCurrencyString(
                          diff,
                        )})`}
                      </span>
                    )}
                    {props.edit && conversion === OrderFeeType.CUSTOM_FEE && (
                      <Button
                        variant={"ghost"}
                        size={"icon"}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          if (props.onChange) {
                            props.onChange(
                              {
                                type: "CUSTOM_FEE",
                                name: item.detail?.name ?? "",
                                taxable: item?.detail?.taxable ?? true,
                              },
                              0,
                            );
                          }
                        }}
                      >
                        <Trash className={"w-4 h-4"} />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
            <TableRow>
              <TableCell colSpan={2}>
                <strong>Final Total</strong>
              </TableCell>
              <TableCell className={"text-right"}>
                <strong>
                  {getCurrencyString(props.paymentInfo.finalTotal)}
                </strong>
              </TableCell>
            </TableRow>
            {props.edit && !couponCode && (
              <TableRow>
                <TableCell colSpan={3}>
                  <Button
                    variant={"link"}
                    size={"link"}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      setEditingCoupon(true);
                    }}
                  >
                    Apply Coupon
                  </Button>
                </TableCell>
              </TableRow>
            )}
            {props.edit && (
              <TableRow>
                <TableCell colSpan={3}>
                  <Button
                    variant={"link"}
                    size={"link"}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      setCustomFee({
                        name: "",
                        value: 0,
                        taxable: true,
                      });
                    }}
                  >
                    Add Custom Fee
                  </Button>
                </TableCell>
              </TableRow>
            )}

            {props?.paidItems?.map((item, index) => {
              return (
                <TableRow key={index}>
                  <TableCell className={"text-left"}>{`${item.methodId
                    ?.charAt(0)
                    ?.toUpperCase()}${item.methodId?.slice(1)}`}</TableCell>
                  <TableCell>{item.method}</TableCell>
                  <TableCell className={"text-right"}>
                    {getCurrencyString(item.amount)}
                  </TableCell>
                </TableRow>
              );
            })}

            {props?.due !== undefined && props.due > 0 && (
              <TableRow>
                <TableCell colSpan={2}>
                  <strong>Amount Owed</strong>
                </TableCell>
                <TableCell className={"text-right"}>
                  <strong>{getCurrencyString(props.due)}</strong>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default PaymentDetailsCard;
