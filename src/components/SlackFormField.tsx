import { useState } from "react";
import { CheckCircle, ExternalLink, Loader2, XCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem as FormValueItem,
  FormLabel,
} from "~/components/ui/form";
import FormItem from "~/components/FormLayout/FormItem";
import { toast } from "sonner";

interface SlackFormFieldProps {
  form: any;
  isPending?: boolean;
}

export function SlackFormField({
  form,
  isPending = false,
}: SlackFormFieldProps) {
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<"success" | "error" | null>(
    null,
  );

  const testWebhook = async () => {
    const webhookUrl = form.getValues("slackWebhookUrl");
    if (!webhookUrl) return;

    setIsTesting(true);
    setTestResult(null);

    try {
      const request = await fetch("/api/backend/slack/test-notification", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!request.ok) {
        toast.error(
          "Failed to send test message. Try saving and testing again.",
        );
        setTestResult("error");
        return;
      }
    } catch {
      setTestResult("error");
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <FormField
      control={form.control}
      name="forwardIncomingMessagesToSlack"
      render={({ field }) => (
        <div className="rounded-lg border p-4 pb-2">
          <FormValueItem className="flex flex-row items-center justify-between">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Forward to Slack</FormLabel>
              <FormDescription>
                When enabled, incoming text messages will be forwarded to your
                Slack channel for team visibility.
              </FormDescription>
            </div>
            <FormControl>
              <Switch checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
          </FormValueItem>
          {field.value && (
            <div className="px-8 border-l-2 border-gray-200 space-y-5">
              <FormItem
                label="Slack Webhook URL"
                name="slackWebhookUrl"
                description={{
                  position: "bottom",
                  label: (
                    <div className="flex flex-col">
                      <p>Your Slack app's incoming webhook URL.</p>
                      <a
                        href="https://api.slack.com/messaging/webhooks"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline inline-flex items-center gap-1 text-sm"
                      >
                        Need help setting up?{" "}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  ),
                }}
                render={({ field: webhookField }) => (
                  <div className="flex gap-2">
                    <Input
                      {...webhookField}
                      placeholder="https://hooks.slack.com/services/..."
                      disabled={isPending}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={testWebhook}
                      disabled={!webhookField.value || isTesting || isPending}
                      className="shrink-0"
                    >
                      {isTesting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        "Test"
                      )}
                    </Button>
                  </div>
                )}
              />

              <FormItem
                label="Channel Override (Optional)"
                name="slackChannelName"
                description={{
                  position: "bottom",
                  label:
                    "Leave blank to use the webhook's default channel. Include # for channels or @ for DMs.",
                }}
                render={({ field: channelField }) => (
                  <Input
                    {...channelField}
                    placeholder="e.g. #customer-support"
                    disabled={isPending}
                  />
                )}
              />

              {testResult && (
                <div className="pb-4">
                  <Alert
                    className={
                      testResult === "success"
                        ? "border-green-200 bg-green-50"
                        : "border-red-200 bg-red-50"
                    }
                  >
                    <div className="flex items-center gap-2">
                      {testResult === "success" ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <AlertDescription
                        className={
                          testResult === "success"
                            ? "text-green-800"
                            : "text-red-800"
                        }
                      >
                        {testResult === "success"
                          ? "Test message sent! Check your Slack channel."
                          : "Test failed. Check your webhook URL."}
                      </AlertDescription>
                    </div>
                  </Alert>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    />
  );
}
