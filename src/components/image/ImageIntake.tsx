import { ImageUploadSimpleType } from "~/components/image/image";
import Image from "next/image";
import { Upload, X } from "lucide-react";
import ImageSelectionDialog from "~/components/ImageSelectionDialog";
import { Button } from "~/components/ui/button";

type ImageIntakeProps = {
  rearrange?: boolean;
  remove?: boolean;
  single?: boolean;
  images: ImageUploadSimpleType[];
  onDeleted?: (image: ImageUploadSimpleType) => void;
  onRearranged?: (images: ImageUploadSimpleType[]) => void;
  onUploaded: (id: ImageUploadSimpleType) => void;
};

const ImageUploadButton = ({
  onUpload,
}: {
  onUpload: (id: string, url: string) => void;
}) => (
  <ImageSelectionDialog onSelection={onUpload}>
    <button
      type={"button"}
      className="flex bg- aspect-square w-full items-center justify-center rounded-md border border-dashed hover:bg-accent bg-border/30"
    >
      <Upload className="h-4 w-4 text-muted-foreground" />
      <span className="sr-only">Upload</span>
    </button>
  </ImageSelectionDialog>
);

const ImageIntake = (props: ImageIntakeProps) => {
  const heroImage = props.images.at(0);

  return (
    <div className={"grid gap-2"}>
      {heroImage && (
        <div className={"relative group"}>
          <Image
            src={heroImage.url}
            alt={heroImage.name}
            width={300}
            height={300}
            className={
              "aspect-square w-full rounded-md object-contain object-center bg-muted/60 border"
            }
          />
          {props.remove && (
            <div className={"absolute top-0 right-0.5"}>
              <Button
                variant={"ghost"}
                size={"icon"}
                onClick={() => props.onDeleted && props.onDeleted(heroImage)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          )}
        </div>
      )}
      <div className={"grid gap-2 grid-cols-3"}>
        {props.images.slice(1).map((image, index) => (
          <div key={index} className={`relative`}>
            <Image
              src={image.url}
              alt={image.name}
              width={84}
              height={84}
              className={
                "aspect-square w-full rounded-md object-contain object-center bg-muted/60 border"
              }
            />
            {props.remove && (
              <div className={"absolute top-0 right-0.5"}>
                <Button
                  variant={"outline"}
                  size={"icon"}
                  onClick={() => props.onDeleted && props.onDeleted(image)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            )}
          </div>
        ))}
        {(props.single !== true || props.images.length === 0) && (
          <ImageUploadButton
            onUpload={(id, url) => {
              props.onUploaded({
                id,
                url,
                name: "",
              });
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ImageIntake;
