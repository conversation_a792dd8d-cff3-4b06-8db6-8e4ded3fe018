import { fullImageUrl } from "~/server/globalTypes";
import { ImageIcon } from "lucide-react";
import React from "react";
import Image from "next/image";

type ImageAvatarPlaceholderProps = {
  imageUrl: string | { url: string } | undefined;
  alt: string;
};

const ImageAvatarPlaceholder = (props: ImageAvatarPlaceholderProps) => {
  const [isImageLoaded, setIsImageLoaded] = React.useState(false);

  return (
    <div
      className={
        "flex items-center justify-center h-6 w-6 shrink-0 overflow-hidden border rounded-md"
      }
    >
      {props.imageUrl && (
        <Image
          className={"aspect-square w-5 h-5 rounded-md"}
          quality={50}
          onLoad={() => setIsImageLoaded(true)}
          src={fullImageUrl(props.imageUrl) ?? ""}
          alt={props.alt}
          width={150}
          height={150}
        />
      )}
      {(!props.imageUrl || !isImageLoaded) && (
        <div>
          <ImageIcon className={"w-5 h-5"} />
        </div>
      )}
    </div>
  );
};

export default ImageAvatarPlaceholder;
