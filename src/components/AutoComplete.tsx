import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "~/components/ui/command";
import { cn } from "~/lib/utils";
import React, { useEffect } from "react";
import { CommandList } from "cmdk";
import Skeleton from "react-loading-skeleton";

type AutoCompleteSelectable = {
  value: string;
  label: string;
};

type AutoCompleteProps = {
  loading: boolean;
  onValueChange: (search: string) => void;
  onSelect: (search: string) => void;
  selectable: AutoCompleteSelectable[];
  value?: string;
};

const AutoComplete = (props: AutoCompleteProps) => {
  const inputRef = React.useRef<HTMLInputElement | null>(null);
  const [open, setOpen] = React.useState(false);

  useEffect(() => {
    if (props.value && inputRef?.current) {
      inputRef.current.value = props.value;
    }
  }, [props.value]);

  return (
    <Command className="overflow-visible bg-transparent mb-4">
      <CommandInput
        ref={(item) => {
          inputRef.current = item;
        }}
        value={props.value}
        placeholder="Search an address"
        className="bg-transparent outline-none placeholder:text-muted-foreground flex-1"
        onBlur={() => {
          setTimeout(() => {
            setOpen(false);
          }, 150);
        }}
        onFocus={() => setOpen(true)}
        onValueChange={(search) => {
          props.onValueChange(search);
        }}
      />

      <CommandList className="relative">
        {open && (
          <div className="absolute w-full z-10 top-0 mt-2 bg-popover text-popover-foreground shadow-md outline-none animate-in">
            <CommandGroup className="h-full overflow-auto border rounded-md">
              {props.selectable.length > 0 &&
                !props.loading &&
                props.selectable.map(({ value, label }, index) => (
                  <CommandItem
                    key={`${value}-${index}`}
                    value={value}
                    className={cn(
                      "flex items-center gap-2 w-full cursor-pointer",
                    )}
                    onSelect={(item) => {
                      props.onSelect(item);
                      setOpen(false);
                    }}
                  >
                    {label}
                  </CommandItem>
                ))}
            </CommandGroup>
            <CommandEmpty className="select-none rounded-sm px-2 py-3 text-sm flex justify-center">
              {props.loading ? (
                <div>
                  <Skeleton />
                </div>
              ) : (
                "No results found."
              )}
            </CommandEmpty>
          </div>
        )}
      </CommandList>
    </Command>
  );
};

export default AutoComplete;
