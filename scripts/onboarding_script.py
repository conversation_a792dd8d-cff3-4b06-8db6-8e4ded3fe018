import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import csv
import json
import jwt
import datetime
# Define the URL
product_url = "http://localhost:3000/api/backend/onboarding/products"
order_url = "http://localhost:3000/api/backend/onboarding/orders"
customers_url = "http://localhost:3000/api/backend/onboarding/customers"

def customers_csv_to_json(csv_file_path):
    json_array = []
    # Read the CSV file
    with open(csv_file_path, mode='r', encoding='utf-8') as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            # Map the CSV row to a JSON object
            
            #split location by new lines to match format in location/types.ts
            row["address"] = row["address"].split("\\n")
            json_object = {
                "firstName": row["firstName"],
                "lastName": row["lastName"],
                "email": row["email"],
                "phoneNumber": row["phoneNumber"],
                "internalNotes": row["internalNotes"],
                "address": {
                    "line1": row["address"][0],
                    "line2": None if row["address"][1] == 'Undefined' else row["address"][1],
                    "city": row["address"][2],
                    "state": row["address"][3],
                    "postalCode": row["address"][4],
                    "country": row["address"][5]
                },
            }
            # Add the JSON object to the array
            json_array.append(json_object)
    return json_array
        
def orders_csv_to_json(csv_file_path):
    # Initialize an empty list to store the JSON objects
    json_array = []
    
    # Read the CSV file
    with open(csv_file_path, mode='r', encoding='utf-8') as csv_file:
        csv_reader = csv.DictReader(csv_file)

        # Iterate over each row in the CSV file
         

        for row in csv_reader:
            # Map the CSV row to a JSON object
            
            #split location by new lines to match format in location/types.ts
            row["eventAddress"] = row["eventAddress"].split("\\n")
            json_object = {
                "createdAt": row["createdAt"],
                "updatedAt": row["updatedAt"],
                "state": row["state"],
                "startTime": row["startTime"],
                "endTime": row["endTime"],
                "setupSurfaceId": int(row["setupSurfaceId"]),
                "baseTotal": float(row["baseTotal"]),
                "taxExempt": row["taxExempt"].lower() == "true",  # Convert to boolean
                "finalTotal": float(row["finalTotal"]),
                "totalPaid": float(row["totalPaid"]),
                "eventAddressId": int(row["eventAddressId"]),
                "eventAddress": {
                    "line1": row["eventAddress"][0],
                    "line2": None if row["eventAddress"][1] == 'Undefined' else row["eventAddress"][1],
                    "city": row["eventAddress"][2],
                    "state": row["eventAddress"][3],
                    "postalCode": row["eventAddress"][4],
                    "country": row["eventAddress"][5]
                },
                "customerEmail": row.get("customerEmail", None), # optional depending on the data the onboarding Customer has
                "customerPhone": row.get("customerPhone", None), # optional depending on the data the onboarding Customer has
                "customerFirstName": row.get("customerFirstName", None), # optional depending on the data the onboarding Customer has
                "customerLastName": row.get("customerLastName", None), # optional depending on the data the onboarding Customer has
                "accountId": int(row["accountId"])
            }
            # Add the JSON object to the array
            json_array.append(json_object)
    
    return json_array

def products_csv_to_json(csv_file_path):
    # Initialize an empty list to store the JSON objects
    json_array = []

    # Read the CSV file
    with open(csv_file_path, mode='r', encoding='utf-8') as csv_file:
        csv_reader = csv.DictReader(csv_file)

        # Iterate over each row in the CSV file
        for row in csv_reader:
            # Map the CSV row to a JSON object
            json_object = {
                "category": int(row["category"]),
                "description": row["description"],
                "name": row["name"],
                "price": float(row["price"]),
                "quantity": int(row["quantity"]),
                "setupTimeMinutes": int(row["setupTimeMinutes"]),
                "takeDownTimeMinutes": int(row["takeDownTimeMinutes"]),
                "display": bool(row["display"]) # Convert to boolean
            }
            # Add the JSON object to the array
            json_array.append(json_object)
    
    return json_array

def send_post_request(url, payload, headers=None):
    try:
        # Send the POST request
        response = requests.post(url, json=payload, headers=headers)
        
        # Check if the request was successful
        if response.status_code == 200:
            print("Request was successful")
            print("Response data:", response.text)
            print(response.headers)
        else:
            print(f"Failed to send request. Status code: {response.status_code}")
            # print("Response text:", response.text)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    api_key = "SuperSecretSharedSecret"
    # Define the headers, if needed
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    # PRODUCTS
    # products_csv_file_path = 'products_test.csv'
    # products = products_csv_to_json(products_csv_file_path)
    # json_data = {
    #     "products": products,
    #     "account": "Demo"
    # }
    # send_post_request(product_url, json_data, headers=headers)  # With headers

    # Customers 
    customers_csv_file_path = 'customers_test.csv'
    customers = customers_csv_to_json(customers_csv_file_path)
    json_data = {
        "customers": customers,
        "account": "Demo"
    }
    send_post_request(customers_url, json_data, headers=headers)  # With headers

    # ORDERS 
    orders_csv_file_path = 'orders_test.csv'
    orders = orders_csv_to_json(orders_csv_file_path)
    json_data = {
        "orders": orders,
        "account": "Demo"
    }
    send_post_request(order_url, json_data, headers=headers)  # With headers

