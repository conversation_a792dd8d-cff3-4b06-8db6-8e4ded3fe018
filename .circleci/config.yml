# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/configuration-reference

version: 2.1
orbs:
  node: circleci/node@5.1.1
  doctl: digitalocean/cli@0.1.1

# define reusable jobs that are references in workflow below
jobs:
  npm_test:
    executor: node/default
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: npm
      - run:
          command: npm run build
          name: Build and Lint
          environment:
            NEXTAUTH_SECRET: something
            SKIP_ENV_VALIDATION: true
      - run:
          command: npm run test
          name: Run tests
          environment:
            NEXTAUTH_SECRET: something
            NODE_ENV: test
            SKIP_ENV_VALIDATION: true
      - persist_to_workspace:
          root: ~/project
          paths:
            - .
  digital_ocean_garbage_collection:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - doctl/install
      - doctl/initialize:
          digitalocean-access-token: DO_REGISTRY_TOKEN
      - run:
          root: ~/project
          command: |
            # doctl registry login -t $DO_REGISTRY_TOKEN
            doctl registry garbage-collection start --interactive false
  prisma_push_staging:
    executor: node/default
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: npm
      - run:
          command: |
            export POSTGRES_PRISMA_URL=$STAGING_DATABASE_URL
            npm install prisma
            npx prisma migrate deploy
          name: Deploy Prisma Migrations
          environment:
            NEXTAUTH_SECRET: something
            SKIP_ENV_VALIDATION: true
  prisma_push_prod:
    executor: node/default
    steps:
      - checkout
      - node/install-packages:
          pkg-manager: npm
      - run:
          command: |
            export POSTGRES_PRISMA_URL=$PROD_DATABASE_URL
            npm install prisma
            npx prisma migrate deploy
          name: Deploy Prisma Migrations
          environment:
            NEXTAUTH_SECRET: something
            SKIP_ENV_VALIDATION: true
  build_and_push_to_staging:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - doctl/install
      - doctl/initialize:
          digitalocean-access-token: DO_REGISTRY_TOKEN
      - setup_remote_docker
      - run:
          root: ~/project
          command: |
            doctl registry login -t $DO_REGISTRY_TOKEN
            docker build -t registry.digitalocean.com/partyplatform/platform:latest .
            docker tag  registry.digitalocean.com/partyplatform/platform:latest registry.digitalocean.com/partyplatform/platform:$CIRCLE_SHA1
            docker push registry.digitalocean.com/partyplatform/platform
      - persist_to_workspace:
          root: ~/project
          paths:
            - .
  build_and_push_to_prod:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - doctl/install
      - doctl/initialize:
          digitalocean-access-token: DO_REGISTRY_TOKEN
      - setup_remote_docker
      - run:
          root: ~/project
          command: |
            doctl registry login -t $DO_REGISTRY_TOKEN
            docker build -t registry.digitalocean.com/partyplatform/platform:prod .
            docker tag  registry.digitalocean.com/partyplatform/platform:prod registry.digitalocean.com/partyplatform/platform:prod-$CIRCLE_SHA1
            docker push registry.digitalocean.com/partyplatform/platform:prod
      - persist_to_workspace:
          root: ~/project
          paths:
            - .

workflows:
  test_and_deploy:
    jobs:
      - npm_test
      - prisma_push_staging:
          requires: # this is the job name from above
            - npm_test
          filters:
            branches:
              only:
                - staging
      - prisma_push_prod:
          requires: # this is the job name from above
            - npm_test
          filters:
            branches:
              only:
                - main
      - build_and_push_to_staging:
          requires: # this is the job name from above
            - npm_test
          filters:
            branches:
              only:
                - staging
      - build_and_push_to_prod:
          requires: # this is the job name from above
            - npm_test
          filters:
            branches:
              only:
                - main

  # run_garbage_collection:
  #   # triggers:
  #   #   - schedule:
  #   #       cron: "0 10 * * *" # run every day at 4am MST (10am UTC)
  #   #       filters:
  #   #         branches:
  #   #           only:
  #   #             - main
  #   jobs:
  #     - digital_ocean_garbage_collection:
  #         filters:
  #           branches:
  #             only:
  #               - main
