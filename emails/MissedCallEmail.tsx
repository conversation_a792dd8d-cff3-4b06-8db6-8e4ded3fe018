import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { Button, Html, Preview } from "@react-email/components";

import { renderToStaticMarkup } from "react-dom/server";

export type MissedCallEmailProps = {
  sender: string;
  receivedString: string;
  conversationId: string;
};

const MissedCallEmail = (props: MissedCallEmailProps) => {
  return (
    <Html>
      <Preview>{`Missed Call from ${props.sender}`}</Preview>
      <div style={emailStyles.body}>
        <div style={emailStyles.container}>
          <h2 style={emailStyles.accentColor1}>You Have a Missed Call</h2>
          <span style={emailStyles.footer}>
            You have a missed call through Party Rental Platform. Here are the
            details:
          </span>
          <p>
            <strong>Caller:</strong> {props.sender || "Not Provided"}
          </p>
          <p>
            <strong>Missed:</strong> {props.receivedString || "Not Provided"}
          </p>
          <Button
            style={emailStyles.button}
            href={`https://dash.partyrentalplatform.com/phone?conversationId=${props.conversationId}`}
          >
            Call Back
          </Button>
        </div>
      </div>
    </Html>
  );
};

MissedCallEmail.PreviewProps = {
  sender: "John Doe",
  message: "Hi, I'm interested in renting a bounce house.",
  receivedString: new Date().toLocaleString(),
};

export const MISSED_CALL_EMAIL: ReactEmailTemplate<MissedCallEmailProps> = {
  emailSender: "contact",
  subject: "You have a Missed Call",
  subjectOverride: (value) => {
    return `Missed Call from ${value.sender}`;
  },
  html: (value) => {
    return renderToStaticMarkup(MissedCallEmail(value));
  },
};

export default MissedCallEmail;

const emailStyles = {
  body: {
    backgroundColor: "#f2f2f2",
    color: "#333333",
    fontFamily: "Arial, sans-serif",
    padding: "20px",
  },
  button: {
    backgroundColor: "#027373",
    borderRadius: "5px",
    color: "#f2f2f2",
    fontSize: "16px",
    fontWeight: "500",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "block",
    padding: "10px",
  },
  container: {
    maxWidth: "600px",
    margin: "auto",
    backgroundColor: "#ffffff",
    padding: "20px",
    borderRadius: "8px",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
  },
  blockquote: {
    fontStyle: "italic",
    color: "#89888c",
    paddingLeft: "14px",
    borderLeft: "3px solid #f27830",
  },
  accentColor1: {
    color: "#027373",
  },
  accentColor3: {
    color: "#89888c",
  },
  footer: {
    color: "#8898aa",
    fontSize: "12px",
    lineHeight: "16px",
  },
  // If you need to use the third accent color somewhere, you can define another key here
};
