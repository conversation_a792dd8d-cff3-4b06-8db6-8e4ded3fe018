import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { Html } from "@react-email/components";

import { renderToStaticMarkup } from "react-dom/server";

export type ContactEmailProps = {
  accountName: string;
  emailFrom: string;
  message: string;
  name: string;
  content: string;
  subject: string;
};

const ContactEmail = (props: ContactEmailProps) => {
  return (
    <Html>
      <div style={emailStyles.body}>
        <div style={emailStyles.container}>
          <h2 style={emailStyles.accentColor1}>New Contact Request</h2>
          <p>Hey {props.accountName || "Team"},</p>
          <p>You have received a new contact request. Here are the details:</p>
          <p>
            <strong>Name:</strong> {props.name || "Not Provided"}
          </p>
          <p>
            <strong>Email:</strong> {props.emailFrom || "Not Provided"}
          </p>
          <p>
            <strong>Message:</strong>
          </p>
          <blockquote style={emailStyles.blockquote}>
            {props.content || "No message provided"}
          </blockquote>
        </div>
      </div>
    </Html>
  );
};

export const CONTACT_EMAIL: ReactEmailTemplate<ContactEmailProps> = {
  emailSender: "contact",
  subject: "Customer Contact",
  subjectOverride: (value) => {
    return `Customer Contact ${value.subject}`;
  },
  html: (value) => {
    return renderToStaticMarkup(ContactEmail(value));
  },
};

export default ContactEmail;

const emailStyles = {
  body: {
    backgroundColor: "#f2f2f2",
    color: "#333333",
    fontFamily: "Arial, sans-serif",
    padding: "20px",
  },
  container: {
    maxWidth: "600px",
    margin: "auto",
    backgroundColor: "#ffffff",
    padding: "20px",
    borderRadius: "8px",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
  },
  blockquote: {
    fontStyle: "italic",
    color: "#89888c",
    paddingLeft: "14px",
    borderLeft: "3px solid #f27830",
  },
  accentColor1: {
    color: "#027373",
  },
  accentColor3: {
    color: "#89888c",
  },
  // If you need to use the third accent color somewhere, you can define another key here
};
