import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { renderToStaticMarkup } from "react-dom/server";
import {
  Body,
  But<PERSON>,
  Column,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

export type NewAccountEmailProps = {
  temporaryPassword: string | undefined;
};

interface WelcomeEmailProps {
  steps?: {
    id: number;
    Description: React.ReactNode;
  }[];
  links?: string[];
}

const PropDefaults: WelcomeEmailProps = {
  steps: [
    {
      id: 1,
      Description: (
        <li className="mb-20" key={1}>
          <strong>Add your first product.</strong> Add your products to your
          dashboard and have them immediately show up on your website!
        </li>
      ),
    },
    {
      id: 2,
      Description: (
        <li className="mb-20" key={2}>
          <strong>Check your settings and schedule.</strong> Make sure your
          hours and settings are configured correctly for your business.
        </li>
      ),
    },
    {
      id: 3,
      Description: (
        <li className="mb-20" key={3}>
          <strong>Configure payment processing.</strong> Configure your Stripe
          account to start processing payments through your website.
        </li>
      ),
    },
    {
      id: 4,
      Description: (
        <li className="mb-20" key={4}>
          <strong>Set up a custom domain.</strong> Add your own custom domain to
          your new website on PRP.
        </li>
      ),
    },
  ],
};

const NewAccountEmail = (props: NewAccountEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Party Rental Platform Welcome</Preview>

      <Tailwind
        config={{
          theme: {
            extend: {
              colors: {
                brand: "#027373",
                offwhite: "#fafbfb",
              },
              spacing: {
                0: "0px",
                20: "20px",
                45: "45px",
              },
            },
          },
        }}
      >
        <Body className="bg-offwhite text-base font-sans">
          <Img
            src={`https://imagedelivery.net/6QiASA1pHsoYecw9egSmhw/f41c84e1-bb2b-4a28-5fe0-49c4aa7ea300/icon`}
            width="125"
            height="125"
            alt="PRP Logo"
            className="mx-auto my-20"
          />
          <Container className="bg-white p-45">
            <Heading className="text-center my-0 leading-8">
              Welcome to Party Rental Platform
            </Heading>

            <Section>
              <Row>
                <Text className="text-base">
                  Congratulations! You're joining a community of rental business
                  owners around the world who use Party Rental Platform to
                  manage their rentals, inventory, and more!
                </Text>

                <Text className="text-base">Here's how to get started:</Text>
              </Row>
            </Section>

            <ul>{PropDefaults.steps?.map(({ Description }) => Description)}</ul>

            <Section className="text-center">
              <Button
                className="bg-brand text-white rounded-lg py-3 px-[18px]"
                href={"https://dash.partyrentalplatform.com"}
              >
                Go to your dashboard
              </Button>
              <br />
              {props.temporaryPassword && (
                <p className={"bg-gray-100 p-2 rounded-md text-xs"}>
                  Temporary Password: {props.temporaryPassword}
                </p>
              )}
            </Section>

            <Section className="mt-45">
              <Row>
                <Column>
                  <Text className="text-base">
                    If you have any questions, feel free to reply to this email
                    or email us at{" "}
                    <Link href="mailto:<EMAIL>">
                      <EMAIL>
                    </Link>
                  </Text>
                </Column>
              </Row>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

NewAccountEmail.PreviewProps = {
  temporaryPassword: "1327naavg0123=-",
} as NewAccountEmailProps;

export const NEW_ACCOUNT_EMAIL: ReactEmailTemplate<NewAccountEmailProps> = {
  emailSender: "prp",
  subject: "Get Started with Your New Account",
  html: (value) => {
    return renderToStaticMarkup(NewAccountEmail(value));
  },
};

export default NewAccountEmail;
