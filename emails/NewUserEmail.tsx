import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { renderToStaticMarkup } from "react-dom/server";
import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import process from "process";

export type NewUserEmailProps = {
  accountLogo: string | undefined;
  resetToken: string;
  accountName: string;
  userName: string;
};

const NewUserEmail = (props: NewUserEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{`Welcome to ${props.accountName}!`}</Preview>

      <Tailwind>
        <Body className={"bg-white my-auto mx-auto font-sans px-2"}>
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px]">
            {props.accountLogo && (
              <Section className={"mt-[32px]"}>
                <Img
                  src={props.accountLogo}
                  width="245"
                  height="160"
                  alt={`${props.accountName} Logo`}
                  className={"my-0 mx-auto"}
                />
              </Section>
            )}
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              New Staff Account
            </Heading>
            <Text className={"text-black text-[14px] leading-[24px]"}>
              Welcome {props.userName},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              You have been invited to join {props.accountName} as a staff
              member, to join please set a password with the link below:{" "}
              <Link
                href={`https://dash.partyrentalplatform.com/auth/reset-password?account=${props.accountName}&token=${props.resetToken}&user=${props.userName}`}
              >
                Create Password
              </Link>
            </Text>
            <Hr />
            <Text className={"text-sm m-0 mt-1 p-0"}>Thanks,</Text>
            <Text className={"text-sm m-0 p-0"}>{props.accountName} Team</Text>

            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you do not recognize this email you can safely ignore it.
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

NewUserEmail.PreviewProps = {
  accountName: "Account Name",
  accountLogo:
    "https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/381ff54a-dcbe-4fea-c5c4-f6210e66fd00/w=490,h=330",
  userName: "SteveD",
  resetToken: "123456",
} as NewUserEmailProps;

export const NEW_USER_EMAIL: ReactEmailTemplate<NewUserEmailProps> = {
  emailSender: "prp",
  subject: "Get Started with Your New Account",
  html: (value) => {
    return renderToStaticMarkup(NewUserEmail(value));
  },
};

export default NewUserEmail;
