import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { renderToStaticMarkup } from "react-dom/server";

export type CustomEmailProps = {
  emailText: string;
  emailSubject: string;
};

const CustomEmail = (props: CustomEmailProps) => {
  return <div dangerouslySetInnerHTML={{ __html: props.emailText }}></div>;
};

export const CUSTOM_EMAIL: ReactEmailTemplate<CustomEmailProps> = {
  emailSender: "party",
  subject: "Party Time!",
  subjectOverride: (value) => {
    return `${value.emailSubject}`;
  },
  html: (value) => {
    return renderToStaticMarkup(CustomEmail(value));
  },
  text: (value) => {
    return value.emailText;
  },
};

export default CustomEmail;
