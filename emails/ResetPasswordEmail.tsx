import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { renderToStaticMarkup } from "react-dom/server";
import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

export type ResetPasswordEmailProps = {
  accountLogo: string | undefined;
  resetToken: string;
  accountName: string;
  userName: string;
};

const ResetPasswordEmail = (props: ResetPasswordEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{`Password Reset!`}</Preview>

      <Tailwind>
        <Body className={"bg-white my-auto mx-auto font-sans px-2"}>
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px]">
            {props.accountLogo && (
              <Section className={"mt-[32px]"}>
                <Img
                  src={props.accountLogo}
                  width="245"
                  height="160"
                  alt={`${props.accountName} Logo`}
                  className={"my-0 mx-auto"}
                />
              </Section>
            )}
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Password Reset
            </Heading>
            <Text className={"text-black text-[14px] leading-[24px]"}>
              Hey {props.userName},
            </Text>
            <Text className="text-black text-[14px] leading-[24px]">
              You have requested a password reset. Please use the following link
              to reset your password:{" "}
              <Link
                href={`https://dash.partyrentalplatform.com/auth/reset-password?account=${props.accountName}&token=${props.resetToken}&user=${props.userName}`}
              >
                Reset Password
              </Link>
            </Text>
            <Hr />
            <Text className={"text-sm m-0 mt-1 p-0"}>Thanks,</Text>
            <Text className={"text-sm m-0 p-0"}>{props.accountName} Team</Text>

            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you did not request a password reset, please contact us{" "}
              <strong>immediately</strong>.
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

ResetPasswordEmail.PreviewProps = {
  accountName: "Account Name",
  // accountLogo:
  //   "https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/381ff54a-dcbe-4fea-c5c4-f6210e66fd00/w=490,h=330",
  userName: "SteveD",
  resetToken: "123456",
} as ResetPasswordEmailProps;

export const RESET_PASSWORD_EMAIL: ReactEmailTemplate<ResetPasswordEmailProps> =
  {
    emailSender: "prp",
    subject: "Password Reset",
    html: (value) => {
      return renderToStaticMarkup(ResetPasswordEmail(value));
    },
  };

export default ResetPasswordEmail;
