import React from "react";
import { ReactEmailTemplate } from "~/server/email/sendMail";
import { renderToStaticMarkup } from "react-dom/server";
import {
  ContractSharedItems,
  OrderContractItems,
  OrderPaidItems,
} from "~/components/Contract/Top/types";
import ContractItemList from "~/components/Contract/ItemList";
import { getCurrencyString, getPaymentInfo } from "~/server/lib/currency";
import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";

export type ReceiptEmailProps = {
  orderId: string;
  contractSharedItems: ContractSharedItems;
  contractItems: OrderContractItems[];
  paidItems: OrderPaidItems[];
  refundedAmount?: number;
  contractLink: string;
  paymentLink: string;
  accountName: string;
  accountLogo: string | undefined;
  accountMinimumDeposit: number;
  accountTimezone: string;
  newOrder?: boolean;
  fullyPaid?: boolean;
  silent?: boolean;
};

const ReceiptEmail = (props: ReceiptEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>{`${props.newOrder ? "New " : "Updated "}Order Receipt from ${
        props.accountName
      }`}</Preview>

      <Tailwind>
        <Body className={"bg-white my-auto mx-auto font-sans px-2"}>
          <Container className="border border-solid border-[#eaeaea] rounded my-[40px] mx-auto p-[20px] max-w-[465px]">
            {props.accountLogo && (
              <Section className={"mt-[32px]"}>
                <Img
                  src={props.accountLogo}
                  width="245"
                  height="160"
                  alt={`${props.accountName} Logo`}
                  className={"my-0 mx-auto"}
                />
              </Section>
            )}
            <Heading className="text-black text-[24px] font-normal text-center p-0 my-[30px] mx-0">
              Order Receipt
            </Heading>
            {props.silent === true ? (
              <Text className="text-black text-[14px] leading-[24px]">
                This is a silent receipt. No email will be sent to the customer.
              </Text>
            ) : (
              <Text className="text-black text-[14px] leading-[24px]">
                Thank you for your order! Below is a summary of your order.
                Please click the link below to read and sign your contract. You
                can also click the link to pay any remaining balance.
              </Text>
            )}
            <Link href={props.contractLink}>
              Click here to read and sign your contract
            </Link>
            <br />
            <Link href={props.paymentLink}>
              Click here to pay any remaining balance
            </Link>
            <Hr />
            <ContractItemList
              contractItems={props.contractItems}
              paidItems={props.paidItems}
              refundAmount={props.refundedAmount}
              contractSharedItems={props.contractSharedItems}
              email={true}
              accountTimezone={props.accountTimezone}
              depositPercentage={props.accountMinimumDeposit}
            />
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

ReceiptEmail.PreviewProps = {
  accountName: "Account Name",
  accountLogo:
    "https://centralarkansasinflatables.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/381ff54a-dcbe-4fea-c5c4-f6210e66fd00/w=490,h=330",
  customerName: "Customer Name",
  contractLink: "https://centralarkansasinflatables.com/contract/123",
  paymentLink: "https://centralarkansasinflatables.com/payment/123",
  accountTimezone: "America/Chicago",
  paidItems: [
    {
      method: "Stripe-Webhook",
      methodId: "123",
      amount: 100,
    },
  ],
  contractSharedItems: {
    ...getPaymentInfo({
      baseTotal: 100,
      discounts: [],
      damageWaiver: {
        percentage: 5,
      },
      fees: [
        {
          name: null,
          type: "TRAVEL_FEE",
          amount: 17.32,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 100,
    }),
    rentalStartDate: new Date(),
    rentalEndDate: new Date(),
  },
  orderId: "123",
  accountMinimumDeposit: 25,
  contractItems: [
    {
      name: "All American Bounce House",
      pricePerUnit: getCurrencyString(100),
      quantity: 1,
      total: getCurrencyString(100),
    },
  ],
} as ReceiptEmailProps;

export const RECEIPT_EMAIL: ReactEmailTemplate<ReceiptEmailProps> = {
  emailSender: "receipt", // <EMAIL>
  subjectOverride: (value) =>
    `Your ${
      value.newOrder ? "" : "Updated "
    }Receipt from ${value.accountName.replace(
      "CentralArkansasInflatables",
      "Central Arkansas Inflatables",
    )} - Order #${value.orderId}`,
  subject: "Your Order Receipt",
  html: (value) => {
    return renderToStaticMarkup(ReceiptEmail(value));
  },
  sendAdminCopy: true,
};

export default ReceiptEmail;
