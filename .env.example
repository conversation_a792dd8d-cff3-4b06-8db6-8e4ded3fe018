#### AWS DB ####
POSTGRES_PRISMA_URL="postgresql://test-user:test-password@localhost:5432/" <- Replace this!

##################

#### Public Env Vars ####
NEXT_PUBLIC_SELF_URL=http://localhost:3000

##################

#### Auth Env Vars ####
# These are the values for Staging so that you can reuse the session across the different applications
# Keep in mind, if you change these values, you will need to log in again.
# If you ever use the PROD DB you'll need to clear cookies before logging in again.
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="Fire"

##################

#### Cloudflare Access ####
## Images ##
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_BUCKET_NAME=images
R2_ACCOUNT_ID=20eb8dcbe341076f88dc184eff6eff50
IMAGE_BASE_URL=https://pub-72603ebd7aba4aa5aca7e181bf957533.r2.dev

##################

#### Stripe Secrets ####
# These are staging values so they're okay to exist here.
## Payment Provider for Customer ##
PRP_STRIPE_PAYMENT_WEBHOOK_SECRET=whsec_PaHqyvQv6FZ3dtSittaAjaloZTpRgq77

## PRP Stripe ##
PRP_STRIPE_WEBHOOK_SECRET=whsec_5WohyR4OdwG5Dujrq2PcmoLa125ZVpg9
NEXT_PUBLIC_PRP_STRIPE_KEY=pk_test_51Okx2OCA1CWVJ5LNteAZWtkRsJMCb44NNrSsAIK3u5XilVBM2EURPSEjYZIGi55lU5IbX8eT5jT68UP1mCFQMSd700CbACsdGQ
PRP_STRIPE_PRIVATE_KEY=sk_test_51Okx2OCA1CWVJ5LNy5DQPiOkz2pGOyM97Jg5PwhezcyMSkLqZNmyrtVREQ6v5mqk9sBIwlpQ7KAhEE207jhBmWAe00At1H1ZsN

## Central Arkansas Inflatables Stripe ##
STRIPE_SECRET_KEY=sk_test_51OEdrxH0Jv34YGonDllKs7DkQkLupCIr4Y2iv0xYyiF46yKR69es3ENeKRfRdGdIJVJnIeIsIoncMBEvL0guHAZQ0047TgbmTK
STRIPE_PUBLIC_KEY=pk_test_51OEdrxH0Jv34YGonW5d2KA7RjkFKX1dJEAiq9G6LhrO8jG0uwMSQ5HVZqwMrtLbQOBMYlOrSucQPzzaWZ8k4I5bh000zGkpaE1
STRIPE_WEBHOOK_SECRET=Invalid
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_51OEdrxH0Jv34YGonW5d2KA7RjkFKX1dJEAiq9G6LhrO8jG0uwMSQ5HVZqwMrtLbQOBMYlOrSucQPzzaWZ8k4I5bh000zGkpaE1

##################

#### Google Maps ####

GOOGLE_MAPS_API_KEY=AIzaSyAQZP0aLfoF79PLnEQE48U3cbDDYat24tA

##################

#### Email ####

RESEND_EMAIL_DOMAIN=mail.partyrentalplatform.com
RESEND_API_KEY=re_abc

##################

#### Shared Secret ####
# Used for CRON and other misc systems to authenticate
SHARED_SECRET=SuperSecretSharedSecret

##################

#### Twilio ####

TWILIO_ACCOUNT_SID=Invalid
TWILIO_AUTH_TOKEN=Invalid

##################

#### POSTHOG ####

NEXT_PUBLIC_POSTHOG_KEY=phc_
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

HUBSPOT_API_KEY=Invalid

##################

#### Slack ####

SLACK_SIGNUP_WEBHOOK_URL=https://hooks.slack.com/services/XXXXXXXXX/XXXXXXXXX/XXXXXXXXXXXXXXXXXXXXXXXX

##################
