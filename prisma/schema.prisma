generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL") // uses connection pooling
}

model Permission {
  id          Int              @id @default(autoincrement())
  policy      String           @unique // reverse-dns style (e.g., com.example.app.read)
  description String?
  roles       RolePermission[]
}

model Role {
  id          Int              @id @default(autoincrement())
  name        String           @unique
  description String?
  permissions RolePermission[]
  staff       StaffRole[]
}

model RolePermission {
  roleId       Int
  permissionId Int
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
}

model Address {
  id         Int      @id @default(autoincrement())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  line1      String? // Address line 1 (e.g., street, PO Box, or company name)
  line2      String? // Address line 2 (e.g., apartment, suite, unit, or building)
  city       String? // City, District, Suburb, Town, or Village
  state      String? // district, province, state, etc
  postalCode String? // postal code, zip code, etc
  country    String? // Two letter country code
  latitude   Float?
  longitude  Float?

  Account           Account[]
  Order             Order[]
  Customer          Customer[]
  originatingRoutes TravelCache[]      @relation("Origin")
  destinationRoutes TravelCache[]      @relation("Destination")
  LogisticTimeline  LogisticTimeline[]
}

model TravelCache {
  id                Int      @id @default(autoincrement())
  originId          Int
  destinationId     Int
  distanceMiles     Int // Consider units, e.g., meters
  travelTimeMinutes Int // Consider units, e.g., seconds
  lastUpdated       DateTime @default(now())

  // Define the relationships
  origin      Address @relation("Origin", fields: [originId], references: [id], onDelete: Cascade)
  destination Address @relation("Destination", fields: [destinationId], references: [id], onDelete: Cascade)

  @@unique([originId, destinationId])
}

model LogisticTimeline {
  id                     String                   @id @default(cuid())
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  startTime              DateTime
  origin                 Address?                 @relation(fields: [originId], references: [id], onDelete: SetNull)
  originId               Int?
  tasks                  Json                     @default("{}")
  account                Account                  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId              Int
  organization           Organization             @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId         String
  LogicistcTimelineStaff LogicistcTimelineStaff[]
}

model LogicistcTimelineStaff {
  staffId    String
  timelineId String
  staff      Staff            @relation(fields: [staffId], references: [id], onDelete: Cascade)
  timeline   LogisticTimeline @relation(fields: [timelineId], references: [id], onDelete: Cascade)

  @@unique([staffId, timelineId])
}

enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELED
  PENDING
  TRIALING
}

model Subscription {
  id                   Int                @id @default(autoincrement())
  accountId            Int
  account              Account            @relation(fields: [accountId], references: [id], onDelete: Cascade)
  stripeSubscriptionId String             @unique
  planLevel            String
  status               SubscriptionStatus
  trialEndsAt          DateTime? // Nullable trial end date
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
}

model PhoneAccount {
  id           Int      @id @default(autoincrement())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  accountSid   String
  authToken    String   @default("")
  apiKeySid    String?
  apiKeySecret String?
  phoneNumber  String?

  // Settings
  voicemailText                    String?  @default("Thank you for calling {{ACCOUNT_NAME}}. We are currently unavailable to take your call. Please leave a message and we will get back to you as soon as possible.")
  showCallerId                     Boolean  @default(false)
  forwardIncomingCallsToMobile     Boolean  @default(false)
  forwardMessagesToMobile          Boolean  @default(false)
  forwardIncomingCallsTo           String[] @default([])
  forwardMessagesTo                String[] @default([])
  forwardMessagesToSlackWebhookUrl String?
  slackChannel                     String?
  mobileClickToCall                Boolean  @default(false)

  messagingStatus   String? // This is the status of the messaging service
  messagingApproved Boolean @default(false) // A2P Laws require approval for messaging

  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int     @unique
}

model PhoneContact {
  id          Int       @id @default(autoincrement())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastRead    DateTime?
  lastMessage DateTime?
  phoneNumber String
  notes       String?

  PhoneCallLog    PhoneCallLog[]
  PhoneMessageLog PhoneMessageLog[]
  account         Account           @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId       Int
  customer        Customer?         @relation(fields: [customerId], references: [id], onDelete: SetNull)
  customerId      String?           @unique
}

model PhoneCallLog {
  id             Int             @id @default(autoincrement())
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  callSid        String
  duration       Int?
  inbound        Boolean
  answered       Boolean         @default(false)
  recordingUrl   String?
  account        Account         @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  phoneContact   PhoneContact    @relation(fields: [phoneContactId], references: [id], onDelete: Cascade)
  phoneContactId Int
  phoneVoicemail PhoneVoicemail?
}

model PhoneVoicemail {
  id             Int           @id @default(autoincrement())
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  recordingUrl   String // link to the downloaded voicemail
  transcription  String?
  phoneCallLog   PhoneCallLog? @relation(fields: [phoneCallLogId], references: [id], onDelete: Cascade)
  phoneCallLogId Int?          @unique
}

model PhoneMessageLog {
  id             Int          @id @default(autoincrement())
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  messageSid     String
  body           String
  inbound        Boolean
  mediaUrl       String?
  errorMessage   String?
  account        Account      @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  phoneContact   PhoneContact @relation(fields: [phoneContactId], references: [id], onDelete: Cascade)
  phoneContactId Int
}

model Organization {
  id                            String                          @id @default(cuid())
  createdAt                     DateTime                        @default(now())
  updatedAt                     DateTime                        @updatedAt
  accounts                      Account[]
  Order                         Order[]
  Product                       Product[]
  Category                      Category[]
  LogisticTimeline              LogisticTimeline[]
  AccountInventoryShareSettings AccountInventoryShareSettings[]
  ProductCleaning               ProductCleaning[]
  Job                           Job[]
}

model Account {
  id                            Int      @id @default(autoincrement())
  name                          String   @unique
  createdAt                     DateTime @default(now())
  updatedAt                     DateTime @updatedAt
  damageWaiverRate              Float? // This is the default rental protection rate for the account, if null it's disabled
  freeTravelRadius              Float? // This is the default free travel radius for the account, if null it's disabled
  travelFeePerMile              Float? // This is the default travel fee per mile for the account, if null it's disabled
  defaultTaxRate                Float    @default(6.5) // this isn't allowed to be null - we need to charge tax
  minimumOrderPaymentPercentage Float    @default(0.25) // This is the minimum payment required to place an order
  businessPhone                 String?
  businessEmail                 String?
  customDomain                  String?
  googleReviewLink              String?
  stripeAccountId               String? // this is the stripe account id for the account, if null this customer does not use stripe.
  businessTimezone              String   @default("America/New_York")
  deleted                       Boolean  @default(false)

  Organization     Organization @relation(fields: [organizationId], references: [id])
  organizationId   String
  billingAddress   Address?     @relation(fields: [billingAddressId], references: [id], onDelete: SetNull)
  billingAddressId Int?
  stripeCustomerId String?

  phoneAccount    PhoneAccount?
  phoneContact    PhoneContact[]
  phoneCallLog    PhoneCallLog[]
  phoneMessageLog PhoneMessageLog[]

  Staff                         Staff[]
  Product                       Product[]
  Category                      Category[]
  Coupon                        Coupon[]
  Schedule                      Schedule[]
  SetupSurface                  SetupSurface[]
  ImageUpload                   ImageUpload[]
  Customer                      Customer[]
  Order                         Order[]
  Sale                          Sale[]
  ShoppingSession               ShoppingSession[]
  TaxRate                       TaxRate[]
  EmailSentLog                  EmailSentLog[]
  CustomEmailDelivery           CustomEmailDelivery[]
  Subscription                  Subscription[]
  LogisticTimeline              LogisticTimeline[]
  WebsitePage                   WebsitePage[]
  websiteSettings               WebsiteSettings?
  ScheduledEmail                ScheduledEmail[]
  ScheduledEmailConfig          ScheduledEmailConfig[]
  Document                      Document[]
  OrderAuditLog                 OrderAuditLog[]
  Rule                          Rule[]
  TextBasedWebsitePage          SimpleWebsitePage[]
  AccountInventoryShareSettings AccountInventoryShareSettings?
  ProductShareSettings          ProductShareSettings[]
  ProductCleaning               ProductCleaning[]
}

model AccountInventoryShareSettings {
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  // Share settings
  receiveSharedCategories Boolean  @default(false)
  receiveSharedProducts   Boolean  @default(false)

  // Relation to Account - one-to-one relationship
  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int     @unique

  // Relation to Organization
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@index([organizationId])
}

model WebsiteSettings {
  id                Int      @id @default(autoincrement())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  settings          Json     @default("{}")
  googleAnalyticsId String?
  postHogId         String?
  afterHeader       String?
  afterBody         String?
  account           Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId         Int      @unique
}

model WebsitePage {
  id              Int      @id @default(autoincrement())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  name            String
  slug            String
  content         Json
  systemPage      Boolean  @default(false)
  published       Boolean  @default(false)
  metaTitle       String?
  metaDescription String?
  account         Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId       Int
}

model SimpleWebsitePage {
  id              Int      @id @default(autoincrement())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  name            String
  slug            String
  metaTitle       String
  metaDescription String
  beforeContent   String
  afterContent    String?
  account         Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId       Int
}

// We'll use Stripe Tax but some customer's won't want to use Stripe, so we'll let them manually enter tax rates
// God bless'em
model TaxRate {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  name        String
  postalCodes String[]
  cities      String[]
  state       String
  rate        Float
  account     Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId   Int
}

enum ScheduleDay {
  SUNDAY
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}

model Schedule {
  id          Int           @id @default(autoincrement())
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  name        String
  description String
  priority    Int           @default(0)
  active      Boolean       @default(true)
  closed      Boolean       @default(false)
  startTime   DateTime // When the schedule starts being active
  endTime     DateTime // When the schedule stops being active, if it's forever put a long date
  openTime    DateTime // the time the business opens - the date is irrelevant
  closeTime   DateTime // the time the business closes - the date is irrelevant
  days        ScheduleDay[]

  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int
}

// This is used for when a customer makes a payment towards an order
model PaymentDetails {
  id            Int             @id @default(autoincrement())
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  method        String // Stripe, cash, check, etc... This is not an enum because we'll allow custom payment methods and providers
  methodId      String          @default("") // This will be present with anything except cash (example: Stripe charge id, last 4 of card, check number)
  processorId   String? // This is the id returned from the payment processor (example: Stripe charge id)
  processor     String? // This is the payment processor (example: Stripe)
  amount        Float
  tip           Float?
  Customer      Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId    String
  order         Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId       Int
  PaymentRefund PaymentRefund[]
}

model PaymentRefund {
  id        String         @id @default(cuid())
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  reason    String
  amount    Float
  payment   PaymentDetails @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  paymentId Int
}

enum OrderState {
  QUOTE // Order started but not paid for
  ABANDONED_QUOTE // Order started but not paid for and they left the page / it's been a while
  ACTIVE // Order has been scheduled.
  CANCELLED // Order started and paid for but cancelled, this is the same as a refund or rain check
  COMPLETED // Order started and paid for and completed successfully
}

// Rental protection was intentionally not added as it's a percentage of the order total and the order total can change
// if the order is updated. We'll calculate the rental protection on the fly when needed.
enum OrderFeeType {
  TRAVEL_FEE
  SURFACE_FEE
  CUSTOM_FEE
}

enum DiscountType {
  COUPON
  SALE
  MANUAL
}

// this is a fee that can be added to an order
model OrderFee {
  id        Int          @id @default(autoincrement())
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  type      OrderFeeType
  name      String?
  amount    Float
  taxable   Boolean      @default(true) // If true, the fee is taxable
  order     Order        @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId   Int
}

model Order {
  id        Int        @id @default(autoincrement())
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  state     OrderState @default(QUOTE)

  startTime      DateTime
  endTime        DateTime
  customerNotes  String?
  internalNotes  String?
  setupSurface   SetupSurface @relation(fields: [setupSurfaceId], references: [id])
  setupSurfaceId Int
  delivered      Boolean      @default(false)

  // financial data - the listing order of the fields is the order they are applied
  baseTotal          Float // Base order amount before additional charges
  OrderDiscount      OrderDiscount[] // Empty if no discounts applied, this stores discounts such as coupons, sales, and manual discounts given th
  damageWaiverRate   Float? // Null if not applied, otherwise stores the rental protection rate (as a percentage or decimal) - this is the rate at which the rental protection was calculated, if updated we need to recalculate the rental protection
  damageWaiverAmount Float? // Null if not applied, otherwise stores the rental protection amount - this is the amount of the rental protection
  travelFeeAmount    Float? // Null if not applied, otherwise stores the travel fee
  OrderFee           OrderFee[] // Additional fees that can be added to an order, they can be added in any order after the rental protection
  taxRate            Float? // Stores the tax rate at the time of the order
  taxAmount          Float? // Stores the calculated tax amount at the time of the order
  taxExempt          Boolean         @default(false) // If true, the order is tax exempt, this is not a field that can be updated after the order has been paid for
  finalTotal         Float // Final total amount after all charges
  totalPaid          Float           @default(0) // This is the total amount paid towards the order - if not

  eventAddress   Address @relation(fields: [eventAddressId], references: [id])
  eventAddressId Int
  couponApplied  Coupon? @relation(fields: [couponId], references: [id], onDelete: SetNull)
  couponId       Int?
  saleApplied    Sale?   @relation(fields: [saleId], references: [id], onDelete: SetNull)
  saleId         Int?

  Customer       Customer     @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId     String
  account        Account      @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  couponAmount          Float? // Not used
  saleAmount            Float? // Not used
  generalDiscountAmount Float? // Not used
  OrderProduct          OrderProduct[]
  PaymentDetails        PaymentDetails[]
  Contract              Contract[]
  OrderEmailLog         OrderEmailLog[]
  ScheduledEmail        ScheduledEmail[]
  OrderSurvey           OrderSurvey[]

  // product availability data
  @@index([organizationId, startTime, endTime])
  @@index([accountId])
}

model OrderSurvey {
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  order          Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId        Int
  customer       Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerId     String
  sentimentScore Float
  feedback       String?

  @@id([orderId, customerId])
}

model OrderAuditLog {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  orderId   Int
  action    String // The action that was taken (e.g., "created", "updated", "deleted")
  data      Json // The data that was changed
  account   Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int
}

model OrderDiscount {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId   Int

  // Core discount info
  type       DiscountType // COUPON, SALE, etc.
  chargeType ChargeType // PERCENTAGE, FIXED_AMOUNT
  amount     Float // The percentage or fixed amount

  // References to original entities (all optional)
  couponId Int?
  coupon   Coupon? @relation(fields: [couponId], references: [id], onDelete: SetNull)
  saleId   Int?
  sale     Sale?   @relation(fields: [saleId], references: [id], onDelete: SetNull)

  // Description/metadata
  name String? // Human-readable name of the discount
}

model EmailSentLog {
  id                   Int      @id @default(autoincrement())
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  sentId               String // This is the id returned from the email service
  allRecipient         String[]
  success              Boolean
  error                String?
  customerDefinedEmail Boolean  @default(false)
  account              Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId            Int
}

model ScheduledEmail {
  id          Int         @id @default(autoincrement())
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  deliveryAt  DateTime // When the email is scheduled to be sent
  subject     String
  previewText String?
  body        String
  sent        Boolean     @default(false) // Indicates if the email has been sent
  action      EmailAction

  accountId Int
  orderId   Int

  // Relations
  account Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model ScheduledEmailConfig {
  id                    Int                 @id @default(autoincrement())
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
  action                EmailAction // The action that triggers the email
  enabled               Boolean             @default(true) // Indicates if the email is enabled
  minutesAfter          Int // How long after the triggering action the email will be sent
  accountId             Int
  customEmailDelivery   CustomEmailDelivery @relation(fields: [customEmailDeliveryId], references: [id], onDelete: Cascade)
  customEmailDeliveryId Int

  // Relations
  account Account @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@unique([accountId, action])
}

enum EmailAction {
  RECEIPT_EMAIL
  ORDER_REMINDER
  ANNIVERSARY_DATE
  ORDER_UPDATE
  ORDER_CANCELLATION
  ORDER_FOLLOW_UP
  ABANDON_CART
}

model CustomEmailDelivery {
  id                   Int                    @id @default(autoincrement())
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  name                 String                 @default("Custom Email")
  text                 String
  subject              String
  previewText          String?
  account              Account                @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId            Int
  OrderEmailLog        OrderEmailLog[]
  ScheduledEmailConfig ScheduledEmailConfig[]
}

model OrderEmailLog {
  sentAt                DateTime            @default(now())
  order                 Order               @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId               Int
  customEmailDelivery   CustomEmailDelivery @relation(fields: [customEmailDeliveryId], references: [id], onDelete: Cascade)
  customEmailDeliveryId Int

  @@id([customEmailDeliveryId, orderId])
}

// This is used for when a customer adds a product to their cart
model OrderProduct {
  quantity                  Int       @default(1)
  pricePaid                 Float     @default(0) // This is the price paid for this product based on the duration of the order. This is NOT the total. the total is calculated by quantity * pricePaid
  beforeRentalBufferMinutes Int       @default(0)
  afterRentalBufferMinutes  Int       @default(0)
  order                     Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId                   Int
  product                   Product   @relation(fields: [productId], references: [id])
  productId                 Int
  // post-order data
  cleanlinessRating         Int? // 0-5, null if not rated yet
  ratedAt                   DateTime?
  ratedBy                   Staff?    @relation(fields: [ratedById], references: [id], onDelete: SetNull)
  ratedById                 String?

  @@id([orderId, productId])
}

model ShoppingSession {
  id             String     @id @default(cuid())
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  account        Account    @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  paymentIntent  String? // This is the payment intent id returned from Stripe
  eventStartTime DateTime?
  eventEndTime   DateTime?
  CartItem       CartItem[]
  landingPage    String? // The referer URL when the cart was created
  refererUrl     String? // The referer URL when the cart was created
  queryParams    Json? // The query params when the cart was created
  customer       Customer?  @relation(fields: [customerId], references: [id], onDelete: SetNull)
  customerId     String?
}

model CartItem {
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  quantity          Int             @default(1)
  product           Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId         Int
  shoppingSession   ShoppingSession @relation(fields: [shoppingSessionId], references: [id], onDelete: Cascade)
  shoppingSessionId String

  @@id([productId, shoppingSessionId])
}

model Customer {
  id            String   @id @default(cuid())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  email         String
  password      String?
  firstName     String?
  lastName      String?
  phoneNumber   String?
  company       String?
  reference     String?
  internalNotes String?
  points        Int      @default(0)
  banned        Boolean  @default(false)
  taxExempt     Boolean  @default(false)

  phoneContact    PhoneContact?
  address         Address?          @relation(fields: [addressId], references: [id], onDelete: SetNull)
  addressId       Int?
  account         Account           @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId       Int
  Order           Order[]
  PaymentDetails  PaymentDetails[]
  ShoppingSession ShoppingSession[]
  Contract        Contract[]
  SignedDocument  SignedDocument[]
  OrderSurvey     OrderSurvey[]

  @@unique([email, accountId])
}

model StaffRole {
  staffId String
  roleId  Int
  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)
  role    Role   @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([staffId, roleId])
}

model StaffPasswordReset {
  staff   Staff    @relation(fields: [staffId], references: [id], onDelete: Cascade)
  staffId String
  token   String
  expires DateTime

  @@id([staffId, token])
}

model User {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  lastLogin DateTime?
  email     String
  password  String
  staff     Staff[]

  @@unique([email])
}

model Staff {
  id                     String                   @id @default(cuid())
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  lastLogin              DateTime?
  email                  String
  name                   String?
  phoneNumber            String?
  password               String
  account                Account                  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId              Int
  user                   User?                    @relation(fields: [userId], references: [id])
  userId                 String?
  StaffRole              StaffRole[]
  StaffPasswordReset     StaffPasswordReset[]
  LogicistcTimelineStaff LogicistcTimelineStaff[]
  ProductCleaning        ProductCleaning[]
  Shift                  Shift[]
  RequestedEdits         ShiftEditRequest[]       @relation("RequestedBy")
  ReviewedEdits          ShiftEditRequest[]       @relation("ReviewedBy")
  OrderProduct           OrderProduct[]

  @@unique([email, accountId])
}

model Job {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  name      String
  color     String // hex color for UI
  active    Boolean  @default(true)

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  Shift Shift[]
}

model Shift {
  id        Int       @id @default(autoincrement())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  clockIn   DateTime
  clockOut  DateTime?
  notes     String?

  staff   Staff  @relation(fields: [staffId], references: [id], onDelete: Cascade)
  staffId String

  job   Job @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId Int

  ShiftEditRequest ShiftEditRequest[]

  @@index([staffId, clockIn])
  @@index([clockIn])
}

model ShiftEditRequest {
  id        Int                    @id @default(autoincrement())
  createdAt DateTime               @default(now())
  updatedAt DateTime               @updatedAt
  status    ShiftEditRequestStatus @default(PENDING)

  // What they want to change to
  newClockIn  DateTime?
  newClockOut DateTime?
  newNotes    String?
  newJobId    Int?

  requestNote String? // Why they want the change

  shift   Shift @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  shiftId Int

  requestedBy   Staff  @relation("RequestedBy", fields: [requestedById], references: [id], onDelete: Cascade)
  requestedById String

  reviewedBy   Staff?    @relation("ReviewedBy", fields: [reviewedById], references: [id], onDelete: SetNull)
  reviewedById String?
  reviewedAt   DateTime?
  reviewNote   String? // Admin's note on approval/denial
}

enum ShiftEditRequestStatus {
  PENDING
  APPROVED
  DENIED
}

model ProductCleaning {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  cleanedAt DateTime @default(now()) // When the actual cleaning happened (might differ from createdAt if logged later)

  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId Int

  cleanedBy   Staff?  @relation(fields: [cleanedById], references: [id], onDelete: SetNull)
  cleanedById String?

  notes String?

  account        Account      @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@index([productId, cleanedAt])
  @@index([cleanedAt])
}

model Product {
  id                        Int          @id @default(autoincrement())
  createdAt                 DateTime     @default(now())
  updatedAt                 DateTime     @updatedAt
  display                   Boolean      @default(true)
  archived                  Boolean      @default(false)
  name                      String
  description               String
  price                     Float
  quantity                  Int
  setupTimeMinutes          Int          @default(0)
  takeDownTimeMinutes       Int          @default(0)
  beforeRentalBufferMinutes Int          @default(0)
  afterRentalBufferMinutes  Int          @default(0)
  scaleTimeWithQuantity     Boolean      @default(false)
  deliverable               Boolean      @default(true)
  category                  Category?    @relation("Category", fields: [categoryId], references: [id], onDelete: SetNull)
  categoryId                Int?
  subCategory               Category?    @relation("SubCategory", fields: [subCategoryId], references: [id], onDelete: SetNull)
  subCategoryId             Int?
  account                   Account      @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId                 Int
  organization              Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId            String
  notes                     String?
  slug                      String? // SEO friendly slug
  metaTitle                 String?
  metaDescription           String?

  setupSurface         ProductSetupSurface[]
  ProductImageUpload   ProductImageUpload[]
  OrderProduct         OrderProduct[]
  CartItem             CartItem[]
  ProductCategory      ProductCategory[]
  ProductRule          ProductRule[]
  ProductShareSettings ProductShareSettings[]
  ProductCleaning      ProductCleaning[]
}

model ProductShareSettings {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId Int
  account   Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int
  shared    Boolean  @default(true)

  @@id([productId, accountId])
}

model ProductCategory {
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId  Int
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId Int

  @@id([productId, categoryId])
}

model Rule {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  name      String
  ruleType  RuleType // Enum: PRICING, AVAILABILITY, etc.
  ruleJson  String // JSON containing conditions and actions
  priority  Int      @default(0)
  isActive  Boolean  @default(true)

  account   Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId Int

  // Many-to-many relationship with products
  products ProductRule[]
}

enum RuleType {
  PRICING
  AVAILABILITY
}

model ProductRule {
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId Int
  rule      Rule    @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  ruleId    Int

  @@id([productId, ruleId])
}

model ProductSetupSurface {
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  notes          String?
  product        Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId      Int
  setupSurface   SetupSurface @relation(fields: [setupSurfaceId], references: [id], onDelete: Cascade)
  setupSurfaceId Int

  @@id([productId, setupSurfaceId])
}

enum ChargeType {
  PERCENTAGE
  DOLLAR
}

model Sale {
  id            Int             @id @default(autoincrement())
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  startDate     DateTime?
  endDate       DateTime?
  name          String
  displayName   String
  description   String
  discount      Float
  discountType  ChargeType      @default(PERCENTAGE)
  account       Account         @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId     Int
  Order         Order[]
  OrderDiscount OrderDiscount[]
}

enum ProductSortStrategy {
  BEST_SELLING
  PRICE_ASC
  PRICE_DESC
  NAME_ASC
  NAME_DESC
  CREATED_AT_ASC
  CREATED_AT_DESC
}

model Category {
  id                  Int                   @id @default(autoincrement())
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  display             Boolean               @default(true)
  name                String
  description         String
  slug                String? // SEO friendly slug
  metaTitle           String?
  metaDescription     String?
  productSortStrategy ProductSortStrategy   @default(BEST_SELLING)
  account             Account               @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId           Int
  organization        Organization          @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId      String
  Product             Product[]             @relation("Category")
  SubProducts         Product[]             @relation("SubCategory")
  CategoryImageUpload CategoryImageUpload[]
  ProductCategory     ProductCategory[]
}

model Coupon {
  id            Int             @id @default(autoincrement())
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  startDate     DateTime?
  endDate       DateTime?
  name          String
  displayName   String
  description   String
  discountType  ChargeType      @default(PERCENTAGE)
  discount      Float
  deleted       Boolean         @default(false)
  account       Account         @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId     Int
  Order         Order[]
  OrderDiscount OrderDiscount[]
}

model SetupSurface {
  id                   Int                   @id @default(autoincrement())
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  archived             Boolean               @default(false)
  name                 String
  description          String
  account              Account               @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId            Int
  feeAmount            Float? // This is the fee amount for this surface to charge to the customer.
  scaleFeeWithQuantity Boolean               @default(false)
  products             ProductSetupSurface[]
  Order                Order[]
}

model ProductImageUpload {
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  product       Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId     Int
  imageUpload   ImageUpload @relation(fields: [imageUploadId], references: [id], onDelete: Cascade)
  imageUploadId String
  priority      Int

  @@id([productId, imageUploadId])
}

model CategoryImageUpload {
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  category      Category    @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  categoryId    Int
  imageUpload   ImageUpload @relation(fields: [imageUploadId], references: [id], onDelete: Cascade)
  imageUploadId String

  @@id([categoryId, imageUploadId])
}

enum ImageType {
  LOGO
  FAVICON
  HERO
}

model ImageUpload {
  id                  String                @id @default(cuid())
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  name                String
  url                 String
  type                ImageType?
  accountId           Int
  account             Account               @relation(fields: [accountId], references: [id], onDelete: Cascade)
  ProductImageUpload  ProductImageUpload[]
  CategoryImageUpload CategoryImageUpload[]
}

enum DocumentType {
  CONTRACT
  POLICY
}

model Document {
  id             String           @id @default(cuid())
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  title          String
  body           Json
  type           DocumentType
  public         Boolean          @default(false)
  account        Account          @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId      Int
  SignedDocument SignedDocument[]
}

model SignedDocument {
  id         String   @id @default(cuid())
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  ipAddress  String
  storageUrl String
  userAgent  String
  extraData  Json?
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId String
  customer   Customer @relation(fields: [customerId], references: [id])
  customerId String
}

model Contract {
  id               String    @id @default(cuid())
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  signed           Boolean   @default(false)
  signedDate       DateTime?
  signedIpAddress  String?
  signedStorageUrl String?
  signedUserAgent  String?
  inPerson         Boolean   @default(false)
  order            Order?    @relation(fields: [orderId], references: [id], onDelete: SetNull)
  orderId          Int?
  customer         Customer  @relation(fields: [customerId], references: [id])
  customerId       String
}
