-- CreateTable
CREATE TABLE "PhoneContact" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "notes" TEXT,
    "accountId" INTEGER NOT NULL,
    "customerId" TEXT,

    CONSTRAINT "PhoneContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PhoneCallLog" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "callSid" TEXT NOT NULL,
    "duration" INTEGER,
    "inbound" BOOLEAN NOT NULL,
    "recordingUrl" TEXT,
    "accountId" INTEGER NOT NULL,
    "phoneContactId" INTEGER NOT NULL,

    CONSTRAINT "PhoneCallLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PhoneMessageLog" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "messageSid" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "inbound" BOOLEAN NOT NULL,
    "mediaUrl" TEXT,
    "errorMessage" TEXT,
    "accountId" INTEGER NOT NULL,
    "phoneContactId" INTEGER NOT NULL,

    CONSTRAINT "PhoneMessageLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PhoneContact_customerId_key" ON "PhoneContact"("customerId");

-- AddForeignKey
ALTER TABLE "PhoneContact" ADD CONSTRAINT "PhoneContact_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhoneContact" ADD CONSTRAINT "PhoneContact_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhoneCallLog" ADD CONSTRAINT "PhoneCallLog_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhoneCallLog" ADD CONSTRAINT "PhoneCallLog_phoneContactId_fkey" FOREIGN KEY ("phoneContactId") REFERENCES "PhoneContact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhoneMessageLog" ADD CONSTRAINT "PhoneMessageLog_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhoneMessageLog" ADD CONSTRAINT "PhoneMessageLog_phoneContactId_fkey" FOREIGN KEY ("phoneContactId") REFERENCES "PhoneContact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
