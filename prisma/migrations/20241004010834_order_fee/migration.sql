-- CreateEnum
CREATE TYPE "OrderFeeType" AS ENUM ('TRAVEL_FEE', 'SURFACE_FEE');

-- CreateTable
CREATE TABLE "OrderFee" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "type" "OrderFeeType" NOT NULL,
    "name" TEXT,
    "amount" DOUBLE PRECISION NOT NULL,
    "orderId" INTEGER NOT NULL,

    CONSTRAINT "OrderFee_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "OrderFee" ADD CONSTRAINT "OrderFee_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;
