import { OrderFeeType, PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  await prisma.$transaction(async (tx) => {
    const orders = await tx.order.findMany({
      include: {
        OrderFee: true,
      },
    });
    const creates = [];
    for (const order of orders) {
      const travelFee = order.travelFeeAmount;

      if (!travelFee) {
        continue;
      }

      if (
        order.OrderFee.find(
          (orderFee) => orderFee.type === OrderFeeType.TRAVEL_FEE,
        )
      ) {
        continue;
      }

      creates.push({
        type: OrderFeeType.TRAVEL_FEE,
        amount: travelFee,
        name: null,
        orderId: order.id,
      });
    }
    const chunkSize = 80;
    const chunks = [];
    for (let i = 0; i < creates.length; i += chunkSize) {
      chunks.push(creates.slice(i, i + chunkSize));
    }
    await Promise.all(
      chunks.map(async (chunk) => {
        await prisma.$transaction(async (tx) => {
          await Promise.all(
            chunk.map((chunkData) => {
              return tx.orderFee.create({
                data: chunkData,
              });
            }),
          );
        });
      }),
    );
    await Promise.all(creates);
    console.log("Order fees created");
  });
}

main()
  .catch(async (e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => await prisma.$disconnect());
