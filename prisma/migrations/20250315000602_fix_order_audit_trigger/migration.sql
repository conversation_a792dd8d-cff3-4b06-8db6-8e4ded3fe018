-- This is an empty migration.

DROP TRIGGER IF EXISTS trg_order_audit_delete ON "Order";
DROP TRIGGER IF EXISTS trg_order_audit_insert_update ON "Order";
DROP FUNCTION IF EXISTS fn_order_audit_log();

-- Then create the new function
CREATE OR REPLACE FUNCTION fn_order_audit_log()
    RETURNS TRIGGER AS $$
DECLARE
    action_name TEXT;
    record_data JSONB;
BEGIN
    -- Determine which action is being performed
    IF (TG_OP = 'INSERT') THEN
        action_name := 'created';
        record_data := to_jsonb(NEW);
    ELSIF (TG_OP = 'UPDATE') THEN
        action_name := 'updated';
        record_data := jsonb_build_object(
                'previous', to_jsonb(OLD),
                'current', to_jsonb(NEW)
                       );
    ELSIF (TG_OP = 'DELETE') THEN
        action_name := 'deleted';
        record_data := to_jsonb(OLD);
    END IF;

    -- Insert the audit record
    INSERT INTO "OrderAuditLog" (
        "createdAt",
        "updatedAt",
        "orderId",
        "accountId",
        "action",
        "data"
    ) VALUES (
                 NOW(),
                 NOW(),
                 CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
                 CASE WHEN TG_OP = 'DELETE' THEN OLD.accountId ELSE NEW.accountId END,
                 action_name,
                 record_data
             );

    -- Return the appropriate record
    IF (TG_OP = 'DELETE') THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create the new triggers
CREATE TRIGGER trg_order_audit_insert_update
    AFTER INSERT OR UPDATE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();

CREATE TRIGGER trg_order_audit_delete
    BEFORE DELETE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();
