/*
  Warnings:

  - The primary key for the `StaffPasswordReset` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `userId` on the `StaffPasswordReset` table. All the data in the column will be lost.
  - The primary key for the `StaffRole` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `userId` on the `StaffRole` table. All the data in the column will be lost.
  - Added the required column `staffId` to the `StaffPasswordReset` table without a default value. This is not possible if the table is not empty.
  - Added the required column `staffId` to the `StaffRole` table without a default value. This is not possible if the table is not empty.

*/
ALTER TABLE "StaffPasswordReset" RENAME COLUMN "userId" TO "staffId";

ALTER TABLE "StaffRole" RENAME COLUMN "userId" TO "staffId";

-- AlterTable
ALTER TABLE "Staff" RENAME CONSTRAINT "User_pkey" TO "Staff_pkey";

-- RenameForeignKey
ALTER TABLE "Staff" RENAME CONSTRAINT "User_accountId_fkey" TO "Staff_accountId_fkey";

-- RenameForeignKey
ALTER TABLE "StaffRole" RENAME CONSTRAINT "UserRole_roleId_fkey" TO "StaffRole_roleId_fkey";
