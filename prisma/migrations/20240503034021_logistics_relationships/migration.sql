/*
  Warnings:

  - Added the required column `accountId` to the `LogisticTask` table without a default value. This is not possible if the table is not empty.
  - Added the required column `accountId` to the `LogisticTimeline` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "LogisticTask" ADD COLUMN     "accountId" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "LogisticTimeline" ADD COLUMN     "accountId" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "LogisticTimeline" ADD CONSTRAINT "LogisticTimeline_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LogisticTask" ADD CONSTRAINT "LogisticTask_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;
