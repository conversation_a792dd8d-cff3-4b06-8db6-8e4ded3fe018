-- AlterTable
ALTER TABLE "PhoneContact" ADD COLUMN     "lastRead" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "PhoneVoicemail" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "recordingUrl" TEXT NOT NULL,
    "transcription" TEXT,
    "phoneCallLogId" INTEGER,

    CONSTRAINT "PhoneVoicemail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PhoneVoicemail_phoneCallLogId_key" ON "PhoneVoicemail"("phoneCallLogId");

-- AddForeignKey
ALTER TABLE "PhoneVoicemail" ADD CONSTRAINT "PhoneVoicemail_phoneCallLogId_fkey" FOREIGN KEY ("phoneCallLogId") REFERENCES "PhoneCallLog"("id") ON DELETE CASCADE ON UPDATE CASCADE;
