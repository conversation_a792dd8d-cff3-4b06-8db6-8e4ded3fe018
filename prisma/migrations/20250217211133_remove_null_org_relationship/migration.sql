/*
  Warnings:

  - Made the column `organizationId` on table `Category` required. This step will fail if there are existing NULL values in that column.
  - Made the column `organizationId` on table `LogisticTimeline` required. This step will fail if there are existing NULL values in that column.
  - Made the column `organizationId` on table `Order` required. This step will fail if there are existing NULL values in that column.
  - Made the column `organizationId` on table `Product` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "Category" DROP CONSTRAINT "Category_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "LogisticTimeline" DROP CONSTRAINT "LogisticTimeline_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "Order" DROP CONSTRAINT "Order_organizationId_fkey";

-- DropForeignKey
ALTER TABLE "Product" DROP CONSTRAINT "Product_organizationId_fkey";

-- AlterTable
ALTER TABLE "Category" ALTER COLUMN "organizationId" SET NOT NULL;

-- AlterTable
ALTER TABLE "LogisticTimeline" ALTER COLUMN "organizationId" SET NOT NULL;

-- AlterTable
ALTER TABLE "Order" ALTER COLUMN "organizationId" SET NOT NULL;

-- AlterTable
ALTER TABLE "Product" ALTER COLUMN "organizationId" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "LogisticTimeline" ADD CONSTRAINT "LogisticTimeline_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Category" ADD CONSTRAINT "Category_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
