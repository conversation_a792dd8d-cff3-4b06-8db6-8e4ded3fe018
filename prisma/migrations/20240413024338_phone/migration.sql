-- CreateTable
CREATE TABLE "PhoneAccount" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "accountSid" TEXT NOT NULL,
    "accountId" INTEGER NOT NULL,

    CONSTRAINT "PhoneAccount_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PhoneAccount_accountId_key" ON "PhoneAccount"("accountId");

-- AddForeignKey
ALTER TABLE "PhoneAccount" ADD CONSTRAINT "PhoneAccount_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
