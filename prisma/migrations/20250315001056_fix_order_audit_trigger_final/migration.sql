-- This is an empty migration.
DROP TRIGGER IF EXISTS trg_order_audit_delete ON "Order";
DROP TRIGGER IF EXISTS trg_order_audit_insert_update ON "Order";
DROP FUNCTION IF EXISTS fn_order_audit_log();

CREATE OR REPLACE FUNCTION fn_order_audit_log()
    RETURNS TRIGGER AS $$
BEGIN
    IF (TG_OP = 'INSERT') THEN
        INSERT INTO "OrderAuditLog" ( "createdAt", "updatedAt", "orderId", "accountId", "action","data")
        VALUES (NOW(), NOW(), NEW."id", NEW."accountId", 'created', to_jsonb(NEW));
    ELSIF (TG_OP = 'UPDATE') THEN
        INSERT INTO "OrderAuditLog" ( "createdAt", "updatedAt", "orderId", "accountId", "action","data")
        VALUES (NOW(), NOW(), NEW."id", NEW."accountId", 'updated', jsonb_build_object('previous', to_jsonb(OLD),'current', to_jsonb(NEW)));
    ELSIF (TG_OP = 'DELETE') THEN
        INSERT INTO "OrderAuditLog" ( "createdAt", "updatedAt", "orderId", "accountId", "action","data")
        VALUES (NOW(), NOW(), NEW."id", NEW."accountId", 'deleted', to_jsonb(OLD));
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the new triggers
CREATE TRIGGER trg_order_audit_insert_update
    AFTER INSERT OR UPDATE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();

CREATE TRIGGER trg_order_audit_delete
    BEFORE DELETE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();
