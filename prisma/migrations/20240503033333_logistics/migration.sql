-- CreateEnum
CREATE TYPE "LogisticAction" AS ENUM ('PICK<PERSON>', 'DELIVERY', 'OTHER');

-- CreateTable
CREATE TABLE "LogisticTimeline" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "originId" INTEGER,

    CONSTRAINT "LogisticTimeline_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LogisticTask" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "timelineId" TEXT NOT NULL,
    "timelineSequence" INTEGER NOT NULL,
    "durationMinutes" INTEGER NOT NULL,
    "action" "LogisticAction" NOT NULL,
    "notes" TEXT,
    "orderId" INTEGER,
    "addressId" INTEGER,

    CONSTRAINT "LogisticTask_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "LogisticTimeline" ADD CONSTRAINT "LogisticTimeline_originId_fkey" FOREIGN KEY ("originId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LogisticTask" ADD CONSTRAINT "LogisticTask_timelineId_fkey" FOREIGN KEY ("timelineId") REFERENCES "LogisticTimeline"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LogisticTask" ADD CONSTRAINT "LogisticTask_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LogisticTask" ADD CONSTRAINT "LogisticTask_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;
