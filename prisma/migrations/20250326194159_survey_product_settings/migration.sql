-- AlterTable
ALTER TABLE "Account" ADD COLUMN     "googleReviewLink" TEXT;

-- CreateTable
CREATE TABLE "AccountInventoryShareSettings" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "receiveSharedCategories" BOOLEAN NOT NULL DEFAULT false,
    "receiveSharedProducts" BOOLEAN NOT NULL DEFAULT false,
    "accountId" INTEGER NOT NULL,
    "organizationId" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "OrderSurvey" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orderId" INTEGER NOT NULL,
    "customerId" TEXT NOT NULL,
    "sentimentScore" DOUBLE PRECISION NOT NULL,
    "feedback" TEXT,

    CONSTRAINT "OrderSurvey_pkey" PRIMARY KEY ("orderId","customerId")
);

-- CreateIndex
CREATE UNIQUE INDEX "AccountInventoryShareSettings_accountId_key" ON "AccountInventoryShareSettings"("accountId");

-- CreateIndex
CREATE INDEX "AccountInventoryShareSettings_organizationId_idx" ON "AccountInventoryShareSettings"("organizationId");

-- CreateIndex
CREATE INDEX "Order_organizationId_startTime_endTime_idx" ON "Order"("organizationId", "startTime", "endTime");

-- CreateIndex
CREATE INDEX "Order_accountId_idx" ON "Order"("accountId");

-- AddForeignKey
ALTER TABLE "AccountInventoryShareSettings" ADD CONSTRAINT "AccountInventoryShareSettings_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountInventoryShareSettings" ADD CONSTRAINT "AccountInventoryShareSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderSurvey" ADD CONSTRAINT "OrderSurvey_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderSurvey" ADD CONSTRAINT "OrderSurvey_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "Customer"("id") ON DELETE CASCADE ON UPDATE CASCADE;
