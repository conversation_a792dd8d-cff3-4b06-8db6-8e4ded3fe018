-- AlterTable
ALTER TABLE "WebsitePage" ADD COLUMN     "metaDescription" TEXT,
ADD COLUMN     "metaTitle" TEXT;

-- CreateTable
CREATE TABLE "SimpleWebsitePage" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "metaTitle" TEXT NOT NULL,
    "metaDescription" TEXT NOT NULL,
    "beforeContent" TEXT NOT NULL,
    "afterContent" TEXT,
    "accountId" INTEGER NOT NULL,

    CONSTRAINT "SimpleWebsitePage_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "SimpleWebsitePage" ADD CONSTRAINT "SimpleWebsitePage_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;
