-- CreateEnum
CREATE TYPE "ProductSortStrategy" AS ENUM ('BEST_SELLING', 'PRICE_ASC', 'PRICE_DESC', 'NAME_ASC', 'NAME_DESC', 'CREATED_AT_ASC', 'CREATED_AT_DESC');

-- AlterTable
ALTER TABLE "Category" ADD COLUMN     "metaDescription" TEXT,
ADD COLUMN     "metaTitle" TEXT,
ADD COLUMN     "productSortStrategy" "ProductSortStrategy" NOT NULL DEFAULT 'BEST_SELLING',
ADD COLUMN     "slug" TEXT;

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "metaDescription" TEXT,
ADD COLUMN     "metaTitle" TEXT;
