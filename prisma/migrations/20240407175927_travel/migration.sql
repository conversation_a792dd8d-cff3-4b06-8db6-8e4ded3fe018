/*
  Warnings:

  - You are about to drop the `Truck` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TruckSchedule` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TruckScheduleDriver` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TruckScheduleOrder` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Truck" DROP CONSTRAINT "Truck_accountId_fkey";

-- DropForeignKey
ALTER TABLE "TruckSchedule" DROP CONSTRAINT "TruckSchedule_truckId_fkey";

-- DropForeignKey
ALTER TABLE "TruckScheduleDriver" DROP CONSTRAINT "TruckScheduleDriver_truckScheduleId_fkey";

-- DropForeignKey
ALTER TABLE "TruckScheduleDriver" DROP CONSTRAINT "TruckScheduleDriver_userId_fkey";

-- DropForeignKey
ALTER TABLE "TruckScheduleOrder" DROP CONSTRAINT "TruckScheduleOrder_orderId_fkey";

-- DropForeignKey
ALTER TABLE "TruckScheduleOrder" DROP CONSTRAINT "TruckScheduleOrder_truckScheduleId_fkey";

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "scaleTimeWithQuantity" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "setupTimeMinutes" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "takeDownTimeMinutes" INTEGER NOT NULL DEFAULT 0;

-- DropTable
DROP TABLE "Truck";

-- DropTable
DROP TABLE "TruckSchedule";

-- DropTable
DROP TABLE "TruckScheduleDriver";

-- DropTable
DROP TABLE "TruckScheduleOrder";

-- CreateTable
CREATE TABLE "TravelCache" (
    "id" SERIAL NOT NULL,
    "originId" INTEGER NOT NULL,
    "destinationId" INTEGER NOT NULL,
    "distanceMiles" INTEGER NOT NULL,
    "travelTimeMinutes" INTEGER NOT NULL,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TravelCache_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TravelCache_originId_destinationId_key" ON "TravelCache"("originId", "destinationId");

-- AddForeignKey
ALTER TABLE "TravelCache" ADD CONSTRAINT "TravelCache_originId_fkey" FOREIGN KEY ("originId") REFERENCES "Address"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TravelCache" ADD CONSTRAINT "TravelCache_destinationId_fkey" FOREIGN KEY ("destinationId") REFERENCES "Address"("id") ON DELETE CASCADE ON UPDATE CASCADE;
