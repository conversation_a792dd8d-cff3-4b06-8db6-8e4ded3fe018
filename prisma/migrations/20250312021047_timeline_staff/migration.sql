-- CreateTable
CREATE TABLE "LogicistcTimelineStaff" (
    "staffId" TEXT NOT NULL,
    "timelineId" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "LogicistcTimelineStaff_staffId_timelineId_key" ON "LogicistcTimelineStaff"("staffId", "timelineId");

-- AddForeignKey
ALTER TABLE "LogicistcTimelineStaff" ADD CONSTRAINT "LogicistcTimelineStaff_staffId_fkey" FOREIGN KEY ("staffId") REFERENCES "Staff"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LogicistcTimelineStaff" ADD CONSTRAINT "LogicistcTimelineStaff_timelineId_fkey" FOREIGN KEY ("timelineId") REFERENCES "LogisticTimeline"("id") ON DELETE CASCADE ON UPDATE CASCADE;
