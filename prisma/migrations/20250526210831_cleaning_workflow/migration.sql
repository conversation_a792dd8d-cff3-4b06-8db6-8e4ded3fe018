-- CreateEnum
CREATE TYPE "ShiftEditRequestStatus" AS ENUM ('PENDING', 'APPROVED', 'DENIED');

-- AlterTable
ALTER TABLE "OrderProduct" ADD COLUMN     "cleanlinessRating" INTEGER,
ADD COLUMN     "ratedAt" TIMESTAMP(3),
ADD COLUMN     "ratedById" TEXT;

-- CreateTable
CREATE TABLE "Job" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "Job_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Shift" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "clockIn" TIMESTAMP(3) NOT NULL,
    "clockOut" TIMESTAMP(3),
    "notes" TEXT,
    "staffId" TEXT NOT NULL,
    "jobId" INTEGER NOT NULL,

    CONSTRAINT "Shift_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShiftEditRequest" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "ShiftEditRequestStatus" NOT NULL DEFAULT 'PENDING',
    "newClockIn" TIMESTAMP(3),
    "newClockOut" TIMESTAMP(3),
    "newNotes" TEXT,
    "newJobId" INTEGER,
    "requestNote" TEXT,
    "shiftId" INTEGER NOT NULL,
    "requestedById" TEXT NOT NULL,
    "reviewedById" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "reviewNote" TEXT,

    CONSTRAINT "ShiftEditRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductCleaning" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "cleanedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "productId" INTEGER NOT NULL,
    "cleanedById" TEXT,
    "notes" TEXT,
    "accountId" INTEGER NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "ProductCleaning_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Shift_staffId_clockIn_idx" ON "Shift"("staffId", "clockIn");

-- CreateIndex
CREATE INDEX "Shift_clockIn_idx" ON "Shift"("clockIn");

-- CreateIndex
CREATE INDEX "ProductCleaning_productId_cleanedAt_idx" ON "ProductCleaning"("productId", "cleanedAt");

-- CreateIndex
CREATE INDEX "ProductCleaning_cleanedAt_idx" ON "ProductCleaning"("cleanedAt");

-- AddForeignKey
ALTER TABLE "OrderProduct" ADD CONSTRAINT "OrderProduct_ratedById_fkey" FOREIGN KEY ("ratedById") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shift" ADD CONSTRAINT "Shift_staffId_fkey" FOREIGN KEY ("staffId") REFERENCES "Staff"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Shift" ADD CONSTRAINT "Shift_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShiftEditRequest" ADD CONSTRAINT "ShiftEditRequest_shiftId_fkey" FOREIGN KEY ("shiftId") REFERENCES "Shift"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShiftEditRequest" ADD CONSTRAINT "ShiftEditRequest_requestedById_fkey" FOREIGN KEY ("requestedById") REFERENCES "Staff"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShiftEditRequest" ADD CONSTRAINT "ShiftEditRequest_reviewedById_fkey" FOREIGN KEY ("reviewedById") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductCleaning" ADD CONSTRAINT "ProductCleaning_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductCleaning" ADD CONSTRAINT "ProductCleaning_cleanedById_fkey" FOREIGN KEY ("cleanedById") REFERENCES "Staff"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductCleaning" ADD CONSTRAINT "ProductCleaning_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductCleaning" ADD CONSTRAINT "ProductCleaning_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
