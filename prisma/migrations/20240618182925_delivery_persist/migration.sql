/*
  Warnings:

  - You are about to drop the `LogisticTask` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "LogisticTask" DROP CONSTRAINT "LogisticTask_accountId_fkey";

-- DropForeignKey
ALTER TABLE "LogisticTask" DROP CONSTRAINT "LogisticTask_addressId_fkey";

-- DropForeignKey
ALTER TABLE "LogisticTask" DROP CONSTRAINT "LogisticTask_orderId_fkey";

-- DropForeignKey
ALTER TABLE "LogisticTask" DROP CONSTRAINT "LogisticTask_timelineId_fkey";

-- AlterTable
ALTER TABLE "LogisticTimeline" ADD COLUMN     "tasks" JSONB NOT NULL DEFAULT '{}';

-- DropTable
DROP TABLE "LogisticTask";

-- DropEnum
DROP TYPE "LogisticAction";
