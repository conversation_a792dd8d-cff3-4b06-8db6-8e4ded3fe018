-- CreateTable
CREATE TABLE "ProductShareSettings" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "productId" INTEGER NOT NULL,
    "accountId" INTEGER NOT NULL,
    "shared" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "ProductShareSettings_pkey" PRIMARY KEY ("productId","accountId")
);

-- AddForeignKey
ALTER TABLE "ProductShareSettings" ADD CONSTRAINT "ProductShareSettings_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductShareSettings" ADD CONSTRAINT "ProductShareSettings_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE CASCADE ON UPDATE CASCADE;
