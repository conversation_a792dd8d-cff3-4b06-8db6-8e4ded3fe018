// import { PrismaClient } from "@prisma/client";
//
// const prisma = new PrismaClient();
//
// async function createOrgRelationships() {
//   try {
//     await prisma.$transaction(async (tx) => {
//       const organizations = await tx.organization.findMany({
//         select: { id: true, accounts: { select: { id: true } } },
//       });
//       if (organizations.length === 0) {
//         console.log("No organizations found.");
//         return;
//       }
//
//       for (const organization of organizations) {
//         const accounts = organization.accounts;
//         const accountIds = accounts.map((account) => account.id);
//         if (accounts.length === 0) {
//           console.log(`No accounts found for organization ${organization.id}.`);
//           continue;
//         }
//
//         const orders = await tx.order.updateMany({
//           where: { accountId: { in: accountIds }, organizationId: null },
//           data: { organizationId: organization.id },
//         });
//
//         console.log(
//           `Updated ${orders.count} orders for organization ${organization.id}.`,
//         );
//
//         const products = await tx.product.updateMany({
//           where: { accountId: { in: accountIds }, organizationId: null },
//           data: { organizationId: organization.id },
//         });
//
//         console.log(
//           `Updated ${products.count} products for organization ${organization.id}.`,
//         );
//
//         const categories = await tx.category.updateMany({
//           where: { accountId: { in: accountIds }, organizationId: null },
//           data: { organizationId: organization.id },
//         });
//
//         console.log(
//           `Updated ${categories.count} categories for organization ${organization.id}.`,
//         );
//
//         const timelines = await tx.logisticTimeline.updateMany({
//           where: { accountId: { in: accountIds }, organizationId: null },
//           data: { organizationId: organization.id },
//         });
//
//         console.log(
//           `Updated ${timelines.count} timelines for organization ${organization.id}.`,
//         );
//       }
//     });
//
//     const ordersWithoutOrg = await prisma.order.findMany({
//       where: { organizationId: null },
//     });
//
//     console.log(
//       `There are ${ordersWithoutOrg.length} orders without an organization.`,
//     );
//
//     console.log("Organizations successfully linked.");
//   } catch (error) {
//     console.error("Error linking organizations:", error);
//   } finally {
//     await prisma.$disconnect();
//   }
// }
//
// // Run the function
// createOrgRelationships();
