-- This is an empty migration.

CREATE OR REPLACE FUNCTION fn_order_audit_log()
    RETURNS TRIGGER AS $$
DECLARE
    action_name TEXT;
    old_data JSONB := NULL;
    new_data JSONB := NULL;
    changed_data JSONB := NULL;
BEGIN
    -- Determine which action is being performed
    IF (TG_OP = 'INSERT') THEN
        action_name := 'created';
        new_data := to_jsonb(NEW);
        -- No old data or changed data for inserts
    ELSIF (TG_OP = 'UPDATE') THEN
        action_name := 'updated';
        old_data := to_jsonb(OLD);
        new_data := to_jsonb(NEW);
        changed_data := (
            SELECT jsonb_object_agg(key, value)
            FROM jsonb_each(to_jsonb(NEW)) AS new_fields(key, value)
            WHERE new_fields.value IS DISTINCT FROM (to_jsonb(OLD) -> new_fields.key)
        );
    ELSIF (TG_OP = 'DELETE') THEN
        action_name := 'deleted';
        old_data := to_jsonb(OLD);
        -- No new data or changed data for deletes
    END IF;

    -- Insert the audit record with structured fields
    INSERT INTO "OrderAuditLog" (
        "createdAt",
        "updatedAt",
        "orderId",
        "accountId",
        "action",
        "data"
    ) VALUES (
                 NOW(),
                 NOW(),
                 CASE WHEN TG_OP = 'DELETE' THEN OLD.id ELSE NEW.id END,
                 CASE WHEN TG_OP = 'DELETE' THEN OLD.accountId ELSE NEW.accountId END,
                 action_name,
                 -- Use the composite structure for backwards compatibility
                 jsonb_build_object(
                         'old', old_data,
                         'new', new_data,
                         'changed_fields', changed_data
                 )
             );

    -- For INSERT and UPDATE operations, return NEW to allow the operation to complete
    -- For DELETE operations, return OLD to allow the deletion to complete
    IF (TG_OP = 'DELETE') THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create separate triggers for AFTER and BEFORE operations
CREATE TRIGGER trg_order_audit_insert_update
    AFTER INSERT OR UPDATE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();

CREATE TRIGGER trg_order_audit_delete
    BEFORE DELETE ON "Order"
    FOR EACH ROW
EXECUTE FUNCTION fn_order_audit_log();
