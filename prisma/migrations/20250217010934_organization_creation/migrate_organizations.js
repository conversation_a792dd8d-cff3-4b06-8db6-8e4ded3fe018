import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function createOrganizationsForAccounts() {
  try {
    await prisma.$transaction(async (tx) => {
      // Step 1: Get all accounts that do NOT have an organization
      const accountsWithoutOrg = await tx.account.findMany({
        // @ts-ignore
        where: { organizationId: null },
        select: { id: true },
      });

      if (accountsWithoutOrg.length === 0) {
        console.log("No accounts without organizations found.");
        return;
      }

      // Step 2: Create a new organization for each account
      const createdOrganizations = await Promise.all(
        accountsWithoutOrg.map(async (account) => {
          return tx.organization.create({
            data: {
              accounts: { connect: { id: account.id } }, // Link it immediately
            },
          });
        }),
      );

      console.log(`Created ${createdOrganizations.length} organizations.`);
    });

    console.log("Organizations successfully created and linked.");
  } catch (error) {
    console.error("Error creating organizations:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createOrganizationsForAccounts();
