import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function createOrganizationsForAccounts() {
  try {
    await prisma.$transaction(async (tx) => {
      // Step 1: Get all accounts that do NOT have an organization
      const staffWithoutUser = await tx.staff.findMany({
        // @ts-ignore
        where: { userId: null },
      });

      if (staffWithoutUser.length === 0) {
        console.log("No staff without a user found.");
        return;
      }

      const seenUsers = {};
      // Step 2: Create a new organization for each account
      for (const staff of staffWithoutUser) {
        // @ts-ignore
        if (seenUsers[staff.email.toLowerCase()] !== undefined) {
          await tx.staff.update({
            where: { id: staff.id },
            data: {
              user: {
                // @ts-ignore
                connect: { id: seenUsers[staff.email.toLowerCase()].id },
              },
            },
          });
          continue;
        }

        // @ts-ignore
        seenUsers[staff.email.toLowerCase()] = await tx.user.create({
          data: {
            email: staff.email,
            password: staff.password,
            staff: { connect: { id: staff.id } }, // Link it immediately
          },
        });
      }
    });
  } catch (error) {
    console.error("Error creating user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createOrganizationsForAccounts();
