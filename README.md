# Party Rental Platform - Platform

This application really puts the platform in Party Rental Platform. This is the main application that will be used by the party rental companies to manage their inventory, orders, and customers.

This application hosts the API for serving Infinity Grid data, and the admin dashboard for managing the data.

## Platform Tech Stack

- Next.js
- React
- TypeScript
- Tailwind CSS
- PostgreSQL
  - Deployed on AWS
- Prisma
- Digital Ocean App Platform
- CircleCI
- Sentry
- HoneyComb (OTEL)
- PostHog [TODO]
  - Feature Flag
  - User Analytics
  - Event Tracking
- Docker (for local development and deployment)

## How to run locally

1. Clone the repo
2. Run `npm install` to install dependencies
3. Setup Local DB (Postgres)
   1. Ask for the `.env` file from the team
4. Copy `.env.example` to `.env` and fill in the values
   1. See step above
5. Run `prisma generate` to create the database tables
6. Run `npm run dev` to start the development server
7. Open `http://localhost:3000` in your browser
8. Enjoy!

## Building Docker Image

1. Install Docker
1. Run `docker build -t <image-name> .` to build the image
1. Run `docker run -p 3000:3000 <image-name>` to run the image
1. Enjoy!

## Linting

1. Run `npm run lint` to lint the project

1. Run `npm run lint:error` to see only the errors causing the linting to fail.

## Deploying

1. Install Docker
2. Login to Digital Ocean Registry `doctl registry login` or `docker login registry.digitalocean.com`
3. Login using API token from Digital Ocean

- username: <EMAIL>
- token: hidden

4. Run `docker build -t registry.digitalocean.com/partyrentalplatform/platform .` to build the image

- If you already have an image built and want to tag it, run `docker tag <image-name> registry.digitalocean.com/partyrentalplatform/platform`

5. Run `docker push registry.digitalocean.com/partyrentalplatform/platform` to push the image to the registry

## Testing

For React components we use `@testing-library/react` and for utility functions we use `jest`.

You can see an example of an API test in the `__tests__/pages/api/account/` directory.

1. Run `npm run test` to run the tests

### Creating a new jest test

Each API route should have a corresponding test file in the `__tests__/pages/api/` directory with 1 positive test and 1 negative test.

1. Create a new file in the `__tests__` directory 2. This test file should be named `*.test.tsx` or `*.test.ts`
2. To mock a module, you must place the `jest.mock(import)` in `jest.setup.ts` file.
3. To mock a module for a specific test, you can use `jest.mock(import, () => {})` in the test file. 5. You can see an example of this in the `__tests__/pages/api/account/upgrade` directory.
4. Write your test
5. Run `npm run test` to run the tests
