import { handleRules } from "~/lib/rules";

describe("lib/rules", () => {
  it("should handle a single rule that applies to a product", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "productId",
                operator: "in",
                value: [1],
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 100,
            },
          },
        },
        priority: 1,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [1],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-02"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 200,
        rules: [1],
      },
    ]);
  });

  it("should not apply rules if the product doesnt map", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "productId",
                operator: "in",
                value: [1],
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 100,
            },
          },
        },
        priority: 1,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [2],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-02"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [2],
      },
    ]);
  });

  it("should handle time based rules", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.startTime.year",
                value: 2022,
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 100,
            },
          },
        },
        priority: 1,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [1],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-02"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 200,
        rules: [1],
      },
    ]);
  });

  it("should handle duration based rules", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.rentalDurationHours",
                value: 5,
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 100,
            },
          },
        },
        priority: 1,
      },
      {
        id: 2,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.startTime.year",
                value: 2022,
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 1000,
            },
          },
        },
        priority: 1,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [1],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-02"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 200,
        rules: [1],
      },
    ]);
  });

  it("should handle rule priorities", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.rentalDurationHours",
                value: 5,
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 100,
            },
          },
        },
        priority: 2,
      },
      {
        id: 2,
        name: "Test Rule 2",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.startTime.year",
                value: 2022,
              },
            ],
          },
          event: {
            type: "PRICE_INCREASE",
            params: {
              price: 1000,
            },
          },
        },
        priority: 1,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [1, 2],
      },
      {
        id: 2,
        basePrice: 100,
        price: 100,
        rules: [],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-02"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 200,
        rules: [1, 2],
      },
      {
        id: 2,
        basePrice: 100,
        price: 100,
        rules: [],
      },
    ]);
  });

  it("should rules with dynamic events", async () => {
    const rules = [
      {
        id: 1,
        name: "Test Rule",
        ruleJson: {
          conditions: {
            all: [
              {
                fact: "cart",
                operator: "greaterThanInclusive",
                path: "$.rentalDurationHours",
                value: 5,
              },
            ],
          },
          event: {
            type: "PRICE_PER_DAY", // 50% off after first day
            params: {
              price: 0.5,
              ignoredDays: 1,
              rentalDurationHours: {
                fact: "cart",
                path: "$.rentalDurationHours",
              },
            },
          },
        },
        priority: 2,
      },
    ];

    const products = [
      {
        id: 1,
        basePrice: 100,
        price: 100,
        rules: [1, 2],
      },
    ];

    const cartInfo = {
      startDate: new Date("2023-01-01"),
      endDate: new Date("2023-01-04"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 100,
        price: 200,
        rules: [1, 2],
      },
    ]);
  });
});

describe("testing real life scenarios", () => {
  const rules = [
    {
      id: 1,
      name: "Weekday Pricing",
      ruleJson: {
        conditions: {
          all: [
            {
              fact: "cart",
              operator: "greaterThanInclusive",
              path: "$.startTime.dayOfWeek",
              value: 5, // Rental starts Friday, or Saturday
            },
            {
              fact: "cart",
              operator: "lessThanInclusive",
              path: "$.endTime.dayOfWeek",
              value: 1, // Rental ends Sunday or Monday
            },
          ],
        },
        event: {
          type: "PRICE_MULTIPLY", // 50% off after first day
          params: {
            price: 1.3,
          },
        },
      },
      priority: 2,
    },
    {
      id: 2,
      name: "Tuesday-Thursday Pricing",
      ruleJson: {
        conditions: {
          all: [
            {
              fact: "cart",
              operator: "greaterThanInclusive",
              path: "$.startTime.dayOfWeek",
              value: 2, // Rental starts Tuesday, Wednesday or Thursday
            },
            {
              fact: "cart",
              operator: "lessThan",
              path: "$.startTime.dayOfWeek",
              value: 5, // Not after Friday
            },
            {
              fact: "cart",
              operator: "lessThan",
              path: "$.endTime.dayOfWeek",
              value: 5, // Not after Friday
            },
            {
              fact: "cart",
              operator: "greaterThanInclusive",
              path: "$.endTime.dayOfWeek",
              value: 3, // Rental ends Wednesday or Thursday
            },
            {
              fact: "cart",
              operator: "lessThan",
              path: "rentalDurationHours",
              value: 24 * 3,
            },
          ],
        },
        event: {
          type: "PRICE_INCREASE", // 50% off after first day
          params: {
            price: 0,
          },
        },
      },
      priority: 10,
    },
    {
      id: 3,
      name: "General and Overnight",
      ruleJson: {
        conditions: {
          all: [
            {
              fact: "cart",
              operator: "greaterThanInclusive",
              path: "$.rentalDurationHours",
              value: 12,
            },
          ],
        },
        event: {
          type: "PRICE_PER_DAY",
          params: {
            price: 0.2,
            ignoredDays: 1,
            rentalDurationHours: {
              fact: "cart",
              path: "$.rentalDurationHours",
            },
          },
        },
      },
      priority: 0,
    },
  ];

  const products = [
    {
      id: 1,
      basePrice: 260,
      price: 260,
      rules: [1, 2, 3],
    },
  ];
  it("Should evaluate a weekend rate.", async () => {
    const cartInfo = {
      startDate: new Date("2023-01-06T07:00:00.000Z"),
      endDate: new Date("2023-01-09T07:00:00.000Z"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 260,
        price: 338,
        rules: products?.[0]?.rules,
      },
    ]);
  });

  it("Should evaluate a base rate.", async () => {
    // Base, 1 Day rental
    const cartInfo = {
      startDate: new Date("2023-01-06T07:00:00.000Z"),
      endDate: new Date("2023-01-06T20:00:00.000Z"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 260,
        price: 260,
        rules: products?.[0]?.rules,
      },
    ]);
  });

  it("Should evaluate an overnight rate.", async () => {
    const cartInfo = {
      startDate: new Date("2023-01-06T07:00:00.000Z"),
      endDate: new Date("2023-01-07T13:00:00.000Z"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 260,
        price: 312,
        rules: products?.[0]?.rules,
      },
    ]);
  });

  it("Should evaluate a weird day rate.", async () => {
    // 6 day rental, at 80% off for additional days after the 1st day.
    // $260 + (5 * 260 * .2) = $520
    const cartInfo = {
      startDate: new Date("2023-01-02T07:00:00.000Z"),
      endDate: new Date("2023-01-08T13:00:00.000Z"),
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 260,
        price: 572,
        rules: products?.[0]?.rules,
      },
    ]);
  });
  it("Should evaluate Tuesday - Thursday (inclusive) rate.", async () => {
    const cartInfo = {
      startDate: new Date("2023-01-03T15:00:00.000Z"), // 9am Tuesday CST
      endDate: new Date("2023-01-05T22:00:00.000Z"), // 4pm Thursday CST
    };

    const result = await handleRules(rules, products, cartInfo);
    expect(result).toEqual([
      {
        id: 1,
        basePrice: 260,
        price: 260,
        rules: products?.[0]?.rules,
      },
    ]);
  });
});
