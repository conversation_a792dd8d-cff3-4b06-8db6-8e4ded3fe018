import { Body, createMocks, Query, RequestMethod } from "node-mocks-http";
import { expect, jest } from "@jest/globals";
import { checkPermission } from "~/pages/api/permissions";
import { Account } from ".prisma/client";
import { db } from "~/server/db";

jest.mock("~/pages/api/permissions");

export const testApiHandler = async (
  handler: (req: any, res: any) => void | Promise<unknown>,
  {
    permissionNodes,
    method = "GET",
    query = {},
    body = {},
  }: {
    permissionNodes?: string[];
    method?: RequestMethod;
    query?: Query;
    body?: Body;
  } = {},
) => {
  const { req, res } = createMocks({
    method,
    query,
    body,
  });

  const mockFunc = checkPermission.mockImplementation(() => {
    return Promise.resolve({
      accountId: 1,
      organizationId: "orgId",
      userId: "userId",
      staffId: "staffId",
      totalPermissions: permissionNodes,
    });
  });

  await handler(req, res);

  if (permissionNodes) {
    expect(mockFunc).toHaveBeenCalled();
  } else {
    expect(mockFunc).not.toHaveBeenCalled();
  }

  return res;
};

export const createMockAccount = (overrideData: Record<string, unknown>) => {
  const accountData: Account = {
    id: 1,
    name: "Test",
    createdAt: new Date(),
    updatedAt: new Date(),
    damageWaiverRate: 1,
    freeTravelRadius: 20,
    googleReviewLink: "google.com",
    travelFeePerMile: 4,
    defaultTaxRate: 0,
    businessPhone: "+***********",
    businessEmail: "<EMAIL>",
    customDomain: "test.com",
    stripeAccountId: "stripeTest",
    businessTimezone: "America/Chicago",
    billingAddressId: null,
    stripeCustomerId: null,
    ...overrideData,
    minimumOrderPaymentPercentage: 25,
    deleted: false,
    organizationId: "orgId",
  };

  // @ts-ignore
  db.account.findFirst.mockResolvedValue({
    ...accountData,
  });
};
