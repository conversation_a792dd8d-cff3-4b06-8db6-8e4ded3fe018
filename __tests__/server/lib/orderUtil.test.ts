import { db } from "~/server/db";
import {
  areProductsAvailable,
  getQuantityOfProductsAvailable,
  getQuantityOfProductsAvailableBulk,
  ProductWithBuffer,
} from "~/server/lib/orderUtil";
import { createId } from "@paralleldrive/cuid2";

const organizationId = createId();

describe("getQuantityOfProductsAvailable", () => {
  const startTime = new Date("2022-01-01T00:00:00Z");
  const endTime = new Date("2022-01-01T03:00:00Z");

  test("should return no deviation in quantity if there are no orders.", async () => {
    db.order.findMany.mockResolvedValue([]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      1,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(0);
  });

  test("should return 1 quantity if there is an order in the time frame", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      2,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("weird time frame query should respond accurately", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const endTime2 = new Date("2022-01-01T01:00:00.000Z");
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      2,
      startTime,
      endTime2,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(1);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with multiple orders", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
      {
        id: 2,
        startTime: new Date(startTime.getTime() + 60 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 60 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      3,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with overlapping orders", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
      {
        id: 2,
        startTime: new Date("2022-01-01T01:30:00Z"),
        endTime: new Date("2022-01-01T03:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      3,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: 2,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with orders outside the time frame", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:30:00Z"),
        endTime: new Date("2022-01-01T04:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      3,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(0);
  });

  test("should return correct quantity with different interval seconds", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      2,
      startTime,
      endTime,
      30 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(6);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T00:30:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T00:30:00.000Z"),
      },
      {
        end: new Date("2022-01-01T01:30:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T01:30:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:30:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        quantity: 1,
        start: new Date("2022-01-01T02:30:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with no products available initially", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailable(
      organizationId,
      1,
      0,
      startTime,
      endTime,
      60 * 60,
      0,
      0,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        quantity: -1,
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        quantity: -1,
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        quantity: -1,
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });
});
describe("getQuantityOfProductsAvailableBulk", () => {
  const startTime = new Date("2022-01-01T00:00:00Z");
  const endTime = new Date("2022-01-01T03:00:00Z");
  test("should return all time slots with empty productInfo if there are no orders.", async () => {
    db.order.findMany.mockResolvedValue([]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 1,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        start: new Date("2022-01-01T00:00:00.000Z"),
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T01:00:00.000Z"),
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T02:00:00.000Z"),
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [],
      },
    ]);
  });

  test("should return correct quantity with multiple orders", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
      {
        id: 2,
        startTime: new Date(startTime.getTime() + 60 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 60 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with overlapping orders", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
      {
        id: 2,
        startTime: new Date("2022-01-01T01:30:00Z"),
        endTime: new Date("2022-01-01T03:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 2 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with orders outside the time frame", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:30:00Z"),
        endTime: new Date("2022-01-01T04:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with different interval seconds", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 2,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      30 * 60,
    );
    expect(result).toHaveLength(6);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T00:30:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T00:30:00.000Z"),
      },
      {
        end: new Date("2022-01-01T01:30:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T01:30:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:30:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T02:30:00.000Z"),
      },
    ]);
  });

  test("should return correct quantity with no products available initially", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 0,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return all time slots with empty productInfo if there are no orders.", async () => {
    db.order.findMany.mockResolvedValue([]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 1,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        start: new Date("2022-01-01T00:00:00.000Z"),
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T01:00:00.000Z"),
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T02:00:00.000Z"),
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [],
      },
    ]);
  });

  test("should return correct quantity with multiple orders", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
      {
        id: 2,
        startTime: new Date(startTime.getTime() + 60 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 60 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: 1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should return all time slots with empty productInfo if orders are outside the time frame", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:30:00Z"),
        endTime: new Date("2022-01-01T04:30:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        start: new Date("2022-01-01T00:00:00.000Z"),
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T01:00:00.000Z"),
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T02:00:00.000Z"),
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [],
      },
    ]);
  });

  test("should return correct quantity with no products available initially", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 0,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [{ productId: 1, quantity: -1 }],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should handle multiple products correctly", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date(startTime.getTime() + 30 * 60 * 1000),
        endTime: new Date(endTime.getTime() - 30 * 60 * 1000),
        OrderProduct: [
          { product: { id: 1, quantity: 1 }, productId: 1, quantity: 1 },
          { product: { id: 2, quantity: 2 }, productId: 2, quantity: 2 },
        ],
      },
    ]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [
        {
          productId: 1,
          quantity: 3,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
        {
          productId: 2,
          quantity: 5,
          beforeRentalBufferMinutes: 0,
          afterRentalBufferMinutes: 0,
        },
      ],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 3 },
        ],
        start: new Date("2022-01-01T00:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 3 },
        ],
        start: new Date("2022-01-01T01:00:00.000Z"),
      },
      {
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 3 },
        ],
        start: new Date("2022-01-01T02:00:00.000Z"),
      },
    ]);
  });

  test("should handle no products correctly", async () => {
    db.order.findMany.mockResolvedValue([]);
    const result = await getQuantityOfProductsAvailableBulk(
      organizationId,
      [],
      startTime,
      endTime,
      60 * 60,
    );
    expect(result).toHaveLength(3);
    expect(result).toEqual([
      {
        start: new Date("2022-01-01T00:00:00.000Z"),
        end: new Date("2022-01-01T01:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T01:00:00.000Z"),
        end: new Date("2022-01-01T02:00:00.000Z"),
        productInfo: [],
      },
      {
        start: new Date("2022-01-01T02:00:00.000Z"),
        end: new Date("2022-01-01T03:00:00.000Z"),
        productInfo: [],
      },
    ]);
  });
});

describe("areProductsAvailable", () => {
  const startTime = new Date("2022-01-01T00:00:00Z");
  const endTime = new Date("2022-01-01T03:00:00Z");
  const products: ProductWithBuffer[] = [
    {
      productId: 1,
      quantity: 2,
      beforeRentalBufferMinutes: 0,
      afterRentalBufferMinutes: 0,
    },
    {
      productId: 2,
      quantity: 3,
      beforeRentalBufferMinutes: 0,
      afterRentalBufferMinutes: 0,
    },
  ];

  test("should return available products when there are no orders affecting the requested quantities", async () => {
    db.order.findMany.mockResolvedValue([]);
    const result = await areProductsAvailable(
      organizationId,
      products,
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [1, 2],
      taken: [],
    });
  });

  test("should return taken products when there are orders affecting the requested quantities", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 3 },
        ],
      },
    ]);
    const result = await areProductsAvailable(
      organizationId,
      products,
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [],
      taken: [1, 2],
    });
  });

  test("should return taken products when there are orders affecting the requested quantities", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 1 },
        ],
      },
    ]);
    const result = await areProductsAvailable(
      organizationId,
      products,
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [],
      taken: [1, 2],
    });
  });

  test("should return taken products with buffer time before the event", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2021-12-31T23:30:00Z"),
        endTime: new Date("2022-01-01T02:30:00Z"),
        OrderProduct: [{ productId: 1, quantity: 2 }],
      },
    ]);
    const result = await areProductsAvailable(
      organizationId,
      [
        {
          productId: 1,
          quantity: 2,
          beforeRentalBufferMinutes: 30,
          afterRentalBufferMinutes: 30,
        },
        {
          productId: 2,
          quantity: 3,
          beforeRentalBufferMinutes: 60,
          afterRentalBufferMinutes: 60,
        },
      ],
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [2],
      taken: [1],
    });
  });

  test("should return taken products with buffer time after the event", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T00:30:00Z"),
        endTime: new Date("2022-01-01T04:00:00Z"),
        OrderProduct: [{ productId: 2, quantity: 3 }],
      },
    ]);
    const result = await areProductsAvailable(
      organizationId,
      [
        {
          productId: 1,
          quantity: 2,
          beforeRentalBufferMinutes: 30,
          afterRentalBufferMinutes: 30,
        },
        {
          productId: 2,
          quantity: 3,
          beforeRentalBufferMinutes: 60,
          afterRentalBufferMinutes: 60,
        },
      ],
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [1],
      taken: [2],
    });
  });

  test("should return taken products with buffer time before and after the event", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2021-12-31T23:30:00Z"),
        endTime: new Date("2022-01-01T04:00:00Z"),
        OrderProduct: [
          { productId: 1, quantity: 2 },
          { productId: 2, quantity: 3 },
        ],
      },
    ]);
    const result = await areProductsAvailable(
      organizationId,
      [
        {
          productId: 1,
          quantity: 2,
          beforeRentalBufferMinutes: 30,
          afterRentalBufferMinutes: 30,
        },
        {
          productId: 2,
          quantity: 3,
          beforeRentalBufferMinutes: 60,
          afterRentalBufferMinutes: 60,
        },
      ],
      startTime,
      endTime,
    );
    expect(result).toEqual({
      available: [],
      taken: [1, 2],
    });
  });
});
