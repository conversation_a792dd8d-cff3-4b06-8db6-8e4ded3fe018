import { describe, expect, it } from "@jest/globals";
import {
  convertFeeTypeToPaymentInfoLineItemName,
  CurrencyValue,
  getCurrencyString,
  getCurrencyValue,
  getMinimumDeposit,
  getPaymentInfo,
  PaymentInfoInput,
} from "~/server/lib/currency";
import { OrderFeeType } from "@prisma/client";

describe("CurrencyValue", () => {
  it("should return the correct value", async () => {
    const value = CurrencyValue.fromPlatform(100);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
  });

  it("should return the correct value with a decimal", async () => {
    const value = CurrencyValue.fromPlatform(100.5);
    expect(value.amount).toBe(100.5);
    expect(value.stripeAmount).toBe(100_50);
  });

  it("should return the correct value with a negative", async () => {
    const value = CurrencyValue.fromPlatform(-100);
    expect(value.amount).toBe(-100);
    expect(value.stripeAmount).toBe(-100_00);
  });

  it("should return the correct value with a negative decimal", async () => {
    const value = CurrencyValue.fromPlatform(-100.5);
    expect(value.amount).toBe(-100.5);
    expect(value.stripeAmount).toBe(-100_50);
  });

  it("should return the correctly rounded value", async () => {
    const value = CurrencyValue.fromPlatform(100.555);
    expect(value.amount).toBe(100.56);
    expect(value.stripeAmount).toBe(100_56);
  });

  it("should return the correctly rounded value with a negative", async () => {
    const value = CurrencyValue.fromPlatform(-100.555);
    expect(value.amount).toBe(-100.55);
    expect(value.stripeAmount).toBe(-100_55);
  });

  it("should never return more than 2 decimals", async () => {
    const value = CurrencyValue.fromPlatform(-100.55555555555);
    expect(value.amount).toBe(-100.55);
    expect(value.stripeAmount).toBe(-100_55);
  });

  it("should return the correct value from stripe", async () => {
    const value = CurrencyValue.fromStripe(100_00);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
  });

  it("should return the correct value with a decimal from stripe", async () => {
    const value = CurrencyValue.fromStripe(100_50);
    expect(value.amount).toBe(100.5);
    expect(value.stripeAmount).toBe(100_50);
  });

  it("should return the correct value with a negative from stripe", async () => {
    const value = CurrencyValue.fromStripe(-100_00);
    expect(value.amount).toBe(-100);
    expect(value.stripeAmount).toBe(-100_00);
  });

  it("should return the correct value with a negative decimal from stripe", async () => {
    const value = CurrencyValue.fromStripe(-100_50);
    expect(value.amount).toBe(-100.5);
    expect(value.stripeAmount).toBe(-100_50);
  });

  it("should return the correctly rounded value from stripe", async () => {
    const value = CurrencyValue.fromStripe(100_56);
    expect(value.amount).toBe(100.56);
    expect(value.stripeAmount).toBe(100_56);
  });

  it("should return the correctly rounded value with a negative from stripe", async () => {
    const value = CurrencyValue.fromStripe(-100_55);
    expect(value.amount).toBe(-100.55);
    expect(value.stripeAmount).toBe(-100_55);
  });

  it("should be able to add values", async () => {
    const value = CurrencyValue.fromPlatform(100);
    const value2 = CurrencyValue.fromPlatform(100);
    const newValue = value.add(value2);
    expect(newValue.amount).toBe(200);
    expect(newValue.stripeAmount).toBe(200_00);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
    expect(value2.amount).toBe(100);
    expect(value2.stripeAmount).toBe(100_00);
  });

  it("should be able to subtract values", async () => {
    const value = CurrencyValue.fromPlatform(100);
    const value2 = CurrencyValue.fromPlatform(100);
    const newValue = value.subtract(value2);
    expect(newValue.amount).toBe(0);
    expect(newValue.stripeAmount).toBe(0);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
    expect(value2.amount).toBe(100);
    expect(value2.stripeAmount).toBe(100_00);
  });

  it("should be able to divide values", async () => {
    const value = CurrencyValue.fromPlatform(100);
    const newValue = value.divide(2);
    expect(newValue.amount).toBe(50);
    expect(newValue.stripeAmount).toBe(50_00);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
  });

  it("should be able to do math from different sources", async () => {
    const value = CurrencyValue.fromPlatform(100);
    const value2 = CurrencyValue.fromStripe(100_00);
    const newValue = value.add(value2);
    expect(newValue.amount).toBe(200);
    expect(newValue.stripeAmount).toBe(200_00);
    expect(value.amount).toBe(100);
    expect(value.stripeAmount).toBe(100_00);
    expect(value2.amount).toBe(100);
    expect(value2.stripeAmount).toBe(100_00);
  });

  it("should be able to get the string value", async () => {
    const value = CurrencyValue.fromPlatform(100);
    const stringValue = value.toPlatformString();
    expect(stringValue).toBe("$100.00");
  });

  it("should be able to get the string from stripe", async () => {
    const value = CurrencyValue.fromStripe(-100_00);
    const stringValue = value.toPlatformString();
    expect(stringValue).toBe("-$100.00");
  });
});

describe("getCurrencyString", () => {
  it("should return the correct string", async () => {
    const value = getCurrencyString(100);
    expect(value).toBe("$100.00");
  });

  it("should return the correct string with a decimal", async () => {
    const value = getCurrencyString(100.5);
    expect(value).toBe("$100.50");
  });

  it("should return the correct string with a negative", async () => {
    const value = getCurrencyString(-100);
    expect(value).toBe("-$100.00");
  });

  it("should return the correct string with a negative decimal", async () => {
    const value = getCurrencyString(-100.5);
    expect(value).toBe("-$100.50");
  });

  it("should return the correctly rounded string", async () => {
    const value = getCurrencyString(100.555);
    expect(value).toBe("$100.56");
  });

  it("should return the correctly rounded string with a negative", async () => {
    const value = getCurrencyString(-100.555);
    expect(value).toBe("-$100.55");
  });

  it("should never return more than 2 decimals", async () => {
    const value = getCurrencyString(-100.55555555555);
    expect(value).toBe("-$100.55");
  });
});

describe("getCurrencyValue", () => {
  it("should return the correct value", async () => {
    const value = getCurrencyValue(100);
    expect(value).toBe(100);
  });

  it("should return the correct value with a decimal", async () => {
    const value = getCurrencyValue(100.5);
    expect(value).toBe(100.5);
  });

  it("should return the correct value with a negative", async () => {
    const value = getCurrencyValue(-100);
    expect(value).toBe(-100);
  });

  it("should return the correct value with a negative decimal", async () => {
    const value = getCurrencyValue(-100.5);
    expect(value).toBe(-100.5);
  });

  it("should return the correctly rounded value", async () => {
    const value = getCurrencyValue(100.555);
    expect(value).toBe(100.56);
  });

  it("should return the correctly rounded value with a negative", async () => {
    const value = getCurrencyValue(-100.555);
    expect(value).toBe(-100.55);
  });

  it("should never return more than 2 decimals", async () => {
    const value = getCurrencyValue(-100.55555555555);
    expect(value).toBe(-100.55);
  });
});

describe("getPaymentInfo", () => {
  // Testing to ensure that no extra items are added or removed.
  it("should return from basic input", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 100,
      discounts: [],
      damageWaiver: null,
      fees: [],
      taxRate: null,
      totalPaid: 0,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(100);
    expect(paymentInfo.totalPaid).toBe(0);
    expect(paymentInfo.finalTotal).toBe(100);
    expect(paymentInfo.amountRemaining).toBe(100);
    expect(paymentInfo.tax).toBeNull();
    expect(paymentInfo.discounts).toHaveLength(0);
    expect(paymentInfo.damageWaiver).toBeNull();
    expect(paymentInfo.fees).toHaveLength(0);
    expect(paymentInfo.displayItems).toHaveLength(1);
  });

  // tests to ensure that the final total is calculated correctly with a customer payment
  it("should account for customer payments", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 100,
      discounts: [],
      damageWaiver: null,
      fees: [],
      taxRate: null,
      totalPaid: 10,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(100);
    expect(paymentInfo.totalPaid).toBe(10);
    expect(paymentInfo.finalTotal).toBe(100);
    expect(paymentInfo.amountRemaining).toBe(90);
    expect(paymentInfo.displayItems).toHaveLength(1);
  });

  // tests to ensure that the math is done in the correct order and that the final total is calculated correctly
  // No coupon, sale, or general discount, but there is a rental protection and travel fee
  it("should accept realistic order scenario", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      damageWaiver: {
        percentage: 10,
      },
      discounts: [],
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
        {
          type: "SURFACE_FEE",
          name: null,
          amount: 5,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 0,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(0);
    // 197.625 is the actual total, this will be rounded to the dollar amount
    // 150 + (150 * .10) + 21 + 5 + (150 * .0625)
    expect(paymentInfo.finalTotal).toBe(202.94);
    expect(paymentInfo.amountRemaining).toBe(paymentInfo.finalTotal);
    expect(paymentInfo.displayItems).toHaveLength(5);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 165,
        amountBefore: 150,
        itemAmount: 15,
        name: "Rental Protection",
      },
      {
        amountAfter: 186,
        amountBefore: 165,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 191,
        amountBefore: 186,
        itemAmount: 5,
        name: "Surface Fee",
        detail: {
          taxable: true,
        },
      },
      {
        amountAfter: 202.94,
        amountBefore: 191,
        itemAmount: 11.94,
        name: "Tax",
      },
    ]);
  });

  it("should handle multiple fees", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      damageWaiver: {
        percentage: 10,
      },
      discounts: [],
      fees: [
        {
          type: "CUSTOM_FEE",
          name: "Your Mom Fee",
          amount: 20,
          taxable: true,
        },
        {
          type: "CUSTOM_FEE",
          name: "Uncle Sam Fee",
          amount: 15,
          taxable: false,
        },
        {
          type: "TRAVEL_FEE",
          name: null,
          amount: 21,
          taxable: true,
        },
        {
          type: "SURFACE_FEE",
          name: null,
          amount: 5,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 0,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(0);
    // 197.625 is the actual total, this will be rounded to the dollar amount
    // 150 + (150 * .10) + 20 + 21 + 5 + (150 * .0625) + 15
    expect(paymentInfo.finalTotal).toBe(239.19);
    expect(paymentInfo.amountRemaining).toBe(paymentInfo.finalTotal);
    expect(paymentInfo.displayItems).toHaveLength(7);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 165,
        amountBefore: 150,
        itemAmount: 15,
        name: "Rental Protection",
      },
      {
        amountAfter: 185,
        amountBefore: 165,
        itemAmount: 20,
        name: "Order Fee",
        detail: {
          name: "Your Mom Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 206,
        amountBefore: 185,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          taxable: true,
        },
      },
      {
        amountAfter: 211,
        amountBefore: 206,
        itemAmount: 5,
        name: "Surface Fee",
        detail: {
          taxable: true,
        },
      },
      {
        amountAfter: 224.19,
        amountBefore: 211,
        itemAmount: 13.19,
        name: "Tax",
      },
      {
        amountAfter: 239.19,
        amountBefore: 224.19,
        itemAmount: 15,
        name: "Order Fee",
        detail: {
          name: "Uncle Sam Fee",
          taxable: false,
        },
      },
    ]);
  });

  // Tests to ensure that the math is done in the correct order and that the final total is calculated correctly
  // No coupon, sale, or general discount, but there is a rental protection and travel fee and customer payment (the deposit)
  it("should continue to calculate correctly a customer makes a payment", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      discounts: [],
      damageWaiver: {
        percentage: 10,
      },
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 49.41,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(49.41);
    // 197.625 is the actual total, this will be rounded to the dollar amount
    // ogAmount = 150 + (150 * .10) + 21 + (150 * .0625)
    // totalPaid = ogAmount * .25
    // ogAmount - totalPaid = 197.63 - 49.41 = 148.22
    expect(paymentInfo.finalTotal).toBe(197.63);
    expect(paymentInfo.amountRemaining).toBe(148.22);
    // this shouldn't change
    expect(paymentInfo.displayItems).toHaveLength(4);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 165,
        amountBefore: 150,
        itemAmount: 15,
        name: "Rental Protection",
      },
      {
        amountAfter: 186,
        amountBefore: 165,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 197.63,
        amountBefore: 186,
        itemAmount: 11.63,
        name: "Tax",
      },
    ]);
  });

  it("should accept a coupon code percentage", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      damageWaiver: {
        percentage: 10,
      },
      discounts: [
        {
          chargeType: "PERCENTAGE",
          type: "COUPON",
          amount: 10,
          entityId: 1,
        },
      ],
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 0,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(0);
    // 197.63 is the final total without the coupon, but this is where coupons are tricky.
    // because the coupon ONLY applies to the base total, not the total after the rental protection, travel fee, or tax
    // BUT because we're taking less income in, the taxable rate is less, so the tax is less
    // so all percentages are shifted. This is why the final total is 180.1
    // shiftedBase = 150 - (150 * .10) = 135
    // subTotal = shiftedBase + (shiftedBase * .1) + 21 = 169.5
    // finalTotal = subTotal + (subTotal * .0625) = 180.09375
    expect(paymentInfo.finalTotal).toBe(180.1);
    expect(paymentInfo.amountRemaining).toBe(paymentInfo.finalTotal);
    expect(paymentInfo.displayItems).toHaveLength(5);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 135,
        amountBefore: 150,
        itemAmount: 15,
        name: "Coupon Code",
      },
      {
        amountAfter: 148.5,
        amountBefore: 135,
        itemAmount: 13.5,
        name: "Rental Protection",
      },
      {
        amountAfter: 169.5,
        amountBefore: 148.5,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 180.1,
        amountBefore: 169.5,
        itemAmount: 10.6,
        name: "Tax",
      },
    ]);
  });
  it("should accept a coupon code flat amount", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      discounts: [
        {
          chargeType: "DOLLAR",
          type: "COUPON",
          amount: 10,
          entityId: 1,
        },
      ],
      damageWaiver: {
        percentage: 10,
      },
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 0,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(0);
    // 197.63 is the final total without the coupon, but this is where coupons are tricky.
    // because the coupon ONLY applies to the base total, not the total after the rental protection, travel fee, or tax
    // BUT because we're taking less income in, the taxable rate is less, so the tax is less
    // so all percentages are shifted. This is why the final total is 180.1
    // shiftedBase = 150 - 10 = 140
    // subTotal = shiftedBase + (shiftedBase * .1) + 21 = 175
    // finalTotal = subTotal + (subTotal * .0625) = 185.9375
    // So, even just changing from a percentage to a flat amount, the final total is different because the tax is different
    expect(paymentInfo.finalTotal).toBe(185.94);
    expect(paymentInfo.amountRemaining).toBe(paymentInfo.finalTotal);

    expect(paymentInfo.displayItems).toHaveLength(5);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 140,
        amountBefore: 150,
        itemAmount: 10,
        name: "Coupon Code",
      },
      {
        amountAfter: 154,
        amountBefore: 140,
        itemAmount: 14,
        name: "Rental Protection",
      },
      {
        amountAfter: 175,
        amountBefore: 154,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 185.94,
        amountBefore: 175,
        itemAmount: 10.94,
        name: "Tax",
      },
    ]);
  });

  it("should have consistent values when the customer pays even with a percentage coupon", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      discounts: [
        {
          chargeType: "PERCENTAGE",
          type: "COUPON",
          amount: 10,
          entityId: 1,
        },
      ],
      damageWaiver: {
        percentage: 10,
      },
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 6.25,
      },
      totalPaid: 45.03,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(45.03);
    // 197.63 is the final total without the coupon, but this is where coupons are tricky.
    // because the coupon ONLY applies to the base total, not the total after the rental protection, travel fee, or tax
    // BUT because we're taking less income in, the taxable rate is less, so the tax is less
    // so all percentages are shifted. This is why the final total is 180.1
    // shiftedBase = 150 - (150 * .10) = 135
    // subTotal = shiftedBase + (shiftedBase * .1) + 21 = 169.5
    // finalTotal = subTotal + (subTotal * .0625) = 180.09375
    // THEN, the customer pays 45.03 (25% of the final total)
    // 180.09375 - 45.03 = 135.0675
    expect(paymentInfo.finalTotal).toBe(180.1);
    expect(paymentInfo.amountRemaining).toBe(135.07);
    expect(paymentInfo.displayItems).toHaveLength(5);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 135,
        amountBefore: 150,
        itemAmount: 15,
        name: "Coupon Code",
      },
      {
        amountAfter: 148.5,
        amountBefore: 135,
        itemAmount: 13.5,
        name: "Rental Protection",
      },
      {
        amountAfter: 169.5,
        amountBefore: 148.5,
        itemAmount: 21,
        name: "Travel Fee",
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
      },
      {
        amountAfter: 180.1,
        amountBefore: 169.5,
        itemAmount: 10.6,
        name: "Tax",
      },
    ]);
  });

  it("should return consistently with the kitchen skink", async () => {
    const input: PaymentInfoInput = {
      baseTotal: 150,
      discounts: [
        {
          chargeType: "DOLLAR",
          type: "COUPON",
          amount: 15,
          entityId: 1,
        },
        {
          chargeType: "PERCENTAGE",
          type: "SALE",
          amount: 10,
          entityId: 1,
        },
        {
          chargeType: "DOLLAR",
          type: "MANUAL",
          amount: 5,
          entityId: 1,
        },
      ],
      damageWaiver: {
        percentage: 8.5,
      },
      fees: [
        {
          type: "TRAVEL_FEE",
          name: "Travel Fee",
          amount: 21,
          taxable: true,
        },
      ],
      taxRate: {
        percentage: 7.25,
      },
      totalPaid: 100,
    };

    const paymentInfo = getPaymentInfo(input);

    expect(paymentInfo.baseTotal).toBe(150);
    expect(paymentInfo.totalPaid).toBe(100);

    expect(paymentInfo.finalTotal).toBe(158.1);
    expect(paymentInfo.amountRemaining).toBe(58.1);
    expect(paymentInfo.displayItems).toHaveLength(7);
    expect(paymentInfo.displayItems).toEqual([
      { amountAfter: 150, amountBefore: 0, itemAmount: 150, name: "Product" },
      {
        amountAfter: 135,
        amountBefore: 150,
        itemAmount: 15,
        name: "Coupon Code",
      },
      {
        amountAfter: 121.5,
        amountBefore: 135,
        itemAmount: 13.5,
        name: "Sale",
      },
      {
        amountAfter: 116.5,
        amountBefore: 121.5,
        itemAmount: 5,
        name: "General Discount",
      },
      {
        amountAfter: 126.41,
        amountBefore: 116.5,
        itemAmount: 9.91,
        name: "Rental Protection",
      },

      {
        amountAfter: 147.41,
        amountBefore: 126.41,
        itemAmount: 21,
        detail: {
          name: "Travel Fee",
          taxable: true,
        },
        name: "Travel Fee",
      },
      {
        amountAfter: 158.1,
        amountBefore: 147.41,
        itemAmount: 10.69,
        name: "Tax",
      },
    ]);
  });
});

describe("getMinimumDeposit", () => {
  it("should return the correct deposit when percentage is less than 1", () => {
    const deposit = getMinimumDeposit(100, 0.1);
    expect(deposit).toBe(10);
  });

  it("should return the correct deposit when percentage is greater than 1", () => {
    const deposit = getMinimumDeposit(100, 10);
    expect(deposit).toBe(10);
  });

  it("should return the correct deposit when percentage is exactly 1", () => {
    const deposit = getMinimumDeposit(100, 1);
    expect(deposit).toBe(1);
  });

  it("should return 0 when finalTotal is NaN", () => {
    const deposit = getMinimumDeposit(NaN, 0.1);
    expect(deposit).toBe(0);
  });

  it("should return 1 when percentage is 0, we always keep a card on file.", () => {
    const deposit = getMinimumDeposit(100, 0);
    expect(deposit).toBe(1);
  });

  it("should return 1 when percentage is negative", () => {
    const deposit = getMinimumDeposit(100, -0.1);
    expect(deposit).toBe(1);
  });

  it("should return at least 1 when calculated deposit is less than 1", () => {
    const deposit = getMinimumDeposit(5, 0.01);
    expect(deposit).toBe(1);
  });

  it("should handle decimals in the final total", () => {
    const deposit = getMinimumDeposit(100.6667, 25);
    expect(deposit).toBe(25.17);
  });

  it("should handle decimals in the final total", () => {
    const deposit = getMinimumDeposit(103.88, 33);
    expect(deposit).toBe(34.29);
  });

  it("should handle handle float or int", () => {
    const deposit = getMinimumDeposit(103.87, 33);
    const other = getMinimumDeposit(103.87, 0.33);
    expect(deposit).toBe(34.28);
    expect(other).toBe(deposit);
  });
});

describe("convertFeeTypeToPaymentInfoLineItemName", () => {
  it("should map all order type fees", () => {
    Object.values(OrderFeeType).forEach((type) => {
      const name = convertFeeTypeToPaymentInfoLineItemName(type);
      expect(name).toBeDefined();
    });
  });
});
