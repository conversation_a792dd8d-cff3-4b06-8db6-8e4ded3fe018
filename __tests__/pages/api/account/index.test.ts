import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApiHandler } from "../../../apiTestUtil";
import handler from "~/pages/api/account";
import { db } from "~/server/db";

describe("/api/account", () => {
  it("should return 405 for non-GET requests", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(405);
  });
  it("should return 401 if account is not found", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(401);
  });
  it("should return 200 with account settings with no subscription", async () => {
    createMockAccount({
      damageWaiverRate: 1,
      freeTravelRadius: 20,
      googleReviewLink: "google.com",
      travelFeePerMile: 4,
      defaultTaxRate: 6,
      billingAddress: {
        id: 1,
        line1: "123 Main St",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
      businessPhone: "+***********",
      businessEmail: "<EMAIL>",
      customDomain: "test.com",
      stripeAccountId: "stripe_account_id",
    });

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      settings: {
        damageWaiverRate: 1,
        travelFeePerMile: 4,
        freeTravelRadius: 20,
        googleReviewLink: "google.com",
        minimumOrderPaymentPercentage: 25,
        businessPhone: "+***********",
        businessEmail: "<EMAIL>",
        customDomain: "test.com",
        stripeConfigured: true,
        billingAddress: {
          line1: "123 Main St",
          postalCode: "12345",
          city: "Anytown",
          state: "CA",
        },
        businessTimezone: "America/Chicago",
      },
      plan: {
        level: "FREE",
      },
    });
  });
  it("should return 200 with plan level", async () => {
    createMockAccount({
      damageWaiverRate: 1,
      freeTravelRadius: 20,
      googleReviewLink: "google.com",
      travelFeePerMile: 4,
      defaultTaxRate: 6,
      billingAddress: {
        id: 1,
        line1: "123 Main St",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
      businessPhone: "+***********",
      businessEmail: "<EMAIL>",
      customDomain: "test.com",
      stripeAccountId: "stripe_account_id",
    });
    // @ts-ignore
    db.subscription.findFirst.mockResolvedValue({
      planLevel: "core",
    });
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      settings: {
        damageWaiverRate: 1,
        travelFeePerMile: 4,
        freeTravelRadius: 20,
        googleReviewLink: "google.com",
        minimumOrderPaymentPercentage: 25,
        businessPhone: "+***********",
        businessEmail: "<EMAIL>",
        customDomain: "test.com",
        stripeConfigured: true,
        billingAddress: {
          line1: "123 Main St",
          postalCode: "12345",
          city: "Anytown",
          state: "CA",
        },
        businessTimezone: "America/Chicago",
      },
      plan: {
        level: "CORE",
      },
    });
  });
});
