import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApiHandler } from "../../../apiTestUtil";
import handler from "~/pages/api/account/edit";
import * as stripeLib from "~/server/lib/stripe";
import { platformStripeServer } from "~/server/lib/stripe";
import { AccountSettings } from "~/server/account/types";
import prisma from "~/../__mocks__/prisma";

describe("/api/account/edit", () => {
  it("should return 405 for non-POST requests", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
    });

    expect(res.statusCode).toBe(405);
  });
  it("should return 422 if there is no body.", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
    });

    expect(res.statusCode).toBe(422);
  });
  it("should return 422 for invalid body", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: {
        businessEmail: "not an email",
      },
    });

    expect(res.statusCode).toBe(422);
  });
  it("should return 422 for invalid body", async () => {
    createMockAccount({
      businessEmail: "<EMAIL>",
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: {
        businessEmail: "<EMAIL>",
        extraData: "extra",
      },
    });

    expect(res._getJSONData()).toEqual({
      error: "Invalid Request",
      message: "businessTimezone is a required field",
    });
    expect(res.statusCode).toBe(422);
  });
  it("should return 422 for invalid email in body", async () => {
    createMockAccount({
      businessEmail: "<EMAIL>",
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: {
        businessTimezone: "America/Los_Angeles",
        businessEmail: "not an email",
        extraData: "extra",
      },
    });

    expect(res._getJSONData()).toEqual({
      error: "Invalid Request",
      message: "Invalid email format",
    });
    expect(res.statusCode).toBe(422);
  });
  it("should return 200 and update", async () => {
    createMockAccount({
      businessEmail: "<EMAIL>",
      billingAddressId: 1,
      damageWaiverRate: 1,
      billingAddress: {
        line1: "123 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
    });

    const newAccountSettings: AccountSettings = {
      businessTimezone: "America/Los_Angeles",
      businessEmail: "<EMAIL>",
      googleReviewLink: "google.com",
      damageWaiverRate: 2,
      billingAddress: {
        line1: "123 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
      minimumOrderPaymentPercentage: 50,
    };

    const findCustomerMock =
      stripeLib.findOrCreateStripeCustomerPlatform.mockImplementation(() => {
        return {
          id: "customer_id",
        };
      });

    const billingPortalMock =
      platformStripeServer.customers.update.mockImplementation(() => {
        return {
          email: "",
        };
      });

    prisma.account.update.mockImplementation((data) => {
      expect(data).toEqual({
        where: {
          id: 1,
        },
        data: {
          businessTimezone: "America/Los_Angeles",
          damageWaiverRate: 2,
        },
      });
      return {
        id: 1,
      };
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: newAccountSettings,
    });

    expect(findCustomerMock).not.toHaveBeenCalled();
    expect(billingPortalMock).not.toHaveBeenCalled();

    expect(res.statusCode).toBe(200);
  });
  it("should return 200 and update address", async () => {
    createMockAccount({
      businessEmail: "<EMAIL>",
      billingAddressId: 1,
      billingAddress: {
        line1: "123 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
    });

    const newAccountSettings: AccountSettings = {
      businessTimezone: "America/Los_Angeles",
      businessEmail: "<EMAIL>",
      googleReviewLink: "google.com",
      billingAddress: {
        line1: "456 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
    };

    const findCustomerMock =
      stripeLib.findOrCreateStripeCustomerPlatform.mockImplementation(() => {
        return {
          id: "customer_id",
        };
      });

    const billingPortalMock =
      platformStripeServer.customers.update.mockImplementation(() => {
        return {
          email: "",
        };
      });

    prisma.address.findFirst.mockImplementation((data) => {
      expect(data).toEqual({
        where: {
          line1: "456 Any Street",
          line2: undefined,
          city: "Anytown",
          country: "US",
          state: "CA",
          postalCode: "12345",
        },
      });
      return Promise.resolve({
        id: 2,
      });
    });

    prisma.account.update.mockImplementation((data) => {
      expect(data).toEqual({
        where: {
          id: 1,
        },
        data: {
          businessTimezone: "America/Los_Angeles",
          billingAddressId: 2,
        },
      });
      return {
        id: 1,
      };
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: newAccountSettings,
    });

    expect(findCustomerMock).not.toHaveBeenCalled();
    expect(billingPortalMock).not.toHaveBeenCalled();

    expect(res.statusCode).toBe(200);
  });
  it("should return 200 and update with new email", async () => {
    createMockAccount({
      businessEmail: "<EMAIL>",
      billingAddress: {
        line1: "123 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
    });

    const newAccountSettings: AccountSettings = {
      businessTimezone: "America/Los_Angeles",
      businessEmail: "<EMAIL>",
      billingAddress: {
        line1: "123 Any Street",
        city: "Anytown",
        state: "CA",
        postalCode: "12345",
      },
    };

    const findCustomerMock =
      stripeLib.findOrCreateStripeCustomerPlatform.mockImplementation(() => {
        return {
          id: "customer_id",
        };
      });

    const billingPortalMock =
      platformStripeServer.customers.update.mockImplementation(
        (customerId, newEmailObject) => {
          expect(newEmailObject).toEqual({
            email: "<EMAIL>",
          });
          return {
            email: "",
          };
        },
      );

    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.write"],
      body: newAccountSettings,
    });

    expect(findCustomerMock).toHaveBeenCalled();
    expect(billingPortalMock).toHaveBeenCalled();

    expect(res.statusCode).toBe(200);
  });
});
