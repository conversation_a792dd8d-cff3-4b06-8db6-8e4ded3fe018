import {
  createMockAccount,
  testApiHandler,
} from "../../../../../../apiTestUtil";
import POST from "~/pages/api/products/[id]/images/attach";
import { db } from "~/server/db";

describe("POST attach images", () => {
  it("should be 422 with an invalid body", async () => {
    const res = await testApiHandler(POST, {
      method: "POST",
      permissionNodes: [],
      query: {},
      body: {},
    });

    expect(res.statusCode).toBe(422);
  });

  it("Should be 400 when not including a product id", async () => {
    const res = await testApiHandler(POST, {
      method: "POST",
      permissionNodes: [],
      query: {},
      body: {
        imageId: "1",
        priority: 1,
      },
    });

    expect(res.statusCode).toBe(400);
    expect(res._getData()).toMatch(/Missing id or imageId/);
  });

  it("should be 400 if id is not a number", async () => {
    const res = await testA<PERSON><PERSON>andler(POST, {
      method: "POST",
      permissionNodes: [],
      query: { id: "not-a-number" },
      body: {
        imageId: "1",
        priority: 1,
      },
    });

    expect(res.statusCode).toBe(400);
    expect(res._getData()).toMatch(/Invalid product id value/);
  });

  it("should be 400 if product not found", async () => {
    createMockAccount({});
    const res = await testApiHandler(POST, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.products.write"],
      query: { id: 1 },
      body: {
        imageId: "1",
        priority: 1,
      },
    });
    expect(res.statusCode).toBe(400);
    expect(res._getData()).toMatch(/Product not found/);
  });

  it("should be 400 if image not found", async () => {
    createMockAccount({});
    db.product.findUnique.mockResolvedValue({ id: 1 });
    const res = await testApiHandler(POST, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.products.write"],
      query: { id: 1 },
      body: {
        imageId: "1",
        priority: 1,
      },
    });
    expect(res.statusCode).toBe(400);
    expect(res._getData()).toMatch(/Image not found/);
  });

  it("should be 200 if image is attached", async () => {
    createMockAccount({});
    db.product.findUnique.mockResolvedValue({ id: 1 });
    db.imageUpload.findUnique.mockResolvedValue({
      id: 1,
      url: "test",
      name: "ImageName.png",
    });
    const res = await testApiHandler(POST, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.products.write"],
      query: { id: 1 },
      body: {
        imageId: "1",
        priority: 1,
      },
    });
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      success: true,
      image: {
        id: 1,
        url: "https://static.dash.partyrentalplatform.com/cdn-cgi/imagedelivery/6QiASA1pHsoYecw9egSmhw/test/format=auto",
        name: "ImageName.png",
      },
    });
  });
});
