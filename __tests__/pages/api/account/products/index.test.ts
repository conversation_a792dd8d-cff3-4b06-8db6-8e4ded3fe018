import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";
import handler from "~/pages/api/products/index";

describe("GET /api/account/products", () => {
  it("should return 200 with no products", async () => {
    db.product.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(db.product.findMany).toHaveBeenCalledTimes(1);
    expect(db.product.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        archived: false,
        NOT: {
          id: {
            in: [],
          },
          quantity: 0,
        },
        OR: undefined,
      },

      select: {
        id: true,
        accountId: true,
        name: true,
        price: true,
        quantity: true,
        display: true,
        afterRentalBufferMinutes: true,
        beforeRentalBufferMinutes: true,
        ProductImageUpload: {
          select: {
            priority: true,
            imageUpload: {
              select: {
                url: true,
              },
            },
          },
        },
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [],
    });
  });
  it("should return the products for the site", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(db.product.findMany).toHaveBeenCalledTimes(1);
    expect(db.product.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        archived: false,
        NOT: {
          id: {
            in: [],
          },
          quantity: 0,
        },
        OR: undefined,
      },

      select: {
        id: true,
        accountId: true,
        name: true,
        price: true,
        quantity: true,
        display: true,
        afterRentalBufferMinutes: true,
        beforeRentalBufferMinutes: true,
        ProductImageUpload: {
          select: {
            priority: true,
            imageUpload: {
              select: {
                url: true,
              },
            },
          },
        },
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [
        getResponseForProduct(Product1, 10),
        getResponseForProduct(Product2, 0),
        getResponseForProduct(Product3, 5),
      ],
    });
  });

  it("should return with the correct availability", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:00:00Z"),
        endTime: new Date("2022-01-01T14:00:00Z"),
        OrderProduct: [
          {
            product: {
              id: 1,
              quantity: 3,
              beforeRentalBufferMinutes: 0,
              afterRentalBufferMinutes: 0,
            },
            productId: 1,
            quantity: 3,
          },
        ],
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        startTime: "2022-01-01T04:00:00Z",
        endTime: "2022-01-02T00:00:00Z",
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [
        getResponseForProduct(Product1, 7),
        getResponseForProduct(Product2, 0),
        getResponseForProduct(Product3, 5),
      ],
    });
  });

  it("should handle products with limits", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        limit: "5",
      },
    });

    expect(db.product.findMany).toHaveBeenCalledTimes(1);
    expect(db.product.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        archived: false,
        NOT: {
          id: {
            in: [],
          },
          quantity: 0,
        },
        OR: undefined,
      },

      select: {
        id: true,
        accountId: true,
        name: true,
        price: true,
        quantity: true,
        display: true,
        afterRentalBufferMinutes: true,
        beforeRentalBufferMinutes: true,
        ProductImageUpload: {
          select: {
            priority: true,
            imageUpload: {
              select: {
                url: true,
              },
            },
          },
        },
      },
      take: 5,
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [
        getResponseForProduct(Product1, 10),
        getResponseForProduct(Product2, 0),
        getResponseForProduct(Product3, 5),
      ],
    });
  });

  it("should handle exclusions", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        exclude: "2,3,4,a,5",
      },
    });

    expect(db.product.findMany).toHaveBeenCalledTimes(1);
    expect(db.product.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        archived: false,
        NOT: {
          id: {
            in: [2, 3, 4, 5],
          },
          quantity: 0,
        },
        OR: undefined,
      },

      select: {
        id: true,
        accountId: true,
        name: true,
        price: true,
        quantity: true,
        display: true,
        afterRentalBufferMinutes: true,
        beforeRentalBufferMinutes: true,
        ProductImageUpload: {
          select: {
            priority: true,
            imageUpload: {
              select: {
                url: true,
              },
            },
          },
        },
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [
        getResponseForProduct(Product1, 10),
        getResponseForProduct(Product2, 0),
        getResponseForProduct(Product3, 5),
      ],
    });
  });

  it("should handle product searching", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        search: "Test",
      },
    });

    expect(db.product.findMany).toHaveBeenCalledTimes(1);
    expect(db.product.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        archived: false,
        NOT: {
          id: {
            in: [],
          },
          quantity: 0,
        },
        OR: [
          {
            name: {
              contains: "Test",
            },
          },
          {
            description: {
              contains: "Test",
            },
          },
        ],
      },

      select: {
        id: true,
        accountId: true,
        name: true,
        price: true,
        quantity: true,
        display: true,
        afterRentalBufferMinutes: true,
        beforeRentalBufferMinutes: true,
        ProductImageUpload: {
          select: {
            priority: true,
            imageUpload: {
              select: {
                url: true,
              },
            },
          },
        },
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      products: [
        getResponseForProduct(Product1, 10),
        getResponseForProduct(Product2, 0),
        getResponseForProduct(Product3, 5),
      ],
    });
  });
});

const Product1 = {
  id: 1,
  name: "Test Product",
  price: 100,
  quantity: 10,
  display: true,
  afterRentalBufferMinutes: 0,
  beforeRentalBufferMinutes: 0,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image1.jpg",
      },
    },
  ],
};

const Product2 = {
  id: 2,
  name: "Test Product 2",
  price: 200,
  quantity: 0,
  display: false,
  afterRentalBufferMinutes: 0,
  beforeRentalBufferMinutes: 0,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image2.jpg",
      },
    },
  ],
};

const Product3 = {
  id: 3,
  name: "Test Product 3",
  price: 300,
  quantity: 5,
  display: true,
  afterRentalBufferMinutes: 0,
  beforeRentalBufferMinutes: 0,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image3.jpg",
      },
    },
  ],
};

const mockProducts = [Product1, Product2, Product3];
const getResponseForProduct = (product: any, available: number) => ({
  id: product.id,
  name: product.name,
  price: product.price,
  quantity: product.quantity,
  display: product.display,
  available: available,
  productThumbnail: product.ProductImageUpload[0].imageUpload.url,
});
