import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApiHandler } from "../../../../apiTestUtil";
import handler from "~/pages/api/account/reports/salesTaxReport";
import { db } from "~/server/db";

describe("/api/account/stats/salesTaxReport", () => {
  it("should return 200 with no orders", async () => {
    createMockAccount({});

    const countCall = db.order.findMany.mockImplementation(() => {
      return Promise.resolve([]);
    });

    const inputStartTime = new Date(
      new Date(new Date().toLocaleDateString()).getTime() -
        1000 * 60 * 60 * 24 * 7,
    );

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        startTime: inputStartTime,
        endTime: new Date(),
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      startDate: expect.stringContaining(".000Z"),
      salesTaxLineItems: [],
    });
    expect(countCall).toHaveBeenCalled();
  });
  it("should return 200", async () => {
    createMockAccount({});

    const countCall = db.order.findMany.mockImplementation(() => {
      return Promise.resolve([
        {
          accountId: 1,
          state: "COMPLETED",
          endTime: new Date(),
          taxExempt: false,
          taxAmount: 20,
          finalTotal: 200,
          eventAddress: {
            city: "City1",
          },
        },
        {
          accountId: 1,
          state: "COMPLETED",
          endTime: new Date(),
          taxExempt: false,
          taxAmount: 10,
          finalTotal: 100,
          eventAddress: {
            city: "City2",
          },
        },
        {
          accountId: 1,
          state: "COMPLETED",
          endTime: new Date(),
          taxExempt: false,
          taxAmount: 10,
          finalTotal: 100,
          eventAddress: {
            city: "City1",
          },
        },
      ]);
    });

    const inputStartTime = new Date(
      new Date(new Date().toLocaleDateString()).getTime() -
        1000 * 60 * 60 * 24 * 7,
    );
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
      query: {
        startTime: inputStartTime,
        endTime: new Date(),
      },
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      startDate: expect.stringContaining(".000Z"),
      salesTaxLineItems: [
        {
          name: "City1",
          taxableSales: 270,
          taxOwed: 30,
        },
        {
          name: "City2",
          taxableSales: 90,
          taxOwed: 10,
        },
      ],
    });
    expect(countCall).toHaveBeenCalled();
  });
});
