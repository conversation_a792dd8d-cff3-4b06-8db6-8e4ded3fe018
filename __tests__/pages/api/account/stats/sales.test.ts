import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApiHandler } from "../../../../apiTestUtil";
import { db } from "~/server/db";
import handler from "~/pages/api/account/stats/sales";

describe("/api/account/stats/sales", () => {
  it("should return 200 with no previous stats", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      payments: [],
      totalAmount: 0,
    });
  });

  it("should query the database for sales data", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([]);

    await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(db.order.findMany).toHaveBeenCalledTimes(1);
    expect(db.order.findMany).toHaveBeenCalledWith({
      where: {
        accountId: 1,
        startTime: {
          gt: expect.any(Date),
          lt: expect.any(Date),
        },
        OR: [
          { state: "ACTIVE" },
          { state: "COMPLETED" },
          { totalPaid: { gt: 0 } },
        ],
      },
    });
  });

  it("should be able to get sales data", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        totalPaid: 100,
        finalTotal: 100,
        startTime: new Date("01/05/2021"),
      },
      {
        id: 2,
        totalPaid: 200,
        finalTotal: 200,
        startTime: new Date("01/05/2021"),
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      payments: [
        {
          name: "1/2021",
          totalReceived: 300,
          totalCommitted: 0,
          total: 300,
        },
      ],
      totalAmount: 300,
    });
  });

  it("should be able to combine months with the same key", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        totalPaid: 100,
        finalTotal: 100,
        startTime: new Date("01/05/2021"),
      },
      {
        id: 2,
        totalPaid: 200,
        finalTotal: 200,
        startTime: new Date("01/05/2021"),
      },
      {
        id: 3,
        totalPaid: 300,
        finalTotal: 300,
        startTime: new Date("02/05/2021"),
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      payments: [
        {
          name: "1/2021",
          totalReceived: 300,
          totalCommitted: 0,
          total: 300,
        },
        {
          name: "2/2021",
          totalReceived: 300,
          totalCommitted: 0,
          total: 300,
        },
      ],
      totalAmount: 600,
    });
  });

  it("should be able to handle last month of the year with sorting", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([
      {
        id: 0,
        totalPaid: 100,
        finalTotal: 100,
        startTime: new Date("11/30/2021"),
      },
      {
        id: 1,
        totalPaid: 100,
        finalTotal: 100,
        startTime: new Date("12/05/2021"),
      },
      {
        id: 4,
        totalPaid: 100,
        finalTotal: 100,
        startTime: new Date("01/02/2021"),
      },
      {
        id: 2,
        totalPaid: 200,
        finalTotal: 200,
        startTime: new Date("12/05/2021"),
      },
      {
        id: 3,
        totalPaid: 300,
        finalTotal: 300,
        startTime: new Date("01/05/2022"),
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      payments: [
        {
          name: "1/2021",
          totalReceived: 100,
          totalCommitted: 0,
          total: 100,
        },
        {
          name: "11/2021",
          totalReceived: 100,
          totalCommitted: 0,
          total: 100,
        },
        {
          name: "12/2021",
          totalReceived: 300,
          totalCommitted: 0,
          total: 300,
        },
        {
          name: "1/2022",
          totalReceived: 300,
          totalCommitted: 0,
          total: 300,
        },
      ],
      totalAmount: 800,
    });
  });

  it("should format finalTotal correctly", async () => {
    createMockAccount({});

    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        totalPaid: 100.12331,
        finalTotal: 200.*********,
        startTime: new Date("01/05/2021"),
      },
      {
        id: 2,
        totalPaid: 200.33334,
        finalTotal: 300.33334,
        startTime: new Date("01/05/2021"),
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      payments: [
        {
          name: "1/2021",
          totalReceived: 300.46,
          totalCommitted: 200.55,
          total: 501.01,
        },
      ],
      totalAmount: 300.46,
    });
  });
});
