import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApi<PERSON>and<PERSON> } from "../../../../apiTestUtil";
import handler from "~/pages/api/account/stats/orders";
import { db } from "~/server/db";

describe("/api/account/stats/orders", () => {
  it("should return 200 with no previous stats", async () => {
    createMockAccount({});

    const countCall = db.order.count.mockImplementation((args) => {
      return 10;
    });

    const transactionCall = db.$transaction.mockImplementation((args) => {
      return [args[0], null];
    });

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(transactionCall).toHaveBeenCalledTimes(1);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      current: 10,
      previous: 0,
    });
    expect(countCall).toHaveBeenCalledTimes(2);
  });
  it("should return 200 with stats for past week", async () => {
    createMockAccount({});

    const countCall = db.order.count.mockImplementation((args) => {
      if (args.where.state === "ACTIVE") {
        return 10;
      } else {
        return 5;
      }
    });

    const transactionCall = db.$transaction.mockImplementation((args) => {
      return [args[0], args[1]];
    });

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(transactionCall).toHaveBeenCalledTimes(1);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      current: 10,
      previous: 5,
    });
    expect(countCall).toHaveBeenCalledTimes(2);
  });
});
