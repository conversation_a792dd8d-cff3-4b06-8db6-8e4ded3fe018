import { describe, expect, it } from "@jest/globals";
import { createMockAccount, testApiHandler } from "../../../apiTestUtil";
import handler from "~/pages/api/account/upgrade";
import * as stripeLib from "~/server/lib/stripe";

describe("/api/account/upgrade", () => {
  it("should return 405 for non-GET requests", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(405);
  });
  it("should return 401 if account is not found", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(res.statusCode).toBe(401);
  });
  it("should return 200 with stripe link", async () => {
    createMockAccount({
      stripeCustomerId: "customer_id",
    });

    const findCustomerMock =
      stripeLib.findOrCreateStripeCustomerPlatform.mockImplementation(() => {
        return {
          id: "customer_id",
        };
      });

    const billingPortalMock =
      stripeLib.platformStripeServer.billingPortal.sessions.create.mockImplementation(
        () => {
          return {
            url: "https://example.com",
          };
        },
      );

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.account.read"],
    });

    expect(findCustomerMock).toHaveBeenCalled();
    expect(billingPortalMock).toHaveBeenCalled();

    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({
      url: "https://example.com",
    });
  });
});
