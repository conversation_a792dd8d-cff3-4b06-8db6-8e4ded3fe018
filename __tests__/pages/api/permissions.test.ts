import { describe, expect, it } from "@jest/globals";
import { stringPermissionCheck } from "~/pages/api/permissionsUtil";

describe("stringPermissionCheck", () => {
  it("should return true if the permission is found", () => {
    // Arrange
    const totalPermissions = ["com.partyrentalplatform.orders.read"];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "read";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(true);
  });

  it("should return false if the permission is not found", () => {
    // Arrange
    const totalPermissions = ["com.partyrentalplatform.orders.read"];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "write";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(false);
  });

  it("should return true if the user has all permissions", () => {
    // Arrange
    const totalPermissions = ["*"];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "write";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(true);
  });

  it("should return false if the user has no permissions", () => {
    // Arrange
    const totalPermissions: string[] = [];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "write";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(false);
  });

  it("should return false if the user has the permission with a different action", () => {
    // Arrange
    const totalPermissions = ["com.partyrentalplatform.orders.read"];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "write";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(false);
  });

  it("should return true if the user has all actions", () => {
    // Arrange
    const totalPermissions = [
      "com.partyrentalplatform.orders.*",
      "com.partyrentalplatform.orders.read",
    ];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "execute";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(true);
  });

  it("should return false if the user has a wildcard middle permission", () => {
    // Arrange
    const totalPermissions = ["com.partyrentalplatform.*.read"];
    const prefix = "com.partyrentalplatform";
    const policy = "orders";
    const action = "read";
    // Act
    const result = stringPermissionCheck(
      totalPermissions,
      `${prefix}.${policy}`,
      action,
    );
    // Assert
    expect(result).toBe(false);
  });
});
