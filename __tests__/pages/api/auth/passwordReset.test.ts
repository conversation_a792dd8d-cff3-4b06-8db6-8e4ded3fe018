import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/auth/passwordReset";
import {
  comparePlainTextPassword,
  handlePlainTextPassword,
} from "~/server/lib/password";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("Password Reset API Endpoint", () => {
  const staffId = "user123";
  const userId = "user123";
  const accountId = 1;
  const password = "newPassword123";
  const token = "resetToken";
  const csrfToken = "csrfToken";

  const mockUser = {
    id: staffId,
    userId: userId,
    accountId: accountId,
    password: "oldPassword",
  };

  const mockToken = {
    staffId: staffId,
    token: "hashedToken",
    expires: new Date(Date.now() + 10000), // valid for 10 seconds
  };

  it("should reset password for valid request", async () => {
    db.staff.findUnique.mockResolvedValue(mockUser);
    db.staffPasswordReset.findMany.mockResolvedValue([mockToken]);
    comparePlainTextPassword.mockImplementation(() => true);
    handlePlainTextPassword.mockResolvedValue("hashedPassword");

    const res = await testApiHandler(handler, {
      method: "POST",
      body: { staffId, accountId, password, token, csrfToken },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
    expect(db.staff.update).toHaveBeenCalledWith({
      where: { id: staffId },
      data: { password: "hashedPassword" },
    });
    expect(db.user.update).toHaveBeenCalledWith({
      where: { id: userId },
      data: { password: "hashedPassword" },
    });
    expect(db.staffPasswordReset.deleteMany).toHaveBeenCalledWith({
      where: { staffId: staffId },
    });
  });

  it("should return 400 for invalid user", async () => {
    db.staff.findUnique.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      body: { staffId, accountId, password, token, csrfToken },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid user" });
  });

  it("should return 400 for invalid token", async () => {
    db.staff.findUnique.mockResolvedValue(mockUser);
    db.staffPasswordReset.findMany.mockResolvedValue([mockToken]);
    comparePlainTextPassword.mockImplementation(() => false);

    const res = await testApiHandler(handler, {
      method: "POST",
      body: { staffId, accountId, password, token, csrfToken },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid token" });
  });

  it("should return 400 for expired token", async () => {
    db.staff.findUnique.mockResolvedValue(mockUser);
    db.staffPasswordReset.findMany.mockResolvedValue([
      { ...mockToken, expires: new Date(Date.now() - 10000) }, // expired token
    ]);
    comparePlainTextPassword.mockImplementation(() => true);

    const res = await testApiHandler(handler, {
      method: "POST",
      body: { staffId, accountId, password, token, csrfToken },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Token expired" });
  });

  it("should return 422 for missing required fields", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      body: {},
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Request",
      message: "5 errors occurred",
    });
  });
});
