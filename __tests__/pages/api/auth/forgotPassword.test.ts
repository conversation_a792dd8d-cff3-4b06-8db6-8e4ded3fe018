import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/auth/forgotPassword";
import { sendResetPasswordEmail } from "~/pages/api/staff/[id]/resetPassword";
import { captureMessage } from "@sentry/nextjs";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("Forgot Password API Endpoint", () => {
  const email = "<EMAIL>";
  const mockUser = {
    id: 1,
    email,
    account: { id: 1 },
  };
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it("should send a reset password email for valid request", async () => {
    db.staff.findMany.mockResolvedValue([mockUser]);

    const res = await testApiHandler(handler, {
      method: "POST",
      body: { email },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
    expect(sendResetPasswordEmail).toHaveBeenCalledWith({
      staff: mockUser,
      newAccount: false,
      permissive: true,
    });
  });

  it("should return 400 for missing email field", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Request",
      message: "email is a required field",
    });
  });

  it("should return 400 for multiple users with the same email", async () => {
    db.staff.findMany.mockResolvedValue([mockUser, mockUser]);
    const res = await testApiHandler(handler, {
      method: "POST",
      body: { email },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid user" });
    expect(captureMessage).toHaveBeenCalledWith(
      "Multiple users with the same email found.",
    );
  });

  it("should return 400 for user not found", async () => {
    db.staff.findMany.mockResolvedValue([]);
    const res = await testApiHandler(handler, {
      method: "POST",
      body: { email },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "We could not find a staff member with that email.",
    });
  });
});
