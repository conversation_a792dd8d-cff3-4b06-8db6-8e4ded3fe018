import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/surface/[id]/delete";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";

describe("surface/[id]/delete.ts API Endpoint", () => {
  const accountId = 1;
  const surfaceId = "1";
  const surfaceData = {
    id: 1,
    name: "Test Surface",
    description: "This is a test surface.",
    accountId,
    archived: false,
    Order: [],
  };

  it("should return 400 if surface ID is invalid", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: "invalid" },
      body: {},
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid surface id" });
  });

  it("should return 404 if surface is not found", async () => {
    db.setupSurface.findUnique.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Surface not found" });
  });

  it("should return 200 and archive surface if it has orders", async () => {
    db.setupSurface.findUnique.mockResolvedValue({
      ...surfaceData,
      Order: [{ id: 1 }],
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.write"],
    });

    expect(db.setupSurface.findUnique).toHaveBeenCalledWith({
      where: {
        accountId,
        id: 1,
        archived: false,
      },
      include: expect.any(Object),
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
    expect(db.setupSurface.update).toHaveBeenCalledWith({
      where: { id: parseInt(surfaceId) },
      data: { archived: true },
    });
  });

  it("should return 200 and delete surface if it has no orders", async () => {
    db.setupSurface.findUnique.mockResolvedValue(surfaceData);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.write"],
    });

    expect(db.setupSurface.findUnique).toHaveBeenCalledWith({
      where: {
        accountId,
        id: 1,
        archived: false,
      },
      include: expect.any(Object),
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
    expect(db.setupSurface.delete).toHaveBeenCalledWith({
      where: { accountId, id: parseInt(surfaceId) },
    });
  });
});
