import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/surface/[id]/index";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";

describe("surface/[id]/index.ts API Endpoint", () => {
  const surfaceId = "1";
  const surfaceData = {
    id: 1,
    name: "Test Surface",
    description: "This is a test surface.",
    accountId: 1,
    archived: false,
  };

  it("should return 400 if surface ID is invalid", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: "invalid" },
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid surface id" });
  });

  it("should return 404 if surface is not found", async () => {
    db.setupSurface.findUnique.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Surface not found" });
  });

  it("should return 200 with surface data", async () => {
    db.setupSurface.findUnique.mockResolvedValue(surfaceData);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(db.setupSurface.findUnique).toHaveBeenCalledWith({
      where: {
        accountId: 1,
        id: 1,
        archived: false,
      },
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      setupSurface: {
        name: surfaceData.name,
        description: surfaceData.description,
      },
    });
  });
});
