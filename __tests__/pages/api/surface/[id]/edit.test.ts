import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/surface/[id]/edit";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";

describe("surface/[id]/edit.ts API Endpoint", () => {
  const accountId = 1;
  const surfaceId = "1";
  const surfaceData = {
    id: 1,
    name: "Test Surface",
    description: "This is a test surface.",
    feeAmount: null,
    scaleFee: false,
    accountId,
  };
  const updatedSurfaceData = {
    name: "Updated Surface",
    description: "This is an updated test surface.",
    feeAmount: null,
    scaleFee: false,
  };

  it("should return 405 if method is not POST", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: surfaceId },
      body: {},
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 400 if surface ID is invalid", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: "invalid" },
      body: updatedSurfaceData,
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Invalid surface id" });
  });

  it("should return 404 if surface is not found", async () => {
    db.setupSurface.findUnique.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: updatedSurfaceData,
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Surface not found" });
  });

  it("should return 422 if request body is invalid", async () => {
    db.setupSurface.findUnique.mockResolvedValue(surfaceData);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: { invalidField: "invalidValue" },
      permissionNodes: ["api.surface.write"],
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 and update surface successfully", async () => {
    db.setupSurface.findUnique.mockResolvedValue(surfaceData);
    db.setupSurface.update.mockResolvedValue({
      id: surfaceId,
      ...updatedSurfaceData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: surfaceId },
      body: updatedSurfaceData,
      permissionNodes: ["api.surface.write"],
    });

    expect(db.setupSurface.findUnique).toHaveBeenCalledWith({
      where: {
        accountId,
        id: 1,
        archived: false,
      },
    });
    const dbConstructSurfaceData = {
      name: "Updated Surface",
      description: "This is an updated test surface.",
      feeAmount: undefined,
      scaleFeeWithQuantity: false,
    };
    expect(db.setupSurface.update).toHaveBeenCalledWith({
      where: { id: surfaceData.id },
      data: dbConstructSurfaceData,
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
    });
  });
});
