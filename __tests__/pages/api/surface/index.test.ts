import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/surface/index";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("surface/index.ts API Endpoint", () => {
  const surfaces = [
    {
      id: 1,
      name: "Surface 1",
    },
    {
      id: 2,
      name: "Surface 2",
    },
  ];

  it("should return 200 with an empty list if no surfaces are found", async () => {
    db.setupSurface.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ setupSurface: [] });
  });

  it("should return 200 with a list of surfaces", async () => {
    db.setupSurface.findMany.mockResolvedValue(surfaces);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(db.setupSurface.findMany).toHaveBeenCalledWith({
      where: {
        accountId: expect.any(Number),
        archived: false,
      },
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      setupSurface: surfaces.map((surface) => ({
        id: surface.id,
        name: surface.name,
      })),
    });
  });

  it("should return 200 with a list of surfaces including archived if query param is set", async () => {
    db.setupSurface.findMany.mockResolvedValue(surfaces);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { includeArchived: "true" },
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(db.setupSurface.findMany).toHaveBeenCalledWith({
      where: {
        accountId: expect.any(Number),
        archived: undefined,
      },
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      setupSurface: surfaces.map((surface) => ({
        id: surface.id,
        name: surface.name,
      })),
    });
  });
});
