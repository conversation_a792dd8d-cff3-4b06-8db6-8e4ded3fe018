import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/surface/create";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("surface/create.ts API Endpoint", () => {
  const accountId = 1;
  const surfaceData = {
    name: "Test Surface",
    description: "This is a test surface.",
    feeAmount: 100,
    scaleFee: false,
  };

  it("should return 405 if method is not POST", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 422 if request body is invalid", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: { invalidField: "invalidValue" },
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 and create surface successfully", async () => {
    db.setupSurface.create.mockResolvedValue({
      id: "surface1",
      ...surfaceData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: surfaceData,
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
    });
  });

  it("should return 200 with no fees", async () => {
    const surfaceData = {
      name: "Test Surface",
      description: "This is a test surface.",
      feeAmount: null,
      scaleFee: false,
    };
    db.setupSurface.create.mockResolvedValue({
      id: "surface1",
      ...surfaceData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: surfaceData,
      permissionNodes: ["api.surface.read"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
    });
  });
});
