import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import handler from "~/pages/api/public/[accountName]/cart/link";
import { db } from "~/server/db";
import { findStripeCustomer, getStripeAccount } from "~/server/lib/stripe";
import { testApiHandler } from "../../../../../apiTestUtil";

jest.mock("~/server/lib/stripe", () => ({
  getStripeAccount: jest.fn(),
  findStripeCustomer: jest.fn(),
}));

describe("link.ts API Endpoint", () => {
  const accountName = "existingAccount";
  const shoppingCartId = "shoppingCartId";
  const email = "<EMAIL>";
  const customerEmail = email.toLowerCase();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 400 if accountName is missing", async () => {
    const res = await testApi<PERSON>andler(handler, {
      method: "POST",
      query: {},
      body: { shoppingCartId, email },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Account name is required",
    });
  });

  it("should return 404 if accountName is not a string", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName: ["not", "a", "string"] },
      body: { shoppingCartId, email },
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Account not found",
    });
  });

  it("should return 404 if account is not found", async () => {
    db.account.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account not found" });
  });

  it("should return 404 if shopping cart is not found", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Shopping cart not found",
    });
  });

  it("should return 422 if request body is invalid", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { invalidField: "invalidValue" },
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 if customer email matches shopping cart customer email", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: { email: customerEmail },
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and create customer if not found", async () => {
    const stripeMock = {
      customers: {
        create: jest.fn(),
      },
      paymentIntents: {
        update: jest.fn(),
      },
    };

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: null,
    });
    db.customer.findFirst.mockResolvedValue(null);
    db.customer.create.mockResolvedValue({ id: "customerId" });
    getStripeAccount.mockReturnValue(stripeMock);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(db.customer.create).toHaveBeenCalledWith({
      data: {
        account: { connect: { id: 1 } },
        email: customerEmail,
      },
    });

    expect(stripeMock.customers.create).toHaveBeenCalledWith({
      email: customerEmail,
      metadata: {
        customerId: "customerId",
        shoppingSessionId: shoppingCartId,
      },
    });

    expect(db.shoppingSession.update).toHaveBeenCalledWith({
      where: { id: shoppingCartId },
      data: { customer: { connect: { id: "customerId" } } },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and update customer when shoppingCart has a different customer", async () => {
    const stripeMock = {
      customers: {
        create: jest.fn(),
      },
      paymentIntents: {
        update: jest.fn(),
      },
    };

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: {
        id: "otherCustomer",
        email: "<EMAIL>",
      },
    });
    db.customer.findFirst.mockResolvedValue(null);
    db.customer.create.mockResolvedValue({ id: "customerId" });
    getStripeAccount.mockReturnValue(stripeMock);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(db.customer.create).toHaveBeenCalledWith({
      data: {
        account: { connect: { id: 1 } },
        email: customerEmail,
      },
    });

    expect(stripeMock.customers.create).toHaveBeenCalledWith({
      email: customerEmail,
      metadata: {
        customerId: "customerId",
        shoppingSessionId: shoppingCartId,
      },
    });
    expect(db.shoppingSession.update).toHaveBeenCalledWith({
      where: { id: shoppingCartId },
      data: { customer: { connect: { id: "customerId" } } },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and update customer when shoppingCart has a different customer", async () => {
    const stripeMock = {
      customers: {
        create: jest.fn(),
      },
      paymentIntents: {
        update: jest.fn(),
      },
    };

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: {
        id: "otherCustomer",
        email: "<EMAIL>",
      },
    });
    db.customer.findFirst.mockResolvedValue({
      id: "customerId",
      email: "<EMAIL>",
    });
    db.customer.create.mockResolvedValue({ id: "customerId" });
    getStripeAccount.mockReturnValue(stripeMock);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(db.customer.create).not.toHaveBeenCalled();

    expect(stripeMock.customers.create).not.toHaveBeenCalled();

    expect(db.shoppingSession.update).toHaveBeenCalledWith({
      where: { id: shoppingCartId },
      data: { customer: { connect: { id: "customerId" } } },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and update customer when shoppingCart has a different customer and paymentIntent without a stripe customer", async () => {
    const stripeMock = {
      customers: {
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
      paymentIntents: {
        update: jest.fn(),
      },
    };

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: {
        id: "otherCustomer",
        email: "<EMAIL>",
      },
      paymentIntent: "paymentIntent",
    });
    db.customer.findFirst.mockResolvedValue({
      id: "customerId",
      email: "<EMAIL>",
    });
    db.customer.create.mockResolvedValue({ id: "customerId" });
    getStripeAccount.mockReturnValue(stripeMock);
    findStripeCustomer.mockReturnValue(undefined);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(db.customer.create).not.toHaveBeenCalled();

    expect(stripeMock.customers.create).toHaveBeenCalledWith({
      email: customerEmail,
      metadata: {
        customerId: "customerId",
        shoppingSessionId: shoppingCartId,
      },
    });
    expect(stripeMock.paymentIntents.update).toHaveBeenCalledWith(
      "paymentIntent",
      {
        customer: "stripeCustomerId",
      },
    );
    expect(db.shoppingSession.update).toHaveBeenCalledWith({
      where: { id: shoppingCartId },
      data: { customer: { connect: { id: "customerId" } } },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and update customer when shoppingCart has a different customer and paymentIntent with a stripe customer", async () => {
    const stripeMock = {
      customers: {
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
      paymentIntents: {
        update: jest.fn(),
      },
    };

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingCartId,
      customer: {
        id: "otherCustomer",
        email: "<EMAIL>",
      },
      paymentIntent: "paymentIntent",
    });
    db.customer.findFirst.mockResolvedValue({
      id: "customerId",
      email: "<EMAIL>",
    });
    db.customer.create.mockResolvedValue({ id: "customerId" });
    getStripeAccount.mockReturnValue(stripeMock);
    findStripeCustomer.mockReturnValue({ id: "stripeCustomerId2" });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId, email },
    });

    expect(db.customer.create).not.toHaveBeenCalled();

    expect(stripeMock.customers.create).not.toHaveBeenCalled();

    expect(stripeMock.paymentIntents.update).toHaveBeenCalledWith(
      "paymentIntent",
      {
        customer: "stripeCustomerId2",
      },
    );
    expect(db.shoppingSession.update).toHaveBeenCalledWith({
      where: { id: shoppingCartId },
      data: { customer: { connect: { id: "customerId" } } },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });
});
