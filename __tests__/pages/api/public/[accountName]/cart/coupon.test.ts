import "@testing-library/jest-dom";

import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import handler from "~/pages/api/public/[accountName]/cart/coupon";
import { db } from "~/server/db";
import { testApi<PERSON>andler } from "../../../../../apiTestUtil";

describe("coupon.test.ts", () => {
  const accountName = "existingAccount";
  const shoppingId = "shoppingId";
  const couponCode = "DISCOUNT10";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 400 if accountName is missing", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: {
        shoppingCartId: shoppingId,
        couponCode,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Account name is required",
    });
  });

  it("should return 400 if accountName is not a string", async () => {
    const res = await testApi<PERSON>and<PERSON>(handler, {
      method: "POST",
      query: { accountName: ["not", "a", "string"] },
      body: {
        shoppingCartId: shoppingId,
        couponCode,
      },
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account not found" });
  });

  it("should return 400 if account is not found", async () => {
    db.account.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingCartId: shoppingId,
        couponCode,
      },
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account not found" });
  });

  it("should return 422 if request body is invalid", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { invalidField: "invalidValue" },
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 400 if coupon requested is invalid", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingCartId: shoppingId, couponCode: "Bad" },
    });

    expect(db.coupon.findFirst).toBeCalledWith({
      where: {
        accountId: 1,
        name: "Bad",
        deleted: false,
        AND: [
          {
            OR: [
              { startDate: null },
              { startDate: { lte: expect.anything() } },
            ],
          },
          {
            OR: [{ endDate: null }, { endDate: { gte: expect.anything() } }],
          },
        ],
      },
    });
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "No Coupon Code Found" }),
    );
  });

  it("should return 200 and return coupon info", async () => {
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.coupon.findFirst.mockResolvedValue({
      accountId: 1,
      name: couponCode,
      discount: 10,
      discountType: "percentage",
      deleted: false,
      startDate: new Date("2024-01-01"),
      endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingCartId: shoppingId,
        couponCode,
      },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
      discountAmount: 10,
      discountType: "percentage",
    });
  });
});
