import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/public/[accountName]/cart/update";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../../apiTestUtil";

describe("update.ts API Endpoint", () => {
  const accountName = "existingAccount";
  const shoppingId = "shoppingId";
  const cart = [
    { productId: 1, amount: 2 },
    { productId: 2, amount: 3 },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return invalid method for get request", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: { accountName },
      body: { shoppingId, cart },
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 400 if accountName is missing", async () => {
    const res = await testApi<PERSON><PERSON><PERSON>(handler, {
      method: "POST",
      query: {},
      body: { shoppingId, cart },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if accountName is not a string", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName: ["not", "a", "string"] },
      body: { shoppingId, cart },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if account is not found", async () => {
    db.account.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName: "nonexistent" },
      body: { shoppingId, cart },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if shopping session is not found", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingId, cart },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Shopping Session",
    });
  });

  it("should return 422 if request body is invalid", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { invalidField: "invalidValue" },
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 and update the cart successfully", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      account: { id: 1 },
    });
    db.$transaction.mockResolvedValue({});
    db.product.findMany.mockResolvedValue([
      { id: 1, name: "Product 1" },
      { id: 2, name: "Product 2" },
    ]);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingId, cart },
    });

    expect(db.cartItem.deleteMany).toBeCalledWith({
      where: { shoppingSessionId: shoppingId },
    });

    expect(db.cartItem.createMany).toBeCalledWith({
      data: cart.map((cartItem) => ({
        productId: cartItem.productId,
        quantity: cartItem.amount,
        shoppingSessionId: shoppingId,
      })),
    });

    expect(db.$transaction).toBeCalledTimes(1);

    expect(db.shoppingSession.update).not.toBeCalled();

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it("should return 200 and update shopping session for updated dates", async () => {
    const startDateTime = new Date();
    const endDateTime = new Date();

    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      account: { id: 1 },
    });
    db.$transaction.mockResolvedValue({});

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { shoppingId, cart: [], startDateTime, endDateTime },
    });

    expect(db.cartItem.deleteMany).toBeCalled();
    expect(db.cartItem.createMany).not.toBeCalled();

    expect(db.shoppingSession.update).toBeCalledWith({
      where: { id: shoppingId, account: { id: 1 } },
      data: { eventStartTime: startDateTime, eventEndTime: endDateTime },
    });

    expect(db.$transaction).toBeCalledTimes(1);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });
});
