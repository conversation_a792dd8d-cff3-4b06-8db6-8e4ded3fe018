import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/public/[accountName]/cart/start";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../../apiTestUtil";

describe("start.ts API Endpoint", () => {
  const accountName = "existingAccount";
  const shoppingId = "shoppingId";

  it("should return 400 if accountName is missing", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if accountName is not a string", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: { accountName: ["not", "a", "string"] },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if account is not found", async () => {
    db.account.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { accountName: "nonexistent" },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 200 and create a shopping session if account is found", async () => {
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { accountName },
    });

    expect(db.shoppingSession.create).toBeCalled();

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      shoppingId: expect.any(String),
    });
  });
});
