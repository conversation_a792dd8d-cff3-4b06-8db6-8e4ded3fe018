import "@testing-library/jest-dom";

import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import handler from "~/pages/api/public/[accountName]/cart/finalize";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../../apiTestUtil";
import { getStripeAccount } from "~/server/lib/stripe";

jest.mock("~/server/lib/stripe", () => ({
  getStripeAccount: jest.fn(),
}));

describe("finalize.ts API Endpoint", () => {
  const accountName = "existingAccount";
  const shoppingId = "shoppingId";
  const customerData = {
    firstName: "John",
    lastName: "Doe",
    phoneNumber: "**********",
    reference: "ref123",
    customerNotes: "Please deliver between 9-10 AM",
  };
  const setupSurface = "Concrete";
  const damageWaiverApplied = true;
  const couponCode = "DISCOUNT10";
  const location = {
    address: "123 Main St",
    city: "Anytown",
    state: "CA",
    zip: "12345",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 400 if accountName is missing", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        couponCode,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if accountName is not a string", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName: ["not", "a", "string"] },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        couponCode,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if account is not found", async () => {
    db.account.findUnique.mockResolvedValue(null);
    db.account.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        couponCode,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Account Unknown" });
  });

  it("should return 400 if shopping session is not found", async () => {
    db.account.findUnique.mockResolvedValue({ id: 1, name: accountName });
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        couponCode,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Shopping Session",
    });
  });

  it("should return 422 if request body is invalid", async () => {
    db.account.findUnique.mockResolvedValue({ id: 1, name: accountName });
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: { invalidField: "invalidValue" },
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 400 if cart is empty", async () => {
    db.account.findUnique.mockResolvedValue({ id: 1, name: accountName });
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [],
      eventStartTime: null,
      eventEndTime: null,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: "Cart is empty" });
  });

  it("should return 400 if customer email is missing", async () => {
    db.account.findUnique.mockResolvedValue({ id: 1, name: accountName });
    db.account.findFirst.mockResolvedValue({ id: 1, name: accountName });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [{ productId: 1, quantity: 2 }],
      eventStartTime: new Date(),
      eventEndTime: new Date(),
      customer: { email: null },
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Customer, must link before finalizing.",
    });
  });

  it("should return 400 if customer is banned", async () => {
    db.account.findUnique.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
    });
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
    });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [{ productId: 1, quantity: 2 }],
      eventStartTime: new Date(),
      eventEndTime: new Date(),
      customer: { email: "<EMAIL>", banned: true },
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Some products are not available",
      taken: [1],
    });
  });

  it("should return 400 if entered coupon isn't valid", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:00:00Z"),
        endTime: new Date("2022-01-01T14:00:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 3 }, productId: 1, quantity: 3 },
        ],
      },
    ]);
    const stripeMock = {
      paymentIntents: {
        create: jest.fn().mockReturnValue({
          id: "paymentIntentId",
          client_secret: "clientSecret",
        }),
        retrieve: jest
          .fn()
          .mockReturnValue({ status: "requires_payment_method" }),
        update: jest.fn(),
      },
      customers: {
        search: jest.fn().mockReturnValue({ data: [] }),
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
    };

    db.account.findUnique.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
    });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [
        {
          productId: 1,
          quantity: 2,
          product: {
            id: 1,
            price: 200,
          },
        },
      ],
      eventStartTime: new Date("2023-01-01T03:00:00Z"),
      eventEndTime: new Date("2023-01-02T03:00:00Z"),
      customer: { email: "<EMAIL>", id: "20" },
    });
    db.customer.update.mockResolvedValue({});
    db.setupSurface.findFirst.mockResolvedValue(null);
    db.setupSurface.create.mockResolvedValue({ id: "setupSurfaceId" });
    getStripeAccount.mockReturnValue(stripeMock);
    db.address.create.mockResolvedValue({ id: "addressId", ...location });
    db.order.create.mockResolvedValue({
      id: 51,
      accountId: 1,
      organizationId: "abc",
    });
    db.orderProduct.createMany.mockResolvedValue({ count: 1 });
    db.contract.create.mockResolvedValue({ id: 52 });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        couponCode: "bad",
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      error: "Invalid Coupon Code",
    });
  });

  it("should return 200 and create order successfully", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:00:00Z"),
        endTime: new Date("2022-01-01T14:00:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 3 }, productId: 1, quantity: 3 },
        ],
      },
    ]);
    const stripeMock = {
      paymentIntents: {
        create: jest.fn().mockReturnValue({
          id: "paymentIntentId",
          client_secret: "clientSecret",
        }),
        retrieve: jest
          .fn()
          .mockReturnValue({ status: "requires_payment_method" }),
        update: jest.fn(),
      },
      customers: {
        search: jest.fn().mockReturnValue({ data: [] }),
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
    };

    db.account.findUnique.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
    });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [
        {
          productId: 1,
          quantity: 2,
          product: {
            id: 1,
            price: 200,
          },
        },
      ],
      eventStartTime: new Date("2023-01-01T03:00:00Z"),
      eventEndTime: new Date("2023-01-02T03:00:00Z"),
      customer: { email: "<EMAIL>", id: "20" },
    });
    db.customer.update.mockResolvedValue({});
    db.setupSurface.findFirst.mockResolvedValue(null);
    db.setupSurface.create.mockResolvedValue({ id: "setupSurfaceId" });
    getStripeAccount.mockReturnValue(stripeMock);
    db.address.create.mockResolvedValue({ id: "addressId", ...location });
    db.order.create.mockResolvedValue({
      id: 51,
      accountId: 1,
      organizationId: "abc",
    });
    db.productRule.findMany.mockResolvedValue([]);
    db.orderProduct.createMany.mockResolvedValue({ count: 1 });
    db.contract.create.mockResolvedValue({ id: 52 });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      status: true,
      paymentIntent: "clientSecret",
      totalAmount: expect.any(Number),
      depositAmount: expect.any(Number),
      lineItems: expect.any(Array),
    });
  });

  it("should return 200 and create order successfully with product rules", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:00:00Z"),
        endTime: new Date("2022-01-01T14:00:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 3 }, productId: 1, quantity: 3 },
        ],
      },
    ]);
    const stripeMock = {
      paymentIntents: {
        create: jest.fn().mockReturnValue({
          id: "paymentIntentId",
          client_secret: "clientSecret",
        }),
        retrieve: jest
          .fn()
          .mockReturnValue({ status: "requires_payment_method" }),
        update: jest.fn(),
      },
      customers: {
        search: jest.fn().mockReturnValue({ data: [] }),
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
    };

    db.account.findUnique.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
    });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [
        {
          productId: 1,
          quantity: 2,
          product: {
            id: 1,
            price: 200,
          },
        },
      ],
      eventStartTime: new Date("2023-01-06T07:00:00Z"),
      eventEndTime: new Date("2023-01-06T13:00:00Z"),
      customer: { email: "<EMAIL>", id: "20" },
    });
    db.customer.update.mockResolvedValue({});
    db.setupSurface.findFirst.mockResolvedValue(null);
    db.setupSurface.create.mockResolvedValue({ id: "setupSurfaceId" });
    getStripeAccount.mockReturnValue(stripeMock);
    db.address.create.mockResolvedValue({ id: "addressId", ...location });
    db.order.create.mockResolvedValue({
      id: 51,
      accountId: 1,
      organizationId: "abc",
    });

    db.productRule.findMany.mockResolvedValue([
      {
        ruleId: 1,
        productId: 1,
        rule: {
          id: 1,
          name: "Test Rule",
          type: "PRICING",
          priority: 1,
          isActive: true,
          ruleJson: JSON.stringify({
            conditions: {
              all: [
                {
                  fact: "cart",
                  operator: "equal",
                  path: "$.startTimeDay",
                  value: 5,
                },
              ],
            },
            event: {
              type: "PRICE_INCREASE",
              params: {
                price: 100,
              },
            },
          }),
        },
      },
    ]);
    db.orderProduct.createMany.mockResolvedValue({ count: 1 });
    db.contract.create.mockResolvedValue({ id: 52 });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        location,
      },
    });

    expect(db.orderProduct.createMany).toBeCalledWith({
      data: [
        {
          orderId: 51,
          productId: 1,
          quantity: 2,
          pricePaid: 200,
        },
      ],
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      status: true,
      paymentIntent: "clientSecret",
      totalAmount: expect.any(Number),
      depositAmount: expect.any(Number),
      lineItems: expect.any(Array),
    });
  });

  it("should return 200 and create order successfully with coupon", async () => {
    db.product.findMany.mockResolvedValue(mockProducts);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2022-01-01T03:00:00Z"),
        endTime: new Date("2022-01-01T14:00:00Z"),
        OrderProduct: [
          { product: { id: 1, quantity: 3 }, productId: 1, quantity: 3 },
        ],
      },
    ]);
    const stripeMock = {
      paymentIntents: {
        create: jest.fn().mockReturnValue({
          id: "paymentIntentId",
          client_secret: "clientSecret",
        }),
        retrieve: jest
          .fn()
          .mockReturnValue({ status: "requires_payment_method" }),
        update: jest.fn(),
      },
      customers: {
        search: jest.fn().mockReturnValue({ data: [] }),
        create: jest.fn().mockReturnValue({ id: "stripeCustomerId" }),
      },
    };
    db.account.findUnique.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.account.findFirst.mockResolvedValue({
      id: 1,
      name: accountName,
      minimumOrderPaymentPercentage: 50,
      organizationId: "abc",
    });
    db.shoppingSession.findFirst.mockResolvedValue({
      id: shoppingId,
      CartItem: [
        {
          productId: 1,
          quantity: 2,
          product: {
            id: 1,
            price: 200,
          },
        },
      ],
      eventStartTime: new Date("2023-01-01T03:00:00Z"),
      eventEndTime: new Date("2023-01-02T03:00:00Z"),
      customer: { email: "<EMAIL>", id: "20" },
    });
    db.customer.update.mockResolvedValue({});
    db.setupSurface.findFirst.mockResolvedValue(null);
    db.setupSurface.create.mockResolvedValue({ id: "setupSurfaceId" });
    getStripeAccount.mockReturnValue(stripeMock);
    db.address.create.mockResolvedValue({ id: "addressId", ...location });
    db.productRule.findMany.mockResolvedValue([]);
    db.order.create.mockResolvedValue({
      id: 51,
      accountId: 1,
      organizationId: "abc",
    });
    db.orderProduct.createMany.mockResolvedValue({ count: 1 });
    db.contract.create.mockResolvedValue({ id: 52 });
    db.coupon.findFirst.mockResolvedValue({
      accountId: 1,
      name: couponCode,
      discount: 10,
      discountType: "percentage",
      deleted: false,
      startDate: new Date("2024-01-01"),
      endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { accountName },
      body: {
        shoppingId,
        customerData,
        setupSurface,
        damageWaiverApplied,
        couponCode,
        location,
      },
    });

    expect(JSON.parse(res._getData())).toEqual({
      status: true,
      paymentIntent: "clientSecret",
      totalAmount: expect.any(Number),
      depositAmount: expect.any(Number),
      lineItems: expect.any(Array),
    });
    expect(res._getStatusCode()).toBe(200);
  });
});

const Product1 = {
  id: 1,
  name: "Test Product",
  price: 100,
  quantity: 1,
  display: true,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image1.jpg",
      },
    },
  ],
};

const Product2 = {
  id: 2,
  name: "Test Product 2",
  price: 200,
  quantity: 0,
  display: false,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image2.jpg",
      },
    },
  ],
};

const Product3 = {
  id: 3,
  name: "Test Product 3",
  price: 300,
  quantity: 5,
  display: true,
  ProductImageUpload: [
    {
      priority: 1,
      imageUpload: {
        url: "https://example.com/image3.jpg",
      },
    },
  ],
};

const mockProducts = [Product1, Product2, Product3];
