import { OrderState } from ".prisma/client";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";
import handler, {
  createDropoffTaskFromOrder,
  createPickupTaskFromOrder,
  DriverOrderType,
  getAndFormatOrders,
  getTasksFromTimelines,
} from "~/pages/api/orders/driver";
import { describe } from "@jest/globals";
import { ITEM_LIST_TYPE } from "~/pages/orders/driver/calendar";
import { endOfDay } from "~/server/lib/time";

describe("handler", () => {
  const query = { startTime: "2023-07-21T05:00:00Z" };

  it("test", async () => {
    const orderResponse = [
      {
        id: 777,
        createdAt: new Date("2024-06-21T16:48:25.070Z"),
        updatedAt: new Date("2024-08-30T02:55:58.922Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T15:00:00.000Z"),
        endTime: new Date("2024-08-31T21:00:00.000Z"),
        customerNotes: null,
        internalNotes: null,
        setupSurfaceId: 2,
        baseTotal: 275,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: 5,
        damageWaiverAmount: 13.75,
        travelFeeAmount: 12,
        taxRate: 10,
        taxAmount: 30.08,
        taxExempt: false,
        finalTotal: 330.83,
        totalPaid: 330.84,
        eventAddressId: 874,
        couponId: null,
        saleId: null,
        customerId: "clxox80tv001ghgq7c266jrgc",
        organizationId: "orgId",
        eventAddress: {
          id: 874,
          createdAt: new Date("2024-06-21T16:47:23.270Z"),
          updatedAt: new Date("2024-06-21T16:47:23.678Z"),
          line1: "Burns Park Drive",
          line2: null,
          city: "North Little Rock",
          state: "AR",
          postalCode: "72118",
          country: "US",
          latitude: 34.7969206,
          longitude: -92.3146125,
        },
        setupSurface: {
          id: 2,
          name: "concrete",
        },
        OrderProduct: [
          {
            product: {
              id: 12,
              takeDownTimeMinutes: 45,
              setupTimeMinutes: 35,
            },
          },
        ],
        totalSetupTimeMinutes: 35,
        totalTakedownTimeMinutes: 45,
      },
      {
        id: 915,
        createdAt: new Date("2024-08-01T02:50:50.636Z"),
        updatedAt: new Date("2024-08-30T04:23:43.143Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T23:00:00.000Z"),
        endTime: new Date("2024-09-01T01:00:00.000Z"),
        customerNotes:
          "I might need to add tables and chairs closer to the event if possible.",
        internalNotes: null,
        setupSurfaceId: 1,
        baseTotal: 350,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: null,
        damageWaiverAmount: null,
        travelFeeAmount: null,
        taxRate: 10,
        taxAmount: 35,
        taxExempt: false,
        finalTotal: 385,
        totalPaid: 385,
        eventAddressId: 1005,
        couponId: null,
        saleId: null,
        customerId: "clzaoedv60014zmgn1ihvh7co",
        organizationId: "orgId",
        eventAddress: {
          id: 1005,
          createdAt: new Date("2024-08-01T02:50:49.308Z"),
          updatedAt: new Date("2024-08-01T02:50:49.637Z"),
          line1: "1108 woodland park rd",
          line2: null,
          city: "Bryanf",
          state: "AR",
          postalCode: "72022",
          country: "US",
          latitude: 34.6038777,
          longitude: -92.51206859999999,
        },
        setupSurface: {
          id: 1,
          name: "grass",
        },
        OrderProduct: [
          {
            product: {
              id: 20,
              takeDownTimeMinutes: 45,
              setupTimeMinutes: 45,
            },
          },
        ],
        totalSetupTimeMinutes: 45,
        totalTakedownTimeMinutes: 45,
      },
      {
        id: 974,
        createdAt: new Date("2024-08-21T15:38:27.311Z"),
        updatedAt: new Date("2024-08-28T23:34:56.061Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T15:30:00.000Z"),
        endTime: new Date("2024-09-01T01:00:00.000Z"),
        customerNotes: null,
        internalNotes: null,
        setupSurfaceId: 1,
        baseTotal: 400,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: null,
        damageWaiverAmount: null,
        travelFeeAmount: 48,
        taxRate: 10,
        taxAmount: 44.8,
        taxExempt: false,
        finalTotal: 492.8,
        totalPaid: 492.8,
        eventAddressId: 1049,
        couponId: null,
        saleId: null,
        customerId: "cm040lyex0026fjalouikbpq2",
        organizationId: "orgId",
        eventAddress: {
          id: 1049,
          createdAt: new Date("2024-08-21T15:35:16.324Z"),
          updatedAt: new Date("2024-08-21T15:35:16.647Z"),
          line1: "9836 Cliffside Drive",
          line2: null,
          city: "Sherwood",
          state: "AR",
          postalCode: "72120",
          country: "US",
          latitude: 34.8527378,
          longitude: -92.20566439999999,
        },
        setupSurface: {
          id: 1,
          name: "grass",
        },
        OrderProduct: [
          {
            product: {
              id: 14,
              takeDownTimeMinutes: 45,
              setupTimeMinutes: 45,
            },
          },
        ],
        totalSetupTimeMinutes: 45,
        totalTakedownTimeMinutes: 45,
      },
      {
        id: 963,
        createdAt: new Date("2024-08-16T17:55:50.710Z"),
        updatedAt: new Date("2024-08-16T17:56:27.178Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T15:00:00.000Z"),
        endTime: new Date("2024-08-31T21:00:00.000Z"),
        customerNotes: null,
        internalNotes: null,
        setupSurfaceId: 1,
        baseTotal: 275,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: 5,
        damageWaiverAmount: 13.75,
        travelFeeAmount: 28,
        taxRate: 10,
        taxAmount: 31.68,
        taxExempt: false,
        finalTotal: 348.43,
        totalPaid: 348.43,
        eventAddressId: 508,
        couponId: null,
        saleId: null,
        customerId: "clu46i2j000087ogx8vlsslqv",
        organizationId: "orgId",
        eventAddress: {
          id: 508,
          createdAt: new Date("2024-03-23T14:21:42.579Z"),
          updatedAt: new Date("2024-03-23T14:21:43.014Z"),
          line1: "3105 Seminole Trail",
          line2: null,
          city: "Sherwood",
          state: "AR",
          postalCode: "72120",
          country: "US",
          latitude: 34.8115695,
          longitude: -92.2295666,
        },
        setupSurface: {
          id: 1,
          name: "grass",
        },
        OrderProduct: [
          {
            product: {
              id: 23,
              takeDownTimeMinutes: 45,
              setupTimeMinutes: 35,
            },
          },
        ],
        totalSetupTimeMinutes: 35,
        totalTakedownTimeMinutes: 45,
      },
      {
        id: 964,
        createdAt: new Date("2024-08-17T15:40:02.217Z"),
        updatedAt: new Date("2024-08-17T15:50:09.704Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T15:00:00.000Z"),
        endTime: new Date("2024-09-01T00:00:00.000Z"),
        customerNotes: null,
        internalNotes: null,
        setupSurfaceId: 1,
        baseTotal: 150,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: 5,
        damageWaiverAmount: 7.5,
        travelFeeAmount: null,
        taxRate: 8,
        taxAmount: 12.6,
        taxExempt: false,
        finalTotal: 170.1,
        totalPaid: 170.1,
        eventAddressId: 1040,
        couponId: null,
        saleId: null,
        customerId: "clzyau5wr000pfjal6rmdz81x",
        organizationId: "orgId",
        eventAddress: {
          id: 1040,
          createdAt: new Date("2024-08-17T15:38:32.005Z"),
          updatedAt: new Date("2024-08-17T15:38:32.307Z"),
          line1: "7900 Calleghan Road",
          line2: null,
          city: "Little Rock",
          state: "AR",
          postalCode: "72210",
          country: "US",
          latitude: 34.6803646,
          longitude: -92.43947,
        },
        setupSurface: {
          id: 1,
          name: "grass",
        },
        OrderProduct: [
          {
            product: {
              id: 6,
              takeDownTimeMinutes: 15,
              setupTimeMinutes: 15,
            },
          },
        ],
        totalSetupTimeMinutes: 15,
        totalTakedownTimeMinutes: 15,
      },
      {
        id: 958,
        createdAt: new Date("2024-08-14T00:54:34.864Z"),
        updatedAt: new Date("2024-08-30T03:12:38.059Z"),
        state: "ACTIVE",
        startTime: new Date("2024-08-31T17:00:00.000Z"),
        endTime: new Date("2024-09-01T15:00:00.000Z"),
        customerNotes: null,
        internalNotes: null,
        setupSurfaceId: 1,
        baseTotal: 250,
        couponAmount: null,
        saleAmount: null,
        generalDiscountAmount: null,
        damageWaiverRate: null,
        damageWaiverAmount: null,
        travelFeeAmount: null,
        taxRate: 7.000000000000001,
        taxAmount: 17.5,
        taxExempt: false,
        finalTotal: 267.5,
        totalPaid: 267.5,
        eventAddressId: 1036,
        couponId: null,
        saleId: null,
        customerId: "clzt52qdf0009fjalgutap08e",
        organizationId: "orgId",
        eventAddress: {
          id: 1036,
          createdAt: new Date("2024-08-14T00:54:33.524Z"),
          updatedAt: new Date("2024-08-14T00:54:34.023Z"),
          line1: "7100 CORRIGAN RD",
          line2: null,
          city: "Mabelvale",
          state: "AR",
          postalCode: "72103",
          country: "US",
          latitude: 34.58981199999999,
          longitude: -92.3590876,
        },
        setupSurface: {
          id: 1,
          name: "grass",
        },
        OrderProduct: [
          {
            product: {
              id: 1,
              takeDownTimeMinutes: 45,
              setupTimeMinutes: 45,
            },
          },
        ],
        totalSetupTimeMinutes: 45,
        totalTakedownTimeMinutes: 45,
      },
    ];

    const tasks = [
      {
        id: "jlok3tfmhrg80pvyiwoocctq",
        index: 1,
        displayAction: "Pickup",
        timelineId: "timeline-cm0g4kcok0003z4ndva0mqmlb",
        durationMinutes: 45,
        durationAffectsTime: false,
        actionTimeFlexibilityMinutes: 360,
        actionTime: new Date("2024-08-31T21:00:00.000Z"),
        orderId: 777,
        notes: null,
        address: {
          id: 874,
          createdAt: new Date("2024-06-21T16:47:23.270Z"),
          updatedAt: new Date("2024-06-21T16:47:23.678Z"),
          line1: "Burns Park Drive",
          line2: null,
          city: "North Little Rock",
          state: "AR",
          postalCode: "72118",
          country: "US",
          latitude: 34.7969206,
          longitude: -92.3146125,
        },
      },
      {
        id: "g27wd5v1ixviy11nlztlns48",
        index: 0,
        displayAction: "Drop Off",
        timelineId: "timeline-cm0g4kcok0003z4ndva0mqmlb",
        durationMinutes: 45,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2024-08-31T23:00:00.000Z"),
        orderId: 915,
        notes:
          "I might need to add tables and chairs closer to the event if possible.",
        address: {
          id: 1005,
          createdAt: new Date("2024-08-01T02:50:49.308Z"),
          updatedAt: new Date("2024-08-01T02:50:49.637Z"),
          line1: "1108 woodland park rd",
          line2: null,
          city: "Bryanf",
          state: "AR",
          postalCode: "72022",
          country: "US",
          latitude: 34.6038777,
          longitude: -92.51206859999999,
        },
      },
      {
        id: "r2tsx5fcegg7rya2tccqasrl",
        index: 2,
        displayAction: "Pickup",
        timelineId: "timeline-cm0g4kcok0003z4ndva0mqmlb",
        durationMinutes: 45,
        durationAffectsTime: false,
        actionTimeFlexibilityMinutes: 360,
        actionTime: new Date("2024-08-31T21:00:00.000Z"),
        orderId: 963,
        notes: null,
        address: {
          id: 508,
          createdAt: new Date("2024-03-23T14:21:42.579Z"),
          updatedAt: new Date("2024-03-23T14:21:43.014Z"),
          line1: "3105 Seminole Trail",
          line2: null,
          city: "Sherwood",
          state: "AR",
          postalCode: "72120",
          country: "US",
          latitude: 34.8115695,
          longitude: -92.2295666,
        },
      },
      {
        id: "n07g6lzrjarsi09dxkd1c3bp",
        index: 2,
        displayAction: "Drop Off",
        timelineId: "timeline-cm0g4fhbz0001z4ndzouhnwaw",
        durationMinutes: 45,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2024-08-31T15:30:00.000Z"),
        orderId: 974,
        notes: null,
        address: {
          id: 1049,
          createdAt: new Date("2024-08-21T15:35:16.324Z"),
          updatedAt: new Date("2024-08-21T15:35:16.647Z"),
          line1: "9836 Cliffside Drive",
          line2: null,
          city: "Sherwood",
          state: "AR",
          postalCode: "72120",
          country: "US",
          latitude: 34.8527378,
          longitude: -92.20566439999999,
        },
      },
      {
        id: "rutzicu6gliguyyetuvlakrx",
        index: 1,
        displayAction: "Drop Off",
        timelineId: "timeline-cm0g4fhbz0001z4ndzouhnwaw",
        durationMinutes: 35,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2024-08-31T15:00:00.000Z"),
        orderId: 963,
        notes: null,
        address: {
          id: 508,
          createdAt: new Date("2024-03-23T14:21:42.579Z"),
          updatedAt: new Date("2024-03-23T14:21:43.014Z"),
          line1: "3105 Seminole Trail",
          line2: null,
          city: "Sherwood",
          state: "AR",
          postalCode: "72120",
          country: "US",
          latitude: 34.8115695,
          longitude: -92.2295666,
        },
      },
      {
        id: "wo8ge3p8zg8y3zarrb8poam3",
        index: 3,
        displayAction: "Drop Off",
        timelineId: "timeline-cm0g4fhbz0001z4ndzouhnwaw",
        durationMinutes: 15,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2024-08-31T15:00:00.000Z"),
        orderId: 964,
        notes: null,
        address: {
          id: 1040,
          createdAt: new Date("2024-08-17T15:38:32.005Z"),
          updatedAt: new Date("2024-08-17T15:38:32.307Z"),
          line1: "7900 Calleghan Road",
          line2: null,
          city: "Little Rock",
          state: "AR",
          postalCode: "72210",
          country: "US",
          latitude: 34.6803646,
          longitude: -92.43947,
        },
      },
      {
        id: "wi6eac57i92vfpqifpc171en",
        index: 4,
        displayAction: "Drop Off",
        timelineId: "timeline-cm0g4fhbz0001z4ndzouhnwaw",
        durationMinutes: 45,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2024-08-31T17:00:00.000Z"),
        orderId: 958,
        notes: null,
        address: {
          id: 1036,
          createdAt: new Date("2024-08-14T00:54:33.524Z"),
          updatedAt: new Date("2024-08-14T00:54:34.023Z"),
          line1: "7100 CORRIGAN RD",
          line2: null,
          city: "Mabelvale",
          state: "AR",
          postalCode: "72103",
          country: "US",
          latitude: 34.58981199999999,
          longitude: -92.3590876,
        },
      },
    ];

    const timelines = [
      {
        id: "cm0g4fhbz0001z4ndzouhnwaw",
        startTime: new Date("2024-08-31T12:15:00.000Z"),
        tasks: tasks.filter(
          (task) => task.timelineId === "timeline-cm0g4fhbz0001z4ndzouhnwaw",
        ),
        origin: {
          line1: "6948 Alcoa Road",
          city: "Benton",
          postalCode: "72015",
          state: "AR",
          country: "US",
        },
        index: 0,
      },
      {
        id: "cm0g4kcok0003z4ndva0mqmlb",
        startTime: new Date("2024-08-31T21:00:00.000Z"),
        tasks: tasks.filter(
          (task) => task.timelineId === "timeline-cm0g4kcok0003z4ndva0mqmlb",
        ),
        origin: {
          line1: "6948 Alcoa Road",
          city: "Benton",
          postalCode: "72015",
          state: "AR",
          country: "US",
        },
        index: 1,
      },
    ];

    db.order.findMany.mockResolvedValue(orderResponse);
    db.logisticTimeline.findMany.mockResolvedValue(timelines);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: {
        startTime: "2024-08-31T05:00:00.000Z",
      },
    });

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.tasks.length).toBe(11);
  });

  it("should return 2 tasks, 1 in the timeline and 1 in the items list", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        origin: "Warehouse",
        tasks: [
          {
            id: "task1",
            index: 0,
            displayAction: "Drop Off",
            timelineId: "timeline1",
            durationMinutes: 30,
            durationAffectsTime: true,
            actionTimeFlexibilityMinutes: -480,
            actionTime: new Date("2023-07-21T10:00:00Z"),
            orderId: 1,
            notes: "Drop off notes",
            address: 1,
          },
        ],
      },
    ]);

    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        endTime: new Date("2023-07-21T10:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5, id: 1 } },
        ],
        state: OrderState.ACTIVE,
        eventAddress: {
          id: 1,
          line1: "123 Main Street",
          city: "Anywhere",
          state: "AR",
          postalCode: "72222",
          country: "US",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: query,
    });

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.tasks).toHaveLength(2);
    expect(data.tasks).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          displayAction: "Drop Off",
          timelineId: "timeline1",
        }),
        expect.objectContaining({
          displayAction: "Pickup",
          timelineId: ITEM_LIST_TYPE,
        }),
      ]),
    );
  });

  it("should return 2 tasks per order when no tasks are in the timeline", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        origin: "Warehouse",
        tasks: [],
      },
    ]);

    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        endTime: new Date("2023-07-21T10:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5, id: 1 } },
        ],
        state: OrderState.ACTIVE,
        eventAddress: {
          id: 1,
          line1: "123 Main Street",
          city: "Anywhere",
          state: "AR",
          postalCode: "72222",
          country: "US",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: query,
    });

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.tasks).toHaveLength(2);
    expect(data.tasks).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          displayAction: "Drop Off",
          timelineId: ITEM_LIST_TYPE,
        }),
        expect.objectContaining({
          displayAction: "Pickup",
          timelineId: ITEM_LIST_TYPE,
        }),
      ]),
    );
  });

  it("should return status 200 and empty arrays when there are no orders or timelines", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([]);
    db.order.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: query,
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      orders: [],
      timelines: [],
      tasks: [],
    });
  });

  it("should call db.logisticTimeline.findMany and db.order.findMany with correct parameters", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([]);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        endTime: new Date("2023-07-21T10:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5 } },
          { product: { setupTimeMinutes: 20, takeDownTimeMinutes: 10 } },
        ],
      },
    ]);

    await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: query,
    });

    expect(db.logisticTimeline.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        startTime: {
          gte: expect.any(Date),
          lt: expect.any(Date),
        },
      },
      select: {
        LogicistcTimelineStaff: true,
        id: true,
        startTime: true,
        origin: true,
        tasks: true,
      },
    });

    expect(db.order.findMany).toHaveBeenCalledWith({
      where: {
        OR: [
          { startTime: { gte: expect.any(Date), lte: expect.any(Date) } },
          { endTime: { gte: expect.any(Date), lte: expect.any(Date) } },
        ],
        AND: {
          organizationId: "orgId",
          state: OrderState.ACTIVE,
        },
      },
      include: {
        eventAddress: true,
        setupSurface: { select: { id: true, name: true } },
        OrderProduct: {
          select: {
            product: {
              select: {
                id: true,
                setupTimeMinutes: true,
                takeDownTimeMinutes: true,
              },
            },
          },
        },
      },
    });
  });

  it("should correctly calculate totalSetupTimeMinutes and totalTakedownTimeMinutes for each order", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([]);
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        endTime: new Date("2023-07-21T10:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5 } },
          { product: { setupTimeMinutes: 20, takeDownTimeMinutes: 10 } },
        ],
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.read"],
      query: query,
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      orders: [
        {
          id: 1,
          startTime: "2023-07-21T10:00:00.000Z",
          endTime: "2023-07-21T10:30:00.000Z",
          totalSetupTimeMinutes: 30,
          totalTakedownTimeMinutes: 15,
          OrderProduct: [
            { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5 } },
            { product: { setupTimeMinutes: 20, takeDownTimeMinutes: 10 } },
          ],
        },
      ],
      timelines: [],
      tasks: expect.arrayContaining([
        expect.objectContaining({
          displayAction: "Drop Off",
          timelineId: ITEM_LIST_TYPE,
          actionTime: "2023-07-21T10:00:00.000Z",
        }),
        expect.objectContaining({
          displayAction: "Pickup",
          timelineId: ITEM_LIST_TYPE,
          actionTime: "2023-07-21T10:30:00.000Z",
        }),
      ]),
    });
  });
});

describe("getTasksFromTimelines", () => {
  it("should return empty arrays when there are no orders or timelines", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([]);

    const tasks = await getTasksFromTimelines(
      "orgId",
      new Date(),
      new Date(),
      [],
    );

    expect(tasks).toEqual({ tasks: [], timelines: [] });
  });

  it("should call db.logisticTimeline.findMany with the correct parameters", async () => {
    const startDate = new Date("2023-07-21T00:00:00Z");
    const endDate = endOfDay(startDate);

    db.logisticTimeline.findMany.mockResolvedValue([]);
    db.order.findMany.mockResolvedValue([]);

    await getTasksFromTimelines("orgId", startDate, endDate, [
      {
        id: 1,
        eventAddress: {
          id: 1,
          line1: "123 Main Street",
          city: "Anywhere",
          state: "AR",
          postalCode: "72222",
          country: "US",
          createdAt: new Date(),
          updatedAt: new Date(),
          line2: null,
          latitude: null,
          longitude: null,
        },
      },
    ]);

    expect(db.logisticTimeline.findMany).toHaveBeenCalledWith({
      where: {
        organizationId: "orgId",
        startTime: {
          gte: expect.any(Date),
          lt: expect.any(Date),
        },
      },
      select: {
        LogicistcTimelineStaff: true,
        id: true,
        startTime: true,
        origin: true,
        tasks: true,
      },
    });
  });

  it("should lookup addresses for non order items", async () => {
    db.logisticTimeline.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        origin: "CUSTOMER",
        LogicistcTimelineStaff: [],
        tasks: [
          {
            id: 1,
            index: 0,
            displayAction: "Drop Off",
            durationMinutes: 30,
            durationAffectsTime: true,
            actionTimeFlexibilityMinutes: -8 * 60,
            actionTime: new Date("2023-07-21T10:00:00Z"),
            orderId: 1,
            notes: "OrderId Drop Off",
            address: 2,
          },
          {
            id: 2,
            index: 1,
            displayAction: "Drop Off",
            durationMinutes: 30,
            durationAffectsTime: true,
            actionTimeFlexibilityMinutes: -8 * 60,
            actionTime: new Date("2023-07-21T10:00:00Z"),
            orderId: null,
            notes: "Random Order Drop off",
            address: 1,
          },
        ],
      },
    ]);

    db.address.findFirst.mockResolvedValue({
      id: 1,
      line1: "123 Main Street",
      city: "Anywhere",
      state: "AR",
      postalCode: "72222",
      country: "US",
      createdAt: new Date("2024-07-21T18:31:44.306Z"),
      updatedAt: new Date("2024-07-21T18:31:44.306Z"),
      line2: null,
      latitude: null,
      longitude: null,
    });

    const results = await getTasksFromTimelines(
      "orgId",
      new Date(),
      new Date(),
      [
        {
          id: 1,
          eventAddress: {
            id: 2,
            line1: "123 Main Street",
            city: "Anywhere",
            state: "AR",
            postalCode: "72222",
            country: "US",
            createdAt: new Date("2024-07-21T18:31:44.306Z"),
            updatedAt: new Date("2024-07-21T18:31:44.306Z"),
            line2: null,
            latitude: null,
            longitude: null,
          },
        },
      ],
    );

    expect(db.address.findFirst).toHaveBeenCalledWith({
      where: {
        id: 1,
      },
    });
    expect(results.tasks).toEqual([
      {
        id: 1,
        index: 0,
        durationAffectsTime: true,

        displayAction: "Drop Off",
        timelineId: undefined,
        durationMinutes: 30,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2023-07-21T10:00:00.000Z"),
        orderId: 1,
        notes: "OrderId Drop Off",
        address: {
          city: "Anywhere",
          country: "US",
          createdAt: new Date("2024-07-21T18:31:44.306Z"),
          id: 2,
          latitude: null,
          line1: "123 Main Street",
          line2: null,
          longitude: null,
          postalCode: "72222",
          state: "AR",
          updatedAt: new Date("2024-07-21T18:31:44.306Z"),
        },
      },
      {
        id: 2,
        index: 1,
        displayAction: "Drop Off",
        timelineId: undefined,
        durationMinutes: 30,
        durationAffectsTime: true,
        actionTimeFlexibilityMinutes: -480,
        actionTime: new Date("2023-07-21T10:00:00.000Z"),
        orderId: null,
        notes: "Random Order Drop off",
        address: {
          city: "Anywhere",
          country: "US",
          createdAt: new Date("2024-07-21T18:31:44.306Z"),
          id: 1,
          latitude: null,
          line1: "123 Main Street",
          line2: null,
          longitude: null,
          postalCode: "72222",
          state: "AR",
          updatedAt: new Date("2024-07-21T18:31:44.306Z"),
        },
      },
    ]);
    expect(results.timelines).toEqual([
      {
        LogicistcTimelineStaff: [],
        id: 1,
        startTime: new Date("2023-07-21T10:00:00.000Z"),
        origin: {
          city: undefined,
          country: undefined,
          line1: undefined,
          line2: undefined,
          postalCode: undefined,
          state: undefined,
        },
        staff: [],
        tasks: undefined,
        index: 0,
      },
    ]);
  });
});

describe("getAndFormatOrders", () => {
  it("should return null if there are no orders", async () => {
    db.order.findMany.mockResolvedValue([]);

    const orders = await getAndFormatOrders("orgId", new Date(), new Date());

    expect(orders).toBeNull();
  });

  it("should return accurately format order times", async () => {
    db.order.findMany.mockResolvedValue([
      {
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        endTime: new Date("2023-07-21T10:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5, id: 1 } },
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5, id: 1 } },
        ],
        state: OrderState.ACTIVE,
      },
      {
        id: 2,
        startTime: new Date("2023-07-21T09:00:00Z"),
        endTime: new Date("2023-07-21T09:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 10, takeDownTimeMinutes: 5, id: 1 } },
        ],
        state: OrderState.ACTIVE,
      },
      {
        id: 3,
        startTime: new Date("2023-07-21T11:00:00Z"),
        endTime: new Date("2023-07-21T11:30:00Z"),
        OrderProduct: [
          { product: { setupTimeMinutes: 0, takeDownTimeMinutes: 0, id: 1 } },
        ],
        state: OrderState.ACTIVE,
      },
    ]);

    const orders = await getAndFormatOrders(
      1,
      new Date("2023-07-21T00:00:00Z"),
      new Date("2023-07-21T23:59:59Z"),
    );

    expect(orders).toEqual([
      {
        OrderProduct: [
          {
            product: {
              id: 1,
              setupTimeMinutes: 10,
              takeDownTimeMinutes: 5,
            },
          },
          {
            product: {
              id: 1,
              setupTimeMinutes: 10,
              takeDownTimeMinutes: 5,
            },
          },
        ],
        endTime: new Date("2023-07-21T10:30:00Z"),
        id: 1,
        startTime: new Date("2023-07-21T10:00:00Z"),
        state: "ACTIVE",
        totalSetupTimeMinutes: 20,
        totalTakedownTimeMinutes: 10,
      },
      {
        OrderProduct: [
          {
            product: {
              id: 1,
              setupTimeMinutes: 10,
              takeDownTimeMinutes: 5,
            },
          },
        ],
        endTime: new Date("2023-07-21T09:30:00Z"),
        id: 2,
        startTime: new Date("2023-07-21T09:00:00Z"),
        state: "ACTIVE",
        totalSetupTimeMinutes: 10,
        totalTakedownTimeMinutes: 5,
      },
      {
        OrderProduct: [
          {
            product: {
              id: 1,
              setupTimeMinutes: 0,
              takeDownTimeMinutes: 0,
            },
          },
        ],
        endTime: new Date("2023-07-21T11:30:00Z"),
        id: 3,
        startTime: new Date("2023-07-21T11:00:00Z"),
        state: "ACTIVE",
        totalSetupTimeMinutes: 0,
        totalTakedownTimeMinutes: 0,
      },
    ]);
  });
});

describe("createDropoffTaskFromOrder", () => {
  it("should return a task with the correct properties for a dropoff", () => {
    const order: DriverOrderType = {
      accountId: 0,
      baseTotal: 0,
      couponAmount: null,
      setupSurface: { id: 0, name: "Surface" },
      couponId: null,
      createdAt: new Date(),
      customerId: "",
      damageWaiverAmount: null,
      damageWaiverRate: null,
      endTime: new Date("2023-07-21T10:30:00Z"),
      eventAddressId: 0,
      finalTotal: 0,
      generalDiscountAmount: null,
      internalNotes: null,
      saleAmount: null,
      saleId: null,
      setupSurfaceId: 0,
      state: OrderState.ACTIVE,
      taxAmount: 3,
      taxExempt: false,
      taxRate: 2,
      totalPaid: 0,
      totalTakedownTimeMinutes: 0,
      travelFeeAmount: 1,
      updatedAt: new Date("2023-07-21T10:00:00Z"),
      id: 1,
      totalSetupTimeMinutes: 30,
      startTime: new Date("2023-07-21T10:00:00Z"),
      customerNotes: "Handle with care",
      OrderProduct: [
        {
          product: {
            setupTimeMinutes: 10,
            takeDownTimeMinutes: 5,
            id: 0,
          },
        },
        {
          product: {
            setupTimeMinutes: 20,
            takeDownTimeMinutes: 10,
            id: 1,
          },
        },
      ],
      eventAddress: {
        id: 1,
        createdAt: new Date("2023-07-21T10:00:00Z"),
        updatedAt: new Date("2023-07-21T10:00:00Z"),
        line1: "123 Main Street",
        line2: null,
        city: "Anywhere",
        state: "AR",
        postalCode: "72222",
        country: "US",
        latitude: null,
        longitude: null,
      },
    };

    const task = createDropoffTaskFromOrder(order);

    expect(task).toEqual({
      id: expect.any(String),
      index: 0,
      displayAction: "Drop Off",
      timelineId: expect.any(String),
      durationMinutes: 30,
      durationAffectsTime: true,
      actionTimeFlexibilityMinutes: -8 * 60,
      actionTime: new Date(order.startTime),
      orderId: order.id,
      notes: order.customerNotes,
      address: order.eventAddress,
    });
  });
});

describe("createPickupTaskFromOrder", () => {
  it("should return a task with the correct properties for a pickup", () => {
    const order: DriverOrderType = {
      accountId: 0,
      baseTotal: 0,
      couponAmount: null,
      setupSurface: { id: 0, name: "Surface" },
      couponId: null,
      createdAt: new Date(),
      customerId: "",
      damageWaiverAmount: null,
      damageWaiverRate: null,
      endTime: new Date("2023-07-21T10:30:00Z"),
      eventAddressId: 0,
      finalTotal: 0,
      generalDiscountAmount: null,
      internalNotes: null,
      saleAmount: null,
      saleId: null,
      setupSurfaceId: 0,
      state: OrderState.ACTIVE,
      taxAmount: 3,
      taxExempt: false,
      taxRate: 2,
      totalPaid: 0,
      totalTakedownTimeMinutes: 0,
      travelFeeAmount: 1,
      updatedAt: new Date("2023-07-21T10:00:00Z"),
      id: 1,
      totalSetupTimeMinutes: 30,
      startTime: new Date("2023-07-21T10:00:00Z"),
      customerNotes: "Handle with care",
      OrderProduct: [
        {
          product: {
            setupTimeMinutes: 10,
            takeDownTimeMinutes: 5,
            id: 0,
          },
        },
        {
          product: {
            setupTimeMinutes: 20,
            takeDownTimeMinutes: 10,
            id: 1,
          },
        },
      ],
      eventAddress: {
        id: 1,
        createdAt: new Date("2023-07-21T10:00:00Z"),
        updatedAt: new Date("2023-07-21T10:00:00Z"),
        line1: "123 Main Street",
        line2: null,
        city: "Anywhere",
        state: "AR",
        postalCode: "72222",
        country: "US",
        latitude: null,
        longitude: null,
      },
    };

    const task = createPickupTaskFromOrder(order);

    expect(task).toEqual({
      id: expect.any(String),
      index: 0,
      displayAction: "Pickup",
      timelineId: expect.any(String),
      durationMinutes: 0,
      durationAffectsTime: false,
      actionTimeFlexibilityMinutes: 6 * 60,
      actionTime: new Date(order.endTime),
      orderId: order.id,
      notes: order.customerNotes,
      address: order.eventAddress,
    });
  });
});
