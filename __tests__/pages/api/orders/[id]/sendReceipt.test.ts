import { describe } from "@jest/globals";
import { test<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../apiTestUtil";
import handler from "~/pages/api/orders/[id]/sendReceipt";
import { db } from "~/server/db";
import { handleOrderPayment } from "~/server/services/orderEvents";
import { sendReactEmail } from "~/server/email/sendMail";
import { RECEIPT_EMAIL } from "../../../../../emails/ReceiptEmail";

describe("basic handler", () => {
  it("Should return 400 without proper id", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.execute"],
      query: {
        startTime: "2024-08-31T05:00:00.000Z",
      },
    });

    expect(res.statusCode).toEqual(400);
    expect(res._getJSONData()).toEqual({ error: "No order id provided" });
  });

  it("Should return 400 without number id", async () => {
    const res = await test<PERSON>pi<PERSON>and<PERSON>(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.execute"],
      query: {
        id: "abc",
      },
    });

    expect(res.statusCode).toEqual(400);
    expect(res._getJSONData()).toEqual({ error: "Invalid order id" });
  });

  it("Should return 404 with valid id but no order", async () => {
    db.order.findUnique.mockResolvedValue(null);
    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.execute"],
      query: {
        id: "123",
      },
    });

    expect(db.order.findUnique).toBeCalledWith({
      where: {
        id: 123,
        accountId: 1,
      },
      include: {
        Customer: true,
        account: true,
        OrderFee: true,
        OrderDiscount: true,
        PaymentDetails: {
          include: {
            PaymentRefund: true,
          },
        },
        Contract: true,
        OrderProduct: {
          include: {
            product: true,
          },
        },
      },
    });
    expect(res.statusCode).toEqual(400);
    expect(res._getJSONData()).toEqual({ error: "Order not found" });
  });
});

describe("sendReceipt", () => {
  it("should calculate refunded amount", async () => {
    db.order.findUnique.mockResolvedValue({
      PaymentDetails: [
        {
          PaymentRefund: [{ amount: 10 }, { amount: 20 }],
        },
        {
          PaymentRefund: [{ amount: 30 }],
        },
      ],
      Contract: [{}],
      OrderProduct: [],
      OrderFee: [],
      account: {
        minimumOrderPaymentPercentage: 0,
        name: "pow",
        businessTimezone: "America/New_York",
        id: 1,
      },
      id: 123,
    });
    db.imageUpload.findFirst.mockResolvedValue({
      id: 1,
      type: "LOGO",
    });

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.execute"],
      query: {
        id: "123",
      },
    });

    expect(handleOrderPayment).toBeCalledWith(
      123,
      1,
      expect.objectContaining({
        refundedAmount: 60,
      }),
    );
    expect(res.statusCode).toEqual(200);
  });

  it("should send silent email", async () => {
    db.order.findUnique.mockResolvedValue({
      PaymentDetails: [
        {
          PaymentRefund: [{ amount: 10 }, { amount: 20 }],
        },
        {
          PaymentRefund: [{ amount: 30 }],
        },
      ],
      Contract: [{}],
      OrderProduct: [],
      OrderFee: [],
      account: {
        minimumOrderPaymentPercentage: 0,
        name: "pow",
        businessTimezone: "America/New_York",
        id: 1,
      },
      id: 123,
    });
    db.imageUpload.findFirst.mockResolvedValue({
      id: 1,
      type: "LOGO",
    });

    const res = await testApiHandler(handler, {
      method: "GET",
      permissionNodes: ["com.partyrentalplatform.api.orders.execute"],
      query: {
        id: "123",
        silent: "true",
      },
    });

    expect(sendReactEmail).toBeCalledWith(
      false,
      {
        businessTimezone: "America/New_York",
        id: 1,
        minimumOrderPaymentPercentage: 0,
        name: "pow",
      },
      "",
      RECEIPT_EMAIL,
      expect.objectContaining({
        refundedAmount: 60,
      }),
      [],
      [],
    );
    expect(res.statusCode).toEqual(200);
  });
});
