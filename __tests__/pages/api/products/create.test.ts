import { describe, expect, it } from "@jest/globals";
import handler from "~/pages/api/products/create";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("products/create.ts API Endpoint", () => {
  const accountId = 1;
  const productData = {
    name: "Test Product",
    description: "This is a test product.",
    price: 100,
    setupTimeMinutes: 10,
    takeDownTimeMinutes: 5,
    display: true,
    categories: [1],
    quantity: 10,
    slug: "test-product",
    metaTitle: "Test Product Meta Title",
    metaDescription: "Test Product Meta Description",
  };

  it("should return 405 if method is not POST", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 422 if request body is invalid", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: { invalidField: "invalidValue" },
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 400 if category is invalid", async () => {
    db.category.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: productData,
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(400);
  });

  it("should return 200 if no categories are provided", async () => {
    const modifiedProductData = { ...productData, categories: [] };
    db.product.create.mockResolvedValue({
      id: "product1",
      ...modifiedProductData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: modifiedProductData,
      permissionNodes: ["test"],
    });

    expect(db.category.findMany).not.toHaveBeenCalled();
    expect(db.product.create).toHaveBeenCalledWith({
      data: {
        accountId,
        organizationId: "orgId",
        archived: false,
        name: productData.name,
        deliverable: true,
        beforeRentalBufferMinutes: 0,
        afterRentalBufferMinutes: 0,
        description: productData.description,
        setupTimeMinutes: productData.setupTimeMinutes,
        takeDownTimeMinutes: productData.takeDownTimeMinutes,
        price: productData.price,
        display: productData.display,
        quantity: productData.quantity,
        slug: productData.slug,
        metaDescription: productData.metaDescription,
        metaTitle: productData.metaTitle,
      },
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      id: "product1",
      success: true,
    });
  });

  it("should return 200 and create product successfully", async () => {
    db.product.create.mockResolvedValue({
      id: "product1",
      ...productData,
      accountId,
    });
    db.category.findMany.mockResolvedValue([
      {
        id: 1,
        name: "Test",
        accountId,
      },
    ]);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: productData,
      permissionNodes: ["test"],
    });

    expect(db.product.create).toHaveBeenCalledWith({
      data: {
        accountId,
        organizationId: "orgId",
        archived: false,
        afterRentalBufferMinutes: 0,
        beforeRentalBufferMinutes: 0,
        deliverable: true,
        name: productData.name,
        description: productData.description,
        setupTimeMinutes: productData.setupTimeMinutes,
        takeDownTimeMinutes: productData.takeDownTimeMinutes,
        price: productData.price,
        display: productData.display,
        quantity: productData.quantity,
        slug: productData.slug,
        metaDescription: productData.metaDescription,
        metaTitle: productData.metaTitle,
      },
    });
    expect(db.productCategory.createMany).toHaveBeenCalledWith({
      data: [
        {
          categoryId: 1,
          productId: "product1",
        },
      ],
    });
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      id: "product1",
      success: true,
    });
  });
});
