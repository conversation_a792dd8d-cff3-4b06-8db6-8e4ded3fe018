import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/emails/[id]/index";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";

describe("emails/[id]/index.ts API Endpoint", () => {
  const accountId = 1;
  const emailId = "1";
  const emailData = {
    id: 1,
    name: "Test Email",
    subject: "Test Subject",
    text: "This is a test email.",
    accountId,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 405 if method is not GET", async () => {
    const res = await testApi<PERSON>andler(handler, {
      method: "POST",
      query: { id: emailId },
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 400 if email ID is invalid", async () => {
    const res = await testApi<PERSON>andler(handler, {
      method: "GET",
      query: { id: "invalid" },
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ message: "Invalid email ID" });
  });

  it("should return 404 if email is not found", async () => {
    db.customEmailDelivery.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: emailId },
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ message: "Email not found" });
  });

  it("should return 200 and the email data if email is found", async () => {
    db.customEmailDelivery.findFirst.mockResolvedValue(emailData);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: emailId },
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      email: {
        name: emailData.name,
        subject: emailData.subject,
        text: emailData.text,
      },
    });
  });
});
