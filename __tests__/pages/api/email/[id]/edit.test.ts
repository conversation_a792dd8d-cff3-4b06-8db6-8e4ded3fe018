import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/emails/[id]/edit";
import { db } from "~/server/db";
import { testApiHandler } from "../../../../apiTestUtil";

describe("emails/[id]/edit.ts API Endpoint", () => {
  const accountId = 1;
  const emailId = "1";
  const emailData = {
    id: 1,
    name: "Test Email",
    subject: "Test Subject",
    text: "This is a test email.",
    accountId,
  };
  const updatedEmailData = {
    name: "Updated Email",
    subject: "Updated Subject",
    text: "This is an updated test email.",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 405 if method is not POST", async () => {
    const res = await testApiHandler(handler, {
      method: "GET",
      query: { id: emailId },
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 400 if email ID is invalid", async () => {
    const res = await testApi<PERSON>andler(handler, {
      method: "POST",
      query: { id: "invalid" },
      body: updatedEmailData,
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ message: "Invalid email ID" });
  });

  it("should return 404 if email is not found", async () => {
    db.customEmailDelivery.findFirst.mockResolvedValue(null);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: emailId },
      body: updatedEmailData,
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ message: "Email not found" });
  });

  it("should return 422 if request body is invalid", async () => {
    db.customEmailDelivery.findFirst.mockResolvedValue(emailData);

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: emailId },
      body: { invalidField: "invalidValue" },
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 and update email successfully", async () => {
    db.customEmailDelivery.findFirst.mockResolvedValue(emailData);
    db.customEmailDelivery.update.mockResolvedValue({
      id: emailId,
      ...updatedEmailData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: { id: emailId },
      body: updatedEmailData,
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      id: emailId,
      email: updatedEmailData,
    });
  });
});
