import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/emails/index";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("emails/index.ts API Endpoint", () => {
  const emails = [
    {
      createdAt: new Date("2021-01-01"),
      id: "email1",
      name: "Email 1",
      subject: "Subject 1",
    },
    {
      createdAt: new Date("2021-01-02"),
      id: "email2",
      name: "Email 2",
      subject: "Subject 2",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 405 if method is not GET", async () => {
    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: {},
      permissionNodes: ["api.email.read"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 200 with an empty list if no emails are found", async () => {
    db.customEmailDelivery.findMany.mockResolvedValue([]);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["api.email.read"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ emails: [] });
  });

  it("should return 200 with a list of emails", async () => {
    db.customEmailDelivery.findMany.mockResolvedValue(emails);

    const res = await testApiHandler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["api.email.read"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      emails: emails.map((email) => ({
        createdAt: email.createdAt.toISOString(),
        id: email.id,
        name: email.name,
        subject: email.subject,
      })),
    });
  });
});
