import { describe, expect, it, jest, beforeEach } from "@jest/globals";
import handler from "~/pages/api/emails/create";
import { db } from "~/server/db";
import { testApiHandler } from "../../../apiTestUtil";

describe("emails/create.ts API Endpoint", () => {
  const accountId = 1;
  const emailData = {
    name: "Test Email",
    subject: "Test Subject",
    text: "This is a test email.",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return 405 if method is not POST", async () => {
    const res = await testApi<PERSON>andler(handler, {
      method: "GET",
      query: {},
      body: {},
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(405);
  });

  it("should return 422 if request body is invalid", async () => {
    const res = await testApi<PERSON>and<PERSON>(handler, {
      method: "POST",
      query: {},
      body: { invalidField: "invalidValue" },
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(422);
    expect(JSON.parse(res._getData())).toEqual(
      expect.objectContaining({ error: "Invalid Request" }),
    );
  });

  it("should return 200 and create email successfully", async () => {
    db.customEmailDelivery.create.mockResolvedValue({
      id: "email1",
      ...emailData,
      accountId,
    });

    const res = await testApiHandler(handler, {
      method: "POST",
      query: {},
      body: emailData,
      permissionNodes: ["test"],
    });

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      id: "email1",
      email: {
        name: emailData.name,
        subject: emailData.subject,
        text: emailData.text,
      },
    });
  });
});
